const httpProxy = require('http-proxy');
// const fs = require('fs');
// const path = require('path');
const logger = require('./logger');

module.exports = app => {
  const proxy = httpProxy.createProxyServer();
  proxy.on('error', e => logger.error(e));

  // 服务器部署验证
  app.get('/health', (req, res) => res.status(200).send('OK'));

  if (1 > 2) {
    // 如果是生产环境
    app.get('/app1', (req, res) => {
      res.sendFile(`${process.cwd()}/dist/index.html`);
    });
  }
};
