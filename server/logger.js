const path = require('path');
const log4js = require('log4js');
const env = process.env.DEPLOY_ENV || 'dev';
const pkg = require('../package.json');

// if (env != 'dev') {
// 日志存放位置
const logDir = env !== 'dev' ? `/alidata1/admin/${pkg.name}` : `./`;

log4js.configure({
  appenders: {
    info: {
      type: 'file',
      filename: path.join(logDir, `logs/${pkg.name}.log`),
    },
    error: {
      type: 'file',
      filename: path.join(logDir, `logs/${pkg.name}_error.log`),
    },
  },
  categories: {
    default: {
      appenders: ['error'],
      level: 'error',
    },
  },
});

const loggerInfo = log4js.getLogger('info');
const loggerError = log4js.getLogger('error');

module.exports = {
  info: msg => {
    loggerInfo.info(msg);
  },
  error: msg => {
    loggerError.error(msg);
  },
};
