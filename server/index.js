const express = require('express');

const portfinder = require('portfinder');

class Server {
  constructor(opts) {
    portfinder.getPort((err, port) => {
      const app = express();
      app.PORT = port;

      require('./express')(app);

      if (process.env.LOCAL_DEV) {
        require('./devMiddleware')(app);
      }

      const config = opts || require('./config');

      app.use(express.static(config.outputPath));

      require('./server')(app);

      require('./error')(app);

      portfinder.basePort = config.port || 8080;

      require('./bin/www')(app, port);
    });
  }
}

module.exports = Server;
