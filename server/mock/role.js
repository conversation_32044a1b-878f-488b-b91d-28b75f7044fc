module.exports = {
  'GET /dev-basp-user/account/getSysDetails': (req, res) => {
    res.send({
      msg: null,
      result: {
        systemKey: '5cb960d2c17ab0e4346b051e',
        pageNum: null,
        pageSize: null,
        systemId: *********,
        systemCode: 'EOS-FMS-OPOMS',
        systemName: '快运中转场运单查询管理平台',
        homeAddress: '-',
        systemDesc: '快运中转场运单查询管理平台',
        systemType: 1,
        isSwitchRole: 1,
        accountFrom: null,
        systemSubscribe: null,
        supportMultiRole: 1,
        moduleType: null,
        accessScheme: null,
        moduleUrl: null,
        status: 1,
        creator: '********',
        createTm: *************,
        lastUpdator: '********',
        lastUpdateTm: *************,
      },
      succ: 'ok',
    });
  },

  'GET /dev-basp-user/indexModule/queryRoles': (req, res) => {
    res.send({
      msg: null,
      result: [
        {
          roleId: '**********',
          roleCode: 'baspAdmin',
          roleName: '百源管理角色',
          isDefaultRole: '1',
          moduleId: null,
          moduleCode: null,
          moduleName: null,
          moduleIcon: null,
          moduleType: null,
          parentId: null,
          sort: null,
          subsysCode: null,
          hidden: null,
          helpUrl: null,
          childModules: [],
        },
        {
          roleId: '**********',
          roleCode: 'TL_ORDER',
          roleName: '华南分公司录单员',
          isDefaultRole: '0',
          moduleId: null,
          moduleCode: null,
          moduleName: null,
          moduleIcon: null,
          moduleType: null,
          parentId: null,
          sort: null,
          subsysCode: null,
          hidden: null,
          helpUrl: null,
          childModules: [],
        },
      ],
      succ: 'ok',
    });
  },
  'GET /dev-basp-user/indexModule/queryMenus': (req, res) => {
    res.send({
      msg: null,
      result: [
        {
          moduleId: 1010790,
          parentId: 353387,
          moduleName: '整车开单',
          moduleCode: 'tl_create_order',
          moduleIcon: 'property-safety',
          sort: 1,
          badge: 0,
          parent: null,
          hidden: 'N',
          helpUrl: null,
          childModules: [
            {
              moduleId: 1010792,
              parentId: 1010790,
              moduleName: '整车开单2',
              moduleCode: 'tl_create_order_sub2',
              moduleIcon: '',
              sort: 1,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 1010791,
              parentId: 1010790,
              moduleName: '整车开单',
              moduleCode: 'tl_create_order_sub',
              moduleIcon: '',
              sort: 2,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 1363269,
              parentId: 1010790,
              moduleName: '专运订单管理',
              moduleCode: 'bill',
              moduleIcon: 'profile',
              sort: 11,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [
                {
                  moduleId: 1363270,
                  parentId: 1363269,
                  moduleName: '专运订单手工录入',
                  moduleCode: 'inputOrder',
                  moduleIcon: 'file-image',
                  sort: 1,
                  badge: 0,
                  parent: null,
                  hidden: 'N',
                  helpUrl: null,
                  childModules: [],
                },
                {
                  moduleId: 1363271,
                  parentId: 1363269,
                  moduleName: '运输订单管理',
                  moduleCode: 'transportationOrderMgr',
                  moduleIcon: 'check-square',
                  sort: 3,
                  badge: 0,
                  parent: null,
                  hidden: 'N',
                  helpUrl: null,
                  childModules: [],
                },
              ],
            },
            {
              moduleId: 1363272,
              parentId: 1010790,
              moduleName: '红冲管理',
              moduleCode: 'redRushMgr',
              moduleIcon: 'dollar',
              sort: 11,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [
                {
                  moduleId: 1363273,
                  parentId: 1363272,
                  moduleName: '红冲',
                  moduleCode: 'redRushList',
                  moduleIcon: 'property-safety',
                  sort: 1,
                  badge: 0,
                  parent: null,
                  hidden: 'N',
                  helpUrl: null,
                  childModules: [],
                },
                {
                  moduleId: 1414788,
                  parentId: 1363272,
                  moduleName: '红冲日志查询',
                  moduleCode: 'redRushLogList',
                  moduleIcon: 'bug',
                  sort: 2,
                  badge: 0,
                  parent: null,
                  hidden: 'N',
                  helpUrl: null,
                  childModules: [],
                },
              ],
            },
          ],
        },
        {
          moduleId: 353413,
          parentId: 353387,
          moduleName: '用户管理（百源）',
          moduleCode: 'userManages(basp)',
          moduleIcon: 'usergroup-delete',
          sort: 2,
          badge: 0,
          parent: null,
          hidden: 'N',
          helpUrl: null,
          childModules: [
            {
              moduleId: 353416,
              parentId: 353413,
              moduleName: '账号授权管理（数据组）',
              moduleCode: 'accountDataGroupManage',
              moduleIcon: '',
              sort: 2,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 353415,
              parentId: 353413,
              moduleName: '账号授权管理（角色）',
              moduleCode: 'accountAssignManage',
              moduleIcon: '',
              sort: 2,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 353414,
              parentId: 353413,
              moduleName: '子账号管理',
              moduleCode: 'leafAccountManage',
              moduleIcon: '',
              sort: 2,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
          ],
        },
        {
          moduleId: 353417,
          parentId: 353387,
          moduleName: '系统管理（百源）',
          moduleCode: 'systemManage(basp)',
          moduleIcon: 'appstore',
          sort: 2,
          badge: 0,
          parent: null,
          hidden: 'N',
          helpUrl: null,
          childModules: [
            {
              moduleId: 353420,
              parentId: 353417,
              moduleName: '资源管理',
              moduleCode: 'resourceManage',
              moduleIcon: '',
              sort: 2,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 353421,
              parentId: 353417,
              moduleName: '数据组管理',
              moduleCode: 'datagroupManage',
              moduleIcon: '',
              sort: 2,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 353419,
              parentId: 353417,
              moduleName: '功能模块管理',
              moduleCode: 'moduleManage',
              moduleIcon: '',
              sort: 2,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 353418,
              parentId: 353417,
              moduleName: '角色管理',
              moduleCode: 'roleManage',
              moduleIcon: '',
              sort: 2,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
          ],
        },
        {
          moduleId: 353422,
          parentId: 353387,
          moduleName: '后台管理（百源）',
          moduleCode: 'BackAppManage',
          moduleIcon: 'windows',
          sort: 2,
          badge: 0,
          parent: null,
          hidden: 'N',
          helpUrl: null,
          childModules: [
            {
              moduleId: 353423,
              parentId: 353422,
              moduleName: '字典管理',
              moduleCode: 'dataDictionary',
              moduleIcon: '',
              sort: 2,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
          ],
        },
        {
          moduleId: 1374029,
          parentId: 353387,
          moduleName: '产品定价',
          moduleCode: 'product_pricing',
          moduleIcon: 'pay-circle',
          sort: 2,
          badge: 0,
          parent: null,
          hidden: 'N',
          helpUrl: null,
          childModules: [
            {
              moduleId: 1374030,
              parentId: 1374029,
              moduleName: '快运产品定价',
              moduleCode: 'kyProPricing',
              moduleIcon: 'edit',
              sort: 1,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 1384055,
              parentId: 1374029,
              moduleName: '增值服务定价',
              moduleCode: 'addSerPricing',
              moduleIcon: 'user-add',
              sort: 2,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 1384056,
              parentId: 1374029,
              moduleName: '价格编码',
              moduleCode: 'priceEncode',
              moduleIcon: 'money-collect',
              sort: 4,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
          ],
        },
        {
          moduleId: 900402,
          parentId: 353387,
          moduleName: '运单管理',
          moduleCode: 'newOrder',
          moduleIcon: '',
          sort: 3,
          badge: 0,
          parent: null,
          hidden: 'N',
          helpUrl: null,
          childModules: [
            {
              moduleId: 900403,
              parentId: 900402,
              moduleName: '新运单管理',
              moduleCode: 'newOrderList',
              moduleIcon: '',
              sort: 1,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
          ],
        },
        {
          moduleId: 930528,
          parentId: 353387,
          moduleName: '客户订单管理',
          moduleCode: 'customerOrderManage',
          moduleIcon: '',
          sort: 3,
          badge: 0,
          parent: null,
          hidden: 'N',
          helpUrl: null,
          childModules: [
            {
              moduleId: 930529,
              parentId: 930528,
              moduleName: '大票零担运单',
              moduleCode: 'largeTicket',
              moduleIcon: '',
              sort: 3,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
          ],
        },
        {
          moduleId: 1374031,
          parentId: 353387,
          moduleName: '基础配置',
          moduleCode: 'base_config',
          moduleIcon: 'setting',
          sort: 3,
          badge: 0,
          parent: null,
          hidden: 'N',
          helpUrl: null,
          childModules: [
            {
              moduleId: 1374032,
              parentId: 1374031,
              moduleName: '产品维护',
              moduleCode: 'proMaintain',
              moduleIcon: 'solution',
              sort: 1,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [
                {
                  moduleId: 1374033,
                  parentId: 1374032,
                  moduleName: '主产品基础配置',
                  moduleCode: 'mainProBaseConfig',
                  moduleIcon: 'code-sandbox',
                  sort: 1,
                  badge: 0,
                  parent: null,
                  hidden: 'N',
                  helpUrl: null,
                  childModules: [],
                },
                {
                  moduleId: 1374034,
                  parentId: 1374032,
                  moduleName: '增值服务',
                  moduleCode: 'addedServices',
                  moduleIcon: 'import',
                  sort: 2,
                  badge: 0,
                  parent: null,
                  hidden: 'N',
                  helpUrl: null,
                  childModules: [],
                },
              ],
            },
            {
              moduleId: 1394708,
              parentId: 1374031,
              moduleName: '进位配置',
              moduleCode: 'carryConfig',
              moduleIcon: 'percentage',
              sort: 2,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [
                {
                  moduleId: 1394709,
                  parentId: 1394708,
                  moduleName: '进位配置',
                  moduleCode: 'carryCfg',
                  moduleIcon: 'number',
                  sort: 1,
                  badge: 0,
                  parent: null,
                  hidden: 'N',
                  helpUrl: null,
                  childModules: [],
                },
              ],
            },
            {
              moduleId: 1394710,
              parentId: 1374031,
              moduleName: '行政信息维护',
              moduleCode: 'admInfoMaintain',
              moduleIcon: 'wallet',
              sort: 3,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [
                {
                  moduleId: 1394711,
                  parentId: 1394710,
                  moduleName: '行政区信息维护',
                  moduleCode: 'admRegionMaintain',
                  moduleIcon: 'profile',
                  sort: 1,
                  badge: 0,
                  parent: null,
                  hidden: 'N',
                  helpUrl: null,
                  childModules: [],
                },
                {
                  moduleId: 1394712,
                  parentId: 1394710,
                  moduleName: '网店信息维护',
                  moduleCode: 'NetworkInfoMaintain',
                  moduleIcon: 'windows',
                  sort: 2,
                  badge: 0,
                  parent: null,
                  hidden: 'N',
                  helpUrl: null,
                  childModules: [],
                },
              ],
            },
          ],
        },
        {
          moduleId: 900398,
          parentId: 353387,
          moduleName: '外发单管理',
          moduleCode: 'outOrder',
          moduleIcon: '',
          sort: 4,
          badge: 0,
          parent: null,
          hidden: 'N',
          helpUrl: null,
          childModules: [
            {
              moduleId: 900399,
              parentId: 900398,
              moduleName: '外发单列表',
              moduleCode: 'outOrderList',
              moduleIcon: '',
              sort: 3,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 930539,
              parentId: 900398,
              moduleName: '外发网点配置',
              moduleCode: 'outerNetManage',
              moduleIcon: '',
              sort: 4,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [
                {
                  moduleId: 930540,
                  parentId: 930539,
                  moduleName: '外发网点列表',
                  moduleCode: 'outerNetList',
                  moduleIcon: '',
                  sort: 4,
                  badge: 0,
                  parent: null,
                  hidden: 'N',
                  helpUrl: null,
                  childModules: [],
                },
              ],
            },
            {
              moduleId: 960707,
              parentId: 900398,
              moduleName: '外发回单审核',
              moduleCode: 'outerOrderCheckManage',
              moduleIcon: '',
              sort: 5,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [
                {
                  moduleId: 960708,
                  parentId: 960707,
                  moduleName: '外发回单审核列表',
                  moduleCode: 'outerOrderVerify',
                  moduleIcon: '',
                  sort: 6,
                  badge: 0,
                  parent: null,
                  hidden: 'N',
                  helpUrl: null,
                  childModules: [],
                },
              ],
            },
          ],
        },
        {
          moduleId: 900400,
          parentId: 353387,
          moduleName: '外发异常管理',
          moduleCode: 'outError',
          moduleIcon: '',
          sort: 5,
          badge: 0,
          parent: null,
          hidden: 'N',
          helpUrl: null,
          childModules: [
            {
              moduleId: 900401,
              parentId: 900400,
              moduleName: '外发异常列表',
              moduleCode: 'outErrorList',
              moduleIcon: '',
              sort: 5,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
          ],
        },
        {
          moduleId: 930531,
          parentId: 353387,
          moduleName: '供应商管理',
          moduleCode: 'supplierManage',
          moduleIcon: '',
          sort: 6,
          badge: 0,
          parent: null,
          hidden: 'N',
          helpUrl: null,
          childModules: [
            {
              moduleId: 930532,
              parentId: 930531,
              moduleName: '供应商列表',
              moduleCode: 'supplierList',
              moduleIcon: '',
              sort: 1,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 1141502,
              parentId: 930531,
              moduleName: '增值服务',
              moduleCode: 'appreciation',
              moduleIcon: '',
              sort: 1,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
            {
              moduleId: 1141500,
              parentId: 930531,
              moduleName: '基础运费',
              moduleCode: 'baseFreight',
              moduleIcon: '',
              sort: 1,
              badge: 0,
              parent: null,
              hidden: 'N',
              helpUrl: null,
              childModules: [],
            },
          ],
        },
      ],
      succ: 'ok',
    });
  },
  'GET /gateway/check_token': (req, res) => {
    res.send({
      obj: {
        deviceid: 'cadeadbcfd1571645585291',
        systemKey: '5d77584b6223f20c4cb08049',
        authType: 'bdus',
        username: '01386840',
        appkey: 'FOP-KACS-CORE',
        userid: '01386840',
      },
      success: true,
    });
  },
  'GET /opomsVehicleOrder/tlRedRushService/queryOrder': (req, res) => {
    res.send({
      requestId: null,
      success: true,
      business: null,
      errorCode: null,
      errorMessage: null,
      params: null,
      date: null,
      version: null,
      obj: {
        order: {
          orderId: '9431056756573697',
          createTime: 1576647986000,
          modifyTime: 1576836550000,
          waybillNo: 'SF7000300233897',
          returnTrackingNo: 'SF1200000166153',
          needReturnTracking: 'Y',
          creator: null,
          modifier: 'SYS',
          deptTree: '001|CN01|755Y|755DL|',
          orderSource: 'OP',
          orderChannel: 'PC',
          clientCode: null,
          channelOrderId: null,
          businessType: 'CARPOOL',
          customerNo: '7556000676',
          customerName: null,
          productCode: 'SE0020',
          expressType: 'B1',
          cargoType: 'SP616',
          limitType: 'T12',
          payType: 3,
          settlementType: '2',
          departureTime: 1576647985000,
          vestedZoneCode: '755U',
          vestedZoneName: null,
          pickupEmployee: '82008028',
          pickupZoneCode: '755U',
          dispatchEmployee: '82008028',
          dispatchZoneCode: '755U',
          currency: 'CNY',
          orderStatus: 'ORDER_STATUS_COLLECT',
          billingStatus: 'BILLING_STATUS_CONFIRM',
          transportStatus: '104',
          estBaseCost: 100.0,
          baseFee: 2000.0,
          carrierEstFee: null,
          carrierBillingFee: null,
          lineCode: '755DL531L1416',
          lineRequireId: '82019121800000123',
          requirePushTime: 1576655018000,
          userRemark: '寄件备注',
          totalWeight: 100.0,
          totalVolume: 20.0,
          totalQuantity: 1,
          totalFee: 3520.0,
          financeBu: null,
          billingBu: null,
          version: 8,
        },
        tlOrderInfo: {
          id: 9431056757032449,
          orderId: '9431056756573697',
          collectTime: 1576836069000,
          deliverTime: null,
          feeConfirmTime: null,
          feeDeliveredTime: null,
          senderContact: '寄件人',
          receiverContact: '收货人',
        },
        addressList: [
          {
            id: 9431137473169024,
            orderId: '9431056756573697',
            waybillNo: null,
            createTime: 1576650449000,
            modifyTime: 1576650449000,
            creator: '01383467',
            modifier: '01383467',
            province: '广东省',
            city: '深圳市',
            district: '南山区',
            provinceCode: 'A440000000',
            cityCode: 'A440300000',
            districtCode: 'A440305000',
            areaCode: '755',
            address: '科研路18号招商银行研发中心A座',
            remark: '寄件备注',
            zoneCode: '755DL',
            zoneName: '深圳重货快运集散点',
            zoneAreaCode: '755DL',
            zoneAreaName: '后海速运营业部',
            hqCode: 'CN01',
            hqName: '华南大区',
            longitude: '113.939354',
            latitude: '22.523846',
            distance: 0.0,
            contactName: '寄件人',
            contactTel: null,
            company: '寄件公司',
            contactPhone: '13222222222',
            addresssType: '1',
            version: null,
          },
          {
            id: 9431137473201793,
            orderId: '9431056756573697',
            waybillNo: null,
            createTime: 1576650449000,
            modifyTime: 1576650449000,
            creator: null,
            modifier: null,
            province: '广东省',
            city: '深圳市',
            district: '南山区',
            provinceCode: 'A440000000',
            cityCode: 'A440300000',
            districtCode: 'A440305000',
            areaCode: '755',
            address: '软件产业基地',
            remark: null,
            zoneCode: null,
            zoneName: null,
            zoneAreaCode: null,
            zoneAreaName: null,
            hqCode: null,
            hqName: null,
            longitude: '116.834185',
            latitude: '36.549502',
            distance: 1914.83,
            contactName: '经停点联系人',
            contactTel: null,
            company: null,
            contactPhone: '13566666666',
            addresssType: '2',
            version: null,
          },
          {
            id: 9431137473267329,
            orderId: '9431056756573697',
            waybillNo: null,
            createTime: 1576650449000,
            modifyTime: 1576650449000,
            creator: '01383467',
            modifier: '01383467',
            province: '山东省',
            city: '济南市',
            district: '长清区',
            provinceCode: 'A370000000',
            cityCode: 'A370100000',
            districtCode: 'A370113000',
            areaCode: '531',
            address: '山东师范大学',
            remark: '收件备注',
            zoneCode: '531L',
            zoneName: '大学城速运营业点',
            zoneAreaCode: '531L',
            zoneAreaName: '大学城速运营业点',
            hqCode: 'CN03',
            hqName: '华北大区',
            longitude: '116.834185',
            latitude: '36.549502',
            distance: 1914.83,
            contactName: '收货人',
            contactTel: null,
            company: '收货公司',
            contactPhone: '***********',
            addresssType: '3',
            version: null,
          },
        ],
        addedServiceList: [
          {
            id: 9431137473463938,
            orderId: '9431056756573697',
            addedServiceCode: 'IN02',
            addedServiceName: '保价',
            value: 4000.0,
            value1: '5.0',
            value2: '20',
            value3: null,
            value4: null,
            version: null,
            createDate: 1576650449000,
            creator: '01383467',
            modifyDate: 1576650449000,
            modifer: '01383467',
          },
          {
            id: 9431137473496704,
            orderId: '9431056756573697',
            addedServiceCode: 'IN67',
            addedServiceName: '个性化包装服务',
            value: 500.0,
            value1: null,
            value2: null,
            value3: null,
            value4: null,
            version: null,
            createDate: 1576650449000,
            creator: '01383467',
            modifyDate: 1576650449000,
            modifer: '01383467',
          },
          {
            id: 9431137473529473,
            orderId: '9431056756573697',
            addedServiceCode: 'IN114',
            addedServiceName: '顺丰护卫',
            value: 1000.0,
            value1: 'SUPERVISE_GUARDIAN',
            value2: null,
            value3: null,
            value4: null,
            version: null,
            createDate: 1576650449000,
            creator: '01383467',
            modifyDate: 1576650449000,
            modifer: '01383467',
          },
        ],
        cargoList: [
          {
            id: 9431137473332865,
            orderId: '9431056756573697',
            waybillNo: null,
            createTime: 1576650449000,
            modifyTime: 1576650449000,
            creator: '01383467',
            modifier: '01383467',
            version: null,
            cargoName: '冰箱',
            cargoTypeCode: '10',
            cargoTypeName: '家电',
            pkgTypeCode: '1',
            pkgTypeName: '裸包装',
            quantity: 1,
            amountPreUnit: null,
            cargoAmount: '600',
            weight: 100.0,
            volume: 20.0,
            remark: null,
          },
        ],
        intlAddress: {
          id: 9431056756901377,
          orderId: '9431056756573697',
          waybillNo: null,
          createTime: 1576647986000,
          modifyTime: 1576647986000,
          intlCountry: '国际整车国家',
          intlCity: '国际整车城市',
          intlContact: '国际整车联系人',
          intlContactMobile: 'we12355435234',
          intlAddress: '国际整车地址',
        },
        requireVehicleList: [
          {
            id: 9431137473398401,
            orderId: '9431056756573697',
            waybillNo: null,
            createDate: 1576650449000,
            modifyDate: 1576650449000,
            vehicleTypeCode: '002005',
            vehicleTypeName: '冷藏车-单温',
            weightTypeCode: '20',
            weightTypeName: '20',
            volumeRance: '20',
            weightRange: '20',
            deptContact: '用车联系人',
            deptContactMobile: '***********',
            remark: null,
            version: null,
          },
        ],
        payZoneCode: null,
        payer: '7556000676',
        paymentType: 'PR_3RD',
      },
    });
  },
  'GET /opomsVehicleOrder/tlOrderPermissionService/getDeptListByUserId': (
    req,
    res,
  ) => {
    res.send({
      requestId: null,
      success: true,
      business: null,
      errorCode: null,
      errorMessage: null,
      params: null,
      date: null,
      version: null,
      obj: [
        {
          code: '020Y',
          name: '广州区部',
        },
        {
          code: '751ZPVA',
          name: '韶关武江直派营业部',
        },
        {
          code: '020ZPWE',
          name: '广州穗北直派营业部',
        },
        {
          code: '020ZPWD',
          name: '广州番禺直派营业部',
        },
        {
          code: '020ZPW',
          name: '广州新塘直派营业部',
        },
        {
          code: '020BBB',
          name: '广州速运虚拟集配站',
        },
        {
          code: '020GHN',
          name: '三元里经营分部',
        },
        {
          code: '020AN',
          name: '天球大厦速运营业点',
        },
        {
          code: '020ANCL',
          name: '金谷创意园营业站',
        },
        {
          code: '020ANAL',
          name: '新市西街营业站',
        },
      ],
    });
  },
  'POST /opomsVehicleOrder/tlOrder/orderDetail': (req, res) => {
    res.send({
      requestId: null,
      success: true,
      business: null,
      errorCode: null,
      errorMessage: null,
      params: null,
      date: null,
      version: null,
      obj: {
        order: {
          orderId: '9434318365385472',
          orderSource: 'OP',
          businessType: 'VEHICLE',
          customerNo: '7556000676',
          payWay: 'PR_3RD',
          senderName: '寄件人',
          senderPhone: '***********',
          senderCompany: '***********',
          senderProvince: '北京市',
          senderProvinceCode: 'A110000000',
          senderCity: '北京市',
          senderCityCode: 'A111000000',
          senderCounty: '东城区',
          senderCountyCode: 'A110101000',
          senderCityAreaCode: '010',
          senderAddress: '清华大学附属小学商务中心区实验小学',
          senderDeptCode: '020Y',
          senderDeptName: '广州区部',
          senderLongitude: '116.455',
          senderLatitude: '39.907',
          pickupEmployee: '012313',
          senderRemark: '清华大学附属小学商务中心区实验小学',
          receiverName: '收货人',
          receiverPhone: '***********',
          receiverCompany: '腾讯科技（深圳）有限公司',
          receiverProvince: '北京市',
          receiverProvinceCode: 'A110000000',
          receiverCity: '北京市',
          receiverCityCode: 'A111000000',
          receiverCounty: '东城区',
          receiverCountyCode: 'A110101000',
          receiverCityAreaCode: '010',
          receiverAddress: '清华大学澜园商业区步行街',
          receiverDeptCode: '010GD',
          receiverDeptName: '清华园速运营业点',
          receiverRemark: '清华大学澜园商业区步行街',
          receiverLongitude: '116.327',
          receiverLatitude: '40.003',
          currency: 'CNY',
          dispatchEmployee: '012313',
        },
        stopOverInfoList: [
          {
            name: '经停点联系人',
            phone: '13566666661',
            province: '北京市',
            provinceCode: 'A110000000',
            city: '北京市',
            cityCode: 'A111000000',
            county: '东城区',
            countyCode: 'A110101000',
            cityAreaCode: '010',
            address: '清华大学洁华幼儿园',
            deptCode: null,
            longitude: '116.327',
            latitude: '39.997',
          },
        ],
        cargo: {
          cargoName: 'VEHICLE',
          cargoTypeCode: '1',
          quantity: 1,
          weight: 100.0,
          volume: 20.0,
          pkgTypeCode: '1',
          pkgTypeName: '裸包装',
          cargoAmount: '600',
        },
        fee: {
          needReturnTrack: 'Y',
          individualPkgFee: 500.0,
          sfGuardianTypeCode: 'SUPERVISE_GUARDIAN',
          sfGuardianFee: 1000.0,
          declaredValue: 4000.0,
          insuranceRate: 1.0,
          estBaseCost: 100.0,
          baseFee: 2000.0,
        },
        vehicle: {
          departureTime: 1576749226000,
          deptContact: '用车联系人',
          deptContactMobile: '***********',
          volumeRance: '3',
          vehicleTypeCode: '002001',
          vehicleTypeName: '厢式运输车',
          weightTypeCode: '3',
          weightTypeName: '3',
          weightRange: '3',
        },
        userId: null,
        intlAddress: {
          intlCountry: '国际整车国家',
          intlCity: '国际整车城市',
          intlContact: '国际整车联系人',
          intlContactMobile: '***********',
          intlAddress: '国际整车地址',
        },
        submitType: null,
      },
    });
  },
  'POST /opomsVehicleOrder/tlRedRushService/queryLog': (req, res) => {
    res.send({
      requestId: null,
      success: true,
      business: null,
      errorCode: null,
      errorMessage: null,
      params: null,
      date: null,
      version: null,
      obj: {
        currentPage: 1,
        pageSize: 10,
        startRecord: 0,
        totalPage: 1,
        totalRecord: 1,
        datas: [
          {
            contents: [
              {
                redRushContent: '拖寄物品类名称',
                objBefore: '冰箱',
                objAfter: '家电',
              },
              {
                redRushContent: '月结卡号',
                objBefore: '9999999999',
                objAfter: '7550045286',
              },
            ],
            operateTime: 1576726831000,
            operator: 'shenxin',
            orderId: '9394343955001984',
            waybillNo: 'SF7000300235715',
            reason: 'reason',
            remark: 'remark',
          },
        ],
      },
    });
  },
  'POST /opomsVehicleOrder/tlOrder/milestone': (req, res) => {
    res.send({
      requestId: null,
      success: true,
      business: null,
      errorCode: null,
      errorMessage: null,
      params: null,
      date: null,
      version: null,
      obj: [
        {
          id: 9434318367417088,
          orderId: '9434318365385472',
          waybillNo: null,
          createTime: 1576747522000,
          occurTime: 1576747522000,
          milestoneType: 'ORDER_STATUS',
          milestoneCode: '',
          milestoneDesc: '新增',
          operator: '01383467',
        },
        {
          id: 9434322096448257,
          orderId: '9434318365385472',
          waybillNo: 'SF7000300235098',
          createTime: 1576747636000,
          occurTime: 1576747636000,
          milestoneType: 'ORDER_STATUS',
          milestoneCode: '',
          milestoneDesc: '提交',
          operator: '01383467',
        },
        {
          id: 9434322111030016,
          orderId: '9434318365385472',
          waybillNo: 'SF7000300235098',
          createTime: 1576747636000,
          occurTime: 1576747637000,
          milestoneType: 'GROUND',
          milestoneCode: '104',
          milestoneDesc: '已下发',
          operator: 'SYSTEM',
        },
        {
          id: 9434322367767297,
          orderId: '9434318365385472',
          waybillNo: 'SF7000300235098',
          createTime: 1576747644000,
          occurTime: 1576747645000,
          milestoneType: 'GROUND',
          milestoneCode: '105',
          milestoneDesc: '已受理',
          operator: 'SYSTEM',
        },
        {
          id: 9434323491515009,
          orderId: '9434318365385472',
          waybillNo: null,
          createTime: 1576747678000,
          occurTime: 1576747679000,
          milestoneType: 'ORDER_OPT',
          milestoneCode: '',
          milestoneDesc: '修改订单',
          operator: '01383467',
        },
        {
          id: 9434323769813632,
          orderId: '9434318365385472',
          waybillNo: 'SF7000300235098',
          createTime: 1576747687000,
          occurTime: 1576747687000,
          milestoneType: 'GROUND',
          milestoneCode: '105',
          milestoneDesc: '已受理',
          operator: 'SYSTEM',
        },
      ],
    });
  },
};
