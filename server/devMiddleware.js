const webpack = require('webpack');
const webpackMiddleware = require('webpack-dev-middleware');
const webpackHotMiddleware = require('webpack-hot-middleware');
const path = require('path');
const FriendlyErrorsPlugin = require('friendly-errors-webpack-plugin');
const apiMocker = require('webpack-api-mocker');
const proxy = require('http-proxy-middleware');
const fs = require('fs');
const Config = fs.readFileSync(path.resolve('src/config.js'), 'utf8');
const basePath = Config.match(/basePath[\s\S]*?\'([a-z,A-Z,-/]+)\'/)[1];
console.log('basePath', basePath);

const createNotifierCallback = () => {
  const notifier = require('node-notifier');

  return (severity, errors) => {
    if (severity !== 'error') return;

    const error = errors[0];
    const filename = error.file && error.file.split('!').pop();

    notifier.notify({
      title: '编译错误',
      message: `${severity}:  ${error.name}`,
      subtitle: filename || '',
      // icon: path.join(__dirname, 'logo.png'),
    });
  };
};

const config = require(`${path.normalize(
  `${process.cwd()}`,
)}/webpack.config.dev`);

module.exports = app => {
  config.plugins.push(
    new FriendlyErrorsPlugin({
      compilationSuccessInfo: {
        messages: [
          `Your application is running here: http://localhost:${app.PORT}`,
        ],
      },
      onErrors: createNotifierCallback(),
      // onErrors: config.dev.notifyOnErrors
      //   ? utils.createNotifierCallback()
      //   : undefined,
    }),
  );
  const compiler = webpack(config);
  const devMiddleware = webpackMiddleware(compiler, {
    noInfo: true,
    publicPath: config.output.publicPath,
    // logger:
    logLevel: 'silent',
    hot: true,
    historyApiFallback: true,
    stats: {
      colors: true,
    },
  });

  app.use(devMiddleware);

  app.use(webpackHotMiddleware(compiler));

  /**
   * 网关代理
   */
  app.use(
    '/systemApi',
    proxy({
      target: 'http://127.0.0.1:8082',
      changeOrigin: true,
      secure: false,
      pathRewrite: {
        '^/systemApi': '', // rewrite path
      },
    }),
  );

  app.use(
    '/threeplorder',
    proxy({
      target: 'http://scs-api-gw.intsit.sfdc.com.cn:1080',
      changeOrigin: true,
      secure: false,
      onProxyReq(proxyReq, req) {
        console.log(proxyReq, req.path);
      },
      onProxyRes(res) {
        console.log(res);
      },
      // pathRewrite: {
      //   '^/node': '/node', // rewrite path
      // },
    }),
  );

  app.use(
    '/apiRest',
    proxy({
      target: 'http://fop.sit.sf-express.com/',
      changeOrigin: true,
      secure: false,
      pathRewrite: {
        '^/apiRest': 'restApi',
      },
    }),
  );

  app.get(new RegExp(`(${basePath})/?`), (req, res) => {
    devMiddleware.fileSystem.readFile(
      `${process.cwd()}/dist/index.html`,
      (err, file) => {
        if (err) {
          res.sendStatus(404);
        } else {
          res.send(file.toString());
        }
      },
    );
  });
  if (process.env.MOCK) {
    apiMocker(app, path.resolve('./server/mock/index.js'));
  }
  app.use(
    '/assets',
    proxy({
      target: 'http://fop.sit.sf-express.com/',
      changeOrigin: true,
      secure: false,
    }),
  );

  app.use(
    '/restApi',
    proxy({
      // target: 'https://10.206.228.113',
      target: 'http://fop.sit.sf-express.com/',
      changeOrigin: true,
      secure: false,
      // pathRewrite: {
      //   '^/restApi': '', // rewrite path
      // },
    }),
  );

  app.use(
    '/',
    proxy({
      // target: 'https://10.206.228.113',
      target: 'http://fop.sit.sf-express.com/',
      changeOrigin: true,
      secure: false,
    }),
  );
};
