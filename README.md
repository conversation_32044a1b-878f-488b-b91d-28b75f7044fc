# 大件计提系统 PC 端

## 项目开发

### 目录结构：

```
|__ server        # express js mock服务端
|__ src
  |__ components  # 组件库
  |__ generators  # 模板代码代码生成 见package.json中的scripts: gen
  |__ models      # 状态管理
  |__ pages       # 业务页面
  |__ router      # 路由
  |__ services    # api
  |__ utils       # 工具方法
|__ ...           # 相关配置
```

### 技术栈：

- "react": "^16.12.0"
- "dva": "^2.4.1"
- "antd": "4.7.3" 版本固定，建议和 ky-giant 公共组件库所用的 antd 版本保持一致

### 编码规范

@kyfe

### 安装：

Node Version: <= V14

```
npm install
```

### dev 环境：

```
npm run dev
```

### 线上环境：

```
npm run build
```

### 部署：

采用顺丰云流水线部署

## 其它

### git 地址：

http://git.sf-express.com/projects/FOP-WEB/repos/fop-web-ica-fcams/browse

### 测试环境地址：

https://fop.sit.sf-express.com/webs/fcams/

### 生产环境地址：

https://fop.sf-express.com/webs/fcams/

### 测试账号

- 01417604
- 顺心账号 sx18070791

## 注意事项

1. 相关权限开通找：01383467
2. 项目存在许多快运自研 npm 包：

- "@ky/caslogin": "^3.0.0", cas 登陆
- "@ky/request": "^1.1.4", api 请求封装
- "@ky/sensors": "^1.1.1",
- "@ky/static-tool": "^1.1.4",
- "ky-giant": "^2.2.17", 公用组件库
  - 项目所用的 antd 版本需要跟着该包所用的 antd 版本升级而升级，不建议单独升级，避免不符合预期的情况产生
