{"name": "cms-boilerplate", "version": "1.0.1", "description": "", "main": "index.js", "scripts": {"start:onlyserver": "cross-env onlyserver=true nodemon serverer.js", "start": "cross-env node serverer.js", "sit": "npm run build:sit && node ssh.js", "dev:mock": "cross-env LOCAL_DEV=true cross-env MOCK=true DEPLOY_ENV=\"dev\" node serverer.js", "dev": "cross-env LOCAL_DEV=true cross-env DEPLOY_ENV=\"dev\" node serverer.js", "build:prod": "cross-env DEPLOY_ENV=\"prod\" webpack --config  webpack.config.prod.js", "build:sit": "cross-env DEPLOY_ENV=\"sit\" webpack --config  webpack.config.prod.js", "gen": "plop --plopfile src/generators/index.js", "build": "npx -p @ky/line-build kycicd build", "prettify": "prettier --write", "lint-staged": "lint-staged"}, "author": "<PERSON>", "license": "ISC", "devDependencies": {"@babel/core": "^7.12.3", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-async-to-generator": "^7.12.1", "@babel/plugin-transform-spread": "^7.12.1", "@babel/polyfill": "^7.8.3", "@babel/preset-env": "^7.12.1", "@babel/preset-react": "^7.12.5", "antd": "4.7.3", "antd-dayjs-webpack-plugin": "^1.0.1", "autoprefixer": "^9.1.3", "autoupdate-webpack-plugin": "^1.0.2", "babel-loader": "^8.1.0", "babel-plugin-dva-hmr": "^0.4.2", "babel-plugin-import": "^1.13.1", "compression": "^1.7.3", "cross-env": "^5.2.1", "css-loader": "^1.0.0", "debug": "^3.1.0", "dva-loading": "^3.0.2", "ejs": "^2.6.1", "es6-promise": "^4.2.4", "eslint": "^7.2.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-kyfe": "^1.7.11", "eslint-config-prettier": "^3.0.1", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-prettier": "^2.6.2", "eslint-plugin-react": "^7.11.1", "eventsource-polyfill": "^0.9.6", "express": "^4.16.3", "extract-text-webpack-plugin": "^4.0.0-beta.0", "file-loader": "^2.0.0", "friendly-errors-webpack-plugin": "^1.7.0", "gg-editor": "^2.0.3", "happypack": "^5.0.1", "html-webpack-plugin": "^4.0.0-alpha", "husky": "^4.3.0", "is-json": "^2.0.1", "lazy-route": "^1.0.7", "less": "^3.9.0", "less-loader": "^4.1.0", "lint-staged": "^10.5.1", "lodash-decorators": "^6.0.1", "log4js": "^2.5.3", "memoize-one": "^5.0.4", "mini-css-extract-plugin": "^1.2.1", "node-notifier": "^6.0.0", "nodemon": "^1.18.11", "numeral": "^2.0.6", "optimize-css-assets-webpack-plugin": "^5.0.4", "plop": "^2.5.3", "portfinder": "^1.0.25", "postcss-flexbugs-fixes": "^4.1.0", "postcss-loader": "^3.0.0", "prettier": "^1.19.1", "prettier-eslint": "^8.8.2", "prettier-stylelint": "^0.4.2", "prop-types": "^15.6.2", "qs": "^6.9.0", "sass": "^1.23.0", "sass-loader": "^8.0.0", "slash2": "^2.0.0", "style-loader": "^0.23.0", "stylelint-config-standard": "^18.2.0", "urijs": "^1.19.1", "url-loader": "^1.1.1", "webpack": "^4.18.0", "webpack-api-mocker": "^1.6.6", "webpack-bundle-analyzer": "^3.9.0", "webpack-cli": "^3.3.12", "webpack-dev-middleware": "^3.3.0", "webpack-dev-server": "^3.1.6", "webpack-hot-middleware": "^2.22.3", "webpack-merge": "^4.1.4", "webpack-parallel-uglify-plugin": "^1.1.0"}, "dependencies": {"@ant-design/compatible": "0.0.1-rc.1", "@ky/caslogin": "^3.0.0", "@ky/request": "^1.1.4", "@ky/static-tool": "^1.1.4", "@sentry/react": "^8.33.1", "classnames": "^2.2.6", "cookie-parser": "^1.4.3", "core-js": "^3.10.1", "dayjs": "1.9.4", "dva": "^2.4.1", "html2canvas": "^1.4.1", "http-proxy-middleware": "^0.19.1", "ky-giant": "^2.3.5", "qrcode.react": "^2.0.0", "react": "^16.12.0", "react-activation": "^0.3.4", "react-container-query": "^0.11.0", "react-dom": "^16.12.0", "react-intl-universal": "^2.2.2", "react-media": "^1.9.2", "react-resizable": "^1.8.0", "react-router-dom": "^4.3.1", "react-viewer": "^3.2.2", "sa-sdk-javascript": "^1.27.1"}}