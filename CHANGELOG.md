
---
## 2020.06.08 更新内容

1.修改头部，左边菜单样式

2.修改 tab 样式

3.修改 noAuth 实现方式，[添加未授权的路由](http://confluence.sf-express.com/pages/viewpage.action?pageId=85207307)

## 2020.06.09 更新内容

1.修改 tab 栏样式

2.修改用户管理-数据组管理 bug

## 2020.06.11 更新内容

1.解决 casLogin 登录先弹出 401 的问题（将 checkToken promise 化），修改了 casLogin.js 以及 app.js

2.window.g_cas.logout()方法统一在 global/logout 中调用

3.更新 download 下载列表，下载时下载按钮会显示 loading

4.删除文件中的 finally，预防可能的兼容性问题

5.修改 importButton，exportButton 中 history 的引用路径

## 2020.06.17 更新内容

1.修改导入导出按钮功能，修复按钮在不作为功能按钮时不能显示的 bug

2.修改用户管理->账号授权管理中新增时角色列表不能翻页的 bug

## 2020.06.24 更新内容

- 导入按钮组件修正同步异步判断逻辑、增加回调函数、同步导出增加导出错误日志展示
