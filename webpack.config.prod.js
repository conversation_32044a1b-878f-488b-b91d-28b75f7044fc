const merge = require('webpack-merge');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

// const OptimizeCSSAssetsPlugin = require('optimize-css-assets-webpack-plugin');
const webpack = require('webpack');
const slash = require('slash2');
const path = require('path');
const fs = require('fs');

const Config = fs.readFileSync('./src/config.js', 'utf8');
// const basePath = Config.match(/basePath[\s\S]*\'(\w+)\'/)[1];
const basePath = Config.match(/basePath[\s\S]*?\'([a-z,A-Z,-/]+)\'/)[1];
const them = require('./them.js');
// const VConsolePlugin = require('vconsole-webpack-plugin');
const webpackConfigBase = require('./webpack.config.base.js');

const prodConfig = {
  mode: 'production',

  entry: {
    app: './src/app.js',
  },

  output: {
    publicPath: `/${basePath}/`,
    filename: 'static/js/[name].[hash:8].js',
    chunkFilename: 'static/js/[name].[hash:8].js',
    path: path.resolve(__dirname, 'dist'),
  },
  module: {
    rules: [
      {
        test: /\.css$/,
        exclude: /node_modules/,
        use: [
          {
            loader: require.resolve('style-loader'),
          },
          {
            loader: 'css-loader',
            options: {
              importLoaders: 1,
            },
          },
          MiniCssExtractPlugin.loader,
        ],
      },
      {
        test: /\.scss$/,
        use: [
          {
            loader: require.resolve('style-loader'),
          },
          {
            loader: 'css-loader',
            options: {
              importLoaders: 1,
            },
          },
          {
            loader: 'sass-loader',
          },
        ],
      },
      {
        test: /\.less$/,
        use: [
          {
            loader: require.resolve('style-loader'),
          },
          {
            loader: require.resolve('css-loader'),
            options: {
              modules: true,
              getLocalIdent: (context, localIdentName, localName) => {
                if (
                  context.resourcePath.includes('node_modules') ||
                  context.resourcePath.includes('ant.design.pro.less') ||
                  context.resourcePath.includes('global.less')
                ) {
                  return localName;
                }
                const match = context.resourcePath.match(/src(.*)/);
                if (match && match[1]) {
                  const antdProPath = match[1].replace('.less', '');
                  const arr = slash(antdProPath)
                    .split('/')
                    .map(a => a.replace(/([A-Z])/g, '-$1'))
                    .map(a => a.toLowerCase());
                  return `antd-pro${arr.join('-')}-${localName}`.replace(
                    /--/g,
                    '-',
                  );
                }
                return localName;
              },
            },
          },
          {
            loader: 'less-loader',
            options: {
              modifyVars: them,
              javascriptEnabled: true,
            },
          },
        ],
      },
    ],
  },
  performance: {
    hints: 'warning', // 枚举
    maxAssetSize: 300000, // 整数类型（以字节为单位）
    maxEntrypointSize: 500000, // 整数类型（以字节为单位）
    assetFilter(assetFilename) {
      // 提供资源文件名的断言函数
      return assetFilename.endsWith('.css') || assetFilename.endsWith('.js');
    },
  },

  plugins: [
    // extract-text-webpack-plugin 提取css， 改为 mini-css-extract-plugin
    new MiniCssExtractPlugin({
      filename: 'static/css/[name].[hash:8].css',
    }),
    new webpack.DefinePlugin({
      'process.env.MOCK': process.env.MOCK,
      'process.env.DEPLOY_ENV': JSON.stringify(process.env.DEPLOY_ENV),
    }),
    new webpack.HotModuleReplacementPlugin(),
    // new ParallelUglifyPlugin({
    //   cacheDir: '.cache/',
    //   uglifyJS: {
    //     output: {
    //       comments: false,
    //     },
    //     compress: {
    //       // 删除所有的 `console` 语句，可以兼容ie浏览器
    //       drop_console: true,
    //       // 内嵌定义了但是只用到一次的变量
    //       collapse_vars: true,
    //       // 提取出出现多次但是没有定义成变量去引用的静态值
    //       reduce_vars: true,
    //     },
    //     warnings: false,
    //   },
    // }),
  ],
};

const newConfig = merge(webpackConfigBase, prodConfig);

module.exports = newConfig;
