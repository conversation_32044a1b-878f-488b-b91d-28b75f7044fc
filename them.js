module.exports = {
  'primary-color': '#DC1E32',
  'primary-color-hover': '#DC1E32',
  'main-color': '#386FFF',
  '@success-color': '#6CCE3B',
  '@info-color': '#32A8E7',
  '@warning-color': '#F3992B',
  '@error-color': '#F06A69',
  'layout-header-height': '56px',
  'border-radius-base': '4px',
  'checkbox-size': '16px',
  'checkbox-group-item-margin-right': '7px',
  'radio-size': '14px',
  'radio-border-width': '3px',
  'btn-font-weight': '500',
  'btn-border-radius-base': '4px',
  'btn-disable-color': '#BBB',
  'btn-font-size-lg': '14px',
  'switch-min-width': '36px',
  'switch-height': '20px',
  'text-color-secondary': '#666666',
  'text-color': '#333333',
  'font-size-base': '14px',
  'primary-5': '#FF4659',
  'primary-1': '#FFF2F3',
  'menu-item-font-size': '14px',
  'layout-header-background': '#272524',
  'border-radius-sm': '4px',
  'input-height-sm': '28px',
  'input-height-base': '32px',
  'input-height-lg': '40px',
  'input-padding-horizontal-sm': '9px',
  'input-padding-horizontal-base': '11px',
  'input-padding-horizontal-lg': '15px',
  'input-placeholder-color': '#BBBBBB',
  'input-border-color': '#DDDDDD',
  'input-color': '#333333',
  'input-hover-border-color': '#FF4659',
  'input-disabled-bg': '#F5F5F5',
  'input-disabled-color': '#BBBBBB',
  'select-border-color': '#DDDDDD',
  'select-item-selected-bg': 'rgba(0, 0, 0, 0)',
  'select-item-selected-color': '#DC1E32',
  'background-color-light': '#F2F2F2',
  'card-head-font-size': '18px',
  'card-head-height': '72px',
  'carousel-dot-width': '32px',
  'carousel-dot-height': '4px',
  'carousel-dot-active-width': '32px',
  'collapse-content-padding': '14px 16px',
  'list-item-padding-sm': '8px 12px',
  'list-item-padding-lg': '12px',
  'table-header-bg': '#F2F2F2',
  'table-padding-vertical': '10px',
  'table-padding-horizontal': '10px',
  'tabs-horizontal-padding': '9px 25px',
};
