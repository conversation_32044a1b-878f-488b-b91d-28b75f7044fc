// const MergeLessPlugin = require('antd-pro-merge-less');
// const AntDesignThemePlugin = require('antd-theme-webpack-plugin');
const merge = require('webpack-merge');
const webpack = require('webpack');
// const VConsolePlugin = require('vconsole-webpack-plugin');
// const autoprefixer = require('autoprefixer');
const slash = require('slash2');

const webpackConfigBase = require('./webpack.config.base.js');
const them = require('./them.js');
// const outFile = path.join(__dirname, './.temp/ant-design-pro.less');
// const stylesDir = path.join(__dirname, './src/');

const devConfig = {
  mode: 'development',
  devtool: 'source-map',
  module: {
    rules: [
      {
        test: /\.css$/,
        use: [
          {
            loader: 'style-loader',
          },
          {
            loader: 'css-loader',
          },
        ],
      },

      {
        test: /\.scss$/,
        use: [
          {
            loader: 'style-loader',
          },

          {
            loader: 'css-loader',
            options: {
              importLoaders: 1,
            },
          },
          // {
          //   loader: 'postcss-loader',
          //   options: {
          //     sourceMap: true,
          //   },
          // },
          {
            loader: 'sass-loader',
            options: {
              sourceMap: true,
              implementation: require('sass'),
            },
          },
        ],
      },

      {
        test: /\.less$/,
        use: [
          {
            loader: require.resolve('style-loader'),
          },
          {
            loader: require.resolve('css-loader'),
            options: {
              modules: true,
              getLocalIdent: (context, localIdentName, localName) => {
                if (
                  context.resourcePath.includes('pages') ||
                  context.resourcePath.includes('node_modules') ||
                  context.resourcePath.includes('ant.design.pro.less') ||
                  context.resourcePath.includes('global.less')
                ) {
                  return localName;
                }
                const match = context.resourcePath.match(/src(.*)/);
                if (match && match[1]) {
                  const antdProPath = match[1].replace('.less', '');
                  const arr = slash(antdProPath)
                    .split('/')
                    .map(a => a.replace(/([A-Z])/g, '-$1'))
                    .map(a => a.toLowerCase());
                  return `antd-pro${arr.join('-')}-${localName}`.replace(
                    /--/g,
                    '-',
                  );
                }
                return localName;
              },
            },
          },
          {
            loader: require.resolve('less-loader'),
            options: {
              modifyVars: them,
              javascriptEnabled: true,
            },
          },
        ],
      },
    ],
  },
  plugins: [
    // new webpack.NamedModulesPlugin(),
    new (require('autoupdate-webpack-plugin'))(['ky-giant']),
    new webpack.HotModuleReplacementPlugin(),
    new webpack.DefinePlugin({
      'process.env.DEPLOY_ENV': JSON.stringify(process.env.DEPLOY_ENV),
      'process.env.MOCK': process.env.MOCK,
    }),
    // }),
    // share the same chunks across different modules
  ],
};

module.exports = merge(webpackConfigBase, devConfig);
