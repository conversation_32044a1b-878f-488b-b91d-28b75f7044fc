// const CopyWebpackPlugin = require('copy-webpack-plugin');
const AntdDayjsWebpackPlugin = require('antd-dayjs-webpack-plugin'); // 将dayjs替换成dayjs
const HtmlWebpackPlugin = require('html-webpack-plugin');
const ParallelUglifyPlugin = require('webpack-parallel-uglify-plugin');
const webpack = require('webpack');
// require('@ky/static-tool/src/loader')
// const StaticPlugin = require('@ky/static-tool');
// const StaticObj = new StaticPlugin({
//   packageName: 'ky-giantss', // 这个是要检查的组件所在的npm包
//   projectCode: 'fop-web-ica-fcams', // 工程项目编码
//   environment() {
//     return process.env.DEPLOY_ENV === 'sit';
//   }, // environment 当前的环境值， 可以配置为一个字符串 如process.env.NODE_ENV,
//   // 值为production 时表示可以进行统计上传
//   // 也可以配置为一个函数，返回true代表可以进行统计，false表示不能进行统计，
//   // 因为目前各个项目代表的环境变量不统一，所以才配置为函数

//   isDev: false, // 是否开启调试测试，true为开启调试，可以模拟上传，看看这个plugin有效果否，//数据会上传到测试数据库
// });
// const PluginStatic = StaticObj.getPlugin();

const path = require('path');

const os = require('os');
const HappyPack = require('happypack');
const happyThreadPool = HappyPack.ThreadPool({ size: os.cpus().length / 2 });

module.exports = {
  entry: {
    app: [
      'eventsource-polyfill',
      'webpack-hot-middleware/client?reload=true',
      './src/app.js',
    ],
  },

  output: {
    filename: '[name].js',
    publicPath: '/',
    path: path.resolve(__dirname, 'dist'),
  },
  resolve: {
    extensions: ['.jsx', '.js', '.ts', '.less'],
    alias: {
      '@': path.join(__dirname, '/src'),
      src: path.join(__dirname, '/src'),
      actions: path.join(__dirname, '/src/actions'),
      style: path.join(__dirname, '/src/style'),
      constants: path.join(__dirname, '/src/constants'),
      containers: path.join(__dirname, '/src/containers'),
      components: path.join(__dirname, '/src/components'),
      reducers: path.join(__dirname, 'reducers'),
      utils: path.join(__dirname, '/src/utils'),
    },
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        use: [{ loader: '@ky/static-tool/src/loader' }],
        exclude: [/(node_modules)/],
      },
      {
        test: /\.(js|jsx)$/,
        // include: path.resolve('src'),
        exclude: /(node_modules)/,
        // use: [
        //   {
        //     loader: 'babel-loader',
        //     options: {
        //       // babel-loader在执行的时候，可能会产生一些运行期间重复的公共文件，造成代码体积大冗余，同时也会减慢编译效率
        //       cacheDirectory: true,
        //     },
        //   },
        // ],
        use: ['happypack/loader?id=happy-babel'],
      },
      {
        test: /\.(jpg|jpeg|gif|png)$/,
        use: [
          {
            loader: 'url-loader',
            options: {
              limit: 10000,
              name: 'static/images/[name].[hash:8].[ext]',
            },
          },
        ],
      },
      {
        test: /\.(eot|ttf|woff|woff2|svg)$/,
        use: 'file-loader?name=fonts/[name].[hash:8].[ext]',
      },
    ],
  },

  optimization: {
    minimizer: [
      new ParallelUglifyPlugin({
        cacheDir: '.cache/',
        uglifyJS: {
          output: {
            comments: false,
          },
          compress: {
            // 删除所有的 `console` 语句，可以兼容ie浏览器
            drop_console: true,
            // 内嵌定义了但是只用到一次的变量
            collapse_vars: true,
            // 提取出出现多次但是没有定义成变量去引用的静态值
            reduce_vars: true,
          },
          warnings: false,
        },
      }),
      // new OptimizeCSSAssetsPlugin({}),
    ],
    // externals: {
    //   react: 'React',
    //   'react-dom': 'ReactDOM',
    //   antd: 'antd',
    //   // lodash: {
    //   //   commonjs: 'lodash',
    //   //   amd: 'lodash',
    //   //   root: '_', // 指向全局变量
    //   // },
    // },
    splitChunks: {
      chunks: 'all', // 如果值为string，有三个可选项：all、async和initial。
      minSize: 30000, // 提取出的新chunk在两次压缩(打包压缩和服务器压缩)之前要大于30kb
      maxSize: 0, // 提取出的新chunk在两次压缩之前要小于多少kb，默认为0，即不做限制
      minChunks: 1, // 被提取的chunk最少需要被多少chunks共同引入
      maxAsyncRequests: 5, // 最大按需载入chunks提取数
      maxInitialRequests: 3, // 最大初始同步chunks提取数
      automaticNameDelimiter: '-', // 自定义连接符，默认是‘～’
      name: true,
      cacheGroups: {
        // antd: {
        //   name: 'antd',
        //   test: /[\\/]node_modules[\\/]antd[\\/]/,
        //   chunks: 'all',
        //   priority: 6,
        //   reuseExistingChunk: false,
        // },
        // 'ant-design': {
        //   name: 'ant-design',
        //   test: /[\\/]node_modules[\\/]@ant-design[\\/]/,
        //   chunks: 'all',
        //   priority: 6,
        //   reuseExistingChunk: false,
        // },
        // 缓存组配置，默认有vendors和default
        vendors: {
          name: 'vendor',
          test: /[\\/]node_modules[\\/]/,
          chunks: 'all',
          priority: 5,
          reuseExistingChunk: true,
        },
        common: {
          name: 'common',
          test: /[\\/]src[\\/]components[\\/]/,
          minSize: 1024,
          chunks: 'all',
          priority: 1,
          reuseExistingChunk: true,
        },
        default: {
          minChunks: 2,
          priority: -1,
        },
      },
    },
  },

  plugins: [
    new HtmlWebpackPlugin({
      template: './src/index.html',
      chunks: ['app'],
      filename: 'index.html',
    }),
    // new PluginStatic({}),
    new webpack.HashedModuleIdsPlugin(),
    new AntdDayjsWebpackPlugin(),
    new HappyPack({
      id: 'happy-babel',
      loaders: [
        {
          loader: 'babel-loader?cacheDirectory=true',
        },
      ],
      threadPool: happyThreadPool,
      verbose: true,
    }),
  ],
};
