import React, { Component } from 'react';
import Intl from 'react-intl-universal';
import { Layout, message } from 'antd';
import Animate from 'rc-animate';
import { connect } from 'dva';
import GlobalHeader from 'src/components/GlobalHeader';
import TopNavHeader from 'src/components/TopNavHeader';

import styles from './Header.less';

const { Header } = Layout;

class HeaderView extends Component {
  state = {
    visible: true,
  };

  static getDerivedStateFromProps(props, state) {
    if (!props.autoHideHeader && !state.visible) {
      return {
        visible: true,
      };
    }
    return null;
  }

  componentDidMount() {
    document.addEventListener('scroll', this.handScroll, { passive: true });
  }

  componentWillUnmount() {
    document.removeEventListener('scroll', this.handScroll);
  }

  getHeadWidth = () => {
    const { isMobile, collapsed, setting } = this.props;
    const { fixedHeader, layout } = setting;
    if (isMobile || !fixedHeader || layout === 'topmenu') {
      return '100%';
    }
    return collapsed ? 'calc(100% - 80px)' : 'calc(100% - 240px)';
  };

  handleNoticeClear = type => {
    message.success(
      'component.noticeIcon.cleared',
      `${Intl.get('component.noticeIcon.cleared')} ${Intl.get(
        `component.globalHeader.${type}`,
      )}`,
    );
    const { dispatch } = this.props;
    dispatch({
      type: 'global/clearNotices',
      payload: type,
    });
  };

  handleMenuClick = ({ key }) => {
    const { dispatch } = this.props;

    if (key === 'logout') {
      dispatch({
        type: 'global/logout',
      });
    }
  };

  handScroll = () => {
    const { autoHideHeader } = this.props;
    const { visible } = this.state;
    if (!autoHideHeader) {
      return;
    }
    const scrollTop =
      document.body.scrollTop + document.documentElement.scrollTop;
    if (!this.ticking) {
      const tabEL = document.querySelector(
        '.antd-pro-layouts-basic-layout-tabs',
      );
      this.ticking = true;
      requestAnimationFrame(() => {
        if (this.oldScrollTop > scrollTop) {
          this.setState({
            visible: true,
          });
          tabEL.style.top = `${
            document.querySelector(
              '.antd-pro-components-global-header-index-header',
            ).offsetHeight
          }px`;
        } else if (scrollTop > 300 && visible) {
          this.setState({
            visible: false,
          });
          tabEL.style.top = 0;
        } else if (scrollTop < 300 && !visible) {
          this.setState({
            visible: true,
          });
          tabEL.style.top = `${
            document.querySelector(
              '.antd-pro-components-global-header-index-header',
            ).offsetHeight
          }px`;
        }
        this.oldScrollTop = scrollTop;
        this.ticking = false;
      });
    }
  };

  render() {
    const { isMobile, handleMenuCollapse, setting } = this.props;
    const { navTheme, layout, fixedHeader } = setting;
    const { visible } = this.state;
    const isTop = layout === 'topmenu';
    const width = this.getHeadWidth();
    const HeaderDom = visible ? (
      <Header
        style={{ padding: 0, width }}
        className={fixedHeader ? styles.fixedHeader : ''}
      >
        {isTop && !isMobile ? (
          <TopNavHeader
            theme={navTheme}
            mode="horizontal"
            onCollapse={handleMenuCollapse}
            onNoticeClear={this.handleNoticeClear}
            onMenuClick={this.handleMenuClick}
            onNoticeVisibleChange={this.handleNoticeVisibleChange}
            {...this.props}
          />
        ) : (
          <GlobalHeader
            onCollapse={handleMenuCollapse}
            onNoticeClear={this.handleNoticeClear}
            onMenuClick={this.handleMenuClick}
            onNoticeVisibleChange={this.handleNoticeVisibleChange}
            {...this.props}
          />
        )}
      </Header>
    ) : null;
    return (
      <Animate component="" transitionName="fade">
        {HeaderDom}
      </Animate>
    );
  }
}

export default connect(({ global, setting, loading }) => ({
  // currentUser: user.currentUser,
  systemRoles: global.roles,
  collapsed: global.collapsed,
  fetchingMoreNotices: loading.effects['global/fetchMoreNotices'],
  fetchingNotices: loading.effects['global/fetchNotices'],
  notices: global.notices,

  setting,
}))(HeaderView);
