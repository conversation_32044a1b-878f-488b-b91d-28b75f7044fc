@import '~antd/lib/style/themes/default.less';

.content {
  margin: 24px;
  padding-top: @layout-header-height + 40px;
  background: #fff;
  // padding-top: 84px !important;
}
.tabs {
  .ant-select-selector {
    transition: none !important;
  }
  // padding: 5px 0 0;
  background: #e6e7e8;
  position: fixed;
  top: @layout-header-height;
  // border-bottom: 1px solid @primary-color;
  z-index: 99;
  width: 100%;
  :global {
    .ant-tabs-nav-container {
      transition: none !important;
    }
    .ant-tabs-nav {
      transition: none !important;
    }

    .ant-tabs-tab {
      position: relative;
      z-index: 1;
      padding: 8px 24px !important;
      // border: 0 !important;
      border-top: 0;
      height: 40px;
      color: #333;
      border: 1px solid #f0efef !important;
      // border-right: 1px solid #f0efef !important;
      min-width: 100px;
      background: #fff !important;
      .ant-tabs-close-x {
        font-size: 14px !important;
      }
      &:hover {
        color: @primary-color !important;
        .ant-tabs-close-x {
          font-size: 14px !important;
          color: @primary-color !important;
        }
      }
      // margin-right: 3px !important;
      // &::before {
      //   border-top: 0 !important;
      //   content: '';
      //   display: block;
      //   position: absolute;
      //   left: 0;
      //   margin-left: -11px;
      //   top: initial !important;
      //   bottom: 0;
      //   background-repeat: no-repeat;
      //   background-color: #fff;
      //   transition: none !important;
      //   -webkit-mask: url(./images/corner.svg) no-repeat 100% 100%;
      //   mask: url(./images/corner.svg) no-repeat 100% 100%;
      //   width: 12px;
      //   height: 12px;
      // }

      // &::after {
      //   border-top: 0 !important;
      //   transition: none !important;
      //   content: '';
      //   display: block;
      //   position: absolute;
      //   right: 0;
      //   margin-right: -11px;
      //   top: initial !important;
      //   bottom: 0;
      //   transform: rotate(90deg);
      //   // border: 1px solid red;
      //   background-repeat: no-repeat;
      //   background-color: #fff;
      //   -webkit-mask: url(./images/corner.svg) no-repeat 100% 100%;
      //   mask: url(./images/corner.svg) no-repeat 100% 100%;
      //   width: 12px;
      //   height: 12px;
      //   .st0 {
      //     fill: blue;
      //   }
      // }
      // background: url(./images/default.png) no-repeat !important;
      // background-size: 100% 100% !important;
      // &:not(:first-child) {
      // margin-left: -3% !important;
      // margin: 0 30px !important;
      transition: none !important;
      // all 0s !important;

      // }
      > div {
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 24px;
        span:first-child {
          flex: 1;
        }
      }
      // border-color: transparent !important;
      // border-left: 6px solid transparent;
      // border-right: 6px solid transparent;
      // border-bottom: 5px solid #ffffff;
      // .ant-tabs-close-x {
      //   color: #fff !important;
      // }
    }
    .ant-tabs-bar {
      background: #fff !important;
      transition: all 0s !important;
      border-right: 1px solid #f0efef !important;
    }
    .ant-tabs-tab-active {
      background: #fff1f0 !important;
      position: relative;
      z-index: 10;
      height: 40px;
      color: @primary-color !important;
      .ant-tabs-close-x {
        font-size: 14px !important;
        color: @primary-color !important;
      }
      // &::before {
      //   background-color: @primary-color;
      // }

      // &::after {
      //   background-color: @primary-color;
      // }
      // background-image: url(./images/active.png) !important;
      background-size: 100% 100% !important;
      .ant-tabs-close-x {
        font-size: 14px !important;
        color: @primary-color !important;
      }
    }

    .ant-tabs-bar {
      border-right: 1px solid #f0efef !important;
      // border: 0 !important;
      margin-bottom: 0;
    }
  }
}
.mainWrap {
  // padding: 10px;
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;

  > div > .ka-content {
    height: 100%;
  }
  > div:last-child {
    flex: 1;
  }
}
