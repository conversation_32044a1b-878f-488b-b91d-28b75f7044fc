/* eslint-disable prefer-destructuring */
import React, { useEffect, useRef, useState, Fragment, useMemo } from 'react';
import { Layout, Tabs, Button } from 'antd';
import { connect } from 'dva';
import { ContainerQuery } from 'react-container-query';
import classNames from 'classnames';
import Media from 'react-media';
import SiderMenu from 'src/components/SiderMenu';
import * as AllIcons from '@ant-design/icons';
import logoImg from '@/assets/logo.png';
import Header from './Header';
import Context from './MenuContext';
import styles from './BasicLayout.less';
const { TabPane: TabsPanel } = Tabs;
const { Content } = Layout;

const query = {
  'screen-xs': {
    minWidth: 320,
    maxWidth: 575,
  },
  'screen-sm': {
    minWidth: 576,
    maxWidth: 767,
  },
  'screen-md': {
    minWidth: 768,
    maxWidth: 991,
  },
  'screen-lg': {
    minWidth: 992,
    maxWidth: 1199,
  },
  'screen-xl': {
    minWidth: 1200,
    maxWidth: 1599,
  },
  'screen-xxl': {
    minWidth: 1600,
  },
};

const LayoutFmt = props => {
  const {
    navTheme,
    layout: PropsLayout,
    children,
    location: { pathname },
    isMobile,
    tabHistory,
    menuData,
    collapsed,
    history,
    dispatch,
    fixedHeader,
    type,
    fixSiderbar,
  } = props;
  // debugger;
  // const [mainWrapHeight, setMainWrapHeight] = useState();
  const [hoverIndex, setHoverIndex] = useState();
  const mainWrap = useRef();
  const { routes, activeModuleCode } = tabHistory;
  const [activeKey, setActiveKey] = useState(activeModuleCode || pathname);
  const [routesFmt, setRoutesFmt] = useState([...routes]);
  const isTop = PropsLayout === 'topmenu';
  const { MenuUnfoldOutlined, MenuFoldOutlined } = AllIcons;
  const contentStyle = !fixedHeader
    ? { paddingTop: 0, margin: 0 }
    : { margin: 0 };

  // useEffect(() => {
  //   if (mainWrap.current) {
  //     document.removeEventListener('resize', computeHeight);
  //     document.addEventListener('resize', computeHeight);
  //   }
  // }, [mainWrap.current]);
  // const computeHeight = () => {
  //   if (mainWrap.current) {
  //     setMainWrapHeight(
  //       document.documentElement.scrollHeight - mainWrap.current.offsetTop,
  //     );
  //   }
  // };
  useEffect(() => {
    if (routes) {
      setRoutesFmt([...routes]);
    }
  }, [routes]);

  useEffect(() => {
    setActiveKey(activeModuleCode);
  }, [activeModuleCode]);
  useEffect(() => {
    // 响应浏览器前进后退，自动对应tab active
    const moduleCode = Object.keys(window.g_pathsMapping).find(
      key => window.g_pathsMapping[key] === pathname,
    );
    dispatch({
      type: 'tabHistory/setActive',
      data: moduleCode,
    });
  }, [pathname]);
  const getLayoutStyle = () => {
    if (fixSiderbar && PropsLayout !== 'topmenu' && !isMobile) {
      return {
        paddingLeft: collapsed ? '80px' : '240px',
      };
    }
    return null;
  };

  const getTabStyle = () => {
    if (fixSiderbar && PropsLayout !== 'topmenu' && !isMobile) {
      const width = window.innerWidth;

      return {
        width: collapsed ? `${width - 80}px` : `${width - 240}px`,
      };
    }
    return null;
  };

  const handleMenuCollapse = coll => {
    dispatch({
      type: 'global/changeLayoutCollapsed',
      payload: coll,
    });
  };

  const pageContent = useMemo(() => {
    const tempPage = routesFmt.find(
      route => route.moduleCode === activeKey && route.components,
    );
    return tempPage ? tempPage.components : children;
  }, [activeKey, routesFmt, children]);
  //   const logoSrc =
  //     navTheme === 'light'
  //       ? 'https://freight.sf-express.com/assets/static/images/ky/sf-black.svg'
  //       : 'https://freight.sf-express.com/assets/static/images/ky/sf-white.svg';
  const logoSrc = logoImg;
  return (
    <Layout>
      {isTop && !isMobile ? null : (
        <SiderMenu
          theme={navTheme}
          onCollapse={handleMenuCollapse}
          menuData={menuData}
          isMobile={isMobile}
          type={type}
          {...props}
          logo={logoSrc}
        />
      )}
      <Layout
        style={{
          ...getLayoutStyle(),
          minHeight: '100vh',
        }}
      >
        <div>
          <Header
            menuData={menuData}
            handleMenuCollapse={handleMenuCollapse}
            logo={logoSrc}
            isMobile={isMobile}
            {...props}
          />
        </div>

        <Content className={styles.content} style={contentStyle}>
          {isMobile && (
            <Button
              type="primary"
              shape="circle"
              onClick={() => handleMenuCollapse(!collapsed)}
              style={{ position: 'fixed', right: 10, top: 120, zIndex: 999 }}
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            />
          )}
          <Tabs
            style={{
              ...getTabStyle(),
            }}
            animated={false}
            className={styles.tabs}
            activeKey={activeKey}
            onTabClick={moduleCode => {
              // setActiveKey(moduleCode);
              const routeInfo = routesFmt.find(
                route => route.moduleCode === moduleCode,
              );
              let path = window.g_pathsMapping[moduleCode];
              let queryString = '';
              const active = routesFmt.find(
                route => route.moduleCode === moduleCode,
              );
              if (active) {
                queryString = active.queryString || '';
                path = path || active.actualPath;
              }
              dispatch({
                type: 'tabHistory/setActive',
                data: moduleCode,
              });
              if (routeInfo && !routeInfo.components) {
                history.push(`${path}${queryString}`);
              }
            }}
            tabPosition="top"
            tabBarGutter="0"
            hideAdd
            type="editable-card"
            onEdit={key => {
              dispatch({
                type: 'tabHistory/remove',
                data: key,
              });
            }}
          >
            {routesFmt.map((route, index) => {
              const { moduleName, moduleCode, params = '' } = route;

              return (
                <TabsPanel
                  dataref={route}
                  forceRender
                  tab={
                    <span
                      onMouseOver={() => {
                        setHoverIndex(index);
                      }}
                      onFocus={() => setHoverIndex(index)}
                    >
                      {Object.values(params)[Object.values(params).length - 1]
                        ? Object.values(params)[
                            Object.values(params).length - 1
                          ]
                        : moduleName}
                    </span>
                  }
                  key={moduleCode}
                  closable={
                    index !== 0 &&
                    (moduleCode === activeModuleCode ||
                      pathname ||
                      hoverIndex === index)
                  }
                ></TabsPanel>
              );
            })}
          </Tabs>

          <div
            id="global-main-wrap"
            className={styles.mainWrap}
            ref={mainWrap}
            // style={{ height: `${mainWrapHeight}px` }}
          >
            {pageContent}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

const BasicLayout = props => (
  // const { title } = props;
  <Fragment>
    {/* <DocumentTitle title={title}> */}
    <ContainerQuery query={query}>
      {params => (
        <Context.Provider>
          <div className={classNames(params)}>
            <LayoutFmt {...props} />
          </div>
        </Context.Provider>
      )}
    </ContainerQuery>
    {/* </DocumentTitle> */}
    {/* <Suspense fallback={null}>{this.renderSettingDrawer()}</Suspense> */}
  </Fragment>
);
/* */

export default connect(({ global, setting, tabHistory }) => ({
  collapsed: global.collapsed,
  layout: setting.layout,
  title: setting.title,
  tabHistory,
  menuData: global.menus,
  ...setting,
}))(props => (
  <Media query="(max-width: 599px)">
    {isMobile => <BasicLayout {...props} isMobile={isMobile} />}
  </Media>
));
