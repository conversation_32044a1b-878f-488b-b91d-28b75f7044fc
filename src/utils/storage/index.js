/**
 * <AUTHOR>
 * @version 1.0.0
 * @description 扩展 localStorage、sessionStorage
 *  usage
 *  npm install utils/storage
 *  import Storage from utils/storage
 *  let localStorage = new Storage({source : window.localStorage});   //默认source为sessionStorage
 *  let sessionStorage = new Storage();
 *  localStorage.set(name,value,timeoutMinutes) //设置指定缓存的值 默认缓存失效时间为50分钟
 *  localStorage.get(name) // 获取指定缓存的值，如果没有返回undefined
 *  localStorage.remove(name) // 移除指定缓存
 *  localStorage.clear()  // 清除所有缓存
 */

import isJSON from 'is-json';

/**
 * source支持 sessionStorage localStorage
 */

function isArray(str) {
  return /^\[[\s\S]*\]$/.test(str);
}

export default class Storage {
  constructor(
    props = {
      source: window.sessionStorage,
    },
  ) {
    const { source } = props;
    this.source = source;
  }

  get(key) {
    const data = this.source;
    const timeout = data[`${key}__expires__`];
    let val;

    // 过期失效
    if (new Date().getTime() >= timeout) {
      this.remove(key);
      val = undefined;
    } else {
      val = data[key];
      (isJSON(val) || isArray(val)) && (val = JSON.parse(val));
    }
    return val;
  }

  // 设置缓存
  // timeout：过期时间（分钟）
  set(key, value, timeout = 50) {
    const data = this.source;
    data[key] = typeof value === 'object' ? JSON.stringify(value) : value;
    if (timeout) {
      data[`${key}__expires__`] = new Date().getTime() + 1000 * 60 * timeout;
    }
    return value;
  }

  /**
   * 批量设置缓存
   * batchSet([{key :'xxx',value:'xxx',time:1},{key :'xxx1',value:'xxx2',time:1},{key :'xxx2',value:'xxx2',time:1}])
   */
  batchSet(batchArr) {
    if (!Array.isArray(batchArr)) return;
    batchArr.forEach(({ key, value, time = 50 }) => {
      this.set(key, value, time);
    });
  }

  /**
   * 批量获取
   * @param {*} keyArr
   */
  batchGet(keyArr) {
    const result = [];
    keyArr.forEach(key => {
      result.push(this.get(key));
    });
    return result;
    // if (!Array.isArray(batchArr)) return;
    // batchArr.forEach(({ key, value, time }) => {
    //   this.set(key, value, time);
    // });
  }

  clear() {
    this.source.clear();
  }

  remove(key) {
    const data = this.source;
    const value = data[key];
    delete data[key];
    delete data[`${key}__expires__`];
    return value;
  }
}
