import requestApi, { setGlobalConfig } from '@ky/request';
import { notification } from 'antd';
import Config from '../config'; // 项目中放systemKey的那个配置文件

const handleError = info => {
  notification.error({
    message: info.description,
    description: '',
  });
};
const zoneCode = localStorage.getItem('zoneCode');
setGlobalConfig({
  errCallback: handleError,
  mapBodyToData: true,
  // showErrorUrl: false,
  headers: {
    systemKey: Config.systemKey,
    'hg-zone-code': zoneCode,
  },
  handleLogin: () => {
    window.g_cas.login();
  },
  removeEmptyField: true,
});

// 由于项目中现有代码都是从 '@/utils/request'中引用 ，如果不想改老代码，可以继续在utils/request.js导出
export default requestApi;
