import moment from 'moment';
import React from 'react';
import { notification } from 'antd';

import { parse, stringify } from 'qs';

export function fixedZero(val) {
  return val * 1 < 10 ? `0${val}` : val;
}

export function success(message) {
  notification.success({
    message: '操作成功',
    description: message,
  });
}
/**
 * 将分钟转为小时
 * @param { String | Number } minutes
 * @returns string
 */
export function minutesToHours(minutes) {
  return keepDecimals(minutes / 60, 2);
}

/**
 * 保留多少位小数
 * @param { String | Number } num
 * @param { Number } digit 保留位数
 * @returns string
 */
export function keepDecimals(num, digit) {
  num += '';

  if (num.indexOf('.') === -1) return num;

  const [integer, decimal] = num.split('.');

  if (decimal.length > digit) {
    num = `${integer}.${decimal.slice(0, digit)}`;
  }

  return num;
}

/**
 * 数字转字符串，按千分位展示
 * todo：未处理科学计数法
 */

 export function formatQian (v) {
  if (Number.isNaN(v) || v === null || v === undefined) return ''

  return String(v).replace(/\d+/, (n) => n.replace(/(\d)(?=(\d{3})+$)/g, '$1,'))
}

// percentage 百分数  decimals 小数点
export function formatRatio(value, type, num) {
  // console.log(value, type, num, 'debug');
  if (value !== null && value !== undefined) {
    if (!isNaN(value) && Number(value) !== Infinity && Number(value) != null) {
      if (type === 'percentage') {
        return `${Number(value * 100).toFixed(2)}%`;
      }
      if (type === 'decimals') {
        return Number(value).toFixed(num);
      }
    }
  }
  return '--';
}

export function error(message) {
  notification.error({
    message: '操作失败',
    description: message,
  });
}

export function getTimeDistance(type) {
  const now = new Date();
  const oneDay = 1000 * 60 * 60 * 24;

  if (type === 'today') {
    now.setHours(0);
    now.setMinutes(0);
    now.setSeconds(0);
    return [moment(now), moment(now.getTime() + (oneDay - 1000))];
  }

  if (type === 'week') {
    let day = now.getDay();
    now.setHours(0);
    now.setMinutes(0);
    now.setSeconds(0);

    if (day === 0) {
      day = 6;
    } else {
      day -= 1;
    }

    const beginTime = now.getTime() - day * oneDay;

    return [moment(beginTime), moment(beginTime + (7 * oneDay - 1000))];
  }

  if (type === 'month') {
    const year = now.getFullYear();
    const month = now.getMonth();
    const nextDate = moment(now).add(1, 'months');
    const nextYear = nextDate.year();
    const nextMonth = nextDate.month();

    return [
      moment(`${year}-${fixedZero(month + 1)}-01 00:00:00`),
      moment(
        moment(
          `${nextYear}-${fixedZero(nextMonth + 1)}-01 00:00:00`,
        ).valueOf() - 1000,
      ),
    ];
  }

  const year = now.getFullYear();
  return [moment(`${year}-01-01 00:00:00`), moment(`${year}-12-31 23:59:59`)];
}

export function getPlainNode(nodeList, parentPath = '') {
  const arr = [];
  nodeList.forEach(node => {
    const item = node;
    item.path = `${parentPath}/${item.path || ''}`.replace(/\/+/g, '/');
    item.exact = true;
    if (item.children && !item.component) {
      arr.push(...getPlainNode(item.children, item.path));
    } else {
      if (item.children && item.component) {
        item.exact = false;
      }
      arr.push(item);
    }
  });
  return arr;
}

export function digitUppercase(n) {
  return n;
  // return nzh.toMoney(n);
}

function getRelation(str1, str2) {
  if (str1 === str2) {
    console.warn('Two path are equal!'); // eslint-disable-line
  }
  const arr1 = str1.split('/');
  const arr2 = str2.split('/');
  if (arr2.every((item, index) => item === arr1[index])) {
    return 1;
  }
  if (arr1.every((item, index) => item === arr2[index])) {
    return 2;
  }
  return 3;
}

function getRenderArr(routes) {
  let renderArr = [];
  renderArr.push(routes[0]);
  for (let i = 1; i < routes.length; i += 1) {
    // 去重
    renderArr = renderArr.filter(item => getRelation(item, routes[i]) !== 1);
    // 是否包含
    const isAdd = renderArr.every(item => getRelation(item, routes[i]) === 3);
    if (isAdd) {
      renderArr.push(routes[i]);
    }
  }
  return renderArr;
}

/**
 * Get router routing configuration
 * { path:{name,...param}}=>Array<{name,path ...param}>
 * @param {string} path
 * @param {routerData} routerData
 */
export function getRoutes(path, routerData) {
  let routes = Object.keys(routerData).filter(
    routePath => routePath.indexOf(path) === 0 && routePath !== path,
  );
  // Replace path to '' eg. path='user' /user/name => name
  routes = routes.map(item => item.replace(path, ''));
  // Get the route to be rendered to remove the deep rendering
  const renderArr = getRenderArr(routes);
  // Conversion and stitching parameters
  const renderRoutes = renderArr.map(item => {
    const exact = !routes.some(
      route => route !== item && getRelation(route, item) === 1,
    );
    return {
      exact,
      ...routerData[`${path}${item}`],
      key: `${path}${item}`,
      path: `${path}${item}`,
    };
  });
  return renderRoutes;
}

// 当月dayNum号24:00前  可以   修改上个月数据，上个月以前数据不能修改
// 当月dayNum号24:00后  不可以  修改上个月和上个月以前的数据
export function timeLimit(dayNum) {
  const time1 = moment()
    .date(dayNum)
    .endOf('day')
    .format('x');
  // // 当前时间
  const nowTime = new Date().getTime();
  return time1 < nowTime;
}

// 当月dayNum号24:00前  可以   修改上个月数据，上个月以前数据不能修改
// 当月dayNum号24:00后  不可以  修改上个月和上个月以前的数据
export function timeFixed(dayNum) {
  // console.log(moment(dayNum), 'moment(dayNum)');
  const time1 = moment(dayNum)
    .startOf('month')
    .add(1, 'month')
    .add(5, 'd')
    .endOf('day')
    .format('x');
  // // 当前时间
  const nowTime = new Date().getTime();
  return time1 > nowTime;
}

// 时间范围选择器限制不能跨月
export function limitCrossMonths(current, datesLimit) {
  if (!datesLimit || datesLimit.length === 0 || datesLimit.length === 1) {
    return false;
  }
  if (datesLimit.length === 2) {
    return (
      // moment(datesLimit[0]).subtract(3, 'month') > moment(current) ||
      moment(datesLimit[0])
        .add(1, 'month')
        .startOf('month') < moment(current)
    );
  }
}

export function getPageQuery() {
  return parse(window.location.href.split('?')[1]);
}

export function getQueryPath(path = '', query = {}) {
  const search = stringify(query);
  if (search.length) {
    return `${path}?${search}`;
  }
  return path;
}

/* eslint no-useless-escape:0 */
const reg = /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;

export function isUrl(path) {
  return reg.test(path);
}

/**
 * 将数字转成xx万
 * @param {}} val
 */
export function formatWan(val) {
  const v = val * 1;
  if (!v || Number.isNaN(v)) return '';

  let result = val;
  if (val > 10000) {
    result = Math.floor(val / 10000);
    result = (
      <span>
        {result}
        <span
          style={{
            position: 'relative',
            top: -2,
            fontSize: 14,
            fontStyle: 'normal',
            marginLeft: 2,
          }}
        >
          万
        </span>
      </span>
    );
  }
  return result;
}

// 给官方演示站点用，用于关闭真实开发环境不需要使用的特性
export function isAntdPro() {
  return window.location.hostname === 'preview.pro.ant.design';
}

// 入参过滤
export function dealObjectValue(obj) {
  const param = {};
  for (const key in obj) {
    if (obj[key] !== null && obj[key] !== undefined && obj[key] !== '') {
      param[key] = obj[key];
    }
  }

  return param;
}

// 去空格
export function getValueFromEvent(e) {
  return e.target.value.replace(/\s+/g, '');
}
