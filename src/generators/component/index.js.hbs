/**
 *
 * {{ properCase name }}
 *
 */

{{#if memo}}
import React, { memo } from 'react';
{{else}}
import React from 'react';
{{/if}}
// import PropTypes from 'prop-types';
// import styled from 'styled-components';

{{#if wantMessages}}
import { FormattedMessage } from 'react-intl';
import messages from './messages';
{{/if}}

function {{ properCase name }}() {
  return (
    <div>
    {{#if wantMessages}}
      <FormattedMessage {...messages.header} />
    {{/if}}
    </div>
  );
}

{{ properCase name }}.propTypes = {};

{{#if memo}}
export default memo({{ properCase name }});
{{else}}
export default {{ properCase name }};
{{/if}}
