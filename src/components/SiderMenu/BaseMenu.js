import React from 'react';
import classNames from 'classnames';
import { Menu } from 'antd';
import { Link } from 'react-router-dom';
import { connect } from 'dva';
import IconFont from 'src/components/IconFont';
import * as AllIcons from '@ant-design/icons';
import Request from 'src/utils/request';
import { noAuthRoutes } from '../../route';
import './index.less';

const { SubMenu } = Menu;
const MenuItem = Menu.Item;

// Allow menu.js config icon as string or ReactNode
//   icon: 'setting',
//   icon: 'icon-geren' #For Iconfont ,
//   icon: 'http://demo.com/icon.png',
//   icon: <Icon type="setting" />,
const getIcon = icon => {
  const DefaultIcon = AllIcons.AppstoreOutlined;
  if (AllIcons[icon]) {
    const Icon = AllIcons[icon];
    return <Icon />;
  }
  if (typeof icon === 'string' && icon.startsWith('icon-')) {
    return <IconFont type={icon} />;
  }
  return <DefaultIcon />;
};

function findMenuData(menuData, moduleCode) {
  let menuDataItem;
  // eslint-disable-next-line array-callback-return
  menuData.some(item => {
    if (item.moduleCode === moduleCode) {
      menuDataItem = item;
      return true;
    }
    if (item.childModules.length > 0) {
      menuDataItem = findMenuData(item.childModules, moduleCode);
      if (menuDataItem) return true;
    }
  });
  return menuDataItem;
}

const openKeysMap = {};
class BaseMenu extends React.Component {
  isInit = true;

  constructor(props) {
    super(props);
    this.state = {
      refreshKey: '',
      selectedKeys: [],
    };
  }

  componentDidMount() {
    window.onpopstate = () => {
      document.documentElement.scrollTop = 0;
      setTimeout(() => {
        this.setState({ refreshKey: Date.now() });
      }, 100);
    };
  }

  componentDidUpdate() {
    // 非授权菜单手动进行tab的push
    const {
      tabHistory,
      dispatch,
      location: { pathname },
      menuData,
    } = this.props;
    const { openKeys } = this.state;

    let routeInfo;
    for (const item in noAuthRoutes) {
      if (pathname.includes(item) && item !== '/') {
        const params = pathname.split(`${item}/`)[1];
        routeInfo = { ...noAuthRoutes[item], actualPath: pathname, params };
        break;
      }
    }

    if (routeInfo) {
      const { moduleCode, parentModuleCode } = routeInfo;
      if (
        // 没有展开菜单
        (!openKeys || openKeys.length === 0) &&
        parentModuleCode
      ) {
        const parentMenuData = findMenuData(menuData, parentModuleCode);
        if (parentMenuData) {
          // 存在父级
          // eslint-disable-next-line react/no-did-update-set-state
          this.setState({
            openKeys: parentMenuData.openKeys,
            selectedKeys: [parentModuleCode],
          });
        }
      }
      if (
        moduleCode &&
        (!tabHistory.routes.find(route => route.moduleCode === moduleCode) ||
          !tabHistory.routes.find(route => route.actualPath === pathname))
      ) {
        dispatch({ type: 'tabHistory/push', data: routeInfo });
      }
    }
  }

  /**
   * 获得菜单子节点
   * @memberof SiderMenu
   */
  getNavMenuItems = (menusData, parentCode) => {
    if (!menusData) {
      return [];
    }
    return menusData
      .filter(item => item.moduleName && item.hidden === 'N')
      .map(item => this.getSubMenuOrItem(item, parentCode))
      .filter(item => item);
  };

  /**
   * get SubMenu or Item
   */
  getSubMenuOrItem = (item, parentCode) => {
    const { menuData, collapsed } = this.props;
    if (
      item.childModules &&
      // !item.hideChildrenInMenu
      item.childModules.some(child => child.moduleName)
    ) {
      const { moduleName: name, moduleCode, openKeys: itemOpenKeys } = item;
      openKeysMap[moduleCode] = itemOpenKeys;
      const titleStyle = () => {
        if (!collapsed) {
          if (
            menuData.some(
              ({ moduleCode: code, moduleId }) =>
                item.moduleCode === code || item.parentId === moduleId,
            )
          ) {
            return { marginLeft: '-4px' };
          }
          return { marginLeft: '-16px' };
        }
        return { marginLeft: 0 };
      };
      return (
        <SubMenu
          // style={
          //   menuData.some(({ moduleCode: code }) => item.moduleCode === code)
          //     ? { marginLeft: '-4px' }
          //     : { marginLeft: '-12px' }
          // }
          title={
            <span style={titleStyle()}>
              {menuData.some(({ moduleCode: code }) => item.moduleCode === code)
                ? getIcon(item.moduleIcon)
                : ''}

              <span>{name}</span>
            </span>
          }
          key={item.moduleCode}
        >
          {this.getNavMenuItems(item.childModules, item.moduleCode)}
        </SubMenu>
      );
    }

    return (
      <MenuItem key={item.moduleCode}>
        {this.getMenuItemPath(item, parentCode)}
      </MenuItem>
    );
  };

  /**
   * 判断是否是http链接.返回 Link 或 a
   * Judge whether it is http link.return a or Link
   * @memberof SiderMenu
   */
  getMenuItemPath = item => {
    const { collapsed, dispatch, tabHistory } = this.props;
    const {
      location: { pathname },
    } = this.props;
    const {
      moduleName: name,
      moduleCode,
      moduleId,
      openKeys: itemopenKeys,
    } = item;
    const itemPath = window.g_pathsMapping[moduleCode] || ''; // this.conversionPath(item.path);
    const { selectedKeys } = this.state;
    if (pathname === itemPath) {
      if (moduleId !== +sessionStorage.getItem('moduleId')) {
        sessionStorage.setItem('moduleId', moduleId);
        Request(
          `/dev-basp-user/indexModule/queryBtns?moduleId=${moduleId}&roleId=${sessionStorage.getItem(
            'roleId',
          )}&time=${Date.now()}`,
          {
            method: 'GET',
          },
        ).then(res => {
          if (res.succ && res.result) {
            sessionStorage.setItem('moduleCode', JSON.stringify(res.result));
          }
          if (
            !tabHistory.routes.find(route => route.moduleCode === moduleCode)
          ) {
            dispatch({ type: 'tabHistory/push', data: item });
          }

          if (
            // this.isInit &&
            !(selectedKeys.length === 1 && selectedKeys[0] === moduleCode)
          ) {
            window.activeTabKey = moduleCode;
            this.setState({
              openKeys: collapsed ? [] : [...itemopenKeys],
              selectedKeys: [...[moduleCode]],
            });
            this.isInit = false;
          }
        });
      }
    }
    // const icon = getIcon(item.moduleIcon);
    const { target } = item;
    // Is it a http link
    if (/^https?:\/\//.test(itemPath)) {
      return (
        <a href={itemPath} target={target} style={{ marginLeft: '-16px' }}>
          {/* {icon} */}
          <span>{name}</span>
        </a>
      );
    }
    const { location, isMobile, onCollapse, menuData } = this.props;
    // hack
    const linkStyle = () => {
      if (!collapsed) {
        if (
          menuData.some(
            ({ moduleCode: code, moduleId: id }) =>
              item.moduleCode === code || item.parentId === id,
          )
        ) {
          return { marginLeft: '-4px' };
        }
        if (
          menuData.some(
            ({ childModules }) =>
              childModules &&
              childModules.some(
                ({ moduleCode: code, moduleId: id }) =>
                  moduleCode === code || item.parentId === id,
              ),
          )
        ) {
          return { marginLeft: '-16px' };
        }
        return { marginLeft: '-28px' };
      }
      return {};
    };

    return (
      <Link
        style={linkStyle()}
        to={itemPath}
        target={target}
        replace={itemPath === location.pathname}
        onClick={() => {
          if (item.openLink) {
            window.open(item.openLink);
          }
          if (window.ky_sensors) {
            window.ky_sensors.quick('autoTrackSinglePage', {
              $title: `${name}`,
            });
          }
          this.isInit = true;
          if (isMobile) onCollapse(true);
        }}
      >
        {/* {icon} */}
        <span>{name}</span>
      </Link>
    );
  };

  conversionPath = path => {
    if (path && path.indexOf('http') === 0) {
      return path;
    }
    return `/${path || ''}`.replace(/\/+/g, '/');
  };

  render() {
    const {
      theme,
      mode,
      // location: { pathname },
      className,
      style,
      menuData,
      // collapsed,
    } = this.props;
    const { selectedKeys, openKeys, refreshKey } = this.state;
    // if pathname can't match, use the nearest parent's key
    // let selectedKeys = this.getSelectedMenuKeys(pathname);
    // if (!selectedKeys.length && openKeys) {
    //   selectedKeys = [openKeys[openKeys.length - 1]];
    // }
    // let props = {};
    // if (openKeys && !collapsed) {
    //   props = {
    //     openKeys: openKeys.length === 0 ? [...selectedKeys] : openKeys,
    //   };
    // }

    // menuData.map((menu,index)=> {
    //   getAllFamilyCodes(menuData);
    // })

    // const cls = classNames(className, {
    //   'top-nav-menu': mode === 'horizontal',
    // });
    const cls = classNames(className, {
      'top-nav-menu': mode === 'horizontal',
      'scroll-style': true,
    });
    return (
      <div>
        <input type="hidden" value={refreshKey} />
        <Menu
          key={refreshKey}
          mode={mode}
          theme={theme}
          // refreshKey={refreshKey}
          onOpenChange={keys => {
            this.setState({
              openKeys: openKeysMap[keys.pop()],
              //  moreThanOne ? [openKeys.pop()] : [...openKeys],
            });
          }}
          selectedKeys={selectedKeys}
          openKeys={openKeys}
          style={style}
          className={cls}
          // {...props}
        >
          {this.getNavMenuItems(menuData)}
        </Menu>
      </div>
    );
  }
}

export default connect(({ tabHistory }) => ({
  tabHistory,
}))(BaseMenu);
