import React, { useState, useEffect } from 'react';
import Request from 'src/utils/request';

export default Comp => props => {
  const [auth, setAuth] = useState('');
  useEffect(() => {
    async function fetchData() {
      const res = await Request(
        `/dev-basp-user/indexModule/queryBtns?moduleId=${sessionStorage.getItem(
          'moduleId',
        )}&roleId=${sessionStorage.getItem('roleId')}&time=${Date.now()}`,
        {
          method: 'GET',
        },
      );
      if (res.succ && res.result) {
        setAuth(res.result);
      } else {
        setAuth(null);
      }
      // ...
    }
    fetchData();
  }, []);
  return <Comp moduleCode={auth} {...props} />;
};
