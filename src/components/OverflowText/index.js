import { Tooltip } from 'antd';
import classNames from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import style from './index.less';

export const OverflowText = ({ children, width }) => {
  const rootNodeRef = useRef(null);
  const [overflow, setOverflow] = useState(false);

  useEffect(() => {
    const rootNode = rootNodeRef.current;
    if (!rootNode) {
      return;
    }
    const { clientHeight, scrollHeight } = rootNode;
    if (scrollHeight > clientHeight) {
      setOverflow(true);
    }
  }, []);

  return (
    <Tooltip title={children}>
      <span
        className={classNames(style.root, { [style.overflow]: overflow })}
        ref={rootNodeRef}
        style={{ width }}
      >
        {children}
      </span>
    </Tooltip>
  );
};
