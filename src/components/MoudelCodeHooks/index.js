import { useState, useEffect } from 'react';
import Request from 'src/utils/request';

export default () => {
  const [auth, setAuth] = useState();
  const fetchData = async () => {
    const res = await Request(
      `/dev-basp-user/indexModule/queryBtns?moduleId=${sessionStorage.getItem(
        'moduleId',
      )}&roleId=${sessionStorage.getItem('roleId')}&time=${Date.now()}`,
      {
        method: 'GET',
      },
    );
    if (res.succ && res.result) {
      setAuth(res.result);
    } else setAuth(null);
  };
  useEffect(() => {
    fetchData();
  }, []);
  return auth;
};
