import React from 'react';
// import { <PERSON>u, Dropdown, Button } from 'antd';
import { connect } from 'dva';
// import book from './book.svg';
import history from '../../../history';
import styles from './index.less';
// eslint-disable-next-line import/order
import { DownloadOutlined } from '@ant-design/icons';

export default connect(({ tabHistory }) => ({
  tabHistory,
}))(props => {
  const { dispatch } = props;

  return (
    <span
      className={styles.download}
      onClick={() => {
        // const moduleCode = 'base_download';
        const path = '/download/list';
        dispatch({
          type: 'global/updateListKey',
          data: 'list',
        });

        history.push(path);
      }}
    >
      <DownloadOutlined />
      <span>下载列表</span>
    </span>
  );
});
// export default class Book extends PureComponent {
//   render() {

//   }
// }
