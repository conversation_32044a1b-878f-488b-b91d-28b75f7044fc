import React, { PureComponent } from 'react';
import Intl from 'react-intl-universal';
import moment from 'moment';
import groupBy from 'lodash/groupBy';
import { Spin, Tag, Menu, Avatar } from 'antd';
import { LogoutOutlined, UserOutlined } from '@ant-design/icons';

// import DeptDropdown from '../../pages/Bill/Components/DeptDropdown';
import HeaderDropdown from '../HeaderDropdown';
import RoleDropdown from '../RoleDropdown';
// import SelectLang from '../SelectLang';
import SelectSkin from '../SelectSkin';
import styles from './index.less';
import Book from './book';
// import logoSrc from './images/logo.png';
const MenuItem = Menu.Item;
// const MenuDivider = Menu.Divider;
export default class GlobalHeaderRight extends PureComponent {
  getNoticeData() {
    const { notices = [] } = this.props;
    if (notices.length === 0) {
      return {};
    }
    const newNotices = notices.map(notice => {
      const newNotice = { ...notice };
      if (newNotice.datetime) {
        newNotice.datetime = moment(notice.datetime).fromNow();
      }
      if (newNotice.id) {
        newNotice.key = newNotice.id;
      }
      if (newNotice.extra && newNotice.status) {
        const color = {
          todo: '',
          processing: 'blue',
          urgent: 'red',
          doing: 'gold',
        }[newNotice.status];
        newNotice.extra = (
          <Tag color={color} style={{ marginRight: 0 }}>
            {newNotice.extra}
          </Tag>
        );
      }
      return newNotice;
    });
    return groupBy(newNotices, 'type');
  }

  getCurrentRoleName() {
    const { systemRoles } = this.props;
    const currentRole = systemRoles.find(
      role => role.roleId === sessionStorage.getItem('roleId'),
    );
    return currentRole ? currentRole.roleName : '';
  }

  renderSystemAndRolesMenu() {
    const { systemRoles } = this.props;
    const menus = [];
    systemRoles.map(role =>
      menus.push(
        <Menu.Item
          onClick={() => {
            const roleIdCache = sessionStorage.getItem('roleId');
            if (roleIdCache && roleIdCache === role.roleId) {
              return;
            }
            sessionStorage.setItem('roleId', role.roleId);
            window.location.reload();
          }}
          key={role.roleId}
        >
          {role.roleName}
        </Menu.Item>,
      ),
    );
    return <Menu>{menus}</Menu>;
  }

  getUnreadData = noticeData => {
    const unreadMsg = {};
    Object.entries(noticeData).forEach(([key, value]) => {
      if (!unreadMsg[key]) {
        unreadMsg[key] = 0;
      }
      if (Array.isArray(value)) {
        unreadMsg[key] = value.filter(item => !item.read).length;
      }
    });
    return unreadMsg;
  };

  changeReadState = clickedItem => {
    const { id } = clickedItem;
    const { dispatch } = this.props;
    dispatch({
      type: 'global/changeNoticeReadState',
      payload: id,
    });
  };

  changeTheme = ({ key }) => {
    const { dispatch } = this.props;
    const navTheme = key === 'light' ? 'light' : 'dark';
    const logo =
      key === 'light'
        ? 'https://freight.sf-express.com/eshowOss/bg1039/32wtvmdo53le-1039-logo.png'
        : 'https://freight.sf-express.com/publicResource/static/images/ky/sf.svg';
    dispatch({
      type: 'setting/changeSetting',
      payload: { navTheme, logo },
    });
  };

  render() {
    const {
      // currentUser,
      // fetchingNotices,
      // onNoticeVisibleChange,
      onMenuClick,
      // onNoticeClear,
      navTheme,
      theme,
    } = this.props;
    const menu = (
      <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick}>
        <MenuItem key="logout">
          <LogoutOutlined />
          {Intl.get('menu.account.logout').d('logout')}
        </MenuItem>
      </Menu>
    );
    let className = styles.right;
    if (theme === 'dark') {
      className = `${styles.right}  ${styles.dark}`;
    }
    const currentUserName = sessionStorage.getItem('username');
    return (
      <div className={className}>
        {/* <span
          onClick={() => {
            window.open(`/fms-tdms?id=${1}`);
          }}
        >
          <Button
            style={{
              fontweight: 700,
              color: '#e44c5c',
              marginRight: '10px',
              backgroundColor: '#fff',
              borderRadius: '10px',
            }}
          >
            切回旧版
          </Button>
        </span> */}
        <SelectSkin
          className={styles.action}
          changeTheme={this.changeTheme}
          navTheme={navTheme}
        />
        <Book />
        {/* <Button onClick={() => this.changeTheme()}>切换肤色</Button> */}
        {/* <DeptDropdown /> */}
        <RoleDropdown overlay={this.renderSystemAndRolesMenu()}>
          <span>
            <UserOutlined /> {this.getCurrentRoleName()}
          </span>
        </RoleDropdown>
        {currentUserName ? (
          <HeaderDropdown overlay={menu}>
            <span className={`${styles.action} ${styles.account}`}>
              <Avatar
                size="small"
                className={styles.avatar}
                src={require('./images/avata.jpg')}
                alt="avatar"
              />
              <span className={styles.name}>{currentUserName}</span>
            </span>
          </HeaderDropdown>
        ) : (
          <Spin size="small" style={{ marginLeft: 8, marginRight: 8 }} />
        )}
        {/* <SelectLang className={styles.action} /> */}
      </div>
    );
  }
}
