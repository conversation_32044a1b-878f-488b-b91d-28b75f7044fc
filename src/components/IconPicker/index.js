import React, { PureComponent } from 'react';
import { AutoComplete } from 'antd';
import * as AllIcons from '@ant-design/icons';
import Styles from './index.less';
import Icons from './icons';

const { Option } = AutoComplete;
export default class IconPicker extends PureComponent {
  componentDidMount() {}

  render() {
    const { ...otherProps } = this.props;
    return (
      // <div className={Styles.picker}>
      <AutoComplete
        //   defaultValue={defaultValue}
        {...otherProps}
        style={{ width: '100%' }}
      >
        {/* {/* <Option value="jack">Jack</Option> */}
        {Icons.map(icon => {
          const TargetIcon = AllIcons[icon];
          // React.lazy(() => import(`@ant-design/icons/${icon}`));
          return (
            <Option key={icon} value={icon}>
              <TargetIcon />
            </Option>
          );
        })}
      </AutoComplete>
      // </div>
    );
  }
}
