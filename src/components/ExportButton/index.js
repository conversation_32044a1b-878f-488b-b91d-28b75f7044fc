import React, { useState, Fragment } from 'react';
import { Button, notification } from 'antd';
import request from 'dva/fetch';
import { cloneDeep } from 'lodash';

import {
  SmileOutlined,
  MehOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import AuthButton from '@/components/AuthButton';
import Config from '@/config';

export default props => {
  const {
    ids,
    selectedOptions,
    options = {},
    text,
    code,
    type,
    dispatch,
    beforeExport,
    ...rest
  } = props;
  const [loading, setLoading] = useState(false);

  const downLoad = option => {
    if (beforeExport && !beforeExport()) {
      return;
    }
    setLoading(true);

    const { requstParams, filename = '导出.xlsx', total } = option;
    const requstValues = cloneDeep(requstParams);
    if (total > 10000) {
      notification.open({
        message: '最多导出一万条数据，请重新筛选数据',
        icon: <SmileOutlined style={{ color: '#108ee9' }} />,
      });
      return;
    }
    if (!requstValues) {
      notification.open({
        message: 'options.requstValues 请求参数未配置',
        icon: <SmileOutlined style={{ color: '#108ee9' }} />,
      });
      return;
    }

    requstValues[1].credentials = 'include';
    requstValues[1].body = requstValues[1].body;
    const body =
      typeof requstValues[1].body === 'function'
        ? requstValues[1].body()
        : requstValues[1].body;
    requstValues[1].body = requstValues[1].body
      ? JSON.stringify(body)
      : undefined;
    requstValues[1].headers = {
      userId: sessionStorage.userid,
      'sgs-userid': sessionStorage.userid,
      systemKey: Config.systemKey,
      'gw-bdus-rid': sessionStorage.roleId || '',
      'Content-Type': 'application/json; charset=utf-8',
    };

    request(...requstValues).then(res => {
      if (res.status !== 200) {
        notification.open({
          message: `导出失败`,
          description: `状态码：${res.status},statusText：${res.statusText}`,
          icon: <SmileOutlined style={{ color: '#108ee9' }} />,
        });
        return;
      }
      res.blob().then(blob => {
        if (typeof FileReader === 'undefined') {
          notification.open({
            message: '您的浏览器不支持 FileReader，请升级浏览器',
            icon: <MehOutlined style={{ color: '#108ee9' }} />,
          });
        }
        const reader = new FileReader();
        reader.addEventListener('loadend', () => {
          let resu = '';
          try {
            resu = JSON.parse(reader.result);
            setLoading(false);
            // resu = eval('('+ reader.result + ')')
            if (resu.code === 500) {
              notification.open({
                message: resu.msg,
                icon: <SmileOutlined style={{ color: '#108ee9' }} />,
              });
            } else if (resu.code === 401) {
              notification.error({
                message: resu.msg,
              });
            } else if (!resu.success) {
              notification.warning({
                message: resu.errorMessage,
                icon: <SmileOutlined style={{ color: '#108ee9' }} />,
              });
            }
          } catch (e) {
            // 捕获错误 说明是文本字符串
            resu = reader.result;
            downloadBlob(blob, filename);
          }
        });
        reader.readAsText(blob);

        // 下载
        function downloadBlob(blob_, fileName) {
          const blobUrl = window.URL.createObjectURL(blob_);
          const a = document.createElement('a');
          a.href = blobUrl;
          a.target = '_blank';
          a.style.display = 'none';
          a.download = fileName;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(blobUrl);
          document.body.removeChild(a);
          setLoading(false);
        }
      });
    });
  };
  if (ids && ids.length) {
    return (
      <Button
        loading={loading}
        code={code}
        icon={<DownloadOutlined />}
        type={`${type || 'primary'} `}
        onClick={() => downLoad(selectedOptions)}
        {...rest}
      >
        {text}
      </Button>
    );
  }
  return (
    <Fragment>
      {code ? (
        <AuthButton
          loading={loading}
          code={code}
          icon={<DownloadOutlined />}
          type={`${type || 'primary'} `}
          onClick={() => downLoad(options)}
          {...rest}
        >
          {text}
        </AuthButton>
      ) : (
        <Button
          loading={loading}
          icon={<DownloadOutlined />}
          type={`${type || 'primary'} `}
          onClick={() => downLoad(options)}
          {...rest}
        >
          {text}
        </Button>
      )}
    </Fragment>
  );
};
