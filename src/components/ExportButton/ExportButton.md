##### 1.使用方式

```React
import ExportButton from '@/components/ExportButton';
<ExportButton
  beforeExport={() => {
              form.validateFields(['versionStr']);
              return validate;
            }}
              code="对应功能按钮编码"
              text="导出"
              options={{
                filename: '导出文件名.xlsx',
                requstParams: [
                  '导出的url',
                  {
                    method: 'POST',
                    body: inputParam,
                  },
                ],
              }}
            />
```

##### 2.API

| 参数         | 说明                                                                                        | 类型     | 默认值 |
| ------------ | ------------------------------------------------------------------------------------------- | -------- | ------ |
| code         | 对应功能按钮的模块编码，与功能模块管理中模块编码一致，非必填                                | string   |        |
| beforeExport | 在导出/下载之前进行的操作，必须有返回值，组件会根据是否有返回值判断下载流程是否继续，非必填 | function |        |
| text         | 按钮显示的文字，默认为导出                                                                  | string   |        |
| options      | 详情请见 options                                                                            | Object   |        |
| type         | 按钮样式，详情见[antd 按钮](https://ant.design/components/button-cn/)                       |          |        |

options

| 参数         | 说明                                                          | 类型   | 默认值    |
| ------------ | ------------------------------------------------------------- | ------ | --------- |
| filename     | 导出/下载的文件名，标注文件类型，必填，默认下载 xlsx 格式表格 | string | 导出.xlsx |
| requstParams | 请求参数，包含请求 url，请求方式，入参等                      | Array  |           |
|              |                                                               |        |           |
