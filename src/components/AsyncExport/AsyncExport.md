##### 1.简介

AsyncExport 用于导出按钮，整合了同步导出所选以及异步导出全部两个功能。通常导出所选的数据量不会太大，因此使用的是同步导出。判断条件在于 props 中 ids 是否有值，如有值，则按钮实现导出所选功能，进行同步导出操作，如没有值，则说明界面为异步导出，跳转至下载列表

##### 2.使用方式

```react
import AsyncExport from '@/components/AsyncExport';
<AsyncExport
  beforeExport={() => {
              const validate = form.getFieldValue(['versionStr']);
              form.validateFields(['versionStr']);
              return validate;
            }}
              code="Truck-export"
              text="导出"
              ids={selectedRows && selectedRows.map(({ id }) => id)}
              selectedOptions={{
                filename: '导出所选.xlsx',
                requstParams: [
                  '/exportById',
                  {
                    method: 'POST',
                    body: {
                      ids: selectedRows && selectedRows.map(({ id }) => id),
                    },
                  },
                ],
              }}
              options={{
                filename: '',
                requstParams: [
                  '/doExportAsync',
                  {
                    method: 'POST',
                    body: inputParam,
                  },
                ],
              }}
            />
```

##### 3.API

| 参数            | 说明                                                                                        | 类型     | 默认值 |
| --------------- | ------------------------------------------------------------------------------------------- | -------- | ------ |
| code            | 对应功能按钮的模块编码，与功能模块管理中模块编码一致，非必填                                | string   |        |
| beforeExport    | 在导出/下载之前进行的操作，必须有返回值，组件会根据是否有返回值判断下载流程是否继续，非必填 | function |        |
| subCode         | 对应模版导出功能按钮的模块编码，与功能模块管理中模块编码一致，非必填                        |          |        |
| title           | 按钮显示的文字，默认为“导出”                                                                | string   |        |
| selectedOptions | 导入的 url，必填                                                                            | object   |        |
| options         | 导出全部的参数，具体数值参照 ExportButton 中的 options                                      | object   |        |
|                 |                                                                                             |          |        |
