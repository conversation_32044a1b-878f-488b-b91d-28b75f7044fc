import React, { Fragment, useState } from 'react';
import { Upload, message, Button, Modal, Divider } from 'antd';
import { ImportOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import history from '@/history';
import AuthButton from '../AuthButton';
import ExportButton from '../ExportButton';
import styles from './index.less';

const { Dragger } = Upload;
const roleId = sessionStorage.getItem('roleId');

const UploadButton = props => {
  const {
    dispatch,
    action,
    setVisible,
    headers,
    errorObj,
    handleSyncImport,
    code,
    hideDownloadErrorLog = false,
  } = props;
  const importProps = {
    name: 'file',
    accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
    headers: {
      credentials: 'include',
      userId: sessionStorage.userid,
      'sgs-userid': sessionStorage.userid,
      systemKey: process.env.SYSTEM_KEY,
      'gw-bdus-rid': roleId || '',
      ...headers,
    },
    data: {
      operator: sessionStorage.getItem('username'),
    },
    action: '',
    onChange(info) {
      const { status, response } = info.file;
      if (status === 'done' && response.success) {
        if (response.obj && response.obj.sync) {
          const {
            countError,
            resultList = [],
            countTotal,
            resultDesc,
            taskId,
            // lifecycle,
          } = response.obj;
          const successCount = countTotal - countError;
          Modal.info({
            title: '导入结果查看',
            okText: '确认',
            content: (
              <div>
                <div className={styles['detail-title']}>上传明细：</div>
                <div>
                  总数:
                  <span className={styles['detail-wrap']}>{countTotal}</span>条
                </div>
                <div>
                  导入成功:
                  <span className={styles['detail-wrap']}>{successCount}</span>
                  条
                </div>
                <div>
                  导入失败:
                  <span className={styles['detail-wrap']}>{countError}</span>条
                </div>
                {(resultDesc || countError > 0) && (
                  <Fragment>
                    <div className={styles['detail-title']}>失败明细：</div>
                    <div className={styles['detail-wrap']}>
                      {resultDesc && <div>{resultDesc}</div>}
                      {resultList && resultList.length
                        ? resultList.map(err => (
                            <div
                              key={err.rowNumber}
                            >{`第${err.rowNumber}行，${err.message}`}</div>
                          ))
                        : ''}
                    </div>

                    {resultList &&
                    resultList.length &&
                    code !== 'attendanceDaysData-import' &&
                    !hideDownloadErrorLog ? (
                      <ExportButton
                        text="错误日志报告"
                        type="default"
                        options={{
                          filename: errorObj.name,
                          requstParams: [
                            errorObj.url,
                            {
                              method: 'POST',
                              body: {
                                taskId,
                              },
                            },
                          ],
                        }}
                      />
                    ) : null}
                  </Fragment>
                )}
              </div>
            ),
            // onOk: () => setVisible(false),
            onOk: () => {
              // setVisible(false);
              handleSyncImport();
            },
          });
        } else {
          Modal.confirm({
            title: '提示',
            content: '是否跳转到下载列表查看导入任务',
            okText: '确认',
            cancelText: '取消',
            onOk: async () => {
              // const moduleCode = 'base_download';
              const path = '/download/list';
              dispatch({
                type: 'global/updateListKey',
                data: 'import',
              });
              history.push(path);
              setVisible(false);
            },
            onCancel() {},
          });
        }
      } else if (status === 'done' && !response.success) {
        message.error(response.errorMessage);
      } else if (status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };
  return (
    // 解决当上传附件后，点击确定或者弹出框取消时，之后再打开弹出框，原来的附件缓存还在弹出框上 原理未知
    <div key={Math.random()}>
      <Dragger {...importProps} action={action}>
        <p className="ant-upload-text">点击选择Excel模板上传</p>
      </Dragger>
    </div>
  );
};

export default connect(({ tabHistory }) => ({ tabHistory }))(props => {
  const {
    // children = '导入',
    title = '导入订单',
    action,
    code,
    modulecode,
    modalUrl,
    modalName,
    modals,
    tabHistory,
    dispatch,
    headers,
    errorObj = {},
    handleSyncImport,
    hideDownloadErrorLog,
    ...rest
  } = props;
  const [visible, setVisible] = useState(false);
  return (
    <Fragment>
      {code ? (
        <AuthButton
          danger
          type="default"
          code={code}
          moduleCode={modulecode}
          icon={<ImportOutlined />}
          onClick={() => {
            setVisible(true);
          }}
          {...rest}
        >
          {title}
        </AuthButton>
      ) : (
        <Button
          danger
          type="default"
          icon={<ImportOutlined />}
          onClick={() => {
            setVisible(true);
          }}
          {...rest}
        >
          {title}
        </Button>
      )}

      <Modal
        title={title}
        visible={visible}
        width={600}
        footer={false}
        centered
        onOk={() => setVisible(false)}
        onCancel={() => setVisible(false)}
        {...props}
      >
        <UploadButton
          code={code}
          className="upload-btn"
          action={action}
          tabHistory={tabHistory}
          dispatch={dispatch}
          setVisible={setVisible}
          headers={headers}
          errorObj={errorObj}
          handleSyncImport={handleSyncImport}
          hideDownloadErrorLog={hideDownloadErrorLog}
        />

        <Divider>导入注意事项</Divider>
        <div>
          1.导入时请务必使用页面提供的模版进行导入，导入模版请点击模版下载按钮进行下载。
        </div>
        <div>
          2.在往模版填充数据时请勿改变模版中列的顺序，否则数据将导入错误。
        </div>
        <div>
          3.请勿改变模版中的格式，如模版中最后一列生效日期为日期格式，请勿更改成数据或者文本格式，否则数据将导入失败。
        </div>
        <div className={styles['modal-download']}>
          {modals ? (
            modals.map(({ url, text }) => (
              <ExportButton
                key={url}
                type="default"
                className={styles['export-btn']}
                text={text.split('.')[0]}
                options={{
                  filename: text,
                  requstParams: [
                    url,
                    {
                      method: 'GET',
                    },
                  ],
                }}
              />
            ))
          ) : (
            <ExportButton
              text="模板下载"
              type="default"
              options={{
                filename: modalName,
                requstParams: [
                  modalUrl,
                  {
                    method: 'GET',
                  },
                ],
              }}
            />
          )}
        </div>
      </Modal>
    </Fragment>
  );
});
