##### 1.简介

ImportButton 用于同步以及异步导入，同步与异步导入的判断条件在于返回的参数 sync 的值，如 sync 为 true，则说明已创建异步导入任务，跳转到下载列表界面查看即可，如为 false，则说明界面为同步导入，直接进行普通导入操作

##### 2.使用方式

```react
import ImportButton from '@/components/ImportButton';
<ImportButton
              code="功能按钮编码"
              subCode="模版导出功能按钮编码"
              title="导入"
              action="导入的url"
              modalUrl="模版下载的url"
              modalName="模板.xlsx"
            />
```

##### 3.API

| 参数      | 说明                                                                 | 类型     | 默认值 |
| --------- | -------------------------------------------------------------------- | -------- | ------ |
| code      | 对应功能按钮的模块编码，与功能模块管理中模块编码一致，非必填         | string   |        |
| subCode   | 对应模版导出功能按钮的模块编码，与功能模块管理中模块编码一致，非必填 | string   |        |
| title     | 按钮显示的文字                                                       | string   |        |
| action    | 导入的 url，必填                                                     | string   |        |
| modalUrl  | 模版下载的 url                                                       | string   |        |
| modalName | 模版下载的文件名，标注文件类型，默认下载 xlsx 格式                   |          |        |
| cb        | 上传成功弹窗确认按钮回调函数                                         | Function |
| headers   | 自定义的业务头部                                                     | object   |
