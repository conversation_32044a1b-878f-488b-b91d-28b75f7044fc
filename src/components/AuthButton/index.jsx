import React, { useMemo } from 'react';
import { Button } from 'antd';

// eslint-disable-next-line func-names
export default function(props) {
  const {
    code,
    // moduleId = sessionStorage.getItem('moduleId'),
    moduleCode,
    children,
    ...restProps
  } = props;
  const auth = useMemo(() => {
    if (moduleCode && moduleCode.indexOf(code) > -1) {
      return true;
    }
    return '';
  }, [code, moduleCode]);

  if (auth) {
    return <Button {...restProps}>{children}</Button>;
  }
  return null;
}
