import React, { PureComponent } from 'react';
import Intl from 'react-intl-universal';
import { Menu } from 'antd';
import classNames from 'classnames';
import { GlobalOutlined } from '@ant-design/icons';

import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';

const MenuItem = Menu.Item;

export default class SelectLang extends PureComponent {
  changeLang = ({ key }) => {
    localStorage.setItem('CUR_LOCAL', key);
    window.location.reload();
  };

  render() {
    const { className } = this.props;
    const selectedLang = Intl.options.currentLocale;

    const locales = Object.keys(Intl.options.locales);
    const languageLabels = {
      'zh-CN': '简体中文',
      'zh-TW': '繁体中文',
      'en-US': 'English',
      'pt-BR': 'Português',
    };
    const languageIcons = {
      'zh-CN': '🇨🇳',
      'zh-TW': '🇭🇰',
      'en-US': '🇬🇧',
      'pt-BR': '🇧🇷',
    };
    const langMenu = (
      <Menu
        className={styles.menu}
        selectedKeys={[selectedLang]}
        onClick={this.changeLang}
      >
        {locales.map(locale => (
          <MenuItem key={locale}>
            <span role="img" aria-label={languageLabels[locale]}>
              {languageIcons[locale]}
            </span>{' '}
            {languageLabels[locale]}
          </MenuItem>
        ))}
      </Menu>
    );
    return (
      <HeaderDropdown overlay={langMenu} placement="bottomRight">
        <span className={classNames(styles.dropDown, className)}>
          <GlobalOutlined
            style={{ color: '#fff' }}
            type="global"
            title={Intl.get('navBar.lang')}
          />
        </span>
      </HeaderDropdown>
    );
  }
}
