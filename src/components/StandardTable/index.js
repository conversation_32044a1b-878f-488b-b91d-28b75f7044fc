import React, { PureComponent } from 'react';
import { Resizable } from 'react-resizable';
import {
  Table, // <PERSON><PERSON>,
  Card,
} from 'antd';
import styles from './index.less';

const ResizeableTitle = props => {
  const { onResize, width, ...restProps } = props;
  if (!width) {
    return <th {...restProps} />;
  }

  return (
    <Resizable
      width={width}
      height={0}
      onResize={onResize}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th {...restProps} />
    </Resizable>
  );
};

function initTotalList(columns) {
  const totalList = [];
  columns.forEach(column => {
    if (column.needTotal) {
      totalList.push({ ...column, total: 0 });
    }
  });
  return totalList;
}

class StandardTable extends PureComponent {
  constructor(props) {
    super(props);
    const { columns, selectedRows } = props;
    const needTotalList = initTotalList(columns);
    this.state = {
      columns,
      selectedRows,
      selectedRowKeys: [],
      needTotalList,
    };
  }

  static getDerivedStateFromProps(nextProps, state) {
    const rawColumns = nextProps.columns;
    const { columns } = state;
    const needTotalList = initTotalList(nextProps.columns);
    // 以递归的方式将表格列筛选以及自定义宽度结合起来，width以state的宽度为主
    const reduceColumns = (rawCol, col) => {
      for (const item of rawCol) {
        if (item.children) {
          const childCol = col.filter(({ title }) => title === item.title);
          if (childCol.length) {
            reduceColumns(item.children, childCol[0].children);
          } else {
            reduceColumns(item.children, item.children);
          }
        } else {
          const filterCol = col.filter(
            ({ dataIndex }) => dataIndex === item.dataIndex,
          );
          item.width = filterCol[0] ? filterCol[0].width : item.width;
        }
      }
      return rawCol;
    };
    if (nextProps.selectedRows && nextProps.selectedRows.length === 0) {
      return {
        selectedRowKeys: [],
        columns: reduceColumns(rawColumns, columns),
        selectedRows: [],
        needTotalList,
      };
    }
    if (nextProps.preserveSelected) {
      const rowKeys = nextProps.selectedRows.map(
        item => item[`${nextProps.rowKey}`],
      );
      return {
        selectedRowKeys: rowKeys,
        columns: reduceColumns(rawColumns, columns),
        selectedRows: nextProps.selectedRows,
      };
    }
    return { columns: reduceColumns(rawColumns, columns) };
  }

  handleResize = index => (e, { size }) => {
    this.setState(({ columns }) => {
      const nextColumns = [...columns];
      nextColumns[index] = {
        ...nextColumns[index],
        width: size.width,
      };
      return { columns: nextColumns };
    });
  };

  handleRowSelectChange = (selectedRowKeys, selectedRows) => {
    let { needTotalList } = this.state;
    needTotalList = needTotalList.map(item => ({
      ...item,
      total: selectedRows.reduce(
        (sum, val) => sum + parseFloat(val[item.dataIndex], 10),
        0,
      ),
    }));
    const { onSelectRow } = this.props;
    if (onSelectRow) {
      onSelectRow(selectedRows);
    }

    this.setState({ selectedRows, selectedRowKeys, needTotalList });
  };

  handleTableChange = (pagination, filters, sorter) => {
    const { onChange } = this.props;
    if (onChange) {
      onChange(pagination, filters, sorter);
    }
  };

  cleanSelectedKeys = () => {
    this.handleRowSelectChange([], []);
  };

  onRowClick = record => {
    const { selectedRowKeys, selectedRows } = this.state;
    if (!(selectedRowKeys && selectedRows)) {
      return false;
    }
    let newSelectedRowKeys = selectedRowKeys.slice();
    let newSelectedRows = selectedRows.slice();
    const { rowKey, multiple = true } = this.props;
    const key = typeof rowKey === 'string' ? record[rowKey] : rowKey(record);
    if (!selectedRowKeys.includes(key)) {
      if (multiple) {
        newSelectedRowKeys.push(key);
        newSelectedRows.push(record);
      } else {
        newSelectedRowKeys = [key];
        newSelectedRows = [record];
      }
    } else {
      const index = newSelectedRowKeys.indexOf(key);
      newSelectedRowKeys.splice(index, 1);
      newSelectedRows.splice(index, 1);
    }
    this.handleRowSelectChange(newSelectedRowKeys, newSelectedRows);

    // 判断是否已经选了
  };

  // 双击
  onDBRowClick = record => {
    const { onDBClick } = this.props;
    if (typeof onDBClick === 'function') {
      onDBClick(record);
    }
  };

  render() {
    const { selectedRowKeys, columns } = this.state;
    const {
      data = {},
      disableColumns, // 失效列置灰处理
      rowKey = 'key',
      title,
      columns: columnsProps,
      showSelection = columns.length,
      multiple = true,
      isShowTip = true,
      // onDoubleClick = () => {},
      ...rest
    } = this.props;
    const { list = [], pagination } = data;

    const paginationProps = {
      showSizeChanger: true,
      showQuickJumper: true,
      ...pagination,
    };

    const rowSelection = showSelection
      ? /* eslint-disable */
        {
          selectedRowKeys,
          type: multiple ? 'checkbox' : 'radio',
          columnWidth: 30,
          fixed: list.length ? 'left' : false,
          onChange: this.handleRowSelectChange,
          getCheckboxProps: record => ({
            disabled: record.disabled,
          }),
        }
      : undefined;

    return (
      <Card
        bordered={false}
        size="small"
        style={{ padding: 0 }}
        title={title}
        className={styles.standardTable}
      >
        <Table
          disabled={!columns.length}
          rowKey={rowKey}
          scroll={{ x: 'max-content' }}
          rowSelection={rowSelection}
          tableLayout="fixed"
          onRow={record => ({
            onClick: () => {
              this.onRowClick(record);
            },
            onDoubleClick: () => {
              this.onDBRowClick(record);
            },
          })}
          rowClassName={record =>
            Number(record.approvalStatus) === 2 && disableColumns
              ? styles.disableStyle
              : ''
          }
          dataSource={list}
          pagination={
            pagination
              ? {
                  showTotal: (total, range) =>
                    `第${range[0]}-${range[1]}条 总共 ${total} 条`,
                  ...paginationProps,
                }
              : false
          }
          onChange={this.handleTableChange}
          components={{
            header: {
              cell: ResizeableTitle,
            },
          }}
          bordered
          columns={columns.map((col, index) => ({
            // width: 100,
            align: 'left',
            ...col,
            onCell: () => ({
              style: {
                maxWidth: col.width,
                // overflow: 'hidden',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis',
                cursor: 'pointer',
              },
            }),
            render: (text, r, index) => {
              if (!col.ellipsis) {
                if (col.width) {
                  return (
                    <div className={col.width ? 'ant-table-cell-ellipsis' : ''}>
                      {col.render ? col.render(text, r, index) : text}
                    </div>
                  );
                }
                return col.render ? col.render(text, r, index) : text;
              }
              return isShowTip ? (
                <Tooltip
                  placement="topLeft"
                  title={col.render ? col.render(text, r, index) : text}
                >
                  {col.render ? col.render(text, r, index) : text}
                </Tooltip>
              ) : col.render ? (
                col.render(text, r, index)
              ) : (
                text
              );
            },

            onHeaderCell: column => ({
              width: column.width,
              onResize: this.handleResize(index),
            }),
          }))}
          {...rest}
        />
      </Card>
    );
  }
}

export default StandardTable;
