/* eslint-disable indent */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-shadow */
import React, { useState, useEffect } from 'react';
import { Select, Tooltip } from 'antd';
import request from 'src/utils/request';
const { Option } = Select;

const queryNet = async data =>
  request(`/opbdsUPMService/epDepartment/selectDeptInfoData`, {
    method: 'POST',
    body: data,
  });
/**
 * @param {*} props.extraParam
 * 入参
 * deptInfo  string  网点信息（不区分网点代码或者网点名称场景）
 * deptCode  string  网点代码（精确查询）
 * deptCodeFuzzy  string  网点代码（模糊查询）
 * deptName  string 网点名称 （模糊查询）
 * distCode  string 城市代码
 * distCodes array  城市代码集合
 * deptTypes array  网点类型集合 1：网点，2：集配站，3：中转场，4：加盟点，5：第三方，
 * typeCodes array  类型代码集合
 * typeLevels array  类型层次集合 0：总部，1:经营本部，2：区部，3：分部/中转场 4:网点
 * hqCodes array  经营本区代码 （可查战区：['CN36']）
 * areaCodes  array  区部代码
 * orgCode  array  所属公司(SF,SX)
 * pageNum  number 分页页数
 * pageSize number 分页大小
 * excludeOverseasFlag boolean  排除海外大区标识（true：排除海外区部网点）
 * freightFlag boolean 快运标识，用于筛选快运的集配站、中转场
 * pageFlag boolean  分页标识，当分页参数为空，查全表时，pageFlag 为false
 * noCacheFlag boolean 是否查询redis缓存
 */
const Nets = props => {
  const [dataSource, setDataSource] = useState([]);
  const [timeId, setTimeId] = useState([]);
  const [holder, setHolder] = useState('输入代码或者名称');
  const {
    onChange,
    value,
    showSearch = true,
    placeholder,
    extraParam = {},
    getList,
    ...rest
  } = props;
  const handleSearch = query => {
    // console.log(query.length);
    if (query.length >= 2) {
      const params = {
        pageNum: 1,
        pageSize: 20,
        ...extraParam,
      };
      if (showSearch) {
        params.deptInfo = query;
      }
      //   if (query.match(/^[0-9a-zA-Z]+$/g)) {
      //     params.deptCodeFuzzy = query;
      //   } else {
      //     params.deptName = query;
      //   }
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          queryList(params);
        }, 300),
      );
    }
  };
  const handleCNCodes = () => {
    const { typeLevels, hqCodes, excludeOverseasFlag } = extraParam;
    const params = {
      typeLevels,
      hqCodes,
      excludeOverseasFlag,
      // pageNum: 1,
      // pageSize: 20,
    };
    queryList(params);
  };
  const queryList = async params => {
    const res = await queryNet(params);
    if (res && res.success) {
      const data = res.obj.list
        ? res.obj.list.map(item => ({
            label: `${item.deptCode} ${item.deptName}`,
            value: item.deptCode,
            ...item,
          }))
        : [];
      if (typeof getList === 'function') {
        getList(data);
      }
      setDataSource(data);
    }
  };
  const handleChange = value => {
    if (typeof value === 'object') {
      value.label = value.label ? value.label.props.children : '';
    }
    onChange(value);
  };
  useEffect(() => {
    !showSearch && handleCNCodes();
    !showSearch && setHolder('');
  }, [showSearch]);
  useEffect(() => {
    value && showSearch && handleSearch(value);
  }, [value]);
  useEffect(() => {
    if (placeholder) {
      setHolder(placeholder);
    }
  }, [placeholder]);
  return (
    <Select
      showSearch={showSearch}
      placeholder={holder}
      allowClear
      value={value}
      filterOption={false}
      defaultActiveFirstOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      {...rest}
    >
      {dataSource.map(item => (
        <Option
          allowClear
          placeholder="请选择战区"
          value={item.value}
          key={item.value}
        >
          <Tooltip placement="rightTop" title={item.label}>
            {item.label}
          </Tooltip>
        </Option>
      ))}
    </Select>
  );
};

export default Nets;
