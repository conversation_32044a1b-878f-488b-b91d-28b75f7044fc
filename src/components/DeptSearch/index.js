/* eslint-disable no-unused-expressions */
/* eslint-disable indent */
/* eslint-disable no-shadow */
import React, { useState, useEffect, Fragment } from 'react';
import { Select, Tooltip } from 'antd';
import { getDept } from '@/services/api';
// import styles from './index.less';
const { Option } = Select;

function deptSearch(props) {
  const {
    onChange,
    value,
    getItem,
    addSXAllZoneCode,
    addAllZoneCode,
    customSearch,
    onlySx,
    ...rest
  } = props;
  const [dataSource, setDataSource] = useState([]);
  const [timeId, setTimeId] = useState([]);

  const handleSearchChange = query => {
    if (query.length >= 1) {
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          getDept(query, customSearch).then(res => {
            // 增加额外数据
            if (res.obj?.list) {
              if(addSXAllZoneCode) {
                const extraDeptCode = 'SX001';
                const extraDeptName = '顺心自有全网';
                if (
                  `${extraDeptCode} ${extraDeptName}`.includes(
                    query.toLocaleUpperCase(),
                  )
                ) {
                  res.obj.list.unshift({
                    deptCode: extraDeptCode,
                    deptName: extraDeptName,
                  });
                }
              }
              if(addAllZoneCode) {
                res.obj.list.unshift({
                  deptCode: '001',
                  deptName: '顺丰全网',
                });
              }
            }

            const data = res.obj.list
              ? res.obj.list.map(item => ({
                  label: `${item.deptCode} ${item.deptName}`,
                  value: item.deptCode,
                  ...item,
                }))
              : [];
            if (typeof getItem === 'function') {
              getItem(data);
            }
            if (onlySx) {
              setDataSource(data.filter(ele => ele.orgCode === 'SX'));
            } else {
              setDataSource(data);
            }
          });
        }, 1000),
      );
    }
  };

  const handleChange = (value, option) => {
    if (typeof value === 'object') {
      value.label = value.label ? value.label.props.children : '';
    }
    onChange(value, option, dataSource);
  };

  useEffect(() => {
    value && handleSearchChange(value);
    !value && setDataSource([]);
  }, [value]);
  return (
    <Fragment>
      <Select
        showSearch
        placeholder="输入代码或者名称"
        defaultActiveFirstOption={false}
        filterOption={false}
        onSearch={handleSearchChange}
        onChange={handleChange}
        value={value}
        allowClear
        {...rest}
      >
        {dataSource.map(item => (
          <Option value={item.value} key={item.value}>
            <Tooltip placement="rightTop" title={item.label}>
              {item.label}
            </Tooltip>
          </Option>
        ))}
      </Select>
    </Fragment>
  );
}
export default deptSearch;
