import React, { PureComponent } from 'react';

import { Menu } from 'antd';
import classNames from 'classnames';
import SkinIcon from './skinIcon';

import HeaderDropdown from '../HeaderDropdown';
import styles from './index.less';

const MenuItem = Menu.Item;

export default class SelectSkin extends PureComponent {
  render() {
    const { className, navTheme, changeTheme } = this.props;
    const skinMap = { light: '浅色模式', dark: '深色模式' };
    const skinLabel = [
      {
        label: '浅色模式',
        value: 'light',
      },
      {
        label: '深色模式',
        value: 'dark',
      },
    ];

    const skinMenu = (
      <Menu
        className={styles.menu}
        selectedKeys={navTheme}
        onClick={changeTheme}
      >
        {skinLabel.map(({ label, value }) => (
          <MenuItem key={value}>
            <span role="img">{label}</span>
          </MenuItem>
        ))}
      </Menu>
    );
    return (
      <HeaderDropdown overlay={skinMenu} placement="bottomRight">
        <span className={classNames(styles.dropDown, className)}>
          <span>
            <SkinIcon />
          </span>
          <span style={{ paddingLeft: '6px' }}>{skinMap[navTheme]}</span>
        </span>
      </HeaderDropdown>
    );
  }
}
