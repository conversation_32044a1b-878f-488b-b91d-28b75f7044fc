import React from 'react';
import Icon from '@ant-design/icons';
// import filterRaw from './btn_screen_icon.svg';
const filterRaw = () => (
  <svg
    width="17px"
    height="14px"
    viewBox="0 0 17 14"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    // xmlns:xlink="http://www.w3.org/1999/xlink"
  >
    <title>换肤@2x</title>
    <g id="换肤" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
      <g
        id="浅色模式"
        transform="translate(-1343.000000, -17.000000)"
        fill="#595959"
        fillRule="nonzero"
      >
        <g id="编组-32备份-2" transform="translate(180.000000, 0.000000)">
          <g id="Group-20" transform="translate(985.000000, 12.000000)">
            <g id="编组-2" transform="translate(178.000000, 1.000000)">
              <g id="换肤" transform="translate(0.000000, 4.000000)">
                <path
                  d="M12.519213,14 L4.3568792,14 C4.01462589,14 3.73717083,13.7227333 3.73717083,13.3807123 L3.73717083,7.76783692 C3.51526134,7.84040281 3.27137337,7.7821617 3.10626898,7.61717583 L0.712761612,5.2252932 C0.470746129,4.983442 0.470746129,4.59133612 0.712761612,4.34948492 L4.86432352,0.200741141 C5.106339,-0.0411100594 5.49871123,-0.0411100594 5.74072671,0.200741141 C5.98274219,0.442592341 5.98274219,0.83469822 5.74072671,1.07654942 L2.02735672,4.78739874 L3.54444153,6.30345374 L3.91866792,5.92948138 C4.09588816,5.75234275 4.36244852,5.69935408 4.59401448,5.79523142 C4.82558516,5.89108155 4.97657272,6.11689739 4.9765682,6.36737584 L4.9765682,12.7614246 L11.8994853,12.7614246 L11.8994853,6.22418491 C11.8994918,5.97371143 12.0504788,5.74790408 12.2820437,5.65205292 C12.5136086,5.55620175 12.7801505,5.60918222 12.9573856,5.78629045 L13.4555536,6.284101 L14.9726384,4.768046 L11.2592878,1.05719668 C11.0172723,0.815345479 11.0172723,0.4232396 11.2592878,0.1813884 C11.5012839,-0.0604628001 11.8936755,-0.0604628001 12.1356716,0.1813884 L16.2872529,4.33013218 C16.529249,4.57198338 16.529249,4.96408926 16.2872529,5.20594046 L13.8937843,7.59780374 C13.6933834,7.79808614 13.3825529,7.83698232 13.1389214,7.69226447 L13.1389214,13.3807317 C13.1389214,13.7227333 12.8614663,14 12.519213,14 L12.519213,14 Z"
                  id="路径"
                ></path>
                <path
                  d="M8.54040313,3.23076923 C6.84990819,3.23082742 5.68363441,1.85718572 5.18206865,1.26652017 C5.16580525,1.24737446 5.14832991,1.22679331 5.13128461,1.20683289 C4.92396529,1.10575052 4.78125,0.894100213 4.78125,0.649395915 C4.78125,0.371269065 4.94654249,0.131666411 5.20233927,0.0389639269 C5.35351856,-0.0158350933 5.65212406,-0.0606054078 5.95325117,0.254998153 C6.00649818,0.310805863 6.0648666,0.379532563 6.13875563,0.466551468 C6.55998173,0.962652332 7.43181361,1.9892853 8.5417519,1.9892853 C8.55379307,1.9892853 8.56571696,1.98916892 8.57781678,1.98891674 C9.38627611,1.97270011 10.2345339,1.4037796 11.0989769,0.297983862 C11.3107334,0.0271118203 11.703694,-0.0221006096 11.9766143,0.187997864 C12.2495737,0.398135133 12.2991849,0.788071383 12.0874478,1.05892403 C10.9801093,2.47545445 9.80779542,3.20595934 8.60309152,3.2301097 C8.58215629,3.23055585 8.56120152,3.23076923 8.54040313,3.23076923 Z"
                  id="路径"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </svg>
);
const SkinIcon = props => <Icon component={filterRaw} {...props} />;
export default props => <SkinIcon {...props} />;
