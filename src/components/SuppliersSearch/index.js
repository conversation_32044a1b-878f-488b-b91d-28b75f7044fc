import React, { useState, useEffect, Fragment } from 'react';
import { Select, Tooltip } from 'antd';
import { connect } from 'dva';
import { supplierSearch } from './api';
const { Option } = Select;
let isFirst = true;
function supplierNameSearch(props) {
  const {
    onChange,
    value,
    gysParams,
    onOther,
    isName,
    systemRoles = [],
    userInfo,
    ...rest
  } = props;

  const [dataSource, setDataSource] = useState([]);
  useEffect(() => {
    if (isFirst && !gysParams) {
      isFirst = false;
      return;
    }
    const userRole = getCurrentRoleName();
    let params = gysParams;
    if (userRole.indexOf('总部') > -1 || userRole.indexOf('分拨区') > -1) {
      params = userInfo.deptCode === gysParams ? undefined : gysParams;
    }
    supplierSearch({ zoneCode: params }).then(res => {
      const data = [];
      let idStr = '';
      if (Array.isArray(res.obj)) {
        res.obj.forEach(item => {
          const val = isName ? item?.companyName : item?.companyCode;
          if (idStr.indexOf(val) === -1) {
            idStr += val;
            data.push({
              label: `${item?.companyName}-${item?.companyCode}`,
              value: val,
              text: item?.companyName,
            });
          }
        });
      }
      setDataSource(data || []);
    });
    onChange(undefined, {});
  }, [gysParams]);

  const getCurrentRoleName = () => {
    const currentRole = systemRoles.find(
      role => role.roleId === sessionStorage.getItem('roleId'),
    );
    return currentRole ? currentRole.roleName : '';
  };

  const handleChange = (v, option) => {
    if (typeof v === 'object') {
      v.label = v.label ? v.label.props.children : '';
    }
    const beChose = dataSource.find(item => item.v === v) || {};
    if (onOther) {
      onOther(v);
    }
    onChange(
      v,
      Object.assign({}, option, {
        text: beChose.text,
      }),
    );
  };

  return (
    <Fragment>
      <Select
        showSearch
        // style={{ width: '100%' }}
        defaultActiveFirstOption={false}
        onChange={handleChange}
        filterOption={(input, option) =>
          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
        }
        // className="map-person-select"
        value={value}
        allowClear
        {...rest}
      >
        {dataSource.map(item => (
          <Option value={item.value} key={item.value} label={item.label}>
            <Tooltip placement="rightTop" title={item.label}>
              {item.label}
            </Tooltip>
          </Option>
        ))}
      </Select>
    </Fragment>
  );
}

export default connect(state => ({
  systemRoles: state.global.roles,
  userInfo: state.global.userInfo,
}))(supplierNameSearch);
