import React, { useMemo } from 'react';
import { Row } from 'antd';
import { connect } from 'dva';

const FormAdaptive = propss => {
  const { children } = propss;
  // console.log(children, 'children');
  const demo = useMemo(() => {
    const demo1 = children;
    demo1.map(({ props, ...item }) => ({
      ...item,
      props: {
        ...props,
        xxl: 6,
        xs: 24,
        xl: 8,
        md: 12,
      },
    }));
    return demo1;
  }, [children]);
  // console.log(demo, 'demo');
  // children.forEach(item => {
  //   item.props = Object.assign({}, item.props, {
  //     xxl: 6,
  //     xs: 24,
  //     xl: 8,
  //     md: 12,
  //   });
  // });
  // const formItemLayout = {
  //   labelCol: {
  //     xs: { span: 6 }, // <576px
  //     // sm: { span: 4 }, // >= 576px
  //     md: { span: 8 }, // >= 768px
  //     lg: { span: 8 }, // >= 992px
  //     xl: { span: 8 }, // >= 1200px
  //     xxl: { span: 6 }, // >= 1600px
  //   },
  //   wrapperCol: {
  //     xs: { span: 18 }, // <576px
  //     // sm: { span: 16 }, // >= 576px
  //     md: { span: 16 }, // >= 768px
  //     lg: { span: 16 }, // >= 992px
  //     xl: { span: 16 }, // >= 1200px
  //     xxl: { span: 18 }, // >= 1600px
  //   },
  // };
  return <Row gutter={{ xs: 24, sm: 12, md: 8, xxl: 6 }}>{demo}</Row>;
};

export default connect(() => ({}))(FormAdaptive);
