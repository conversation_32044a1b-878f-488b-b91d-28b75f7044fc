import React, { useState } from 'react';
import { Select } from 'antd';
import { connect } from 'dva';
import { useDebounce } from 'react-use';
import { queryNet } from '@/services/api';
import './index.scss';
const { Option } = Select;
/**
 * @param {*} props
 * @param {*} props type 1：网点，2：集配站，3：中转场，4：加盟点，5：第三方， 数组形式
 * @param {*} typeLevel 4：集配站，3：分部，2：区部 1:大区
 */

let currentValue;
let lock = true;
function DeptSearch(props) {
  const {
    dispatch,
    type,
    orgCode,
    isArea = false,
    typeLevel = 2,
    valueInOptions = 'value',
    extraParam = {},
    ...restProps
  } = props;
  const [dataSource, setDataSource] = useState([]);
  // const deptCode = dept ? '' : dept.code;

  function fetch(value, callback) {
    currentValue = value;

    function query() {
      if (value.match(/^[0-9a-zA-Z]+$/g) && value.length > 2) {
        const param = { deptCodeFuzzy: value, pageNum: 1, pageSize: 10 };
        if (isArea) {
          param.orgCode = 'SF';
        }
        if (type) {
          param.deptTypes = type;
          param.orgCode = orgCode;
        }
        if (isArea && typeLevel) {
          queryNet({ ...param, ...extraParam }).then(d => {
            const { obj } = d;
            const data = [];
            if (obj.list) {
              const filterData = obj.list.filter(
                item => item.typeLevel === typeLevel,
              );
              // if (!filterData || filterData.length === 0) {
              //   message.error('暂无查询结果');
              // }
              filterData.forEach(r => {
                data.push({
                  value: r.deptCode,
                  text: `${r.deptCode} ${r.deptName}`,
                  ...r,
                });
              });
            } else {
              // message.error('暂无查询结果');
            }
            callback(data);
          });
        } else {
          queryNet({ ...param, ...extraParam }).then(d => {
            if (currentValue === value) {
              const { obj } = d;
              const data = [];
              if (obj.list) {
                obj.list.forEach(r => {
                  data.push({
                    value: r.deptCode,
                    text: `${r.deptCode} ${r.deptName}`,
                    label: r.deptName,
                    ...r,
                  });
                });
              } else {
                // message.error('暂无查询结果');
              }
              callback(data);
            }
          });
        }
      } else if (!value.match(/^[0-9a-zA-Z]+$/g) && value.length >= 2) {
        const param = { deptName: value, pageNum: 1, pageSize: 10 };
        if (isArea) {
          param.orgCode = 'SF';
        }
        if (type) {
          param.deptTypes = type;
          param.orgCode = orgCode;
        }
        if (isArea && typeLevel) {
          queryNet({ ...param, ...extraParam }).then(d => {
            const { obj } = d;
            const data = [];
            if (obj.list) {
              const filterData = obj.list.filter(
                item => item.typeLevel === typeLevel,
              );
              if (!filterData || filterData.length === 0) {
                // message.error('暂无查询结果');
              }
              filterData.forEach(r => {
                data.push({
                  value: r.deptCode,
                  text: `${r.deptCode} ${r.deptName}`,
                  label: r.deptName,
                  ...r,
                });
              });
            } else {
              // message.error('暂无查询结果');
            }
            callback(data);
          });
        } else {
          queryNet({ ...param, ...extraParam }).then(d => {
            if (currentValue === value) {
              const { obj } = d;
              const data = [];
              if (obj.list) {
                obj.list.forEach(r => {
                  data.push({
                    value: r.deptCode,
                    text: `${r.deptCode} ${r.deptName}`,
                    label: r.deptName,
                    ...r,
                  });
                });
              } else {
                // message.error('暂无查询结果');
              }
              callback(data);
            }
          });
        }
      }
    }

    query();
  }
  const handleSearch = value => {
    if (!lock) {
      return;
    }
    if (value) {
      fetch(value, data => setDataSource(data));
    } else {
      setDataSource([]);
    }
  };
  const [searchValue, setSearchValue] = useState('');
  // 搜索时的防抖
  useDebounce(
    () => {
      handleSearch(searchValue);
    },
    300,
    [searchValue],
  );
  const options = dataSource.map(d => (
    <Option key={d[`${valueInOptions}`]} dataref={d} label={d.label}>
      {d.text}
    </Option>
  ));

  return (
    <Select
      showSearch
      style={{ width: '100%' }}
      allowClear
      // value={this.state.value}
      // placeholder={this.props.placeholder}
      // style={this.props.style}
      dropdownMatchSelectWidth={false}
      defaultActiveFirstOption={false}
      showArrow={false}
      filterOption={false}
      onSearch={e => {
        setSearchValue(e);
      }}
      onCompositionStart={() => {
        lock = false;
      }}
      onCompositionEnd={() => {
        lock = true;
      }}
      notFoundContent={null}
      {...restProps}
    >
      {options || []}
    </Select>
  );
}

export default connect(() => ({}))(DeptSearch);
