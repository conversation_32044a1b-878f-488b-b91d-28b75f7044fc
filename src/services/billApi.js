// import { stringify } from 'qs';
// import request from '@/utils/request';

// /**
//  * 获取登录员工网点
//  * @param {*} keyword 网点编号
//  * @param {*} page 页数
//  * @returns
//  *  id 网点编码
//  *  text 网点名称
//  */
// export function getUserDept() {
//   return request(`/opomsVehicleOrder/auth/user/info`, { method: 'GET' });
// }

// /**
//  * 获取登录员工网点列表
//  * @param {*} keyword 网点编号
//  * @param {*} page 页数
//  * @returns
//  *  id 网点编码
//  *  text 网点名称
//  */
// export function getDeptList(keyword, page) {
//   return request(
//     `/opomsVehicleOrder/auth/userAuthorityInfoPage?${stringify(keyword, page)}`,
//     {
//       method: 'GET',
//     },
//   );
// }

// export function getDeptListNew(deptCodeName) {
//   return request(
//     `/opomsVehicleOrder/tlOrderPermissionService/getDeptList?${stringify({
//       deptCodeName,
//     })}`,
//     {
//       method: 'GET',
//     },
//   );
// }

// export function queryDept(body) {
//   return request(`/opomsVehicleOrder/basic/queryDept`, {
//     method: 'POST',
//     body,
//   });
// }

// export function milestone(body) {
//   return request(`/opomsVehicleOrder/tlOrder/milestone`, {
//     method: 'POST',
//     body,
//   });
// }

// /**
//  * 前端需要根据本接口返回的报价费用范围、最小费率，最低保价金额进行判断
//  * @param {*} declaredValue 声明价值(元)
//  * @returns Promise
//  * @description
//  *  maxDeclaredValue	最大声明价值（万元）
//     minCharge	最小保价费用（元）
//     minDeclaredValue
//     minRate	最小保价费率（千分之）
//  */
// export function getDeclaredValue(
//   declaredValue,
//   eoorCustomerMonthlyCard,
//   eoorStationCode,
//   insuranceRate,
// ) {
//   return request(
//     `/opomsVehicleOrder/validate/insurance?${stringify({
//       declaredValue,
//       eoorCustomerMonthlyCard,
//       eoorStationCode,
//       insuranceRate,
//     })}`,
//     {
//       method: 'GET',
//     },
//   );
// }

// export function queryGisTipInfo(city, q) {
//   return request('/opbdsBaseinfoService/gisTisInfo/queryGisTipInfo', {
//     method: 'POST',
//     body: {
//       city,
//       q,
//     },
//   });
// }

// /**
//  * 保存订单
//  * @param {*} orderInfo
//  */
// export function saveOrder(orderInfo) {
//   // 计算合计费用
//   const quotaInsurance = orderInfo.eoOrderServiceModel.quotaInsurance
//     ? orderInfo.eoOrderServiceModel.quotaInsurance
//     : 0;
//   const individualityPackageFee = orderInfo.eoOrderServiceModel
//     .individualityPackageFee
//     ? orderInfo.eoOrderServiceModel.individualityPackageFee
//     : 0;
//   const { eoorExternalQuotes } = orderInfo.eoOrderModel;
//   orderInfo.eoOrderModel.eoorAmount =
//     parseFloat(quotaInsurance) +
//     parseFloat(individualityPackageFee) +
//     parseFloat(eoorExternalQuotes);

//   //
//   return request('/opomsVehicleOrder/submit/order', {
//     method: 'POST',
//     data: orderInfo,
//   });
// }

// /**
//  * 公司名称联想
//  * @param {*} ebcuNameCn 公司名称关键字
//  * @returns
//  *  ebcuNameCn	公司名称
//     pmCode	月结卡号
//  */
// export function queryCompany(ebcuNameCn) {
//   return request(
//     `/opomsVehicleOrder/basic/queryCompany?${stringify({
//       ebcuNameCn,
//     })}`,
//     {
//       method: 'GET',
//     },
//   );
// }

// /**
//  * 工号查询
//  * @param {*} escoCompanyNo 网点
//  * @param {*} esusLoginName 工号
//  * @returns
//  *  esusLoginName	工号
//  *  esusUserNameCn	姓名
//  */
// export function queryEmployee(
//   esusLoginName,
//   escoCompanyNo,
//   pageNum = 1,
//   pageSize = 10,
// ) {
//   return request(
//     `/opomsVehicleOrder/basic/queryEmployee?${stringify({
//       esusLoginName,
//       escoCompanyNo,
//       pageNum,
//       pageSize,
//     })}`,
//     {
//       method: 'GET',
//     },
//   );
// }

// export function queryEmployeePage(
//   esusLoginName,
//   escoCompanyNo,
//   pageNum = 1,
//   pageSize = 10,
// ) {
//   return request(`/opomsVehicleOrder/basic/queryEmployeePage?`, {
//     method: 'POST',
//     body: {
//       empInfo: esusLoginName,
//       deptCode: escoCompanyNo,
//       pageNum,
//       pageSize,
//     },
//   });
// }
// /**
//  * @param {*} userId	登陆用户，从header获取	string
//  */
// export function getDeptListByUserId() {
//   return request(
//     '/opomsVehicleOrder/tlOrderPermissionService/getDeptListByUserId',
//     {
//       method: 'GET',
//     },
//   );
// }

// export function getDeptTreeByDeptCode(deptCode) {
//   return request(
//     `/opomsVehicleOrder/tlOrderPermissionService/getDeptTreeByDeptCode?deptCode=${deptCode}`,
//     {
//       method: 'GET',
//     },
//   );
// }

// /**
//  * 月结卡号校验
//  * @param {*} eoorStationCode 网点
//  * @param {*} paymentTypeCode 支付方式
//  * @param {*} pmCode 月结卡号
//  */
// export function monthlyCard(eoorStationCode, paymentTypeCode, pmCode) {
//   return request(
//     `/opomsVehicleOrder/validate/monthlyCard?${stringify({
//       eoorStationCode,
//       paymentTypeCode,
//       pmCode,
//     })}`,
//     {
//       method: 'GET',
//     },
//   );
// }

// /**
//  * 根据地址匹配网点
//  * @param {*} address
//  * @param {*} cityCode
//  * @returns
//  *  deptCode	网点代码
//     unitName	网点名称
//  */
// export function matchDept(address, cityCode) {
//   return request(
//     `/opomsVehicleOrder/basic/matchDept?${stringify({ address, cityCode })}`,
//     {
//       method: 'GET',
//       //   body: {
//       //     address,
//       //     cityCode,
//       //     // cityCode: cityCode.match(/^[A-Z]+\d{3}/)[0],
//       //   },
//     },
//   );
// }

// export function getOrderDetail(eoorOrderNo) {
//   return request('/opomsVehicleOrder/queryorder/detail', {
//     method: 'GET',
//     body: {
//       eoorOrderNo,
//     },
//   });
// }

// /**
//  * 获取订单筛选状态
//  * @param {*} eoorOrderNo
//  * 	currentPage number
//     limit		number
//     rows		array<object>
//     eoomOperatorName	操作人	string
//     eoomStatusName	状态名称	string
//     modifyTime	里程碑时间	string
//     total
//  */
// export function getOrderStatusEnum(eoorOrderNo) {
//   return request('/opomsVehicleOrder/querytracking/ordertracking', {
//     method: 'GET',
//     body: {
//       eoorOrderNo,
//     },
//   });
// }

// /**
//  * 判断是否可以修改订单
//  * @param {*} eoorOrderNo
//  * return success
//  */
// export function isCanUpdateOrderIssued(eoorOrderNo) {
//   return request('/opomsVehicleOrder/validate/isCanUpdateOrderIssued', {
//     method: 'GET',
//     body: {
//       eoorOrderNo,
//     },
//   });
// }

// /**
//  * 取消订单
//  * @param {*} eoorOrderNo
//  * return success
//  */
// export function cancelOrder(eoorOrderNo) {
//   return request('/opomsVehicleOrder/submit/cancel', {
//     method: 'POST',
//     data: { eoorOrderNo },
//   });
// }

// export function queryVehicleInfo(weightTypeCode) {
//   return request('/opomsVehicleOrder/basic/queryVehicleInfo', {
//     method: 'POST',
//     body: { weightTypeCode },
//   });
// }

// export function addressSplit(address) {
//   return request('/opbdsBaseinfoService/gisAddressSplit/addressSplit', {
//     method: 'POST',
//     body: { address, opt: 'cx1' },
//   });
// }

// /**
//  * 月结客户信息查询
//  * @param {*} pmCode
//  * return
//  *  address	地址	string
//     company	公司	string
//     contact	联系人	string
//     phone	联系方式
//  */
// export function queryMonthlyCustomer(pmCode) {
//   return request('/opomsVehicleOrder/basic/queryMonthlyCustomer', {
//     method: 'GET',
//     body: {
//       pmCode,
//     },
//   });
// }

// export function getProdConfDetail(productCode = 'SE0020') {
//   return request(
//     `/opomsVehicleOrder/tlRateConfService/getProdConfDetail?${stringify({
//       productCode,
//     })}`,
//     {
//       method: 'GET',
//     },
//   );
// }

// export function updateProdConf(updateProd) {
//   return request(`/opomsVehicleOrder/tlRateConfService/updateProdConf`, {
//     method: 'POST',
//     body: updateProd,
//   });
// }

// export function queryRoutingInfo(mainlineRequireId) {
//   return request(
//     `/opomsVehicleOrder/tlCapacityService/queryRoutingInfo?${stringify({
//       mainlineRequireId,
//     })}`,
//     {
//       method: 'GET',
//     },
//   );
// }

// export function queryGpsPath(mainlineRequireId) {
//   return request(
//     `/opomsVehicleOrder/tlCapacityService/queryGpsPath?${stringify({
//       mainlineRequireId,
//     })}`,
//     {
//       method: 'GET',
//     },
//   );
// }
