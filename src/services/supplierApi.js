import request from '@/utils/request';

// 查询
// export function querySupplierList(body) {
//   // return request(`/ospmSupplierServices/supplierRest/querySupplier`, {  // 当前接口
//   // /ospmSupplierServices/supplierRest/selectSupplierBySiteCode  // 计提系统和用工审批的新接口
//   return request(
//     `/tdmsAccrueService/supplierRestService/querySupplierByZoneCode`, // 中转老接口
//     {
//       method: 'POST',
//       body,
//     },
//   );
// }
// 查询供应商（新接口）
export function querySupplierList(body) {
  return request(
    `/tdmsAccrueService/supplierRest/querySupplier`, // 中转老接口
    {
      method: 'POST',
      body,
    },
  );
}
// 查询供应商（旧接口）
export function querySupplierListOld(body) {
  return request(
    `/tdmsAccrueService/supplierRestService/querySupplierByZoneCode`, // 中转老接口
    {
      method: 'POST',
      body,
    },
  );
}
// 新增
export function addSupplier(body) {
  return request(`/tdmsAccrueService/supplierRest/saveSupplier`, {
    method: 'POST',
    body,
  });
}
// 修改
export function editSupplierList(body) {
  return request(`/tdmsAccrueService/supplierRest/updateSupplier`, {
    method: 'POST',
    body,
  });
}
// 导出
export function exportSupplierList(body) {
  return request(`/tdmsAccrueService/supplierRest/export`, {
    method: 'POST',
    body,
  });
}
// 下载模板
export function domnTemplate() {
  return request(`/tdmsAccrueService/supplierRest/import/download/tmp`, {
    method: 'GET',
  });
}
// 上传文件
export function importTemplate(body) {
  return request(`/tdmsAccrueService/supplierRest/import`, {
    method: 'POST',
    body,
  });
}

// 顺丰通过网点查看战区
export function codeToDepart(body) {
  return request(`/opbdsUPMService/epDepartment/queryByDeptCode`, {
    method: 'POST',
    body,
  });
}
// 查看供应商是否存在
export function queryNameToCode(body) {
  return request(`/tdmsAccrueService/supplierRest/querySupplierCode`, {
    method: 'POST',
    body,
  });
}
// // 查看网点供应商
export function querySuppliers(body) {
  return request(`/tdmsAccrueService/supplierRest/querySupplierBySiteCode`, {
    method: 'POST',
    body,
  });
}

// // 查询所属部门
export function querySections(body) {
  return request(`/opbdsUPMService/epDepartment/selectFunctionalDepartments`, {
    method: 'POST',
    body,
  });
}
