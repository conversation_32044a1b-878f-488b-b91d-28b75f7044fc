import { stringify } from 'qs';
import request from 'src/utils/request';

export async function queryProjectNotice() {
  return request('/api/project/notice');
}
export async function queryMenus(roleId) {
  return request(
    `/dev-basp-user/indexModule/queryMenus?roleId=${roleId}&time=${new Date().getTime() -
      ******** * 15}`,
  );
}

// function mock(data) {
//   return new Promise(resolve => resolve(data));
// }
export async function queryRoles() {
  return request('/dev-basp-user/indexModule/queryRoles');
}
export async function querySystem() {
  return request('/dev-basp-user/account/getSysDetails');
}

export async function queryActivities() {
  return request('/api/activities');
}

export async function queryRule(params) {
  return request(`/api/rule?${stringify(params)}`);
}

export async function removeRule(params) {
  return request('/api/rule', {
    method: 'POST',
    body: {
      ...params,
      method: 'delete',
    },
  });
}

export async function addRule(params) {
  return request('/api/rule', {
    method: 'POST',
    body: {
      ...params,
      method: 'post',
    },
  });
}

export async function updateRule(params = {}) {
  return request(`/api/rule?${stringify(params.query)}`, {
    method: 'POST',
    body: {
      ...params.body,
      method: 'update',
    },
  });
}

export async function fakeSubmitForm(params) {
  return request('/api/forms', {
    method: 'POST',
    body: params,
  });
}

export async function fakeChartData() {
  return request('/api/fake_chart_data');
}

export async function queryTags() {
  return request('/api/tags');
}

export async function queryBasicProfile(id) {
  return request(`/api/profile/basic?id=${id}`);
}

// export async function clearDeptCodeCache() {
//   return request(
//     `/opomsVehicleOrder/tlOrderPermissionService/clearDeptCodeCache`,
//   );
// }

// 通过工号查询元数据用户的基础信息（网点，职能，工号，姓名。。。。）
// export function getEmpDeptDetailInfo(body) {
//   return request(`/opbdsUPMService/bdusEmpData/queryEmpDeptInfo`, {
//     method: 'POST',
//     body,
//   });
// }

export async function queryAdvancedProfile() {
  return request('/api/profile/advanced');
}

export async function queryFakeList(params) {
  return request(`/api/fake_list?${stringify(params)}`);
}

export async function removeFakeList(params) {
  const { count = 5, ...restParams } = params;
  return request(`/api/fake_list?count=${count}`, {
    method: 'POST',
    body: {
      ...restParams,
      method: 'delete',
    },
  });
}

export async function addFakeList(params) {
  const { count = 5, ...restParams } = params;
  return request(`/api/fake_list?count=${count}`, {
    method: 'POST',
    body: {
      ...restParams,
      method: 'post',
    },
  });
}

export async function updateFakeList(params) {
  const { count = 5, ...restParams } = params;
  return request(`/api/fake_list?count=${count}`, {
    method: 'POST',
    body: {
      ...restParams,
      method: 'update',
    },
  });
}

export async function fakeAccountLogin(params) {
  return request('/api/login/account', {
    method: 'POST',
    body: params,
  });
}

export async function fakeRegister(params) {
  return request('/api/register', {
    method: 'POST',
    body: params,
  });
}

export async function queryNotices(params = {}) {
  return request(`/api/notices?${stringify(params)}`);
}

export async function getFakeCaptcha(mobile) {
  return request(`/api/captcha?mobile=${mobile}`);
}

export async function getRoutes(mobile) {
  return request(`/api/captcha?mobile=${mobile}`);
}

// 获取公司名称
export function getSupplierName(data) {
  return request(`/tdmsAccrueService/user/fuzzyQuerySupplierName`, {
    method: 'POST',
    body: {
      fuzzySupplierName: data,
    },
  });
}
// 获取网点代码的相关信息 SX&SF 网点名称
/**
 * @author：LWQ
 * @function: 输入代码远程搜索，
 * @description： 1：网点，2：集配站，3：中转场，4：加盟点，5：第三方， 数组形式
 * @return 网点名称，网点编码，分部名称，分部编码，区部名称，区部编码，所属公司
 */
export function getDept(queryString, customSearch = {}) {
  return request('/opbdsUPMService/epDepartment/selectDeptInfoData', {
    method: 'POST',
    body: {
      deptInfo: queryString,
      // deptTypes: ['2', '3'],
      excludeOverseasFlag: 1,
      pageNum: 1,
      pageSize: 20,
      ...customSearch,
    },
  });
}

// 获取分拨区 SF
export function getAllocationAreaSF() {
  return request('/opbdsUPMService/epDepartment/selectDeptInfoData', {
    method: 'POST',
    body: {
      typeLevels: [2],
      hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
      excludeOverseasFlag: true,
    },
  });
}

//  获取分拨区 SX  顺心带上省区
export function getAllocationAreaSX() {
  return request(`/opbdsUPMService/epDepartment/selectDeptInfoData`, {
    method: 'POST',
    body: {
      typeCodes: ['区部一级职能', '区部二级职能', '省部职能'],
      orgCode: 'SX',
      typeLevels: ['1'],
    },
  });
}

// 人员查询接口
// 1. 人员＋部门 登录专用 包含很多敏感信息
export function getEmpDeptDetailInfo(data) {
  return request(`/opbdsUPMService/opEmployee/selectEmpDeptInfo`, {
    method: 'POST',
    body: data,
  });
}
// 2. SF + SX 查询人员基本信息
export function getEmpDeptBaseInfo(data) {
  return request(`/opbdsUPMService/opEmployee/selectByEmpCode`, {
    method: 'POST',
    body: data,
  });
}
// 3. SF 查询人员基本信息 缺失orgCode-公司归属
export function getEmpDeptBaseInfoSF(data) {
  return request(`/opbdsUPMService/epEmployee/selectByEmpCode`, {
    method: 'POST',
    body: data,
  });
}

/**
 * @description: 供应商查询
 * @param {type}
 * @return:
 */
export function supplierSearch(params) {
  return request(
    `/ospmSupplierServices/supplierRest/selectSupplierBySiteCode`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 流向城市代码 批量查询接口 精确
export function cityCodeListSearch(params) {
  return request(
    `/opbdsBaseinfoService/gisAddressInfo/batchQueryAddressByCityCode`,
    {
      method: 'POST',
      body: {
        cityCodeList: params,
      },
    },
  );
}

// 流向城市代码 批量查询接口 精确
export function zoneCodeListSearch(params) {
  return request(`/opbdsUPMService/epDepartment/selectDeptNameBatch`, {
    method: 'POST',
    body: params,
  });
}

// 月台号查询  1-装车，2-卸车
export async function queryPlatformList(params) {
  return request(`/sdmBasicService/platformInfo/queryPlatformWeb`, {
    method: 'POST',
    body: params,
  });
}
// 班次编号拉取--从快管PC那边取数
export async function queryShiftCode(params) {
  return request(
    `/sdmCoreStaffServices/workShiftSettingRest/queryShiftDetailByDeptCode`,
    {
      method: 'POST',
      body: params,
    },
  );
}
