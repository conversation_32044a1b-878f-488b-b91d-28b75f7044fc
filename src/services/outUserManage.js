import request from '@/utils/request';

// let baseApi = process.env.NODE_ENV == 'development' ? '' : '/restApi'
const baseApi = '';
// 用户列表
export async function queryOutUser(data) {
  return request(`${baseApi}/opbdsUPMService/outSoUseManag/query`, {
    method: 'POST',
    body: data,
  });
}
// 场站获取供应商(顺心)
export async function siteToCompany(data) {
  return request(`${baseApi}/opbdsUPMService/outSoUseManag/queryOutSource`, {
    method: 'POST',
    body: data,
  });
}
// 场站获取供应商(顺丰)
export async function siteToSFCompany(data) {
  return request(
    `${baseApi}/tdmsAccrueService/supplierRestService/querySupplierByZoneCode`,
    {
      method: 'POST',
      body: data,
    },
  );
}
// 修改
export async function editUserInfo(data) {
  return request(`${baseApi}/opbdsUPMService/outSoUseManag/modifiy`, {
    method: 'POST',
    body: data,
  });
}
// 新增
export async function addUserInfo(data) {
  return request(`${baseApi}/opbdsUPMService/outSoUseManag/add`, {
    method: 'POST',
    body: data,
  });
}
// 删除
export async function delUserInfo(data) {
  return request(`${baseApi}/opbdsUPMService/outSoUseManag/delete`, {
    method: 'POST',
    body: data,
  });
}
// 导出所选
export async function exportUserList(data) {
  return request(`${baseApi}/opbdsUPMService/outSoUseManag/export`, {
    method: 'POST',
    body: data,
    responseType: 'blob',
  });
}
// 导出全部
export async function asyncExportUserList(data) {
  return request(`${baseApi}/opbdsUPMService/outSoUseManag/export/async`, {
    method: 'POST',
    body: data,
    responseType: 'blob',
  });
}

// 启用
export async function startUsing(data) {
  return request(`${baseApi}/opbdsUPMService/outSoUseManag/updateEnable`, {
    method: 'POST',
    body: data,
  });
}
// 启用
export async function stopUsing(data) {
  return request(`${baseApi}/opbdsUPMService/outSoUseManag/updateDisable`, {
    method: 'POST',
    body: data,
  });
}
// 顺丰数据修改
export async function updateSF(data) {
  return request(`${baseApi}/opbdsUPMService/outSoUseManag/modifySfUserInfo`, {
    method: 'POST',
    body: data,
  });
}
