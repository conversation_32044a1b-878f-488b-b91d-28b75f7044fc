import request from '@/utils/request';

const baseApi = process.env.NODE_ENV === 'development' ? '' : '/restApi';
// 获取打卡情况
export async function queryClockList(data) {
  return request(`${baseApi}/sdmCoreStaffServices/clockQuery/query`, {
    method: 'POST',
    body: data,
  });
}
// 导出打卡列表
export async function exportClockList(data) {
  return request(`${baseApi}/sdmCoreStaffServices/clockQuery/export`, {
    method: 'POST',
    body: data,
  });
}
// 导出打卡列表
export async function exportAsyncList(data) {
  return request(`${baseApi}/sdmCoreStaffServices/clockQuery/exportSync`, {
    method: 'POST',
    body: data,
  });
}

// 用户角色
export async function checkUserRole() {
  return request(`${baseApi}/sdmCoreStaffServices/clockQuery/isRoot`, {
    method: 'GET',
  });
}
// 用户工号查网点
export async function userNoToCode() {
  return request(`${baseApi}/sdmCoreStaffServices/clockQuery/getSiteByUserNo`, {
    method: 'GET',
  });
}
