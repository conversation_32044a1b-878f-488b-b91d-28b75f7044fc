import _dvaDynamic from 'dva/dynamic';
import BaseRoutes from './base';
import renderRoutes from './renderRoutes';
import MainRoutes from './main';

const routes = [
  {
    path: '/exception/403',
    name: '403',
    noAuth: true,
    component: _dvaDynamic({
      component: () => import('src/pages/Base/Exception/403'),
    }),
  },
  {
    path: '/',
    component: _dvaDynamic({
      component: () => import('src/layouts/BasicLayout'),
      LoadingComponent: require('src/components/PageLoading/index').default,
    }),
    noAuth: true,
    name: 'base',
    routes: [
      {
        path: '/',
        name: 'base',
        component: _dvaDynamic({
          component: () => import('src/pages/Home'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
      {
        path: '/download/list',
        name: 'base_download',
        noAuth: {
          moduleName: `下载列表`,
          moduleCode: 'base_download',
          moduleIcon: 'home',
        },
        component: _dvaDynamic({
          models: () => [],
          component: () => import('src/pages/Download'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
      ...BaseRoutes,
      ...MainRoutes,
    ],
  },
];

const pathsMapping = {};
const noAuthRoutes = {};
function getPathsMapping(_routes) {
  // eslint-disable-next-line array-callback-return
  _routes.map(route => {
    const { name, path, noAuth } = route;
    pathsMapping[name] = path;
    if (noAuth) noAuthRoutes[path] = noAuth;
    if (route.routes) getPathsMapping(route.routes);
  });
}

getPathsMapping(routes);
const { routeDOM } = renderRoutes(routes);
export { routeDOM, pathsMapping, noAuthRoutes };
