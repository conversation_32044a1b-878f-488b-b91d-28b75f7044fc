import _dvaDynamic from 'dva/dynamic';

export default [
  // 菜单重构后
  {
    path: '/selfAccrualManage',
    name: 'selfAccrualManage', // 自有计量计提管理
    routes: [
      {
        path: '/selfAccrualManage/liftTruckAccrual',
        name: 'liftTruckAccrual', // 计量计提
        routes: [
          {
            path: '/selfAccrualManage/liftTruckAccrual/fieldTask', // 场内任务-装卸
            name: 'fieldTask',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/ForkAccrue/TruckAccrued/fieldTask'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/selfAccrualManage/liftTruckAccrual/fieldPersonnel', // 场内人员-装卸
            name: 'fieldPersonnel',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/ForkAccrue/TruckAccrued/fieldPerson'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/selfAccrualManage/liftTruckAccrual/sortForkliftTask', // 场内任务-叉车分拣
            name: 'sortForkliftTask',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import('src/pages/SelfAccrualManage/ForkAccrue/SortForkTask'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/selfAccrualManage/liftTruckAccrual/sortForkliftPersonnel', // 场内人员-叉车分拣
            name: 'sortForkliftPersonnel',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/ForkAccrue/ForkAccruedSeach'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/selfAccrualManage/liftTruckAccrual/flowCargoSort', // 流向货量-包干制分拣
            name: 'flowCargoSort',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import('src/pages/SelfAccrualManage/ForkAccrue/FlowCargo'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/selfAccrualManage/fixedtAccrual',
        name: 'fixedtAccrual', // 固定计提均摊计提
        routes: [
          {
            path: '/selfAccrualManage/fixedtAccrual/transitPerson',
            name: 'transitPerson', // 场内人员
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/FixedtAccrual/FieldPersonnel'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/selfAccrualManage/fixedtAccrual/fieldAttendance',
            name: 'fieldAttendance', // 场内出勤
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/FixedtAccrual/FieldAttendance'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/selfAccrualManage/abnormalAccrualManage',
        name: 'abnormalAccrualManage', // 异常计提管理
        routes: [
          {
            path:
              '/selfAccrualManage/abnormalAccrualManage/abnormalAccrualApply', // 异常计提申请
            name: 'abnormalAccrualApply',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/AbnormalAccrue/AbnormalAccrueApply'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path:
              '/selfAccrualManage/abnormalAccrualManage/liftTruckRepetitionScan', // 装卸车重复扫描
            name: 'liftTruckRepetitionScan',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/AbnormalAccrue/RepetitionLiftTruck'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/selfAccrualManage/transitAccrualConfig',
        name: 'transitAccrualConfig', // 中转计提配置
        routes: [
          {
            path: '/selfAccrualManage/transitAccrualConfig/accrualPriceConfig',
            name: 'accrualPriceConfig', // 计提单价配置
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/TransitAccrueConfig/AccruePriceConfig'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path:
              '/selfAccrualManage/transitAccrualConfig/sxAccrualPriceConfig',
            name: 'sxAccrualPriceConfig', // 顺心计提单价配置
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/TransitAccrueConfig/SXAccruePriceConfig'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path:
              '/selfAccrualManage/transitAccrualConfig/fieldPersonnelConfig',
            name: 'fieldPersonnelConfig', // 场站人员排班配置
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/TransitAccrueConfig/FieldScheduleConfig'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/selfAccrualManage/transitAccrualConfig/cargoWarning', // 货量预警
            name: 'cargoWarning',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/TransitAccrueConfig/CargoWarming'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/selfAccrualManage/transitAccrualConfig/attendanceDaysData',
            name: 'attendanceDaysData', // 公司规定出勤天数
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/TransitAccrueConfig/AttendanceDay'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/selfAccrualManage/transitAccrualConfig/sortInclusiveConfig',
            name: 'sortInclusiveConfig', // 分拣包干制配置
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/TransitAccrueConfig/SortOutConfig'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/selfAccrualManage/transitAccrualConfig/cargoVolumeConfig', // 货量折算系数配置
            name: 'cargoVolumeConfig',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/TransitAccrueConfig/CargoConvertConfig'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path:
              '/selfAccrualManage/transitAccrualConfig/loadTransportLineConfig', // 货量折算线路配置
            name: 'loadTransportLineConfig',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/TransitAccrueConfig/LiftTruckIntegration'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path:
              '/selfAccrualManage/transitAccrualConfig/unloadDirectDivisionConfig', // 大单件卸车直分配置
            name: 'unloadDirectDivisionConfig',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/TransitAccrueConfig/UnloadDirectDivisionConfig'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/selfAccrualManage/accrualPayableManage',
        name: 'accrualPayableManage', // 自有计提应付管理
        routes: [
          {
            path: '/selfAccrualManage/accrualPayableManage/accrualPayable', // 计提应付
            name: 'accrualPayable',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import('src/pages/SelfAccrualManage/AccrueManage'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/selfAccrualManage/accrualPayableManage/sxAccrualPayable', // 顺心计提应付
            name: 'sxAccrualPayable',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import('src/pages/SelfAccrualManage/SxAccrueManage'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/selfAccrualManage/selfSupplierManage',
        name: 'selfSupplierManage', // 供应商管理
        routes: [
          {
            path:
              '/selfAccrualManage/selfSupplierManage/selfSupplierInformation', // 供应商信息查询
            name: 'selfSupplierInformation',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import('src/pages/SelfAccrualManage/SupplierManage'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/selfAccrualManage/scanDetailQuery',
        name: 'scanDetailQuery', // 扫描明细查询
        routes: [
          {
            path: '/selfAccrualManage/scanDetailQuery/scanSortForkliftTask', // 叉车细分托盘扫描明细
            name: 'scanSortForkliftTask',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import(
                  'src/pages/SelfAccrualManage/ScanDetailQuery/ScanSortForkliftTask'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
    ],
  },
  {
    path: '/outStaffManage',
    name: 'outStaffManage', // 外包员工管理
    routes: [
      {
        path: '/outStaffManage/outer_manage',
        name: 'outer_manage',
        routes: [
          {
            path: '/outStaffManage/outer_manage/out_source',
            name: 'out_source', // 外包用户管理
            routes: [
              {
                path:
                  '/outStaffManage/outer_manage/out_source/out_source_staff',
                name: 'out_source_staff', // 外包员工管理
                authority: ['m'],
                component: _dvaDynamic({
                  app: window.g_app,
                  component: () =>
                    import('src/pages/OutStaffManage/UserManage/Staff'),
                  LoadingComponent: require('src/components/PageLoading/index')
                    .default,
                }),
                exact: true,
              },
              {
                path:
                  '/outStaffManage/outer_manage/out_source/out_source_staff_test',
                name: 'out_source_staff_test', // 外包员工管理
                authority: ['m'],
                component: _dvaDynamic({
                  app: window.g_app,
                  component: () =>
                    import('src/pages/OutStaffManage/UserManage/StaffTest'),
                  LoadingComponent: require('src/components/PageLoading/index')
                    .default,
                }),
                exact: true,
              },
            ],
          },
          {
            path: '/outStaffManage/outer_manage/work_code',
            name: 'work_code', // 动态二维码
            component: _dvaDynamic({
              app: window.g_app,
              component: () => import('src/pages/OutStaffManage/Qrcode'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/outStaffManage/outer_manage/daily_attendance',
            name: 'daily_attendance', // 外包打卡
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/DailyAttendance/index'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/outStaffManage/outer_manage/blackListManageSX',
            name: 'blackListManageSX', // 外包打卡
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/BlackListManageSX/index'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          // {
          //   path: '/outer_manage/user_manage',
          //   name: 'user_manage', // 外包用户管理
          //   authority: ['m'],
          //   component: _dvaDynamic({
          //     app: window.g_app,
          //     component: () => import('src/pages/UserManage/Staff'),
          //     LoadingComponent: require('src/components/PageLoading/index')
          //       .default,
          //   }),
          //   exact: true,
          // },
        ],
      },
      {
        path: '/outStaffManage/supplier_manage',
        name: 'supplier_manage',
        routes: [
          {
            path: '/outStaffManage/supplier_manage/supplier_maintain',
            name: 'supplier_maintain', // 供应商维护
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/SupplierManage'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/outStaffManage/employment_needs',
        name: 'employment_needs', // 用工需求管理
        routes: [
          {
            path: '/outStaffManage/employment_needs/needs_details',
            name: 'needs_details', // 用工需求监控
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/EmploymentNeeds'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/outStaffManage/out_attendance_manage',
        name: 'out_attendance_manage', // 外包考勤管理
        routes: [
          {
            path:
              '/outStaffManage/out_attendance_manage/deal_attendance_manage',
            name: 'deal_attendance_manage', // 考勤处理
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/OutAttendance/DealAttendance'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path:
              '/outStaffManage/out_attendance_manage/review_attendance_manage',
            name: 'review_attendance_manage', // 考勤审核
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import(
                  'src/pages/OutStaffManage/OutAttendance/ReviewAttendance'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/outStaffManage/out_attendance_manage/confirm_attendance',
            name: 'confirm_attendance', // 考勤确认
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import(
                  'src/pages/OutStaffManage/OutAttendance/ConfirmAttendance'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path: '/outStaffManage/out_attendance_manage/confirm_work_schedule',
            name: 'confirm_work_schedule', // 供应商考勤班次确认
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import(
                  'src/pages/OutStaffManage/OutAttendance/ConfirmSchedule'
                ),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path:
              '/outStaffManage/out_attendance_manage/attendance_target_manage',
            name: 'attendance_target_manage', // 考勤指标数据管理
            // authority: ['m'],
            routes: [
              {
                path:
                  '/outStaffManage/out_attendance_manage/attendance_target_manage/staff_stable_rate',
                name: 'staff_stable_rate', // 考勤处理
                // authority: ['m'],
                component: _dvaDynamic({
                  app: window.g_app,
                  component: () =>
                    import(
                      'src/pages/OutStaffManage/OutAttendance/TargetManage/StaffStableRate'
                    ),
                  LoadingComponent: require('src/components/PageLoading/index')
                    .default,
                }),
                exact: true,
              },
              {
                path:
                  '/outStaffManage/out_attendance_manage/attendance_target_manage/staff_attendance_rate',
                name: 'staff_attendance_rate', // 考勤处理
                // authority: ['m'],
                component: _dvaDynamic({
                  app: window.g_app,
                  component: () =>
                    import(
                      'src/pages/OutStaffManage/OutAttendance/TargetManage/StaffAttendanceRate'
                    ),
                  LoadingComponent: require('src/components/PageLoading/index')
                    .default,
                }),
                exact: true,
              },
            ],
          },
        ],
      },
      {
        path: '/outStaffManage/data_manage',
        name: 'data_manage', // 外包计重管理
        routes: [
          {
            path: '/outStaffManage/data_manage/count_weight',
            name: 'count_weight', // 计重供应商数据管理
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/DataManage/CountWeight'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/outStaffManage/suppliers_count_time',
        name: 'suppliers_count_time', // 外包计时管理
        routes: [
          {
            path:
              '/outStaffManage/suppliers_count_time/suppliers_count_time_manage',
            name: 'suppliers_count_time_manage', // 计时供应商数据管理
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/DataManage/CountTime'),

              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/outStaffManage/outer_business_test',
        name: 'outer_business_test', // 外包业务数据审核管理
        routes: [
          {
            path: '/outStaffManage/outer_business_test/business_auditing',
            name: 'business_auditing', // 业务审核
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/DataManage/BusinessReview'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/outStaffManage/should_pay',
        name: 'should_pay', // 外包应付计算管理
        routes: [
          {
            path: '/outStaffManage/should_pay/pay_accout',
            name: 'pay_accout', // 应付管理
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/DataManage/Payable'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/outStaffManage/suppliers_settle_review',
        name: 'suppliers_settle_review', // 供应商预提管理
        routes: [
          {
            path:
              '/outStaffManage/suppliers_settle_review/suppliers_withhold_manage',
            name: 'suppliers_withhold_manage', // 预提管理
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/DataManage/Withhold'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path:
              '/outStaffManage/suppliers_settle_review/suppliers_accruals_manage',
            name: 'suppliers_accruals_manage', // 预提管理
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/DataManage/AccrualsManage'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
          {
            path:
              '/outStaffManage/suppliers_settle_review/suppliers_settlement_manage',
            name: 'suppliers_settlement_manage', // 结算管理
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/DataManage/SettlementManage'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/outStaffManage/base_data_manage',
        name: 'base_data_manage', // 基础数据管理
        routes: [
          {
            path: '/outStaffManage/base_data_manage/power_setting',
            name: 'power_setting', // 供应商权限配置
            // authority: ['m'],
            component: _dvaDynamic({
              app: window.g_app,
              component: () =>
                import('src/pages/OutStaffManage/BaseDataManage/PowerSetting'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
    ],
  },
  {
    path: '/kuaihuoManage',
    name: 'kuaihuoManage', // 快活后台管理
    routes: [
      {
        path: '/kuaihuoManage/inforMannage',
        name: 'userRegisterInforMannage', // 用注册信息管理
        routes: [
          {
            path: '/kuaihuoManage/inforMannage/userInforManage',
            name: 'userInforManage',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () =>
                import('src/pages/KuaihuoManage/UserInforMannage'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
      {
        path: '/kuaihuoManage/individualUserInformation',
        name: 'individualUserInformation', // 个人用户画像
        routes: [
          {
            path: '/kuaihuoManage/individualUserInformation/userInformation',
            name: 'userInformation',
            component: _dvaDynamic({
              app: window.g_app,
              models: () => [],
              component: () => import('src/pages/KuaihuoManage/IndividualUser'),
              LoadingComponent: require('src/components/PageLoading/index')
                .default,
            }),
            exact: true,
          },
        ],
      },
    ],
  },
  // 菜单重构前
  // {
  //   path: '/transitPriceRules',
  //   name: 'transitPriceRules', // 中转计提配置
  //   routes: [
  //     {
  //       path: '/transitPriceRules/accountingPriceConfig',
  //       name: 'accountingPriceConfig', // 计提单价配置
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () =>
  //           import('src/pages/TransitAccrueConfig/AccruePriceConfig'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //     {
  //       path: '/transitPriceRules/fieldScheduleConfig',
  //       name: 'fieldScheduleConfig', // 场站人员排班配置
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () =>
  //           import('src/pages/TransitAccrueConfig/FieldScheduleConfig'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //     {
  //       path: '/transitPriceRules/cargo-warming', // 货量预警
  //       name: 'cargo-warming',
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () => import('src/pages/TransitAccrueConfig/CargoWarming'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //     {
  //       path: '/transitPriceRules/punchCardDays',
  //       name: 'punchCardDays', // 公司规定出勤天数
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () =>
  //           import('src/pages/TransitAccrueConfig/AttendanceDay'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //     {
  //       path: '/transitPriceRules/sortOutConfig',
  //       name: 'sortOutConfig', // 分拣包干制配置
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () =>
  //           import('src/pages/TransitAccrueConfig/SortOutConfig'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //     {
  //       path: '/transitPriceRules/cargoConvertConfig', // 货量折算系数配置
  //       name: 'cargoConvertConfig',
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () =>
  //           import('src/pages/TransitAccrueConfig/CargoConvertConfig'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //     {
  //       path: '/transitPriceRules/liftTruckIntegration', // 装卸一体计提线路配置
  //       name: 'liftTruckIntegration',
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () =>
  //           import('src/pages/TransitAccrueConfig/LiftTruckIntegration'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //   ],
  // },
  // {
  //   path: '/forkAccrue',
  //   name: 'forkAccrued', // 叉车计提
  //   routes: [
  //     {
  //       path: '/forkAccrue/forkAccruedSeach',
  //       name: 'forkAccruedSeach', // 场内人员-叉车分拣
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () => import('src/pages/ForkAccrue/ForkAccruedSeach'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //     {
  //       path: '/forkAccrue/sortForkTask',
  //       name: 'sortForkTask', // 场内任务-叉车分拣
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () => import('src/pages/ForkAccrue/SortForkTask'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //     {
  //       path: '/forkAccrue/flowCargoAccrue',
  //       name: 'flowCargoAccrue', // 流向货量-包干制分拣
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () => import('src/pages/ForkAccrue/FlowCargo'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //   ],
  // },
  // {
  //   path: '/truckAccrued',
  //   name: 'truckAccrued', // 装卸车计提
  //   routes: [
  //     // {
  //     //   path: '/truckAccrued/transitPerson', // 场内人员
  //     //   name: 'transitPerson',
  //     //   component: _dvaDynamic({
  //     //     app: window.g_app,
  //     //     models: () => [],
  //     //     component: () => import('src/pages/TruckAccrued/fieldPerson'),
  //     //     LoadingComponent: require('src/components/PageLoading/index').default,
  //     //   }),
  //     //   exact: true,
  //     // },
  //     {
  //       path: '/truckAccrued/transitTask', // 场内任务
  //       name: 'transitTask',
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () => import('src/pages/TruckAccrued/fieldTask'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //   ],
  // },
  // // 自有计提应付管理
  // {
  //   path: '/accruePayableManage',
  //   name: 'accruePayableManage',
  //   routes: [
  //     {
  //       path: '/accruePayableManage/accrueManage', // 计提应付
  //       name: 'accrueManage',
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () => import('src/pages/AccrueManage'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //   ],
  // },
  // // 供应商管理
  // {
  //   path: '/supplierManage',
  //   name: 'supplierManage',
  //   routes: [
  //     {
  //       path: '/supplierManage/supplierInformation', // 供应商信息查询
  //       name: 'supplierInformation',
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () => import('src/pages/SupplierManage'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //   ],
  // },
  // // 异常计提管理
  // {
  //   path: '/abnormalAccrueManage',
  //   name: 'abnormalAccrueManage',
  //   routes: [
  //     {
  //       path: '/abnormalAccrueManage/abnormalAccrueApply', // 异常计提申请
  //       name: 'abnormalAccrueApply',
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () =>
  //           import('src/pages/AbnormalAccrue/AbnormalAccrueApply'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //     {
  //       path: '/abnormalAccrueManage/liftTruckScan', // 装卸车重复扫描
  //       name: 'liftTruckScan',
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () =>
  //           import('src/pages/AbnormalAccrue/RepetitionLiftTruck'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //   ],
  // },
  // // 基础数据
  // {
  //   path: '/baseDate',
  //   name: 'baseDate',
  //   routes: [
  //     {
  //       path: '/baseDate/cargoWarming', // 货量预警
  //       name: 'cargoWarming',
  //       component: _dvaDynamic({
  //         app: window.g_app,
  //         models: () => [],
  //         component: () => import('src/pages/BaseDate/CargoWarming'),
  //         LoadingComponent: require('src/components/PageLoading/index').default,
  //       }),
  //       exact: true,
  //     },
  //   ],
  // },
];
