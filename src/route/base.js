import React from 'react';
import _dvaDynamic from 'dva/dynamic';

export default [
  {
    path: '/userMgr',
    name: 'so_userManage',
    routes: [
      {
        component: _dvaDynamic({
          component: () => import('src/pages/Base/404'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
      {
        component: () =>
          React.createElement(require('src/pages/Base/404').default, {
            pagesPath: 'src/pages',
            hasRoutesInConfig: true,
          }),
      },
    ],
  },
  {
    path: '/soUserMgr',
    name: 'userManages(basp)', // 用户管理（百源）
    routes: [
      {
        path: '/soUserMgr/assign',
        name: 'accountAssignManage', // 账号授权管理（角色）
        authority: ['m'],
        component: _dvaDynamic({
          app: window.g_app,
          component: () => import('ky-giant/es/BY/UserAssignMgr'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
      {
        path: '/soUserMgr/dataGroup',
        name: 'accountDataGroupManage', // 账号授权管理（数据组）
        component: _dvaDynamic({
          app: window.g_app,
          component: () => import('ky-giant/es/BY/UserDataGroupMgr'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
      {
        path: '/soUserMgr/leafAccount',
        name: 'leafAccountManage', // 子账号管理
        component: _dvaDynamic({
          app: window.g_app,
          component: () => import('ky-giant/es/BY/UserLeafAccountMgr'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
      {
        component: _dvaDynamic({
          component: () => import('src/pages/Base/404'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
    ],
  },
  {
    path: '/sysMgr',
    name: 'systemManage(basp)', // 系统管理（百源）
    routes: [
      {
        path: '/sysMgr/role',
        name: 'roleManage', // 角色管理
        // authority: ['m'],
        component: _dvaDynamic({
          app: window.g_app,
          component: () => import('ky-giant/es/BY/RoleMgr'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
      {
        path: '/sysMgr/resource',
        name: 'resourceManage', // 资源管理
        component: _dvaDynamic({
          app: window.g_app,
          component: () => import('ky-giant/es/BY/ResourceMgr'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
      {
        path: '/sysMgr/datagroup',
        name: 'datagroupManage', // 数据组管理
        component: _dvaDynamic({
          app: window.g_app,
          models: () => [],
          component: () => import('ky-giant/es/BY/DataGroupMgr'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
      {
        path: '/sysMgr/module',
        name: 'moduleManage', // 功能模块管理
        component: _dvaDynamic({
          app: window.g_app,
          models: () => [],
          component: () => import('ky-giant/es/BY/ModuleMgr'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
      {
        component: _dvaDynamic({
          component: () => import('src/pages/Base/404'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
    ],
  },
  {
    path: '/soBgMgr',
    name: 'BackAppManage', // 后台管理（百源）
    routes: [
      {
        path: '/soBgMgr/dict',
        name: 'dataDictionary', // 字典管理
        authority: ['m'],
        component: _dvaDynamic({
          app: window.g_app,
          component: () => import('ky-giant/es/BY/DictionaryMgr'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
      {
        component: _dvaDynamic({
          component: () => import('src/pages/Base/404'),
          LoadingComponent: require('src/components/PageLoading/index').default,
        }),
        exact: true,
      },
    ],
  },
];
