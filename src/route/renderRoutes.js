import React from 'react';
import { Switch, Route, Redirect } from 'react-router-dom';
import KeepAlive from 'react-activation';
// import { deflate } from 'zlib';

const RouteInstanceMap = {
  get(key) {
    // eslint-disable-next-line no-underscore-dangle
    return key._routeInternalComponent;
  },
  has(key) {
    // eslint-disable-next-line no-underscore-dangle
    return key._routeInternalComponent !== undefined;
  },
  set(key, value) {
    // eslint-disable-next-line no-underscore-dangle
    key._routeInternalComponent = value;
  },
};

// Support pass props from layout to child routes
const RouteWithProps = ({
  path,
  exact,
  strict,
  name,
  render,
  location,
  sensitive,
  ...rest
}) => (
  <Route
    path={path}
    exact={exact}
    strict={strict}
    location={location}
    sensitive={sensitive}
    render={props => render({ ...props, ...rest })}
  />
);

function withRoutes(route) {
  if (RouteInstanceMap.has(route)) {
    return RouteInstanceMap.get(route);
  }

  const { Routes } = route;
  let len = Routes.length - 1;
  let Component = args => {
    const { render, ...props } = args;
    return render(props);
  };
  while (len >= 0) {
    const AuthRoute = Routes[len];
    const OldComponent = Component;
    Component = props => (
      <AuthRoute {...props}>
        <OldComponent {...props} />
      </AuthRoute>
    );
    len -= 1;
  }

  const ret = args => {
    const { render, ...rest } = args;
    return (
      <RouteWithProps
        {...rest}
        render={props => <Component {...props} route={route} render={render} />}
      />
    );
  };
  RouteInstanceMap.set(route, ret);
  return ret;
}

function getAllModuleCodeMap(
  // eslint-disable-next-line no-unused-expressions
  // eslint-disable-next-line no-underscore-dangle
  menus = window.g_app._store.getState().global.menus,
  result = ['403', 'base'],
) {
  menus.map(menu => {
    result.push(menu.moduleCode);
    if (menu.childModules) {
      return getAllModuleCodeMap(menu.childModules, result);
    }
    return null;
  });
  // result.push(menu.moduleCode);
  return result;
}

export default function renderRoutes(
  routes,
  // extraProps = {},
  switchProps = {},
  pathsMapping = {},
) {
  if (routes) {
    const moduleCodes = getAllModuleCodeMap();

    const routeDOM = (
      <Switch {...switchProps}>
        {routes.map((route, i) => {
          if (moduleCodes.indexOf(route.name) > -1 || route.noAuth) {
            pathsMapping[route.name] = route.path;
            if (route.redirect) {
              return (
                <Redirect
                  key={route.key || i}
                  from={route.path}
                  to={route.redirect}
                  exact={route.exact}
                  strict={route.strict}
                />
              );
            }
            const RouteRoute = route.Routes
              ? withRoutes(route)
              : RouteWithProps;
            return (
              <RouteRoute
                key={route.name}
                path={route.path}
                exact={route.exact}
                strict={route.strict}
                sensitive={route.sensitive}
                render={props => {
                  const childRoutes = renderRoutes(
                    route.routes,
                    {},
                    pathsMapping,
                  ).routeDOM;
                  if (route.component) {
                    if (route.notKeepAlive) {
                      return (
                        <route.component {...props} route={route}>
                          {childRoutes}
                        </route.component>
                      );
                    }
                    return (
                      <KeepAlive
                        saveScrollPosition="screen"
                        name={route.name}
                        id={route.name}
                      >
                        <route.component {...props} route={route}>
                          {childRoutes}
                        </route.component>
                      </KeepAlive>
                    );
                  }
                  return childRoutes;
                }}
              />
            );
          }
        })}
      </Switch>
    );

    return { routeDOM, pathsMapping };
  }
  return {
    routeDOM: [],
    pathsMapping,
  };
}
