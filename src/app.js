import React from 'react';
import { Router } from 'dva/router';
import { connect } from 'dva';
import { AliveScope } from 'react-activation';
import initDVA from 'src/models/initDVA';
import { ConfigProvider } from 'antd';
import ZHCN from 'antd/es/locale-provider/zh_CN';
import CAS from '@ky/caslogin';
import * as Sentry from '@sentry/react';
import dayjs from 'dayjs';
import history from './history';
import initLocales from './locales/initLocales';
import './global.less';
import Config from './config';
import 'core-js';
import 'dayjs/locale/zh-cn';
import sensors from 'sa-sdk-javascript';

dayjs.locale('zh-cn');

if (process.env.DEPLOY_ENV !== 'dev') {
  Sentry.init({
    dsn: Config.dsn,
    integrations: [
      Sentry.browserTracingIntegration(),
      Sentry.captureConsoleIntegration({
        levels: ['error'],
      }),
      Sentry.httpClientIntegration({
        failedRequestStatusCodes: [403, 404, 500, 599],
      }),
    ],
    tracesSampleRate: 0.1,
    replaysSessionSampleRate: 0,
    replaysOnErrorSampleRate: 0.1,
  });
}

@connect(state => ({
  global: state.global,
}))
class App extends React.PureComponent {
  componentWillMount() {
    const cas = new CAS({
      appKey: Config.appKey,
      appSecret: Config.appSecret,
      ENV:
        process.env.DEPLOY_ENV === 'dev' || process.env.DEPLOY_ENV === 'sit'
          ? 'SIT'
          : 'PRD',
    });
    const { dispatch } = this.props;
    window.g_cas = cas;
    window.g_cas.login().then(() => {
      dispatch({
        type: 'global/fetchSystemInfo',
      });

      dispatch({
        type: 'global/getRolesAndMenus',
      });
      // 初始化SDK
      sensors.init({
        server_url: Config.SERVER_URL, // 上报地址
        show_log: false, // 是否打印日志
      });

      // 注册公共属性
      sensors.registerPage({
        platform_type: Config.PLATFORM_TYPE,
        platform_name: Config.PLATFORM_NAME,
        system_code: Config.SYSTEM_CODE,
        charge_sys_code: 'FOP-FCAMS-CORE',
      });
      // 挂载
      window.ky_sensors = sensors;
      sensors.login(sessionStorage.userid);
      sensors.setProfile({
        emp_id: sessionStorage.userid, // 必填，同login的传参保持一致
        // emp_name:'用户名', // 可选
        // zone_code:'网点代码', // 可选
        // city_code:'城市代码', // 可选
        // org_code:'组织代码', // 可选
      });
    });
  }

  componentDidMount() {
    window.AliveScope = this.aliveScope;
  }

  render() {
    const { routeDOM, pathsMapping } = require('./route');
    window.g_pathsMapping = pathsMapping;
    return (
      <Router history={history}>
        <AliveScope
          ref={ref => {
            this.aliveScope = ref;
          }}
        >
          {routeDOM}
        </AliveScope>
      </Router>
    );
  }
}

// 创建应用
const app = initDVA(history, {});
window.g_app = app; // DVA动态引入DVA需要DVA实例

// 注册视图

const render = () => {
  app.router(() => (
    <ConfigProvider locale={ZHCN}>
      <App />
    </ConfigProvider>
  ));
  app.start('#app');
};
// 启动应用
initLocales().then(() => {
  render();
});

if (module.hot) {
  module.hot.accept('./route', () => {
    render();
  });
}
