@import '~antd/lib/style/themes/default.less';
@import '~antd/dist/antd.less';
html,
body,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
  .ant-layout-sider {
    z-index: 1000;
  }

  .ant-layout-header {
    height: 56px !important;
    z-index: 999;
    // box-shadow: 0 1px 4px 0 rgba(0, 21, 41, 0.12);
    .antd-pro-components-global-header-index-trigger {
      color: #333;
    }
    .antd-pro-components-global-header-index-header {
      background: #fff;
      height: 56px !important;
      .antd-pro-components-global-header-index-right {
        color: #000;
      }
    }
  }
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.globalSpin {
  width: 100%;
  margin: 40px 0 !important;
}

ul,
ol {
  list-style: none;
}
.ky-feedback-pc-icon {
  z-index: 5 !important;
}
// .ant-drawer {
//   z-index: 10000;
// }
.antd-pro-layouts-basic-layout-content {
  background: #f5f5f5 !important;
  // .antd-pro-layouts-basic-layout-mainWrap {
  //   padding: 16px;
  // }
  .standard-table-content {
    padding: 16px;
  }
  margin: 20px;
  // padding-top: 64px !important;
  .ant-tabs-top > .ant-tabs-nav,
  .ant-tabs-bottom > .ant-tabs-nav,
  .ant-tabs-top > div > .ant-tabs-nav,
  .ant-tabs-bottom > div > .ant-tabs-nav {
    margin: 0 !important;
    background: #fff !important;
  }
  .antd-pro-layouts-basic-layout-tabs {
    background: #fff !important;
  }

  .ant-table-body {
    margin: 0;
  }

  .ant-table {
    width: 100%;
  }

  .ant-btn-link {
    height: 22px;
  }
  .ant-form-item {
    margin-bottom: 16px !important;
  }

  .self-form-label .ant-form-item-label {
    display: inline-block;
    overflow: hidden;
    line-height: 32px;
    white-space: nowrap;
    text-align: right;
    vertical-align: middle;
    > label {
      line-height: 18px;
      display: inline-block;
      padding: 0 4px;
      white-space: normal;
    }
  }
  @media (max-width: 575px) {
    .ant-form-item-label,
    .ant-form-item-control-wrapper {
      display: block;
      // width: 100%;

      width: auto !important;
    }
  }

  .ant-legacy-form-item-label {
    display: flex !important;
    align-items: center;
    justify-content: flex-end;
    padding-right: 5px;
  }
  .ant-legacy-form-item {
    margin-bottom: 24px;
  }

  .tableListOperator > div > button {
    margin-right: 8px;
  }
  .tableListOperator,
  .table-btn-group {
    padding-top: 4px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .table-btn-group-card {
    padding-top: 16px;
  }

  .table-btn-group {
    padding-top: 4px;
    padding-bottom: 16px;
  }

  .submitButtons {
    display: flex;
    align-items: center;
  }

  // .ant-table.ant-table-small thead > tr > th {
  //   background-color: #fff !important;
  // }

  .flex {
    display: flex;
    align-items: center;
    justify-content: center;
    div.ant-typography {
      margin-bottom: 0;
    }
  }

  .react-resizable {
    position: relative;
    background-clip: padding-box;
  }

  .react-resizable-handle {
    position: absolute;
    width: 10px;
    height: 100%;
    bottom: 0;
    right: -5px;
    cursor: col-resize;
    z-index: 1;
  }
  .tableListForm {
    padding-bottom: 0;
    > form {
      width: 100%;
      .ant-form-item {
        margin-bottom: 16px !important;
      }
    }
  }
}

.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
  font-weight: 600;
}
.ant-menu-vertical .ant-menu-item::after,
.ant-menu-vertical-left .ant-menu-item::after,
.ant-menu-vertical-right .ant-menu-item::after,
.ant-menu-inline .ant-menu-item::after {
  left: 0;
  right: auto !important;
}
.ant-menu-sub.ant-menu-inline > .ant-menu-item,
.ant-menu-sub.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
  line-height: 48px !important;
  height: 48px !important;
}
.ant-menu-vertical > .ant-menu-item,
.ant-menu-vertical-left > .ant-menu-item,
.ant-menu-vertical-right > .ant-menu-item,
.ant-menu-inline > .ant-menu-item,
.ant-menu-vertical > .ant-menu-submenu > .ant-menu-submenu-title,
.ant-menu-vertical-left > .ant-menu-submenu > .ant-menu-submenu-title,
.ant-menu-vertical-right > .ant-menu-submenu > .ant-menu-submenu-title,
.ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
  line-height: 48px !important;
  height: 48px !important;
}
.ant-tabs-card.ant-tabs-top > .ant-tabs-nav .ant-tabs-tab,
.ant-tabs-card.ant-tabs-top > div > .ant-tabs-nav .ant-tabs-tab {
  border-radius: 0 !important;
}
// 时间选择控件样式
.ant-picker-header {
  border-bottom: 1px solid #dddddd !important;
}
.ant-picker-body {
  border-bottom: 1px solid #dddddd !important;
}
.ant-picker-datetime-panel .ant-picker-time-panel {
  border-left: 1px solid #dddddd !important;
}
.ant-picker-time-panel-column > li {
  transform: translateY(28px);
}
ul.ant-picker-time-panel-column {
  border-right: 1px solid #dddddd;
  &:nth-child(1) {
    &::before {
      color: #999999;
      background: #f5f5f5;
      content: '时';
      position: sticky;
      display: block;
      width: 100%;
      top: 0;
      left: 0;
      // text-align: center;
      padding-left: 30px;
      z-index: 10;
    }
  }
  &:nth-child(2) {
    &::before {
      color: #999999;
      background: #f5f5f5;
      content: '分';
      position: sticky;
      display: block;
      width: 100%;
      top: 0;
      left: 0;
      // text-align: center;
      padding-left: 30px;
      z-index: 10;
    }
  }
  &:nth-child(3) {
    border-right: none;
    &::before {
      color: #999999;
      background: #f5f5f5;
      content: '秒';
      position: sticky;
      display: block;
      width: 100%;
      top: 0;
      left: 0;
      // text-align: center;
      padding-left: 30px;
      z-index: 10;
    }
  }
}
.ant-picker-dropdown .ant-picker-panel > .ant-picker-time-panel {
  padding-top: 0 !important;
}
.ant-picker-time-panel .ant-picker-content {
  border-bottom: 1px solid #dddddd;
}
.ant-picker-time-panel-column {
  width: 80px !important;
}
.ant-picker-time-panel-column
  > li.ant-picker-time-panel-cell
  .ant-picker-time-panel-cell-inner {
  padding: 0 0 0 30px !important;
}
.ant-picker-time-panel-column
  > li.ant-picker-time-panel-cell-selected
  .ant-picker-time-panel-cell-inner {
  background: none !important;
  color: @primary-color !important;
}
.ant-picker-cell-selected,
.ant-picker-cell-range-start,
.ant-picker-cell-range-end {
  .ant-picker-cell-inner {
    border-radius: 20px !important;
    border: none !important;
    &::before {
      display: none !important;
    }
  }
}
.ant-picker-cell-in-view.ant-picker-cell-in-range::before {
  background: #e6e7e8 !important;
}
.ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before,
.ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before {
  background: #e6e7e8 !important;
}
.table-list {
  padding: 16px;
}
.search-con {
  padding: 24px 24px 4px;
  background-color: #fff;
  width: 100%;
}
.btn-con {
  box-sizing: border-box;
  width: 100%;
  padding: 16px 13px 0px;
  background-color: #fff;
  margin-top: 20px;
}
.ant-btn > .ant-btn-loading-icon .anticon {
  padding-right: 0 !important;
}

.margin-bottom-12 {
  margin-bottom: 12px;
}

.empty-tip:empty::after {
  content: '--';
}

.label-colon {
  &.required::before {
    display: inline-block;
    width: 6px;
    margin-left: -6px;
    content: '*';
    color: var(--theme-primary);
  }
  &.no-indent::before {
    margin-right: 4px;
    margin-left: 0;
  }
  &::after {
    margin: 0 4px 0 2px;
    content: ':';
  }
}

.label-required {
  &::before {
    display: inline-block;
    width: 6px;
    margin-left: -6px;
    content: '*';
    color: var(--antd-wave-shadow-color);
  }

  &.no-indent::before {
    margin-right: 4px;
    margin-left: 0;
  }
}