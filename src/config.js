const config = {
  dev: {
    appKey: 'FOP-WEB-CMS-CAS',
    appSecret: 'SF@2Q1w4E3U#',
    systemKey: '5f2b9f505089b0e486610b16',
    dsn:
      'https://<EMAIL>/9',
    serverUrl: 'https://ubs.sf-express.com/sa?project=digital',
  },

  sit: {
    appKey: 'FOP-WEB-CMS-CAS',
    appSecret: 'SF@2Q1w4E3U#',
    systemKey: '5f2b9f505089b0e486610b16',
    dsn:
      'https://<EMAIL>/9',
    serverUrl: 'https://ubs.sf-express.com/sa?project=digital',
  },

  prod: {
    appKey: 'FOP-WEB-CMS-CAS',
    appSecret: 'SF@1Q2w3E4U#',
    systemKey: '5f2ba04fd746f20cd0d05428',
    dsn:
      'https://<EMAIL>/9',
    serverUrl: 'https://ubs.sf-express.com/sa?project=digital',
  },
};
const menuList = {
  'userManages(basp)': 'all',
  'systemManage(basp)': 'all',
  BackAppManage: 'all',
  'plan-tool': 'all',
  'time-commitment': 'all',
  transport_management: 'all',
  'plan-management': 'all',
  transport_demand: 'all',
  transport_base_config: 'all',
  transport_resource_manage: 'all',
  transitPriceRules: 'all', // 计提单价配置
  truckAccrued: 'all',
  forkAccrued: 'all', // 叉车计提
  attendanceAccrued: 'all',
  attendanceDays: 'all',
  attendanceDay: 'all',
  accruePayableManage: 'all',
  supplierManage: 'all',
  abnormalAccrueManage: 'all',
  SortForkTask: 'all',
  baseDate: 'all',
  selfAccrualManage: 'all',
  liftTruckAccrual: 'all',
  sortForkliftAccrual: 'all',
  abnormalAccrualManage: 'all',
  transitAccrualConfig: 'all',
  outStaffManage: 'all',
  outer_manage: 'all',
  supplier_manage: 'all',
  out_attendance_manage: 'all',
  data_manage: 'all',
  outer_business_test: 'all',
  should_pay: 'all',
  base_data_manage: 'all',
  kuaihuoManage: 'all',
  individualUserInformation: 'all',
  userRegisterInforMannage: 'all',
};

// eslint-disable-next-line func-names
const filterMenus = function(menu, menuFilter) {
  return menu
    .map(item => {
      if (!menuFilter[item.moduleCode]) {
        return false;
      }
      // eslint-disable-next-line eqeqeq
      if (menuFilter[item.moduleCode] == 'all') {
        return item;
      }
      return Object.assign({}, item, {
        childModules: filterMenus(
          item.childModules,
          menuFilter[item.moduleCode],
        ),
      });
    })
    .filter(item => item);
};

// eslint-disable-next-line func-names
const getMenus = function(menu) {
  return filterMenus(menu, menuList);
};
const comonConfig = {
  basePath: 'webs/fcams', // 编译产生的目录，路由地址 /cms/xx/xx ，及上传到nginx的cms目录等
};
// 关键在这
process.env.APP_KEY = config[process.env.DEPLOY_ENV].appKey;
process.env.APP_SECRET = config[process.env.DEPLOY_ENV].appSecret;
process.env.SYSTEM_KEY = config[process.env.DEPLOY_ENV].systemKey;
process.env.BASE_PATH = 'webs/fcams';

// 关键在这

const SENSORS_CONFIG = {
  SERVER_URL: config[process.env.DEPLOY_ENV].serverUrl,
  SYSTEM_CODE: 'fop-web-ica-fcams', // 填写Git项目名
  PLATFORM_TYPE: 'Web', // 填写类型 eg: H5/Web
  PLATFORM_NAME: '大件计提系统', // eg:丰声微服务，则为SFIM
  CHARGE_SYS_CODE: 'FOP-FCAMS-CORE', // 后台服务系统编码
};

export default {
  ...SENSORS_CONFIG,
  getMenus,
  ...config[process.env.DEPLOY_ENV],
  ...comonConfig,
};
