import intl from 'react-intl-universal';

const locales = {
  'zh-CN': require('./zh-CN.js').default,
  'en-US': require('./en-US.js').default,
};

export default function initLocales() {
  // require.ensure(['./zh-CN.js', './en-US.js'], () => {

  // });
  return new Promise((reolve, reject) => {
    intl
      .init({
        currentLocale: sessionStorage.getItem('CUR_LOCAL') || 'zh-CN', // TODO: determine locale here
        locales,
      })
      .then(() => {
        reolve('OK');
      })
      .catch(err => {
        reject(err);
      });
  });
}
