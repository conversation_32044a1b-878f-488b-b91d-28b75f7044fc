// import { routerRedux } from 'dva/router';
import {
  queryNotices,
  querySystem,
  queryRoles,
  queryMenus,
  // clearDeptCodeCache,
  getAllocationAreaSF, // 获取顺丰的分拨区
  getAllocationAreaSX, // 获取顺心的分拨区
  getEmpDeptDetailInfo, // 获取登录人的详细信息
} from 'src/services/api';
import Config from '../config';

/**
 * 给菜单加上对应的moduleCode关系字符串，用作主菜单是否展开的逻辑
 * @param {*} menus
 * @param {*} parents
 */
function setExpandedKeys(menus, parents = '') {
  menus.forEach(menu => {
    const parentArray = parents ? parents.split(',') : [];
    parentArray.push(menu.moduleCode);
    menu.openKeys = parentArray;
    if (menu.childModules) {
      const newParents = parents ? parents.split(',') : [];
      newParents.push(menu.moduleCode);
      setExpandedKeys(menu.childModules, newParents.join(','));
    }
  });
}

export default {
  namespace: 'global',
  state: {
    collapsed: false,
    roles: [],
    menus: [],
    userInfo: {
      deptCode: '',
      empCode: '',
      orgCode: '',
    },
    system: {
      systemKey: '',
      systemId: 0,
      systemCode: 'EOS-FOP-FNP',
      systemName: '大件计提系统',
      // homeAddress: 'fop.sit.sf-express.com/fnp/',
      systemDesc: '大件计提系统',
      systemType: 1,
      isSwitchRole: 1,
    },
    notices: [],
    listKey: 'list', // 下载列表的key
    readyStatus: false,
    areaListSX: [],
    areaListSF: [],
  },

  effects: {
    logout() {
      setTimeout(() => {
        window.g_cas.logout();
      }, 200);
    },
    *getRolesAndMenus(_, { call, put }) {
      if (sessionStorage.userid) {
        const dataRes = yield call(queryRoles);
        if (dataRes.succ === 'ok') {
          yield put({
            type: 'updateRoles',
            data: dataRes.result,
          });
          const getRoleId = roles => {
            const roleIdCache = sessionStorage.getItem('roleId');
            const role = roles.find(r => r.roleId === roleIdCache);
            if (role) {
              return roleIdCache;
            }
            if (roles[0]) {
              sessionStorage.setItem('roleId', roles[0].roleId);
              return roles.length ? roles[0].roleId : '';
            }
          };
          const role = getRoleId(dataRes.result);
          const openLink = munus =>
            munus.map(item => {
              if (
                item.childModules &&
                item.childModules.some(child => child.moduleName) &&
                item.hidden === 'N'
              ) {
                openLink(item.childModules);
              }
              if (item.moduleCode === 'employMaintain' && item.hidden === 'N') {
                item.openLink = '/webs/sdm-core/employ/approve';
              }
              return item;
            });
          // console.log(openLink, 'openLink');
          // 当前登录人的角色编码
          const roleIdCacheCode = sessionStorage.getItem('roleId');
          const logRoleCode = dataRes.result.find(
            r => r.roleId === roleIdCacheCode,
          );
          yield put({
            type: 'logRoleCode',
            data: logRoleCode,
          });
          if (role) {
            sessionStorage.setItem('roleId', role);
            yield put({
              type: 'updateReadyStatus',
              data: true,
            });

            // 获取分拨区 SF
            const allocationAreaSF = yield call(getAllocationAreaSF);
            if (allocationAreaSF.success && allocationAreaSF.obj) {
              const allocationAreaSFList = allocationAreaSF.obj.list.map(
                item => ({
                  label: `${item.deptCode}  ${item.deptName}`,
                  value: item.deptCode,
                  labelValue: item.deptName,
                }),
              );
              yield put({
                type: 'updateAreaSF',
                data: allocationAreaSFList,
              });
            }

            // 获取分拨区 SX
            const allocationAreaSX = yield call(getAllocationAreaSX);
            if (
              allocationAreaSX.success &&
              allocationAreaSX.obj &&
              allocationAreaSX.obj.list !== null
            ) {
              const allocationAreaSXList = allocationAreaSX.obj.list.map(
                item => ({
                  label: `${item.deptCode}  ${item.deptName}`,
                  value: item.deptCode,
                }),
              );
              yield put({
                type: 'updateAreaSX',
                data: allocationAreaSXList,
              });
            }
            // eslint-disable-next-line no-new
            new window.KYFeedbackPC({
              appCode: 'FOP-FCAMS-CORE',
              userId: sessionStorage.userid, // 员工工号
              onOpen: () => {},
              onSuccess: () => {},
              onClose: () => {},
            });
            const menusRes = yield call(queryMenus, getRoleId(dataRes.result));
            setExpandedKeys(menusRes.result);
            const { getMenus } = Config;
            if (menusRes.succ === 'ok') {
              openLink(menusRes.result);

              const res = getMenus(menusRes.result);
              yield put({
                type: 'updateMenus',
                data: res,
              });
            } else {
              // TODO 获取菜单失败
            }
            // 获取登录人详情;
            const userDetail = yield call(
              getEmpDeptDetailInfo,
              sessionStorage.userid,
              // 'sx18080191',
            );
            if (userDetail.success) {
              const locaUserId = localStorage.getItem('userid');
              if (locaUserId !== sessionStorage.userid) {
                localStorage.setItem('userid', sessionStorage.userid);
                localStorage.setItem('zoneCode', userDetail.obj.deptCode);
                window.location.reload();
              }
              sessionStorage.setItem(
                'userAttr',
                JSON.stringify(userDetail.obj),
              );
              yield put({
                type: 'updateUserInfo',
                data: userDetail.obj,
              });
            }
          } else {
            // yield put(routerRedux.push('/exception/403'));
            // TODO 获取角色失败
          }
        } else {
          // yield put(routerRedux.push('/exception/403'));
        }
      }
    },
    *fetchRoles(_, { call, put }) {
      const data = yield call(queryRoles);
      if (data.succ === 'ok') {
        yield put({
          type: 'updateRoles',
          data: data.result,
        });
      } else {
        // todo
      }
    },

    *fetchSystemInfo(_, { call, put }) {
      if (sessionStorage.userid) {
        const data = yield call(querySystem);
        if (data.succ === 'ok') {
          yield put({
            type: 'updateSystem',
            data: data.result,
          });
        } else {
          // todo
        }
      }
    },
    *fetchNotices(_, { call, put, select }) {
      const data = yield call(queryNotices);
      yield put({
        type: 'saveNotices',
        payload: data,
      });
      const unreadCount = yield select(
        state => state.global.notices.filter(item => !item.read).length,
      );
      yield put({
        type: 'user/changeNotifyCount',
        payload: {
          totalCount: data.length,
          unreadCount,
        },
      });
    },
  },

  reducers: {
    updateListKey(state, { data }) {
      return {
        ...state,
        listKey: data,
      };
    },
    updateRoles(state, { data }) {
      return {
        ...state,
        roles: data,
      };
    },
    logRoleCode(state, { data }) {
      return {
        ...state,
        logRoleCode: data,
      };
    },
    updateUserInfo(state, { data }) {
      return {
        ...state,
        userInfo: data,
      };
    },
    updateAreaSF(state, { data }) {
      return {
        ...state,
        areaListSF: data,
      };
    },
    updateAreaSX(state, { data }) {
      return {
        ...state,
        areaListSX: data,
      };
    },
    updateReadyStatus(state, { data }) {
      return {
        ...state,
        readyStatus: data,
      };
    },
    updateMenus(state, { data }) {
      return {
        ...state,
        menus: JSON.parse(JSON.stringify(data)),
      };
    },
    updateSystem(state, { data }) {
      const { systemCode, systemName } = data;
      sessionStorage.setItem('systemCode', systemCode);
      sessionStorage.setItem('systemName', systemName);
      return {
        ...state,
        system: data,
      };
    },
    changeLayoutCollapsed(state, { payload }) {
      return {
        ...state,
        collapsed: payload,
      };
    },
  },

  subscriptions: {
    // setup({ history }) {
    setup() {
      // Subscribe history(url) change, trigger `load` action if pathname is `/`
      // return history.listen(({ pathname, search }) => {
      //   if (typeof window.ga !== 'undefined') {
      //     window.ga('send', 'pageview', pathname + search);
      //   }
      // });
    },
  },
};
