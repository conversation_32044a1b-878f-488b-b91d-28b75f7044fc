import { routerRedux } from 'dva/router';

import history from '../history';
import sessionStorage from '../utils/storage/sessionStorage';

export default {
  namespace: 'tabHistory',
  state: sessionStorage.get('tabHistory') || {
    routes: [
      {
        moduleName: '首页',
        moduleCode: 'base',
        moduleIcon: 'home',
        sort: 1,
      },
    ],
    activeModuleCode: 'base',
  },

  effects: {
    *custom({ data }, { call, put, select }) {
      yield put({ type: 'push', data });
      yield put(routerRedux.push(data.path));
    },
    *remove({ data }, { put, select }) {
      const state = yield select(_state => _state.tabHistory);

      const newRoutes = state.routes.slice();
      const targetIndex = newRoutes.findIndex(
        ({ moduleCode }) => moduleCode === data,
      );
      let prevIndex = 0;
      if (targetIndex > 0) {
        prevIndex = targetIndex - 1;
      }
      // 按需加载
      const { pathsMapping } = require('../route/index');
      const prevModuleCode = newRoutes[prevIndex].moduleCode;
      // 如果 pathsMapping 找不到，直接取 prevModuleCode
      const prevPath = pathsMapping[prevModuleCode];
      newRoutes.splice(targetIndex, 1);
      setTimeout(() => {
        window.AliveScope.drop(data);
      }, 100);
      yield put(routerRedux.push(prevPath || prevModuleCode));
      if (!prevPath) {
        yield put({ type: 'setActive', data: prevModuleCode });
      }
      yield put({ type: 'updateRoutes', data: newRoutes });
    },
  },

  reducers: {
    updateRoutes(state, { data }) {
      const newState = {
        ...state,
        routes: data,
      };
      sessionStorage.set('tabHistory', newState);
      return newState;
    },
    push(state, { data }) {
      // 把 query 参数也考虑进去
      // TODO ifmatch，params，将param替换modulename
      const { location } = history;
      const queryString = location.search;
      data.queryString = queryString;
      const newRoutes = state.routes.slice();
      newRoutes.push(data);
      const newState = {
        ...state,
        routes: newRoutes,
      };
      sessionStorage.set('tabHistory', newState);

      return newState;
    },
    setActive(state, { data }) {
      const newState = {
        ...state,
        activeModuleCode: data,
      };
      sessionStorage.set('tabHistory', newState);
      return newState;
    },
  },

  subscriptions: {
    setup({ history }) {
      // Subscribe history(url) change, trigger `load` action if pathname is `/`
      // return history.listen(({ pathname, search }) => {
      //   if (typeof window.ga !== 'undefined') {
      //     window.ga('send', 'pageview', pathname + search);
      //   }
      // });
    },
  },
};
