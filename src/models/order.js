/**
 * 整车开单状态管理模型
 */
import sessionStorage from 'src/utils/storage/sessionStorage';
import { getDeptTreeByDeptCode } from 'src/services/billApi';

export default {
  namespace: 'order',

  state: {
    noDepts: false,
    dept: {
      code: '',
      name: '',
    },
    deptTree: '',
  },

  effects: {
    *getDeptTreeByDeptCode({ data }, { put, call }) {
      const res = yield call(getDeptTreeByDeptCode, data.code);
      yield put({
        type: 'updateDept',
        data: {
          dept: data,
          deptTree: res.obj ? res.obj : '',
        },
      });
    },
  },

  reducers: {
    updateDept(state, action) {
      sessionStorage.set('dept', action.data.dept);
      sessionStorage.set('deptTree', action.data.deptTree);
      return {
        ...state,
        ...action.data,
      };
    },
    noDepts(state) {
      return {
        ...state,
        noDepts: true,
      };
    },
  },
};
