import dva from 'dva';
import createLoading from 'dva-loading';
import Global from './global';
import TabHistory from './tabHistory';
// import Order from './order';

const models = [
  { namespace: 'global', model: Global },
  { namespace: 'tabHistory', model: TabHistory },
  // { namespace: 'order', model: Order },
  // { namespace: 'menu', model: require('./menu').default },
  { namespace: 'setting', model: require('./setting').default },
  // { namespace: 'user', model: require('./user').default },
];

export default (history, runTimeconfig = {}) => {
  const app = dva({
    history,
    ...runTimeconfig,
  });
  app.use(createLoading());

  models.map(({ namespace, model }) =>
    app.model({
      namespace,
      ...model,
    }),
  );
  return app;
};
