import React, {
  useState,
  PureComponent,
  useEffect,
  Fragment,
  useRef,
} from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Input, Button, Select, DatePicker } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import {
  rangePickerColStyle,
  rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
} from 'ky-giant';
import { withRouter } from 'react-router-dom';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import StandardTable from 'src/components/StandardTable';
// import AuthButton from '@/components/AuthButton';
import authDecorator from 'src/components/AuthDecorator';
import ExportButton from 'src/components/ExportButton';
import EditModal from './Components/EditModal';

import {
  searchRecordList, // 查询审核记录列表
  searchRecordDetail, // 查询审核记录详情
} from './services/api';

import './index.scss';
const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;
const rowKey = 'id';

let inputParam = {};

const Page = ({ systemRole, moduleCode }) => {
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(true);
  const [form] = Form.useForm();
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  const rolesRef = useRef(null); // 角色信息
  // const [selectedRows, setSelectedRows] = useState([]);
  const [int, setInt] = useState({});
  const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const columns = [
    {
      title: '用户ID',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userId',
      render: (value, row) => row.firmUser.userId,
    },
    {
      title: '姓名',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'name',
      render: (value, row) => row.firmUser.name,
    },
    // {
    //   title: '手机',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 150,
    //   dataIndex: 'phone',
    //   render: (value, row) => row.firmUser.phone,
    // },
    {
      title: '用户类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'source',
      render: text => {
        switch (text) {
          case 1:
            return '企业用户';
          case 2:
            return '个人用户';
          default:
            return '';
        }
      },
    },
    {
      title: '企业名称',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'firmName',
    },
    {
      title: '企业编号',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'firmNo',
    },
    {
      title: '营业执照号',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'licenseNo',
    },
    {
      title: '审核状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'auditStatus',
      render: text => {
        switch (text) {
          case 0:
            return '审核中';
          case 1:
            return '审核成功';
          case 2:
            return '审核失败';
          default:
            return '';
        }
      },
    },
    {
      title: '账号状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'status',
      render: (value, row) => {
        switch (row.firmUser.status) {
          case 1:
            return '启用';
          case 2:
            return '停用';
          default:
            return '';
        }
      },
    },
    {
      title: '注册申请时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'submitTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '注册通过时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'approvalTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },

    {
      title: '异常原因',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'failReason',
    },
    {
      title: '审核人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'auditor',
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      ellipsis: true,
      width: 120,
      dataIndex: 'operation',
      render: (value, row) => (
        <Fragment>
          {rolesRef.current && row.auditStatus !== 2 && (
            <Button
              type="link"
              size="small"
              onClick={e => {
                edit(e, row);
              }}
            >
              编辑
            </Button>
          )}
          {rolesRef.current && row.auditStatus !== 1 && (
            <Button
              type="link"
              size="small"
              onClick={e => {
                check(e, row);
              }}
            >
              去审核
            </Button>
          )}
        </Fragment>
      ),
    },
  ];
  // 查询参数的获取
  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    const data = cloneDeep(params);
    if (data.time && data.time.length === 2) {
      data.beginSubmitTime = moment(data.time[0]).valueOf();
      data.endSubmitTime = moment(data.time[1]).valueOf();
    }
    delete data.time;
    inputParam = data;
    refresh({ pageNum, pageSize });
    return {
      ...inputParam,
    };
  };
  // 页面刷新的请求
  const refresh = (pagination = {}) => {
    setLoading(true);
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    // getQueryParams();
    const param = { ...inputParam, pageNum, pageSize };
    searchRecordList(param).then(res => {
      const { success: resSuccess, obj } = res;
      if (resSuccess) {
        const { list, pageNum: current, total } = obj;
        setTotalNum(total);
        // setSelectedRows([]);
        setLoading(false);
        setDatas({
          pagination: {
            total,
            current,
            pageSize,
          },
          list,
        });
      } else {
        // setSelectedRows([]);
        setLoading(false);
        setDatas({
          pagination: {
            total: 0,
            current: 1,
            pageSize: 10,
          },
          list: [],
        });
      }
    });
  };
  // 页面刷新的第一次请求
  useEffect(() => {
    getQueryParams();
  }, []);

  // 权限查询
  useEffect(() => {
    getCurrentRoleCode();
  }, []);
  const getCurrentRoleCode = () => {
    const roleId = sessionStorage.getItem('roleId');
    if (roleId && systemRole && systemRole.length) {
      const role = systemRole.find(item => item.roleId === roleId);
      if (role) {
        // 百源和科技接口人权限
        if (role.roleCode === '99999999' || role.roleCode === 'baspAdmin') {
          rolesRef.current = true;
        } else {
          refresh();
        }
      }
    }
  };

  // const handleSelectRows = rows => {
  //   setSelectedRows(rows);
  // };

  const handleStandardTableChange = (
    pagination, // filtersArg, sorter
  ) => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // const getDownloadIdList = () => {
  //   const params = getQueryParams();
  //   return {
  //     ...params,
  //   };
  // };
  // 编辑
  const edit = (e, value) => {
    e.stopPropagation();
    searchRecordDetail(value).then(res => {
      if (res.success) {
        setInt(res.obj);
        setEditingTarget(true); // 编辑
        setEditModalVisible(true);
      }
    });
  };
  // 审核
  const check = (e, value) => {
    e.stopPropagation();
    searchRecordDetail(value).then(res => {
      if (res.success) {
        // if (res.obj.auditStatus !== 1) {
        setInt({
          ...res.obj,
        });
        // setTimeout(() => {
        setEditingTarget(false); // 编辑
        setEditModalVisible(true);
        // }, 500);
      }
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={{ source: 1 }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="姓名" name="name">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="企业名称" name="firmName">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="企业平台ID" name="firmNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="审核状态" name="auditStatus">
                <Select
                  allowClear
                  options={[
                    { label: '审核中', value: 0 },
                    { label: '审核成功', value: 1 },
                    { label: '审核失败', value: 2 },
                  ]}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="状态" name="status">
                <Select
                  allowClear
                  options={[
                    { label: '启用', value: 1 },
                    { label: '停用', value: 2 },
                  ]}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="用户类型" name="source">
                <Select
                  disabled
                  allowClear
                  options={[
                    { label: '企业用户', value: 1 },
                    { label: '个人用户', value: 2 },
                  ]}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...rangePickerColStyle}>
              <FormItem label="注册时间" name="time" {...rangePickerLayout}>
                <RangePicker showTime allowClear style={{ width: '100%' }} />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <ExportButton
            moduleCode={moduleCode}
            code="userInfor-export"
            text="导出"
            icon={<ExportOutlined />}
            options={{
              total: totalNum,
              filename: '企业用户信息表.xlsx',
              requstParams: [
                `/fpmFirmServices/auditRecord/export`,
                {
                  method: 'POST',
                  body: { ...inputParam },
                },
              ],
            }}
          />
        </div>
      </div>
      {/* <div className="table-btn-group">
        {editModalVisible && (
          <EditModal
            editing={editingTarget}
            visible={editModalVisible}
            value={int}
            onCancel={() => {
              setEditModalVisible(false);
              setEditingTarget(null);
              // setSelectedRows([]);
            }}
            onOk={() => {
              setEditModalVisible(false);
              setEditingTarget(null);
              // setSelectedRows([]);

              refresh();
            }}
            width={850}
          />
        )}
        <div className="tableListOperator">
          <div>
            <ExportButton
              moduleCode={moduleCode}
              code="userInfor-export"
              text="导出"
              icon={<ExportOutlined />}
              options={{
                total: totalNum,
                filename: '企业用户信息表.xlsx',
                requstParams: [
                  `/fpmFirmServices/auditRecord/export`,
                  {
                    method: 'POST',
                    body: { ...inputParam },
                  },
                ],
              }}
            />
          </div>
        </div>
        <div>
          <span className="submitButtons">
            <Button icon={<ReloadOutlined />} type="primary" htmlType="submi">
              查询
            </Button>
            <Button
              icon={<RollbackOutlined />}
              style={{ marginLeft: 8 }}
              onClick={() => {
                form.resetFields();
                inputParam = {};
                refresh();
              }}
            >
              重置
            </Button>
          </span>
        </div>
      </div> */}
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey={rowKey}
        // pagination={false}
        // title={tableTitle}
        showSelection={false}
        loading={loading}
        data={datas}
        columns={columns}
        // onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {editModalVisible && (
        <EditModal
          editing={editingTarget}
          visible={editModalVisible}
          value={int}
          onCancel={() => {
            setEditModalVisible(false);
            setEditingTarget(null);
            // setSelectedRows([]);
          }}
          onOk={() => {
            setEditModalVisible(false);
            setEditingTarget(null);
            // setSelectedRows([]);

            refresh();
          }}
          width={850}
        />
      )}
    </div>
  );
};

@connect(state => ({
  systemRole: state.global.roles,
}))
@authDecorator
class Container extends PureComponent {
  render() {
    const { dispatch, history, userInfo, systemRole, moduleCode } = this.props;
    return (
      <Page
        systemRole={systemRole}
        moduleCode={moduleCode}
        dispatch={dispatch}
        history={history}
        userInfo={userInfo}
      />
    );
  }
}

export default withRouter(Container);
