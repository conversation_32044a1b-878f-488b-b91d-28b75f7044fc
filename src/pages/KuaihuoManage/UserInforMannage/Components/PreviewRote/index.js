import React, { useState } from 'react';
import { Mo<PERSON>, Spin } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

const PreviewRote = props => {
  const { previewVisible, handleCancel, previewImage, title } = props;
  const [deg, setDeg] = useState(90); // 图片地址

  // 点击图片旋转
  const handleRote = () => {
    setDeg(deg + 90);
    document.getElementById(
      'preview-img',
    ).style.transform = `rotate(${deg}deg)`;
    document.getElementById(
      'preview-img',
    ).style.WebkitTransform = `rotate(${deg}deg)`;
    document.getElementById(
      'preview-img',
    ).style.MozTransform = `rotate(${deg}deg)`;
    document.getElementById(
      'preview-img',
    ).style.OTransform = `rotate(${deg}deg)`;
    document.getElementById(
      'preview-img',
    ).style.MsTransform = `rotate(${deg}deg)`;
  };

  return (
    <Modal
      mask={false}
      style={{ left: 400 }}
      visible={previewVisible}
      footer={null}
      onCancel={handleCancel}
      className="view-modal"
      title={
        <div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              paddingRight: '14px',
            }}
          >
            <span>{title}</span>
            <Spin
              indicator={
                <ReloadOutlined
                  className="common-icon-style"
                  style={{
                    color: '#bfbfbf',
                    marginLeft: '5px',
                    marginRight: '20px',
                    cursor: 'pointer',
                    float: 'left',
                    Zindex: 999,
                  }}
                  onClick={handleRote}
                />
              }
              size="middle"
            />
          </div>
        </div>
      }
    >
      <div className="view-content">
        <img
          alt="example"
          style={{ width: '100%' }}
          src={previewImage}
          id="preview-img"
        />
      </div>
    </Modal>
  );
};

export default PreviewRote;
