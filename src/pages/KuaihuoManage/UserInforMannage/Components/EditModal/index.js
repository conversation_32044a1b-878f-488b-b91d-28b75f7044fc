import React, { useState, useEffect, useMemo } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Form,
  Input,
  Button,
  Modal,
  Select,
  Upload,
  message,
  notification,
  DatePicker,
} from 'antd';
import { success, error } from 'src/utils/utils';
import Config from 'src/config';
import { SmileOutlined, UploadOutlined } from '@ant-design/icons';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import requests from 'dva/fetch';
import PreviewRote from '../PreviewRote';

import {
  submitAuditRecord, // 提交审核结果
  update, // 编辑完成更新数据
} from '../../services/api';
import {
  failReasonList,
  auditStatusList,
  sourceList,
  statusList,
} from '../status';
// import './index.scss';
const { Item: FormItem } = Form;

// 去审核 && 编辑的弹出框
const EditModal = props => {
  const [form] = Form.useForm();
  const { onOk, editing, body, value, ...rest } = props;
  const [isShowModal, setIsShowModal] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false); // 图片预览Modal
  const [previewImage, setPreviewImage] = useState(''); // 图片预览地址
  const [headerValue, setHeaderValue] = useState(false);
  const [fileList, setFileList] = useState();
  const [fileList2, setFileList2] = useState();
  //   const [defaultListA, setDefaultListA] = useState([]);
  //   const [defaultListB, setDefaultListB] = useState([]);

  const tem = cloneDeep(value);
  // 图片的回显处理
  const pictureShow = (datas, keyName) => {
    const requstParams = [
      `/fpmFirmServices/image/getPic/${datas}`,
      {
        method: 'GET',
      },
    ];
    requstParams[1].credentials = 'include';
    requstParams[1].body = body
      ? JSON.stringify(
          typeof body === 'function' ? body() : requstParams[1].body,
        )
      : undefined;
    requstParams[1].headers = {
      userId: sessionStorage.userid,
      'sgs-userid': sessionStorage.userid,
      systemKey: Config.systemKey,
      'gw-bdus-rid': sessionStorage.getItem('roleId') || '',
      'Content-Type': 'application/json; charset=utf-8',
    };
    requests(...requstParams).then(res => {
      if (res.status !== 200) {
        notification.open({
          message: `图片获取失败`,
          description: `状态码：${res.status},statusText：${res.statusText}`,
          icon: <SmileOutlined style={{ color: '#108ee9' }} />,
        });
        return;
      }
      res.blob().then(blob => {
        const blobUrl = window.URL.createObjectURL(blob);
        if (keyName === 'licensePicName') {
          form.setFieldsValue({
            licensePicName: [
              {
                uid: '-1',
                name: '营业执照.png',
                status: 'done',
                url: blobUrl,
                response: { obj: tem.licensePicName },
              },
            ],
          });
        }
        if (keyName === 'incuCertPicName') {
          form.setFieldsValue({
            incuCertPicName: [
              {
                uid: '-1',
                name: '在职证明.png',
                status: 'done',
                url: blobUrl,
                response: { obj: tem.incuCertPicName },
              },
            ],
          });
        }
      });
    });
  };

  useEffect(() => {
    if (tem.licensePicName != null) {
      pictureShow(tem.licensePicName, 'licensePicName');
    } else {
      form.setFieldsValue({ licensePicName: [] });
      notification.info({
        message: '无营业执照',
      });
    }
    if (tem.incuCertPicName != null) {
      pictureShow(tem.incuCertPicName, 'incuCertPicName');
    } else {
      form.setFieldsValue({ incuCertPicName: [] });
      notification.info({
        message: '无证件照',
      });
    }
  }, []);

  tem.status = tem.firmUser.status ? tem.firmUser.status.toString() : '';
  if (tem) {
    tem.userId = tem.firmUser.userId;
    tem.name = tem.firmUser.name;
    // tem.phone = tem.firmUser.phone;
  } else {
    message.info('数据未获取');
    return false;
  }
  // 编辑框的时间戳处理
  if (tem && tem.submitTime) {
    tem.submitTime = moment(tem.submitTime);
  }
  const formValMemo = useMemo(
    () => ({
      ...tem,
      licensePicName: [
        {
          uid: '-1',
          name: '营业执照.png',
          status: 'done',
          url: '',
          response: { obj: tem.licensePicName },
        },
      ],
      incuCertPicName: [
        {
          uid: '-1',
          name: '在职证明.png',
          status: 'done',
          url: '',
          response: { obj: tem.incuCertPicName },
        },
      ],
    }),
    [tem],
  );

  // 提交
  const submits = async () => {
    const values = await form.validateFields();
    // if (values.auditStatus !== 1) {
    values.licenseName = values.firmName;
    values.licensePicName =
      values.licensePicName.length > 0
        ? values.licensePicName[0].response.obj
        : [];
    values.incuCertPicName =
      values.incuCertPicName.length > 0
        ? values.incuCertPicName[0].response.obj
        : [];
    // console.log(values, '提交按钮获取参数');
    const res = await update(values);
    if (res.success) {
      success(`${editing ? '修改' : '新增'}成功！`);
      onOk();
    } else {
      error(res.errorMessage);
    }
  };

  // 通过 && 不通过的请求
  const pass = async () => {
    const dates = await form.validateFields();
    if (dates.auditStatus !== 1) {
      dates.auditStatus = dates.failReason ? 2 : 1;
      dates.licenseName = dates.firmName;
      dates.licensePicName =
        dates.licensePicName.length !== 0
          ? dates.licensePicName[0].response.obj
          : '';
      dates.incuCertPicName =
        dates.incuCertPicName.length !== 0
          ? dates.incuCertPicName[0].response.obj
          : '';
      const ress = await update(dates);
      if (ress.success) {
        dates.auditor = sessionStorage.username;
        dates.serialNo = ress.obj.serialNo;
        // dates.auditorName = sessionStorage.username;
        submitAuditRecord(dates).then(res => {
          if (res.success) {
            message.success('审核通过');
            onOk();
          } else {
            notification.warning({
              message: res.errorMessage,
            });
          }
        });
      } else {
        notification.warning({
          message: ress.errorMessage,
        });
      }
    } else {
      notification.warning({
        message: '审核成功数据不允许审核',
      });
    }
  };

  const back = () => {
    onOk();
  };

  const cancelShowModal = () => {
    setIsShowModal(false);
  };

  const showModal = () => {
    setIsShowModal(true);
  };

  const formProps = {
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
  };

  const uploadProps = {
    action: '/fpmFirmServices/image/uploadImageForm',
    headers: {
      systemKey: Config.systemKey,
    },
  };

  // 图片上传个数限制在一张
  const normFile = e => {
    const arr = e.fileList.slice(-1);
    setFileList(arr);
    if (Array.isArray(e)) {
      return e;
    }
    return e && arr;
  };

  const normFile2 = e => {
    const arr2 = e.fileList.slice(-1);
    setFileList2(arr2);
    if (Array.isArray(e)) {
      return e;
    }
    return e && arr2;
  };

  const handleCancel = () => {
    setPreviewVisible(false);
  };

  // 图片的预览
  const onPreview = async file => {
    let src = file.url;
    if (!src) {
      src = await new Promise(resolve => {
        const reader = new FileReader();
        reader.readAsDataURL(file.originFileObj);
        reader.onload = () => resolve(reader.result);
      });
    }
    setHeaderValue(file.name);
    setPreviewImage(src);
    setPreviewVisible(true);
  };

  return (
    <div>
      <Modal
        key="3"
        style={{ left: -400 }}
        title={`${editing ? '编辑' : '审核'}页面`}
        onOk={submits}
        {...rest}
        footer={
          editing
            ? [
                <Button key="back" onClick={back}>
                  取消
                </Button>,
                <Button key="submits" type="primary" onClick={submits}>
                  确定
                </Button>,
              ]
            : [
                <Button key="back" onClick={back}>
                  取消
                </Button>,
                <Button
                  key="submit1"
                  style={{ backgroundColor: '#1890ff', color: '#fff' }}
                  onClick={pass}
                >
                  通过
                </Button>,
                <Button
                  key="submit2"
                  type="primary"
                  // style={{ marginLeft: '100px' }}
                  onClick={showModal}
                >
                  不通过
                </Button>,
              ]
        }
      >
        <Form form={form} {...formProps} initialValues={formValMemo}>
          <Row gutter={{ md: 4, lg: 5, xl: 20 }}>
            <Col md={10} sm={24}>
              <FormItem label="用户ID" name="userId">
                <Input placeholder="请输入" disabled />
              </FormItem>
            </Col>
            <Col md={10} sm={24}>
              <FormItem label="企业名称" name="firmName">
                <Input placeholder="请输入" disabled={!editing} />
              </FormItem>
            </Col>
            <Col md={10} sm={24}>
              <FormItem label="营业执照号" name="licenseNo">
                <Input placeholder="请输入" disabled />
              </FormItem>
            </Col>
            <Col md={10} sm={24}>
              <FormItem label="来源编号" name="sourceNo">
                <Input placeholder="请输入" disabled />
              </FormItem>
            </Col>
            <Col md={24} sm={24}>
              <FormItem
                label="审核流水号"
                name="serialNo"
                labelCol={{ md: 3 }}
                wrapperCol={{ md: 17 }}
              >
                <Input placeholder="请输入" disabled />
              </FormItem>
            </Col>
            <Col md={10} sm={24}>
              <FormItem label="姓名" name="name">
                <Input placeholder="请输入" disabled={!editing} />
              </FormItem>
            </Col>
            {/* <Col md={10} sm={24}>
              <FormItem label="手机" name="phone">
                <Input placeholder="请输入" disabled={!editing} />
              </FormItem>
            </Col> */}
            <Col md={10} sm={24}>
              <FormItem label="企业平台ID" name="firmNo">
                <Input placeholder="请输入" disabled />
              </FormItem>
            </Col>
            <Col md={10} sm={24}>
              <FormItem label="注册申请时间" name="submitTime">
                <DatePicker disabled style={{ width: '100%' }} />
              </FormItem>
            </Col>
            <Col md={10} sm={24}>
              <FormItem label="用户类型" name="source">
                <Select
                  disabled
                  allowClear
                  options={sourceList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col md={10} sm={24}>
              <FormItem label="审核状态" name="auditStatus">
                <Select
                  allowClear
                  disabled
                  options={auditStatusList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col md={10} sm={24}>
              <FormItem label="账号状态" name="status">
                <Select
                  disabled={!editing}
                  allowClear
                  options={statusList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>

            <Col md={10} sm={24}>
              <FormItem
                name="licensePicName"
                label="营业执照图片"
                valuePropName="fileList"
                getValueFromEvent={normFile}
              >
                <Upload
                  disabled={!editing}
                  {...uploadProps}
                  onPreview={onPreview}
                  listType="picture"
                  data={{
                    userId: tem.userId,
                    type: '2',
                  }}
                  //   defaultFileList={[...defaultListA]}
                >
                  {fileList && fileList.length > 0 ? null : (
                    <Button disabled={!editing} icon={<UploadOutlined />}>
                      上传营业执照
                    </Button>
                  )}
                </Upload>
              </FormItem>
            </Col>
            <Col md={10} sm={24}>
              <FormItem
                name="incuCertPicName"
                label="在职证明图片"
                valuePropName="fileList"
                getValueFromEvent={normFile2}
              >
                <Upload
                  {...uploadProps}
                  disabled={!editing}
                  onPreview={onPreview}
                  listType="picture"
                  data={{
                    userId: tem.userId,
                    type: '3',
                  }}
                  //   defaultFileList={[...defaultListB]}
                >
                  {fileList2 && fileList2.length > 0 ? null : (
                    <Button disabled={!editing} icon={<UploadOutlined />}>
                      上传在职证明
                    </Button>
                  )}
                </Upload>
              </FormItem>
            </Col>
          </Row>

          {isShowModal && (
            <Modal
              key="4"
              closable={false}
              visible={isShowModal}
              footer={[
                <Button onClick={cancelShowModal}>取消</Button>,
                <Button key="submit" type="primary" onClick={pass}>
                  确定
                </Button>,
              ]}
            >
              <FormItem
                label="不通过原因"
                name="failReason"
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <Select
                  allowClear
                  options={failReasonList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Modal>
          )}
        </Form>
      </Modal>
      {/* 图片预览 */}
      {previewVisible && (
        <PreviewRote
          previewVisible={previewVisible}
          handleCancel={handleCancel}
          previewImage={previewImage}
          title={headerValue}
        />
      )}
    </div>
  );
};

export default connect(() => ({}))(props => <EditModal {...props}></EditModal>);
