import React, { useState, Fragment, PureComponent, useEffect } from 'react';
import { connect } from 'dva';
import { Row, Popover, Table, Col, Form, Input, Select } from 'antd';

import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
} from 'ky-giant';

import { withRouter } from 'react-router-dom';
// import { cloneDeep } from 'lodash';
import moment from 'moment';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import { formatRatio } from 'src/utils/utils';
// import ExportButton from '@/components/ExportButton';
import MoudelCodeHooks from 'src/components/MoudelCodeHooks';
import { searchRecordList } from './services/api';

import './index.scss';
const { Item: FormItem } = Form;
let inputParam = {};

const Page = () => {
  const [form] = Form.useForm();
  const moduleCode = MoudelCodeHooks();
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  // const [selectedRows, setSelectedRows] = useState([]);
  // const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const columns = [
    {
      title: '工号',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'staffId',
    },
    {
      title: '用户ID',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userId',
    },
    {
      title: '姓名',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'name',
    },
    {
      title: '平台资源',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'platUser',
      render: text => {
        switch (text) {
          case true:
            return '是';
          case false:
            return '否';
          default:
            return '';
        }
      },
    },
    {
      title: '性别',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'gender',
    },
    {
      title: '户籍',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'census',
    },
    {
      title: '年龄',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'age',
    },
    {
      title: '学历',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'education',
    },
    {
      title: '工作经验',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'experience',
      render: v => {
        if (v) {
          const jsonArr = JSON.parse(v);
          const headName = [];
          jsonArr.forEach((value, index) => {
            headName[index] = value.name;
          });
          const contentText = headName.join(',');
          const content = (
            <Table
              columns={fixedColumns}
              dataSource={fixedColumnsFunction(jsonArr, headName)}
              pagination={false}
              bordered
            />
          );
          return (
            <Fragment>
              <Popover content={content} trigger="hover">
                <sapn>{contentText || '--'}</sapn>
              </Popover>
            </Fragment>
          );
        }
        return null;
      },
    },
    // {
    //   title: '工作经验',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 170,
    //   dataIndex: 'experience',
    // },
    {
      title: '物流行业经验',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'logistics',
      render: text => {
        switch (text) {
          case true:
            return '有';
          case false:
            return '无';
          default:
            return '';
        }
      },
    },

    {
      title: '履约率',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'rate',
      render: value => formatRatio(value, 'percentage'),
    },
    {
      title: '注册时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'createTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '注销时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'destroyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
  ];

  const fixedColumnsFunction = (value, labelName) => {
    const fixedData = [];
    for (let i = 0; i < value.length; i += 1) {
      let strName = '';
      value[i].children.forEach(item => {
        strName = `${item.name},${strName}`;
      });
      fixedData.push({
        key: i,
        name: labelName[i],
        description: strName.substring(0, strName.lastIndexOf(',')),
      });
    }
    return fixedData;
  };
  const fixedColumns = [
    {
      title: '一级',
      dataIndex: 'name',
      align: 'center',
      width: 150,
    },
    {
      title: '二级',
      align: 'center',
      dataIndex: 'description',
    },
  ];

  // 查询参数的获取
  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = params;
    refresh({ pageNum, pageSize });
    return {
      ...params,
    };
  };

  // 页面刷新的请求
  const refresh = (pagination = {}) => {
    setLoading(true);
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = { ...inputParam, pageNum, pageSize };
    searchRecordList(param).then(res => {
      const { success: resSuccess, obj } = res;
      if (resSuccess) {
        const { list, pageNum: current, total } = obj;
        // setTotalNum(total);
        // setSelectedRows([]);
        setLoading(false);
        setDatas({
          pagination: {
            total,
            current,
            pageSize,
          },
          list,
        });
      } else {
        // setSelectedRows([]);
        setLoading(false);
        setDatas({
          pagination: {
            total: 0,
            current: 1,
            pageSize: 10,
          },
          list: [],
        });
      }
    });
  };
  // 页面刷新的第一次请求
  useEffect(() => {
    getQueryParams();
  }, []);

  // const handleSelectRows = rows => {
  //   setSelectedRows(rows);
  // };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="姓名" name="name">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="平台资源" name="resource">
                <Select
                  allowClear
                  options={[
                    { label: '是', value: true },
                    { label: '否', value: false },
                  ]}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="是否注销" name="destroy">
                <Select
                  allowClear
                  options={[
                    { label: '是', value: true },
                    { label: '否', value: false },
                  ]}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>

      <div className="btn-con">
        <div>
          {/* <ExportButton
              moduleCode={moduleCode}
              code="userInformation-exportAll"
              text="导出"
              disabled={datas.list && datas.list.length < 1}
              icon={<ExportOutlined />}
              options={{
                filename: '用户画像列表.xls',
                total: totalNum,
                requstParams: [
                  `/fpmCrewServices/user/getUserInfoExcel`,
                  {
                    method: 'POST',
                    body: { ...inputParam },
                  },
                ],
              }}
            /> */}
          <AsyncExport
            modulecode={moduleCode}
            code="userInformation-exportAll"
            type="primary"
            disabled={datas.list && datas.list.length < 1}
            text="导出全部"
            options={{
              requstParams: [
                '/fpmCrewServices/user/getUserInfoExcelAsync',
                {
                  method: 'POST',
                  body: inputParam,
                },
              ],
            }}
          />
        </div>
      </div>

      {/* <div>
          <span className="submitButtons">
            <Button icon={<ReloadOutlined />} type="primary" htmlType="submi">
              查询
            </Button>
            <Button
              icon={<RollbackOutlined />}
              style={{ marginLeft: 8 }}
              onClick={() => {
                form.resetFields();
                inputParam = {};
                refresh();
              }}
            >
              重置
            </Button>
          </span>
        </div> */}
      {/* </div> */}
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="userId"
        // pagination={false}
        showSelection={false}
        loading={loading}
        data={datas}
        columns={columns}
        // onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
    </div>
  );
};

@connect(state => ({
  systemRole: state.global.roles,
}))
class Container extends PureComponent {
  render() {
    const { userInfo, systemRole } = this.props;
    return <Page systemRole={systemRole} userInfo={userInfo} />;
  }
}

export default withRouter(Container);
