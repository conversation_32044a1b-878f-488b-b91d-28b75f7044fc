// import { stringify } from 'qs';
import request from '@/utils/request';

// 查询用户画像列表
export async function searchRecordList(params) {
  return request(`/fpmCrewServices/user/getUserInfo`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// // 导出查询审核记录列表
// export function exportRecordList(params) {
//   return request(`/fpmFirmServices/auditRecord/export`, {
//     method: 'POST',
//     body: {
//       ...params,
//     },
//   });
// }

// // 查询审核记录详情
// export function searchRecordDetail(params) {
//   return request(`/fpmFirmServices/auditRecord/get`, {
//     method: 'POST',
//     body: {
//       ...params,
//     },
//   });
// }

// // 提交审核结果
// export function submitAuditRecord(params) {
//   return request(`/fpmFirmServices/auditRecord/audit`, {
//     method: 'POST',
//     body: {
//       ...params,
//     },
//   });
// }

// // 编辑完成更新数据
// export async function update(params) {
//   return request(`/fpmFirmServices/auditRecord/update`, {
//     method: 'POST',
//     body: {
//       ...params,
//     },
//   });
// }
