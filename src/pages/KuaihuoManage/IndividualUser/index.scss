// @import '~antd/lib/style/themes/default.less';
// @import '~@/utils/utils.less';
:root {
  --card-height: 0;
}
.react-resizable {
  position: relative;
  background-clip: padding-box;
}

.react-resizable-handle {
  position: absolute;
  width: 10px;
  height: 100%;
  bottom: 0;
  right: -5px;
  cursor: col-resize;
  z-index: 1;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
  div.ant-typography {
    margin-bottom: 0;
  }
}
.ant-table-body {
  overflow: auto;
}

:global {
  .ant-input-affix-wrapper {
    // display: block;
    display: flex !important;
    margin-left: -5px;
  }
}

.tableListForm {
  :global {
    .ant-input-affix-wrapper {
      display: flex;
      margin-left: -5px;
    }
    span.ant-input-affix-wrapper {
      margin-left: 0 !important;
    }
    .ant-form-item {
      display: flex;
      margin-right: 0;
      margin-bottom: 24px;
      > .ant-form-item-label {
        // width: auto;
        // width: auto;
        padding-right: 8px;
        line-height: 32px;
      }
      .ant-form-item-control {
        line-height: 32px;
      }
    }
    .ant-form-item-control-wrapper {
      flex: 1;
    }
  }
  .submitButtons {
    display: block;
    // margin-bottom: 24px;
    white-space: nowrap;
  }
}

.ant-form-item-children {
  display: flex;
  span {
    flex: 1;
    text-indent: 5px;
  }
}

.ant-input-number {
  width: 100%;
}
.active-items {
  // hack
  // height: 224px;
  // height: var(--card-height);
  //  transition: height .3s ease-in;
  overflow: hidden;
}
.close-items {
  height: 100px;
  overflow: hidden;
}
.toggle-btn {
  display: inline-block;
  padding-left: 15px;
}
.infor {
  font-size: 16px;
  margin-bottom: 10px;
}
span.margin_left {
  margin-right: 20px;
}
// .tip {
//   position: relative;
//   top: 7px;
//   right: -17px;
//   font-size: 16px;
// }
span.anticon.anticon-question-circle.tip {
  position: absolute;
  top: 7px;
  right: -15px;
  z-index: 999;
  font-size: 16px;
}
// button.ant-btn.ant-btn-link {
//   position: absolute;
// }
button.ant-btn.search.ant-btn-link {
  position: absolute;
  top: 0px;
  right: -60px;
}
.view-modal {
  // 设置文字图片禁止选中
  -moz-user-select: none; /*火狐*/
  -webkit-user-select: none; /*webkit浏览器*/
  -ms-user-select: none; /*IE10*/
  -khtml-user-select: none; /*早期浏览器*/
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  .ant-modal-close-x {
    width: 34px !important;
  }
  .common-icon-style :hover {
    color: rgba(0, 0, 0, 0.45);
    font-weight: 700;
  }
}
