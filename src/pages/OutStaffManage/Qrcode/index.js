import React, { useState, useEffect, useRef } from 'react';

import { Card } from 'antd';
import QRCode from 'qrcode.react';
import { connect } from 'dva';
import { ReloadOutlined } from '@ant-design/icons';
import { queryQrcode } from 'src/services/qrcode';
import styles from './style.less';
import logo from './fast-icon.png';

const Qrcode = props => {
  const { userInfo } = props;
  const [src, setSrc] = useState('');
  const intervalRef = useRef();
  const getCode = async data => {
    const res = await queryQrcode({ ...data }).finally(() =>
      clearInterval(intervalRef.current),
    );
    if (res && res.success) {
      setSrc(JSON.stringify(res.obj));
    }
  };
  const fleshCode = () => {
    getCode({ zoneCode: userInfo.deptCode });
  };
  useEffect(() => {
    if (userInfo.deptCode) {
      const id = setInterval(() => {
        getCode({ zoneCode: userInfo.deptCode });
      }, 3000);
      intervalRef.current = id;
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  });
  return (
    <Card>
      <div className={styles.container}>
        <div className={styles.scan}>网点： {userInfo.deptCode}</div>
        {userInfo.deptCode && (
          <QRCode
            value={src} // 生成二维码的内容
            size={500} // 二维码的大小
            fgColor="#000000" // 二维码的颜色
            imageSettings={{
              // 中间有图片logo
              src: logo,
              height: 60,
              width: 60,
              excavate: true,
            }}
          />
        )}
        <div className={styles.scan} onClick={fleshCode}>
          <ReloadOutlined />
          <span className={styles.scanTip}> 每秒自动更新 </span>
        </div>
        <p>
          <label> 温馨提示： </label>
          <span>为保障场地的考勤数据， 请不要拍照将二维码信息发送给陌生人</span>
        </p>
      </div>
    </Card>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(Qrcode);
