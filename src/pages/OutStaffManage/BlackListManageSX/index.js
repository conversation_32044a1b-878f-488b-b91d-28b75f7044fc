/* eslint-disable no-return-assign */
import React, { Component, Fragment, PureComponent, useState } from 'react';
import { Card, message } from 'antd';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import moment from 'moment';
import { connect } from 'dva';
import { withRouter } from 'react-router-dom';
import StandardTable from '@/components/StandardTable';
import AuthButton from '@/components/AuthButton';
import authDecorator from '@/components/AuthDecorator';
// import { AuthButton } from 'ky-giant';

// import {
//   query,
//   addUserInfo,
//   editInfo,
//   detail,
//   exportUserList,
//   asyncExportUserList,
//   stopUsing,
//   startUsing,
//   updateSF,
// } from '@/services/outUserManage';
import {
  query,
  addUserInfo,
  editInfo,
  // detail,
  // exportUserList,
  // asyncExportUserList,
  stopUsing,
  // startUsing,
  // updateSF,
} from './services/api';
import CreateAndEdit from './components/createAndEdit';
// import styles from './style.less';
import { statusList } from './components/status';

import SearchForm from './components/search';
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const ShowEye = props => {
  const { title } = props;
  const [show, setShow] = useState(false);
  const showPass = () => {
    setShow(!show);
    // console.log(props);
    props.showPass();
  };

  return (
    <div>
      <span style={{ paddingRight: 10 }}>{title}</span>
      {show && <EyeOutlined onClick={() => showPass()} />}
      {!show && <EyeInvisibleOutlined onClick={() => showPass()} />}
    </div>
  );
};
class UserManage extends Component {
  // 将传入props的值传到state中
  static getDerivedStateFromProps(nextProps) {
    return {
      moduleCode: nextProps.moduleCode,
      userInfo: nextProps.userInfo,
      systemRole: nextProps.systemRole,
    };
  }

  state = {
    orgCode: '',
    code: '',
    editName: '',
    siteName: '',
    selectedRows: [],
    loading: false,
    obj: {
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    },
    searchValues: '',
    showModal: false, // 是否show弹窗
    title: '',
    editStatus: false,
    editObj: '',
    btnLoading: [false, false],
    // isRoot: false,
    confirmLoading: false, // 弹窗确认loading
    showTable: true,
  };

  columns = [
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 100,
    },
    {
      title: <ShowEye showPass={e => this.showPass(e)} title="身份证号码" />,
      dataIndex: 'certificateNo',
      width: 200,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      render: value => {
        const data = statusList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '原因',
      dataIndex: 'reason',
      width: 120,
      ellipsis: true,
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      render: timeTrans,
      ellipsis: true,
    },
    {
      title: '生效时间',
      dataIndex: 'startWorkTime',
      width: 180,
      render: timeTrans,
      ellipsis: true,
    },
    {
      title: '失效时间',
      dataIndex: 'endWorkTime',
      width: 180,
      render: timeTrans,
      ellipsis: true,
    },
    {
      title: '修改人',
      dataIndex: 'modifier',
      width: 100,
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      width: 180,
      render: timeTrans,
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operate',
      width: 100,
      fixed: 'right',
      render: (text, row) => {
        const { moduleCode } = this.props;
        return (
          <Fragment>
            <AuthButton
              type="link"
              moduleCode={moduleCode}
              // style={{ marginRight: 15 }}
              code="blackListManageSX-edit"
              onClick={e => this.handleEdit(e, row, 'edit')}
            >
              编辑
            </AuthButton>
          </Fragment>
        );
      },
    },
  ];

  // 所属网点传code
  handleInit = (code, siteName) => {
    this.setState({ code, siteName });
  };

  // 获取默认的公司属性
  handleInitOrgCode = orgCode => {
    this.setState({ orgCode });
  };

  // 获取是否有权限
  // handleInitRoot = isRoot => {
  //   this.setState({ isRoot });
  // };

  // 修改时网点传code
  handleInitEdit = name => {
    this.setState({ editName: name });
  };

  handleSearch = (values, isWord) => {
    const { obj } = this.state;
    const { current, pageSize } = obj.pagination;
    if (!isWord) {
      this.setState(
        {
          // decryptPhone: false,
          decryptCertificates: false,
          showTable: false,
        },
        () => {
          this.setState({
            showTable: true,
          });
        },
      );
    }
    const data = {
      ...values,
      pageNum: isWord ? current : 1,
      pageSize: isWord ? pageSize : 10,
    };
    this.setState({
      searchValues: values,
      selectedRows: [],
    });
    this.getOutUser(data);
  };

  // 新增
  handleCreate = () => {
    this.setState({
      showModal: true,
      title: '新增',
      editStatus: false,
      editObj: '',
      confirmLoading: false,
    });
  };

  // 修改
  handleEdit = (e, row) => {
    e.stopPropagation();
    this.setState({
      showModal: true,
      title: '修改',
      editStatus: true,
      confirmLoading: false,
      editObj: row,
    });
  };

  // 关闭弹窗
  handleHide = () => {
    this.setState({
      showModal: false,
    });
  };

  // 确定修改或者新增
  handleConfirm = values => {
    const { editStatus, siteName } = this.state;
    // const { certificateNoShow } = editObj;
    if (editStatus) {
      // const isChange = certificateNoShow !== values.certificateNo;
      this.editUser({ ...values });
    } else {
      values.branchName = values.branchName ? values.branchName : siteName;
      this.addUser({ ...values });
    }
  };

  handleStart = async () => {
    const { searchValues, selectedRows } = this.state;
    if (selectedRows.length > 0) {
      // if (!selectedRows[0].comAttr) {
      //   message.error('顺丰外包数据暂不支持启用');
      //   return false;
      // }
      const ids = selectedRows.map(ele => ele.id);
      // const idList = selectedRows.map(ele => ele.extId);
      const res = await stopUsing({ status: 0, ids });
      if (res.success) {
        message.success('启用成功');
        this.getOutUser({
          pageSize: 10,
          pageNum: 1,
          ...searchValues,
        });
      }
    } else {
      message.error('请选择数据');
    }
  };

  handleStop = async () => {
    const { searchValues, selectedRows } = this.state;
    if (selectedRows.length > 0) {
      // if (!selectedRows[0].comAttr) {
      //   message.error('顺丰外包数据暂不支持禁用');
      //   return false;
      // }
      // const idList = selectedRows.map(ele => ele.extId);
      const ids = selectedRows.map(ele => ele.id);
      const res = await stopUsing({ ids, status: 2 });
      if (res.success) {
        message.success('禁用成功');
        this.getOutUser({
          pageSize: 10,
          pageNum: 1,
          ...searchValues,
        });
      }
    } else {
      message.error('请选择数据');
    }
  };

  // 删除数据
  // handleDel = () => {
  //   const { selectedRows, isRoot, code } = this.state;
  //   if (selectedRows.length > 0) {
  //     const siteCode = selectedRows.map(ele => ele.belongCode);
  //     const filterSite = siteCode.filter(ele => code === ele);
  //     const sameLength = siteCode.length === filterSite.length;
  //     if (!selectedRows[0].comAttr) {
  //       message.error('顺丰外包数据暂不支持删除');
  //       return false;
  //     }
  //     if (isRoot || sameLength) {
  //       const ids = selectedRows.map(ele => ele.userNo);
  //       const idList = selectedRows.map(ele => ele.extId);

  //       Modal.confirm({
  //         title: '提示',
  //         content: '确定删除?',
  //         className: styles.modalCenter,
  //         cancelText: '取消',
  //         okText: '确定',
  //         onOk: () => {
  //           this.delUser({ ids, idList });
  //         },
  //       });
  //     } else {
  //       message.error('无权限删除数据');
  //     }
  //   } else {
  //     message.error('请选择数据');
  //   }
  // };

  // 选择数据
  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };

  // 翻页
  handleStandardTableChange = pages => {
    const { pageSize, current } = pages;
    this.setState(
      {
        showTable: false,
        // decryptPhone: false,
        decryptCertificates: false,
        obj: {
          pagination: {
            pageSize,
            current,
          },
        },
      },
      () => {
        this.setState({
          showTable: true,
        });

        const { searchValues } = this.state;
        const data = {
          ...searchValues,
          // decryptPhone: false,
          decryptCertificates: false,
          pageNum: current,
          pageSize,
        };
        this.getOutUser(data);
      },
    );
  };

  // 查询列表
  getOutUser = async data => {
    const { pageNum, pageSize } = data;
    this.setState({
      obj: {
        list: [],
        pagination: {
          pageSize: 10,
          current: 1,
          total: 0,
        },
      },
      loading: true,
      selectedRows: [],
    });

    const res = await query(data).finally(() =>
      this.setState({ loading: false }),
    );
    if (res && res.success) {
      const { obj } = res;
      if (obj) {
        const { list = [], total = 0 } = obj;
        if (obj.list) {
          obj.list.forEach((ele, ind) => (ele.only = `${ele.userNo}_${ind}`));
        }
        this.setState({
          obj: {
            list: list || [],
            pagination: {
              pageSize,
              current: pageNum,
              total,
            },
          },
          selectedRows: [],
        });
      }
    }
  };

  // 修改
  editUser = async data => {
    const { searchValues } = this.state;
    this.setState({
      confirmLoading: true,
    });
    // const { comAttr, phone, userNo } = data;
    const requestUrl = editInfo(data);
    const res = await requestUrl.finally(() => {
      this.setState({
        confirmLoading: false,
      });
    });

    if (res.success) {
      message.success('修改成功');
      this.handleHide();
      this.getOutUser({
        pageSize: 10,
        pageNum: 1,
        ...searchValues,
      });
    } else {
      message.error(res.errorMessage);
    }
  };

  // 新增
  addUser = async data => {
    const { searchValues, code, orgCode } = this.state;
    this.setState({
      confirmLoading: true,
    });
    const res = await addUserInfo(data).finally(() =>
      this.setState({
        confirmLoading: false,
      }),
    );

    if (res.success) {
      message.success('新增成功');
      this.handleHide();
      this.getOutUser({
        pageSize: 10,
        pageNum: 1,
        ...searchValues,
        belongCode: searchValues.belongCode ? searchValues.belongCode : code,
        comAttr: searchValues.comAttr ? searchValues.comAttr : orgCode,
      });
    }
  };

  // 删除
  // delUser = async data => {
  //   this.setState({ loading: true });
  //   try {
  //     await detail(data).finally(() => this.setState({ loading: false }));
  //     await detail(data);
  //     const { searchValues } = this.state;
  //     message.success('删除成功');
  //     this.getOutUser({
  //       pageSize: 10,
  //       pageNum: 1,
  //       ...searchValues,
  //     });
  //   } catch (err) {
  //     console.log(err);
  //   }
  //   // finally {
  //   //   this.setState({ loading: false });
  //   // }
  // };

  // 加密与解密
  showPass = () => {
    // const flag = type === '手机号码' ? 'decryptPhone' : 'decryptCertificates';
    const flag = 'decryptCertificates';
    this.setState(
      prevState => ({
        [flag]: !prevState[flag],
      }),
      () => {
        const {
          searchValues,
          // decryptPhone,
          decryptCertificates,
          obj,
        } = this.state;
        const values = { ...searchValues, decryptCertificates };
        if (obj.list.length > 0) {
          this.handleSearch(values, true);
        }
      },
    );
  };

  render() {
    const {
      selectedRows,
      loading,
      obj,
      showModal,
      title,
      editStatus,
      editObj,
      btnLoading,
      confirmLoading,
      showTable,
      searchValues,
    } = this.state;
    return (
      <div className="table-list">
        <div className="tableListForm">
          <SearchForm
            handleCreate={this.handleCreate}
            handleEdit={this.handleEdit}
            // handleDel={this.handleDel}
            handleSearch={this.handleSearch}
            // handleExport={this.handleExport}
            // initCode={this.handleInit}
            // initOrgCode={this.handleInitOrgCode}
            initRoot={this.handleInitRoot}
            btnLoading={btnLoading}
            start={this.handleStart}
            stop={this.handleStop}
            selectedRows={selectedRows}
            datas={obj}
            searchValues={searchValues}
          />
          {showTable && (
            <StandardTable
              size="small"
              selectedRows={selectedRows}
              data={obj}
              columns={this.columns}
              multiple
              loading={loading}
              onSelectRow={this.handleSelectRows}
              rowKey="only"
              onChange={this.handleStandardTableChange}
            />
          )}

          {showModal && (
            <CreateAndEdit
              showModal={showModal}
              handleHide={this.handleHide}
              handleConfirm={this.handleConfirm}
              editStatus={editStatus}
              editObj={editObj}
              title={title}
              initCode={this.handleInitEdit}
              confirmLoading={confirmLoading}
            />
          )}
        </div>
      </div>
    );
  }
}

// export default UserManage;

@connect(state => ({
  userInfo: state.global.userInfo,
  systemRole: state.global.roles,
}))
@authDecorator
class Container extends PureComponent {
  render() {
    const { moduleCode, userInfo, systemRole } = this.props;
    return (
      <UserManage
        moduleCode={moduleCode}
        userInfo={userInfo}
        systemRole={systemRole}
      />
    );
  }
}

export default withRouter(Container);
