/* eslint-disable react/destructuring-assignment */
import React, { useEffect } from 'react';
import { Form, Input, Select, Row, Col, DatePicker } from 'antd';
import { connect } from 'dva';
import {
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
  // rangePickerColStyle,
  // rangePickerLayout,
} from 'ky-giant';
import { checkUserRole } from '@/services/clock';
// import ExportButton from '@/components/ExportButton';
// import AsyncExport from '@/components/AsyncExport';
import AuthButton from '@/components/AuthButton';
import authDecorator from '@/components/AuthDecorator';
import ExportButton from '@/components/ExportButton';
// import styles from '../../style.less';
import { statusList } from '../status';
const { Item } = Form;
// const { Option } = Select;

const SearchForm = props => {
  const { userInfo, moduleCode, selectedRows, datas, searchValues } = props;
  const [form] = Form.useForm();
  // const statusList = [
  //   {
  //     value: 0,
  //     label: '生效',
  //   },
  //   {
  //     value: 1,
  //     label: '失效',
  //   },
  //   {
  //     value: 2,
  //     label: '禁用',
  //   },
  // ];

  //  查询
  const onFinish = values => {
    props.handleSearch(values);
  };

  // 新增
  const handleCreate = () => {
    props.handleCreate();
  };
  // 修改
  // const handleEdit = () => {
  //   props.handleEdit();
  // };
  // 删除
  // const handleDel = () => {
  //   props.handleDel();
  // };
  //   重置
  const resetForm = () => {
    form.resetFields();
  };

  const checkRoot = async () => {
    const res = await checkUserRole();
    if (res.success) {
      // setIsRoot(res.obj);
      props.initRoot(res.obj);
    }
  };
  const startUsing = () => {
    props.start();
  };
  const stopUsing = () => {
    props.stop();
  };

  useEffect(() => {
    checkRoot();
  }, []);

  useEffect(() => {
    // if (userInfo.deptCode) {
    //   const num = userInfo.orgCode === 'SX' ? 1 : 0;
    //   form.setFieldsValue({
    //     belongCode: userInfo.deptCode,
    //     comAttr: num,
    //   });
    //   props.initOrgCode(num);
    //   setUserLimit(num === 0);
    // }
  }, [userInfo.deptCode]);

  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        accountStatus: 1,
        comAttr: userInfo && userInfo.orgCode === 'SX' ? 1 : 0,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item name="userName" label="姓名">
                <Input placeholder="请输入姓名" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item label="生效时间" name="startWorkTime">
                <DatePicker showTime style={{ width: '100%' }} allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item label="失效时间" name="endWorkTime">
                <DatePicker showTime style={{ width: '100%' }} allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="status" label="状态">
                <Select
                  placeholder="请选择"
                  allowClear
                  options={statusList}
                ></Select>
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="blackListManageSX-add"
          style={{ marginRight: 15 }}
          onClick={handleCreate}
        >
          新增
        </AuthButton>
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          disabled={selectedRows.length < 1}
          code="blackListManageSX-start"
          style={{ marginRight: 15 }}
          onClick={startUsing}
        >
          启用
        </AuthButton>
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          disabled={selectedRows.length < 1}
          code="blackListManageSX-stop"
          style={{ marginRight: 15 }}
          onClick={stopUsing}
        >
          禁用
        </AuthButton>
        <ExportButton
          text="导出所选"
          moduleCode={moduleCode}
          // disabled={datas.length < 1}
          disabled={selectedRows.length < 1}
          code="blackListManageSX-export-all"
          style={{ marginRight: 15 }}
          type="primary"
          options={{
            // total: totalNum,
            filename: '顺心黑名单列表.xls',
            requstParams: [
              `/opbdsUPMService/epEmployeeSxWhiteList/export`,
              {
                method: 'POST',
                body: { ids: selectedRows.map(ele => ele.id) },
              },
            ],
          }}
        />
        <ExportButton
          text="导出全部"
          type="primary"
          moduleCode={moduleCode}
          disabled={datas.list && datas.list.length < 1}
          code="blackListManageSX-export-some"
          style={{ marginRight: 15 }}
          options={{
            // total: totalNum,
            filename: '顺心黑名单列表.xls',
            requstParams: [
              `/opbdsUPMService/epEmployeeSxWhiteList/export`,
              {
                method: 'POST',
                body: { ...searchValues },
              },
            ],
          }}
        />
      </div>
      {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
      {/* </div> */}
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
