/* eslint-disable indent */
/* eslint-disable react/destructuring-assignment */
import React from 'react';
import {
  Form,
  Input,
  DatePicker,
  // message,
  // Select,
  Modal,
  Button,
  Row,
  Col,
} from 'antd';
import moment from 'moment';

import { connect } from 'dva';
// import DeptSearch from '@/components/DeptSearch';

// import { checkUserRole } from '@/services/clock';
// import { siteToCompany,siteToSFCompany } from '@/services/outUserManage'
// import styles from '../../style.less';
// import { querySuppliers } from '@/services/supplierApi';

const { Item } = Form;
const { TextArea } = Input;

// const { Option } = Select;
// const { RangePicker } = DatePicker;
const layout = {
  labelCol: { md: 8, sm: 24, xs: 24 },
  wrapperCol: { md: 16, sm: 24, xs: 24 },
};
const SearchForm = props => {
  const [form] = Form.useForm();
  const { editObj } = props;
  // const [company, setCompany] = useState([]);
  // const [isRoot, setIsRoot] = useState(true);
  // const [site, setSite] = useState('');
  // const [isSF, setIsSF] = useState(true);
  // const [idRequired, setIdRequired] = useState(true);
  // const [resource,setResource] = useState(0);

  const regStr = `^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$`;
  const editCer = editObj && editObj.certificateNo.replace(/\*/g, '\\*');
  const reg = props.editStatus
    ? new RegExp(`${regStr}|(^${editCer}$)`)
    : new RegExp(regStr, 'g');
  const handleConfirm = () => {
    form.validateFields().then(nameList => {
      // console.log(nameList);
      if (nameList) {
        nameList.startWorkTime = Number(
          moment(nameList.startWorkTime).format('x'),
        );
        nameList.endWorkTime = Number(moment(nameList.endWorkTime).format('x'));
        if (editObj) {
          Object.assign(nameList, {
            id: editObj.id,
          });
        }
        props.handleConfirm(nameList);
      }
    });
    //
  };

  // const getCompany = async data => {
  //   const values = {
  //     ...data,
  //     // pageSize: 100,
  //     // pageNum: 1,
  //   };
  //   const res = await querySuppliers(values);
  //   if (res.success) {
  //     const list =
  //       res.obj.map(({ companyCode, companyName, companyNameShort }) => ({
  //         outSource: companyName,
  //         outSourceAbbr: companyNameShort,
  //         outSourceCode: companyCode,
  //       })) || [];
  //     setCompany(list);
  //   }
  // };

  // const checkRoot = async () => {
  //   const res = await checkUserRole();
  //   if (res.success) {
  //     setIsRoot(res.obj);
  //   }
  // };
  // useEffect(() => {
  //   // console.log(editObj);
  //   if (props.editStatus) {
  //     const value = editObj.belongCode;
  //     const type = editObj.comAttr === 1 ? 'SX' : 'SF';
  //     // if (value) {
  //     //   getCompany({ orgCode: value, srcCode: type });
  //     // }
  //     if (isRoot && !editObj.comAttr) {
  //       setIsSF(false);
  //     } else {
  //       setIsSF(true);
  //     }
  //     // if (type === 'SX') {
  //     //   setIdRequired(true);
  //     // } else {
  //     //   setIdRequired(false);
  //     // }
  //   } else if (userInfo && userInfo.deptCode) {
  //     if (userInfo.orgCode === 'SX') {
  //       setIdRequired(true);
  //     } else {
  //       setIdRequired(false);
  //     }
  //     form.setFieldsValue({
  //       belongCode: userInfo.deptCode,
  //     });
  //   }
  // }, [userInfo.deptCode]);
  // useEffect(() => {
  //   checkRoot();
  // }, []);

  return (
    <Modal
      visible={props.showModal}
      onCancel={props.handleHide}
      title={props.title}
      footer={[
        <Button key="back" onClick={props.handleHide}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={props.confirmLoading}
          onClick={handleConfirm}
        >
          确定
        </Button>,
      ]}
    >
      <Form
        {...layout}
        form={form}
        initialValues={{
          userName: editObj && editObj.userName ? editObj.userName : '',
          reason: editObj && editObj.reason ? editObj.reason : '',
          certificateNo:
            editObj && editObj.certificateNo ? editObj.certificateNo : '',
          startWorkTime:
            editObj && editObj.startWorkTime
              ? moment(editObj.startWorkTime)
              : undefined,
          endWorkTime:
            editObj && editObj.endWorkTime
              ? moment(editObj.endWorkTime)
              : undefined,
        }}
      >
        <Row gutter={{ md: 4, lg: 12, xl: 12 }}>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="userName"
              label="姓名"
              rules={[{ required: true, message: '请输入姓名!' }]}
            >
              <Input placeholder="请输入姓名" />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="certificateNo"
              label="身份证号码"
              rules={[
                {
                  required: true,
                  message: '请输入身份证号码!',
                },
                {
                  pattern: reg,
                  message: '请输入正确的身份证号码 !',
                },
              ]}
            >
              <Input
                placeholder="请输入身份证号码"
                maxLength={18}
                disabled={props.editStatus}
              />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="reason"
              label="维护原因"
              rules={[{ required: true, message: '请输入内容!' }]}
            >
              <TextArea maxLength={100} />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              label="生效时间"
              name="startWorkTime"
              rules={[{ required: true, message: '请选择' }]}
            >
              <DatePicker showTime style={{ width: '100%' }} />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              label="失效时间"
              name="endWorkTime"
              rules={[{ required: true, message: '请选择' }]}
            >
              <DatePicker showTime style={{ width: '100%' }} />
            </Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(SearchForm);
