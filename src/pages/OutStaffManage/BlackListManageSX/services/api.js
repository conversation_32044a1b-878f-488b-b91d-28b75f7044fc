import request from '@/utils/request';

const baseApi = '';
// 查询
export async function query(data) {
  return request(`${baseApi}/opbdsUPMService/epEmployeeSxWhiteList/query`, {
    method: 'POST',
    body: data,
  });
}
// 查看
export async function detail(data) {
  return request(`${baseApi}/opbdsUPMService/epEmployeeSxWhiteList/detail`, {
    method: 'POST',
    body: data,
  });
}

// 修改
export async function editInfo(data) {
  return request(`${baseApi}/opbdsUPMService/epEmployeeSxWhiteList/modifiy`, {
    method: 'POST',
    body: data,
  });
}
// 新增
export async function addUserInfo(data) {
  return request(`${baseApi}/opbdsUPMService/epEmployeeSxWhiteList/add`, {
    method: 'POST',
    body: data,
  });
}

export async function stopUsing(data) {
  return request(
    `${baseApi}/opbdsUPMService/epEmployeeSxWhiteList/updateStatus`,
    {
      method: 'POST',
      body: data,
    },
  );
}
// // 顺丰数据修改
// export async function updateSF(data) {
//   return request(`${baseApi}/opbdsUPMService/outSoUseManag/modifySfUserInfo`, {
//     method: 'POST',
//     body: data,
//   });
// }
