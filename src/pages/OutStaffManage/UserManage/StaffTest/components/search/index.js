import React, { useEffect, useState } from 'react';
import { Form, Input, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import DeptSearch from 'src/components/DeptSearch';
import NetSearch from 'src/components/NetSelect';
import ExportButton from 'src/components/ExportButton';
import authDecorator from 'src/components/AuthDecorator';
import SuppliersSearch from 'src/components/SuppliersSearch';
// import styles from '../../style.less';

const { Item } = Form;
const { Option } = Select;

const SearchForm = props => {
  const [form] = Form.useForm();
  const { searchValues, moduleCode, userInfo, handleSearch } = props;
  const [allocateList, setAllocateList] = useState([]); // 分拨区
  const companyAttr = [
    {
      value: 'SX',
      text: '顺心',
    },
    {
      value: 'SF',
      text: '顺丰',
    },
  ];
  const faceSuccess = [
    {
      value: true,
      text: '否',
    },
    {
      value: false,
      text: '是',
    },
  ];

  //  查询
  const onFinish = values => {
    handleSearch(values);
  };

  const resetForm = () => form.resetFields();
  const getList = list => {
    setAllocateList(list);
  };
  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        sourceType: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
        zoneCode: userInfo.deptCode,
      });
    }
  }, [userInfo.deptCode]);

  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);
  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        accountStatus: 1,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item name="sourceType" label="公司属性">
                <Select placeholder="请选择" allowClear>
                  {companyAttr.map(ele => (
                    <Option value={ele.value} key={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  placeholder="请选择"
                  extraParam={{
                    typeLevels: ['2'],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                  }}
                  showSearch={false}
                  getList={getList}
                  // disabled={!isRoot}
                />
              </Item>
            </Col>
            {/* disabled={!limit} */}
            <Col {...colStyle}>
              <Item name="zoneCode" label="中转场">
                <DeptSearch />
              </Item>
            </Col>

            <Col {...colStyle}>
              <Item name="staffId" label="工号">
                <Input placeholder="请输入" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="name" label="姓名">
                <Input placeholder="请输入" allowClear />
              </Item>
            </Col>
            <Item
              shouldUpdate={(prevValues, curValues) =>
                prevValues.zoneCode !== curValues.zoneCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <Item name="accrueCode" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      allowClear
                      gysParams={getFieldValue('zoneCode') || userInfo.deptCode}
                    />
                  </Item>
                </Col>
              )}
            </Item>
            <Col {...colStyle}>
              <Item name="faceSuccess" label="是否异常数据">
                <Select placeholder="请选择" allowClear>
                  {faceSuccess.map(ele => (
                    <Option value={ele.value} key={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <ExportButton
          text="导出全部"
          style={{ marginRight: 15 }}
          moduleCode={moduleCode}
          code="staff_test-export-all"
          options={{
            // total: totalNum,
            filename: '外包人证管理列表.xlsx',
            requstParams: [
              `/fpmCrewServices/user/getUserFaceInfoExcel
                `,
              {
                method: 'POST',
                body: { ...searchValues },
              },
            ],
          }}
        />
      </div>
      {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
