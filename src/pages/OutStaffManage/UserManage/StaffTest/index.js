/* eslint-disable no-return-assign */
import React, { useState } from 'react';
// import { Card } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import StandardTable from 'components/StandardTable';
import Search from './components/search';
import { queryList } from './services';
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const PageIndex = () => {
  const [selectedRows, setSelectedRows] = useState([]);

  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });
  const [searchValues, setSearchValues] = useState({});
  const [loading, setLoading] = useState(false);

  const columns = [
    {
      title: '公司属性',
      dataIndex: 'sourceType',
      width: 100,
    },

    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 120,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },

    {
      title: '工号',
      dataIndex: 'staffId',
      width: 100,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      width: 100,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: 80,
    },
    {
      title: '异常原因',
      dataIndex: 'info',
      width: 120,
    },
    {
      title: '外包供应商',
      dataIndex: 'accrueName',
      width: 200,
    },
    // {
    //   title: '外包供应商简称',
    //   dataIndex: 'aa',
    //   width: 180,
    // },
    {
      title: '外包供应商编码',
      dataIndex: 'accrueCode',
      width: 180,
    },
    {
      title: '手机号码',
      dataIndex: 'phone',
      width: 150,
    },
    {
      title: '身份证号码',
      dataIndex: 'idCard',
      width: 180,
    },
    {
      title: '注册时间',
      dataIndex: 'createTime',
      width: 180,
      render: timeTrans,
    },
  ];
  const handleSearch = values => {
    setSearchValues(values);
    getList({ ...values, pageSize: 10, pageNum: 1 });
  };
  const changePage = pages => {
    const { pageSize, current } = pages;
    const data = {
      pageSize,
      pageNum: current,
      ...searchValues,
    };
    getList(data);
  };
  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };
  const getList = async data => {
    setLoading(true);
    const res = await queryList(data);
    setLoading(false);

    if (res && res.success && res.obj) {
      setLoading(false);

      const { records = [], size, current, total } = res.obj;
      if (res.obj.records) {
        res.obj.records.forEach(
          (ele, ind) => (ele.only = `${ele.userId}_${ind}`),
        );
      }
      setDatas({
        list: records || [],
        pagination: {
          current,
          pageSize: size,
          total,
        },
      });
    }
  };

  return (
    <div className="table-list">
      <div className="tableListForm">
        <Search handleSearch={handleSearch} searchValues={searchValues} />
        <StandardTable
          size="small"
          selectedRows={selectedRows}
          onSelectRow={handleSelectRows}
          onChange={changePage}
          data={datas}
          loading={loading}
          rowKey="only"
          columns={columns}
        />
      </div>
    </div>
  );
};

export default connect(state => ({
  userInfo: state.global.userInfo,
}))(PageIndex);
