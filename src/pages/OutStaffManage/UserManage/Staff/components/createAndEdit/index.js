/* eslint-disable indent */
/* eslint-disable react/destructuring-assignment */
import React, { useEffect, useState } from 'react';
import { Form, Input, message, Select, Modal, Button, Row, Col } from 'antd';
import { connect } from 'dva';
import DeptSearch from '@/components/DeptSearch';

import { checkUserRole } from '@/services/clock';
// import { siteToCompany,siteToSFCompany } from '@/services/outUserManage'
// import styles from '../../style.less';
import { querySuppliers, querySections } from '@/services/supplierApi';
const { Item } = Form;
const { Option } = Select;
// const { RangePicker } = DatePicker;
const layout = {
  labelCol: { md: 8, sm: 24, xs: 24 },
  wrapperCol: { md: 16, sm: 24, xs: 24 },
};
const SearchForm = props => {
  const [form] = Form.useForm();
  const { userInfo } = props;
  const [company, setCompany] = useState([]);
  const [section, setSection] = useState([]);
  const [isRoot, setIsRoot] = useState(true);
  const [site, setSite] = useState('');
  const [isSF, setIsSF] = useState(true);
  const [idRequired, setIdRequired] = useState(true);
  // const [resource,setResource] = useState(0);
  const status = [
    {
      value: 1,
      text: '启用',
    },
    {
      value: 0,
      text: '禁用',
    },
  ];
  const companyAttr = [
    {
      value: 1,
      text: '顺心',
    },
  ];

  const regStr = `^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$`;
  const editCer =
    props.editObj && props.editObj.certificateNoShow.replace(/\*/g, '\\*');
  const reg = props.editStatus
    ? new RegExp(`${regStr}|(^${editCer}$)`)
    : new RegExp(regStr, 'g');
  const handleConfirm = () => {
    form.validateFields().then(nameList => {
      // console.log(nameList);
      if (nameList) {
        nameList.branchName = site;
        // console.log(nameList);
        if (nameList.phoneShow) {
          const phoneReg = /^1[3456789]\d{9}$/;
          if (nameList.phoneShow.includes('***')) {
            if (props.editObj.phoneShow === nameList.phoneShow) {
              nameList.phone = props.editObj.phone;
            } else if (!phoneReg.test(nameList.phoneShow)) {
              message.error('请正确输入手机号码');
              return false;
            }
          } else {
            if (!phoneReg.test(nameList.phoneShow)) {
              message.error('请正确输入手机号码');
              return false;
            }
            nameList.phone = nameList.phoneShow;
          }
        } else {
          nameList.phone = '';
        }
        // if (nameList.certificateNoShow === props.editObj.certificateNoShow) {
        //   nameList.certificateNo = props.editObj.certificateNo;
        // } else {
        //   nameList.certificateNo = nameList.certificateNoShow;
        // }
        if (nameList.certificateNoShow) {
          nameList.certificateNo = nameList.certificateNoShow;
        }
        if (props.editObj.id && nameList.comAttr === 1) {
          nameList.extId = props.editObj.extId;
        }
        delete nameList.phoneShow;
        delete nameList.certificateNoShow;

        nameList.userName = nameList.userName?.trim();
        nameList.userNo = nameList.userNo?.trim();

        props.handleConfirm(nameList);
      }
    });
    //
  };
  // const getChangeVal = value => {
  //   const type = form.getFieldValue('comAttr');
  //   // console.log(SXorSF);
  //   if (props.editStatus) {
  //     form.setFieldsValue({
  //       outSource: '',
  //       outSourceCode: '',
  //       outSourceAbbr: '',
  //     });
  //   }
  //   if (value) {
  //     getDept(value).then(res => {
  //       const site = res.obj && res.obj.list
  //         ? res.obj.list
  //             .filter(ele => ele.deptCode === value)
  //             .map(ele => ele.deptName)
  //             .join()
  //         : '';
  //       console.log(site)
  //       setSite(site);
  //     });
  //     getCompany({ belongCode:value },type)
  //   } else {
  //     form.setFieldsValue({
  //       outSourceCode: '',
  //       outSourceAbbr: '',
  //     });
  //   }
  // };

  const getItem = list => {
    const siteCode = form.getFieldValue('belongCode');
    const type = form.getFieldValue('comAttr');
    const filterList = list.filter(ele => ele.value === siteCode);
    const name = filterList.length > 0 ? filterList[0].deptName : '';
    setSite(name);
    props.initCode(name);
    // console.log(siteCode, '===', name);
    if (!props.editObj) {
      if (name) {
        // getCompany({ belongCode:siteCode },type)
        getCompany({ orgCode: siteCode, srcCode: type ? 'SX' : 'SF' });
      }
    }
    if (name) {
      getSection({ firstCenterSiteid: siteCode }, name);
    }
  };
  const getComVal = val => {
    // setResource(value)
    setCompany([]);
    setSection([]);
    form.setFieldsValue({
      belongCode: '',
      outSource: '',
      outSourceCode: '',
      outSourceAbbr: '',
      netCode: '',
      netName: '',
    });
    if (val) {
      setIdRequired(true);
    } else {
      setIdRequired(false);
    }
    // const belongCode = form.getFieldValue('belongCode');
    // getCompany({ belongCode });
  };
  const getCompanyCode = val => {
    company.forEach(ele => {
      if (ele.outSource === val) {
        form.setFieldsValue({
          outSourceCode: ele.outSourceCode,
          outSourceAbbr: ele.outSourceAbbr,
        });
      }
    });
  };
  const getSectionCode = val => {
    section.forEach(ele => {
      if (ele.deptCode === val) {
        form.setFieldsValue({
          netCode: ele.deptCode,
          netName: ele.deptName,
        });
      }
    });
  };
  // 获取供应商
  // const getCompany = async (data,type) => {
  // if (type) {
  //   setCompany([])
  //   const res = await siteToCompany(data);
  //   if (res.success) {
  //     const list = res.obj &&
  //         res.obj.map(({ outSource, outSourceAbbr, outSourceCode }) => ({outSource,outSourceAbbr,outSourceCode}))||[]
  //     setCompany(list);
  //   } else {
  //     message.error(res.errorMessage);
  //   }
  // } else {
  //   const res = await siteToSFCompany({ zoneCode: data.belongCode });
  //   if (res.success) {
  //     const list = res.obj &&
  //         res.obj.map(({ supplierNo, supplierName }) => ({outSource:supplierName,outSourceAbbr:'',outSourceCode:supplierNo}))||[];
  //     setCompany(list);
  //   } else {
  //     message.error(res.errorMessage);
  //   }
  // }
  // };
  const getCompany = async data => {
    const values = {
      ...data,
      status: 1,
      // pageSize: 100,
      // pageNum: 1,
    };
    const res = await querySuppliers(values);
    if (res.success) {
      const list =
        res.obj.map(({ companyCode, companyName, companyNameShort }) => ({
          outSource: companyName,
          outSourceAbbr: companyNameShort,
          outSourceCode: companyCode,
        })) || [];
      setCompany(list);
    }
  };

  const getSection = async (data, name) => {
    const values = {
      ...data,
    };
    const res = await querySections(values);
    if (res.success) {
      if (res.obj?.length) {
        const list =
          res.obj.map(({ deptCode, deptName }) => ({
            deptCode,
            deptName,
          })) || [];
        setSection(list);
      } else {
        if (name) {
          const siteCode = form.getFieldValue('belongCode');
          setSection([
            {
              deptCode: siteCode,
              deptName: name,
            },
          ]);
        }
      }
    }
  };

  const changeSuppliers = value => {
    form.setFieldsValue({
      outSource: '',
      outSourceCode: '',
      outSourceAbbr: '',
      netCode: '',
      netName: '',
    });

    const siteCode = form.getFieldValue('belongCode');
    getSection({ firstCenterSiteid: siteCode });
  };
  // const getCode = async type => {
  //   const res = await userNoToCode();
  //   if (res.success) {
  //     // setIsRoot(res.obj)
  //     form.setFieldsValue({
  //       belongCode: res.obj,
  //     });
  //     // props.initCode(res.obj)
  //     getCompany({ belongCode: res.obj },type);
  //   }
  // };
  const checkRoot = async () => {
    const res = await checkUserRole();
    if (res.success) {
      setIsRoot(res.obj);
    }
  };
  useEffect(() => {
    // console.log(props.editObj);
    if (props.editStatus) {
      const value = props.editObj.belongCode;
      const type = props.editObj.comAttr === 1 ? 'SX' : 'SF';
      // if (value) {
      //   getCompany({ orgCode: value, srcCode: type });
      // }
      if (isRoot && !props.editObj.comAttr) {
        setIsSF(false);
      } else {
        setIsSF(true);
      }
      if (type === 'SX') {
        setIdRequired(true);
      } else {
        setIdRequired(false);
      }
    } else if (userInfo && userInfo.deptCode) {
      setIdRequired(true);

      form.setFieldsValue({
        belongCode: userInfo.deptCode,
      });
    }
  }, [userInfo.deptCode]);
  useEffect(() => {
    checkRoot();
  }, []);

  return (
    <Modal
      visible={props.showModal}
      onCancel={props.handleHide}
      title={props.title}
      footer={[
        <Button key="back" onClick={props.handleHide}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={props.confirmLoading}
          onClick={handleConfirm}
        >
          确定
        </Button>,
      ]}
    >
      <Form
        {...layout}
        form={form}
        initialValues={{
          comAttr: 1,
          belongCode: props.editStatus ? props.editObj.belongCode : '',
          outSource:
            props.editObj && props.editObj.outSource
              ? props.editObj.outSource
              : '',
          outSourceCode:
            props.editObj && props.editObj.outSourceCode
              ? props.editObj.outSourceCode
              : '',
          outSourceAbbr:
            props.editObj && props.editObj.outSourceAbbr
              ? props.editObj.outSourceAbbr
              : '',
          userName:
            props.editObj && props.editObj.userName
              ? props.editObj.userName
              : '',
          userNo:
            props.editObj && props.editObj.userNo ? props.editObj.userNo : '',
          phoneShow:
            props.editObj &&
            props.editObj.phoneShow &&
            props.editObj.comAttr &&
            props.editObj.comAttr === 1
              ? props.editObj.phoneShow
              : '',
          certificateNoShow:
            props.editObj && props.editObj.certificateNoShow
              ? props.editObj.certificateNoShow
              : '',
          accountStatus:
            (props.editObj && props.editObj.accountStatus === 0) ||
            props.editObj.accountStatus === 1
              ? props.editObj.accountStatus
              : '',
          netCode: props.editObj?.netCode,
          netName: props.editObj?.netName,
        }}
      >
        <Row gutter={{ md: 4, lg: 12, xl: 12 }}>
          <Col md={20} sm={24} xs={24}>
            <Item name="comAttr" label="公司属性">
              <Select
                disabled={!isRoot || !isSF || props.editStatus}
                onChange={getComVal}
              >
                {companyAttr.map(ele => (
                  <Option value={ele.value} key={ele.value}>
                    {ele.text}
                  </Option>
                ))}
              </Select>
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item name="belongCode" label="中转场">
              <DeptSearch
                // onChange={getChangeVal}
                getItem={getItem}
                onChange={changeSuppliers}
                disabled={!isSF || props.editStatus}
                allowClear={false}
              ></DeptSearch>
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="netCode"
              label="所属部门"
              rules={[{ required: true, message: '请选择' }]}
            >
              <Select onChange={getSectionCode}>
                {section.map(ele => (
                  <Option
                    key={`${ele.deptCode}${ele.deptName}`}
                    value={ele.deptCode}
                  >
                    {ele.deptName}
                  </Option>
                ))}
              </Select>
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item name="netName" label="所属部门" style={{ display: 'none' }}>
              <Input />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="outSource"
              label="外包供应商"
              rules={[{ required: true, message: '请选择' }]}
            >
              <Select
                onChange={getCompanyCode}
                disabled={!isSF || props.editStatus}
              >
                {company.map(ele => (
                  <Option
                    key={`${ele.outSourceCode}${ele.outSource}${ele.outSourceAbbr}`}
                    value={ele.outSource}
                  >
                    {ele.outSource}
                  </Option>
                ))}
              </Select>
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item name="outSourceCode" label="外包供应商编码">
              <Input
                placeholder="请输入内容"
                disabled={idRequired ? true : props.editStatus}
              />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item name="outSourceAbbr" label="外包供应商简称">
              <Input
                placeholder="请输入内容"
                disabled={idRequired ? true : !isSF || props.editStatus}
              />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="userName"
              label="姓名"
              rules={[{ required: true, message: '请输入内容!' }]}
            >
              <Input placeholder="请输入内容" />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item name="userNo" label="工号">
              <Input
                placeholder="请输入内容"
                disabled={!isSF || props.editStatus}
              />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="phoneShow"
              label="手机号码"
              rules={[
                { required: true, message: '请输入内容!' },
                // {
                //   pattern: new RegExp(/^1[3456789]\d{9}$/, 'g'),
                //   message: '请输入正确的手机号码 !',
                // },
              ]}
            >
              <Input placeholder="请输入内容" maxLength={11} />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="certificateNoShow"
              label="身份证号码"
              rules={[
                {
                  required: idRequired,
                  message: '请输入内容!',
                },
                {
                  pattern: reg,
                  message: '请输入正确的身份证号码 !',
                },
              ]}
            >
              <Input
                placeholder="请输入内容"
                maxLength={18}
                disabled={!isSF || props.editStatus}
              />
            </Item>
          </Col>
          {props.editStatus && (
            <Col md={20} sm={24} xs={24}>
              <Item name="accountStatus" label="账号状态">
                <Select disabled={!isSF}>
                  {status.map(ele => (
                    <Option value={ele.value} key={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
          )}
        </Row>
      </Form>
    </Modal>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(SearchForm);
