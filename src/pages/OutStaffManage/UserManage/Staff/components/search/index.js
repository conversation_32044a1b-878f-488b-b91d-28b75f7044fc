/* eslint-disable react/destructuring-assignment */
import React, { useEffect, useState } from 'react';
import { Form, Input, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import NetSearch from 'src/components/NetSelect';
import SuppliersSearch from 'src/components/SuppliersSearch';

import DeptSearch from 'src/components/DeptSearch';
import { checkUserRole } from 'src/services/clock';
// import ExportButton from '@/components/ExportButton';
// import AsyncExport from '@/components/AsyncExport';
import AuthButton from 'src/components/AuthButton';
import authDecorator from 'src/components/AuthDecorator';
// import styles from '../../style.less';

const { Item } = Form;
const { Option } = Select;

const SearchForm = props => {
  const [form] = Form.useForm();

  const [allocateList, setAllocateList] = useState([]); // 分拨区
  const { userInfo, moduleCode, selectedRows, datas } = props;
  const [userLimit, setUserLimit] = useState(false);
  // const formRef = useRef(null)
  // console.log(userInfo.deptCode)
  const companyAttr = [
    {
      value: 1,
      text: '顺心',
    },
  ];

  //  查询
  const onFinish = values => {
    props.handleSearch(values);
  };

  // 新增
  const handleCreate = () => {
    props.handleCreate();
  };
  // 修改
  const handleEdit = () => {
    props.handleEdit();
  };
  // 删除
  const handleDel = () => {
    props.handleDel();
  };
  //   重置
  const resetForm = () => {
    form.resetFields();
  };
  const exportList = type => {
    // console.log(type)
    props.handleExport(type);
  };
  const checkRoot = async () => {
    const res = await checkUserRole();
    if (res.success) {
      // setIsRoot(res.obj);
      props.initRoot(res.obj);
    }
  };
  const startUsing = () => {
    props.start();
  };
  const stopUsing = () => {
    props.stop();
  };
  const getItem = list => {
    const siteCode = form.getFieldValue('belongCode');
    const site = list.filter(ele => ele.value === siteCode);
    const name = site.length > 0 ? site[0].deptName : '';
    // if (site.length > 0) {
    //   if (site[0].orgCode !== 'SX') {
    //     form.setFieldsValue({
    //       allocationAreaCode: site[0].areaCode,
    //     });
    //   }
    // }
    props.initCode(siteCode, name);
  };
  const getList = list => {
    setAllocateList(list);
  };
  useEffect(() => {
    checkRoot();
    // queryUserIds();
  }, []);
  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);
  useEffect(() => {
    if (userInfo.deptCode) {
      const num = userInfo.orgCode === 'SX' ? 1 : 0;
      form.setFieldsValue({
        belongCode: userInfo.deptCode,
      });
      props.initOrgCode(num);
      setUserLimit(num === 0);
    }
  }, [userInfo.deptCode]);

  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        accountStatus: 1,
        comAttr: 1,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              {/* disabled={!isRoot} */}
              <Item name="comAttr" label="公司属性">
                <Select placeholder="请选择">
                  {companyAttr.map(ele => (
                    <Option value={ele.value} key={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  placeholder="请输入分拨区名称或代码"
                  extraParam={{
                    typeLevels: ['2'],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                  }}
                  showSearch
                  // disabled={!isRoot}
                  getList={getList}
                />
              </Item>
            </Col>
            {/* disabled={!limit} */}
            <Col {...colStyle}>
              <Item name="belongCode" label="中转场">
                <DeptSearch getItem={getItem} />
              </Item>
            </Col>

            <Col {...colStyle}>
              <Item name="userNo" label="工号">
                <Input placeholder="请输入工号" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="userName" label="姓名">
                <Input placeholder="请输入姓名" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="accountStatus" label="账号状态">
                <Select>
                  <Option value="" key="">
                    全部
                  </Option>
                  <Option value={1} key={1}>
                    启用
                  </Option>
                  <Option value={0} key={0}>
                    禁用
                  </Option>
                </Select>
              </Item>
            </Col>
            <Item
              shouldUpdate={(prevValues, curValues) =>
                prevValues.belongCode !== curValues.belongCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <Item name="outSource" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      isName
                      allowClear
                      gysParams={
                        getFieldValue('belongCode') || userInfo.deptCode
                      }
                    />
                  </Item>
                </Col>
              )}
            </Item>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="staff-addItem"
          style={{ marginRight: 15 }}
          onClick={handleCreate}
        >
          新增
        </AuthButton>
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="staff-edit"
          style={{ marginRight: 15 }}
          disabled={selectedRows.length !== 1}
          onClick={handleEdit}
        >
          修改
        </AuthButton>
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="staff-delete"
          style={{ marginRight: 15 }}
          onClick={handleDel}
          disabled={userLimit && datas.list && datas.list.length < 1}
        >
          删除
        </AuthButton>
        {!userLimit && (
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="staff-start"
            style={{ marginRight: 15 }}
            onClick={startUsing}
          >
            启用
          </AuthButton>
        )}
        {!userLimit && (
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="staff-stop"
            style={{ marginRight: 15 }}
            onClick={stopUsing}
          >
            禁用
          </AuthButton>
        )}
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="staff-export-some"
          style={{ marginRight: 15 }}
          onClick={() => exportList('some')}
          loading={props.btnLoading[0]}
          disabled={selectedRows.length < 1}
        >
          导出所选
        </AuthButton>
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="staff-export-all"
          loading={props.btnLoading[1]}
          style={{ marginRight: 15 }}
          onClick={() => exportList('all')}
          disabled={datas.list && datas.list.length < 1}
        >
          导出全部
        </AuthButton>
      </div>
      {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
