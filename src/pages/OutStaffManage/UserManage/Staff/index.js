/* eslint-disable no-return-assign */
import React, { Component, useState } from 'react';
import { Modal, message } from 'antd';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import moment from 'moment';
import StandardTable from 'src/components/StandardTable';
import {
  queryOutUser,
  addUserInfo,
  editUserInfo,
  delUserInfo,
  exportUserList,
  asyncExportUserList,
  stopUsing,
  startUsing,
  updateSF,
} from 'src/services/outUserManage';
import CreateAndEdit from './components/createAndEdit';
import styles from './style.less';
import SearchForm from './components/search';
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
function ShowEye(props) {
  const { title } = props;
  const [show, setShow] = useState(false);
  const showPass = () => {
    setShow(!show);
    // console.log(props);
    props.showPass(props.title);
  };

  return (
    <div>
      <span style={{ paddingRight: 10 }}>{title}</span>
      {show && <EyeOutlined onClick={() => showPass()} />}
      {!show && <EyeInvisibleOutlined onClick={() => showPass()} />}
    </div>
  );
}
class UserManage extends Component {
  state = {
    orgCode: '',
    code: '',
    editName: '',
    siteName: '',
    selectedRows: [],
    loading: false,
    obj: {
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    },
    searchValues: '',
    showModal: false, // 是否show弹窗
    title: '',
    editStatus: false,
    editObj: '',
    btnLoading: [false, false],
    isRoot: false,
    confirmLoading: false, // 弹窗确认loading
    showTable: true,
  };

  columns = [
    {
      title: '公司属性',
      dataIndex: 'comAttr',
      width: 100,
      render: text => (text ? '顺心' : '顺丰'),
    },
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 150,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 150,
    },
    {
      title: '中转场代码',
      dataIndex: 'belongCode',
      width: 120,
    },
    {
      title: '中转场',
      dataIndex: 'branchName',
      width: 180,
    },
    {
      title: '所属部门',
      dataIndex: 'netName',
      width: 180,
    },
    {
      title: '工号',
      dataIndex: 'userNo',
      width: 100,
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 100,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: 80,
      render: text => text || '',
    },
    {
      title: '供应商',
      dataIndex: 'outSource',
      width: 220,
    },
    {
      title: '外包供应商简称',
      dataIndex: 'outSourceAbbr',
      width: 150,
    },
    {
      title: '外包供应商编码',
      dataIndex: 'outSourceCode',
      width: 150,
    },
    {
      title: <ShowEye showPass={e => this.showPass(e)} title="手机号码" />,
      dataIndex: 'phoneShow',
      width: 150,
    },
    {
      title: <ShowEye showPass={e => this.showPass(e)} title="身份证号码" />,
      dataIndex: 'certificateNoShow',
      width: 200,
    },
    {
      title: '账号状态',
      dataIndex: 'accountStatus',
      width: 100,
      render: text => (text ? '启用' : '禁用'),
    },
    {
      title: '创建人',
      dataIndex: 'create',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '修改人',
      dataIndex: 'modify',
      width: 100,
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      width: 180,
      render: timeTrans,
    },
  ];

  // 所属网点传code
  handleInit = (code, siteName) => {
    this.setState({ code, siteName });
  };

  // 获取默认的公司属性
  handleInitOrgCode = orgCode => {
    this.setState({ orgCode });
  };

  // 获取是否有权限
  handleInitRoot = isRoot => {
    this.setState({ isRoot });
  };

  // 修改时网点传code
  handleInitEdit = name => {
    this.setState({ editName: name });
  };

  handleSearch = (values, isWord) => {
    const searchValues = {
      ...values,
      userNo: values.userNo?.trim(),
      userName: values.userName?.trim(),
    };
    // const { belongCode } = values;
    const { obj } = this.state;
    const { current, pageSize } = obj.pagination;
    // if (!belongCode) {
    //   message.error('网点名称必填');
    //   return;
    // }
    if (!isWord) {
      this.setState(
        {
          decryptPhone: false,
          decryptCertificates: false,
          showTable: false,
        },
        () => {
          this.setState({
            showTable: true,
          });
        },
      );
    }
    const data = {
      ...searchValues,
      pageNum: isWord ? current : 1,
      pageSize: isWord ? pageSize : 10,
    };
    this.setState({
      searchValues,
      selectedRows: [],
    });
    this.getOutUser(data);
  };

  // 新增
  handleCreate = () => {
    this.setState({
      showModal: true,
      title: '新增',
      editStatus: false,
      editObj: '',
      confirmLoading: false,
    });
  };

  // 修改
  handleEdit = () => {
    const { selectedRows } = this.state;
    // console.log(selectedRows);
    if (selectedRows.length === 1) {
      // const { phone } = selectedRows[0];
      // if (!phone) {
      //   message.error('暂无权限修改该条数据');
      //   return false;
      // }
      this.setState({
        showModal: true,
        title: '修改',
        editStatus: true,
        confirmLoading: false,
        editObj: selectedRows[0],
      });
    } else if (selectedRows.length < 1) {
      message.error('请选择一条数据后，再修改');
    } else {
      message.error('仅能选择一条数据，在修改');
    }
  };

  // 关闭弹窗
  handleHide = () => {
    this.setState({
      showModal: false,
    });
  };

  // 确定修改或者新增
  handleConfirm = values => {
    const { editStatus, editObj, siteName, editName } = this.state;
    const { certificateNoShow } = editObj;
    if (editStatus) {
      const { selectedRows } = this.state;
      const { id } = selectedRows[0];
      values.branchName = values.branchName ? values.branchName : editName;
      const isChange = certificateNoShow !== values.certificateNo;
      this.editUser({ ...values, isChange, id });
    } else {
      values.branchName = values.branchName ? values.branchName : siteName;
      this.addUser({ ...values });
    }
  };

  // 删除数据
  handleDel = () => {
    const { selectedRows, isRoot, code } = this.state;
    if (selectedRows.length > 0) {
      const siteCode = selectedRows.map(ele => ele.belongCode);
      const filterSite = siteCode.filter(ele => code === ele);
      const sameLength = siteCode.length === filterSite.length;
      if (!selectedRows[0].comAttr) {
        message.error('顺丰外包数据暂不支持删除');
        return false;
      }
      if (isRoot || sameLength) {
        const ids = selectedRows.map(ele => ele.userNo);
        const idList = selectedRows.map(ele => ele.extId);

        Modal.confirm({
          title: '提示',
          content: '确定删除?',
          className: styles.modalCenter,
          cancelText: '取消',
          okText: '确定',
          onOk: () => {
            this.delUser({ ids, idList });
          },
        });
      } else {
        message.error('无权限删除数据');
      }
    } else {
      message.error('请选择数据');
    }
  };

  // 选择数据
  handleSelectRows = rows => {
    // console.log('选中');
    // console.log(rows);
    this.setState({
      selectedRows: rows,
    });
  };

  // 导出
  handleExport = type => {
    const {
      searchValues,
      selectedRows,
      decryptPhone,
      decryptCertificates,
    } = this.state;

    let data;
    if (type === 'some') {
      if (selectedRows.length < 1) {
        Modal.error({
          content: '请选择数据',
        });
        return;
      }
      const filterArr = selectedRows.filter(
        (ele, index, arr) => ele.belongCode === arr[0].belongCode,
      );
      if (selectedRows[0].comAttr) {
        const ids = selectedRows.map(ele => ele.extId);
        this.exportSomeList({
          ids,
          comAttr: selectedRows[0].comAttr,
          decryptPhone,
          decryptCertificates,
        });
      } else if (
        filterArr.length === selectedRows.length &&
        !selectedRows[0].comAttr
      ) {
        const ids = selectedRows.map(ele => ele.userNo);
        this.exportSomeList({
          ids,
          comAttr: selectedRows[0].comAttr,
          belongCode: selectedRows[0].belongCode,
          decryptPhone,
          decryptCertificates,
        });
      } else {
        message.error('顺丰数据暂不支持多个网点导出');
      }
    } else {
      const { obj } = this.state;
      const { list } = obj;
      if (list.length < 1) {
        message.error(`无数据可导`);
        return;
      }
      data = { ...searchValues, decryptPhone, decryptCertificates };
      this.asyncExportList(data);
    }
  };

  downloadBlob = (blob_, fileName) => {
    const blobUrl = window.URL.createObjectURL(blob_);
    const a = document.createElement('a');
    a.href = blobUrl;
    a.target = '_blank';
    a.style.display = 'none';
    document.body.appendChild(a);
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(blobUrl);
    document.body.removeChild(a);
  };

  // 翻页
  handleStandardTableChange = pages => {
    const { pageSize, current } = pages;
    // const { searchValues } = this.state;
    // const data = {
    //   ...searchValues,
    //   pageSize,
    //   pageNum: current,
    // };
    // this.setState({
    //   obj: {
    //     pagination: {
    //       current,
    //       pageSize,
    //     },
    //   },
    // });
    this.setState(
      {
        showTable: false,
        decryptPhone: false,
        decryptCertificates: false,
        obj: {
          pagination: {
            pageSize,
            current,
          },
        },
      },
      () => {
        this.setState({
          showTable: true,
        });

        const { searchValues } = this.state;
        const data = {
          ...searchValues,
          decryptPhone: false,
          decryptCertificates: false,
          pageNum: current,
          pageSize,
        };
        this.getOutUser(data);
      },
    );
  };

  // 查询列表
  getOutUser = async data => {
    const { pageNum, pageSize } = data;
    this.setState({
      obj: {
        list: [],
        pagination: {
          pageSize: 10,
          current: 1,
          total: 0,
        },
      },
      loading: true,
      selectedRows: [],
    });

    const res = await queryOutUser(data).finally(() =>
      this.setState({ loading: false }),
    );
    if (res && res.success) {
      const { obj } = res;
      if (obj) {
        const { list = [], total = 0 } = obj;
        if (obj.list) {
          obj.list.forEach((ele, ind) => (ele.only = `${ele.userNo}_${ind}`));
        }
        this.setState({
          obj: {
            list: list || [],
            pagination: {
              pageSize,
              current: pageNum,
              total,
            },
          },
          selectedRows: [],
        });
      }
    }
  };

  // 修改
  editUser = async data => {
    const { searchValues } = this.state;
    this.setState({
      confirmLoading: true,
    });
    const { comAttr, phone, userNo } = data;
    const requestUrl = comAttr
      ? editUserInfo(data)
      : updateSF({ userNo, phone });
    const res = await requestUrl.finally(() => {
      this.setState({
        confirmLoading: false,
      });
    });

    if (res.success) {
      message.success('修改成功');
      this.handleHide();
      this.getOutUser({
        pageSize: 10,
        pageNum: 1,
        ...searchValues,
      });
    } else {
      message.error(res.errorMessage);
    }
  };

  // 新增
  addUser = async data => {
    const { searchValues, code, orgCode } = this.state;
    this.setState({
      confirmLoading: true,
    });
    const res = await addUserInfo(data).finally(() =>
      this.setState({
        confirmLoading: false,
      }),
    );

    if (res.success) {
      message.success('新增成功');
      this.handleHide();
      this.getOutUser({
        pageSize: 10,
        pageNum: 1,
        ...searchValues,
        belongCode: searchValues.belongCode ? searchValues.belongCode : code,
        comAttr: searchValues.comAttr ? searchValues.comAttr : orgCode,
      });
    }
  };

  handleStart = async () => {
    const { searchValues, selectedRows } = this.state;
    if (selectedRows.length > 0) {
      if (!selectedRows[0].comAttr) {
        message.error('顺丰外包数据暂不支持启用');
        return false;
      }
      const ids = selectedRows.map(ele => ele.id);
      const idList = selectedRows.map(ele => ele.extId);
      const res = await startUsing({ ids, idList });
      if (res.success) {
        message.success('启用成功');
        this.getOutUser({
          pageSize: 10,
          pageNum: 1,
          ...searchValues,
        });
      }
    } else {
      message.error('请选择数据');
    }
  };

  handleStop = async () => {
    const { searchValues, selectedRows } = this.state;
    if (selectedRows.length > 0) {
      if (!selectedRows[0].comAttr) {
        message.error('顺丰外包数据暂不支持禁用');
        return false;
      }
      const idList = selectedRows.map(ele => ele.extId);
      const ids = selectedRows.map(ele => ele.id);
      const res = await stopUsing({ ids, idList });
      if (res.success) {
        message.success('禁用成功');
        this.getOutUser({
          pageSize: 10,
          pageNum: 1,
          ...searchValues,
        });
      }
    } else {
      message.error('请选择数据');
    }
  };

  // 删除
  delUser = async data => {
    this.setState({ loading: true });
    try {
      await delUserInfo(data).finally(() => this.setState({ loading: false }));
      await delUserInfo(data);
      const { searchValues } = this.state;
      message.success('删除成功');
      this.getOutUser({
        pageSize: 10,
        pageNum: 1,
        ...searchValues,
      });
    } catch (err) {
      console.log(err);
    }
    // finally {
    //   this.setState({ loading: false });
    // }
  };

  // 导出所选
  exportSomeList = async data => {
    this.setState({
      btnLoading: [true, false],
    });
    const res = await exportUserList(data);
    this.setState({
      btnLoading: [false, false],
    });
    this.downloadBlob(res, '外包用户管理列表.xls');
  };

  // 异步全部导出
  asyncExportList = async data => {
    this.setState({
      btnLoading: [false, true],
    });
    const res = await asyncExportUserList(data);
    this.setState({
      btnLoading: [false, false],
    });
    this.downloadBlob(res, '外包用户管理列表.xlsx');
  };

  // 加密与解密
  showPass = type => {
    const flag = type === '手机号码' ? 'decryptPhone' : 'decryptCertificates';
    this.setState(
      prevState => ({
        [flag]: !prevState[flag],
      }),
      () => {
        const {
          searchValues,
          decryptPhone,
          decryptCertificates,
          obj,
        } = this.state;
        const values = { ...searchValues, decryptPhone, decryptCertificates };
        if (obj.list.length > 0) {
          this.handleSearch(values, true);
        }
      },
    );
  };

  render() {
    const {
      selectedRows,
      loading,
      obj,
      showModal,
      title,
      editStatus,
      editObj,
      btnLoading,
      confirmLoading,
      showTable,
    } = this.state;
    return (
      <div className="table-list">
        <div className="tableListForm">
          <SearchForm
            handleCreate={this.handleCreate}
            handleEdit={this.handleEdit}
            handleDel={this.handleDel}
            handleSearch={this.handleSearch}
            handleExport={this.handleExport}
            initCode={this.handleInit}
            initOrgCode={this.handleInitOrgCode}
            initRoot={this.handleInitRoot}
            btnLoading={btnLoading}
            start={this.handleStart}
            stop={this.handleStop}
            selectedRows={selectedRows}
            datas={obj}
          />
          {showTable && (
            <StandardTable
              size="small"
              selectedRows={selectedRows}
              data={obj}
              columns={this.columns}
              multiple
              loading={loading}
              onSelectRow={this.handleSelectRows}
              rowKey="only"
              onChange={this.handleStandardTableChange}
            />
          )}

          {showModal && (
            <CreateAndEdit
              showModal={showModal}
              handleHide={this.handleHide}
              handleConfirm={this.handleConfirm}
              editStatus={editStatus}
              editObj={editObj}
              title={title}
              initCode={this.handleInitEdit}
              confirmLoading={confirmLoading}
            />
          )}
        </div>
      </div>
    );
  }
}

export default UserManage;
