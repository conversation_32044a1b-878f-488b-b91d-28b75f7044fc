import React, { useEffect, useState } from 'react';
import { Form, DatePicker, Input, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import NetSearch from 'components/NetSelect';
import AuthButton from 'components/AuthButton';
const { Item } = Form;
const { Option } = Select;

const SearchForm = props => {
  const [form] = Form.useForm();
  const {
    userInfo,
    selectedRows,
    handleEmpower,
    handleStatus,
    moduleCode,
    handleSearch,
  } = props;
  const [allocateList, setAllocateList] = useState([]); // 分拨区
  const status = [
    {
      value: 0,
      text: '有效',
    },
    {
      value: 1,
      text: '失效',
    },
    {
      value: 2,
      text: '禁用',
    },
  ];
  const companyAttr = [
    {
      value: 'SF',
      text: '顺丰',
    },
    {
      value: 'SX',
      text: '顺心',
    },
  ];
  const onFinish = () => {
    form.validateFields().then(nameList => {
      if (nameList) {
        if (nameList.startDate) {
          nameList.startDate = nameList.startDate.format('x');
        }
        if (nameList.endDate) {
          nameList.endDate = nameList.endDate.format('x');
        }
        handleSearch(nameList);
      }
    });
  };
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };
  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        srcCode: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
        zoneCode: userInfo.deptCode,
      });
    }
  }, [userInfo.deptCode]);

  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);

  return (
    <Form {...formItemLayout} onFinish={onFinish} form={form}>
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item name="srcCode" label="公司属性">
                <Select placeholder="请选择">
                  {companyAttr.map(ele => (
                    <Option key={ele.value} value={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  placeholder="请选择"
                  extraParam={{
                    typeLevels: ['2'],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                  }}
                  showSearch={false}
                  getList={getList}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="zoneCode" label="中转场">
                <NetSearch
                  placeholder="请选择"
                  extraParam={{
                    excludeOverseasFlag: 1,
                  }}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="supplierCode" label="供应商">
                <Input placeholder="请选择" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="userName" label="企业用户">
                <Input placeholder="请输入" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="userId" label="用户ID">
                <Input placeholder="请输入" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="startDate" label="生效时间">
                <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="endDate" label="失效时间">
                <DatePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="status" label="状态">
                <Select placeholder="请选择" allowClear>
                  {status.map(ele => (
                    <Option key={ele.value} value={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>

      <div className="btn-con">
        <div>
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="power_setting-empower"
            style={{ marginRight: 15 }}
            onClick={() => handleEmpower()}
            disabled={selectedRows.length !== 1}
          >
            授权
          </AuthButton>
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="power_setting-start"
            style={{ marginRight: 15 }}
            onClick={() => handleStatus('启用')}
            disabled={selectedRows.length !== 1 || selectedRows[0].status === 0}
          >
            启用
          </AuthButton>
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="power_setting-stop"
            style={{ marginRight: 15 }}
            onClick={() => handleStatus('禁用')}
            disabled={selectedRows.length !== 1 || selectedRows[0].status === 2}
          >
            禁用
          </AuthButton>
        </div>
        {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
      </div>
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(SearchForm);
