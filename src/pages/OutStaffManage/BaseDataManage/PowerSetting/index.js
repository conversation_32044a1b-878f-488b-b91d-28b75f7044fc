import React, { useState, useEffect } from 'react';
import { message, Modal } from 'antd';
import StandardTable from '@/components/StandardTable';
import request from '@/utils/request';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import moment from 'moment';
import authDecorator from '@/components/AuthDecorator';
import ModalFrom from './components/modal';
import SearchForm from './components/search';
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const ShowEye = props => {
  const [show, setShow] = useState(false);
  const showWord = () => {
    const curShow = !show;
    setShow(curShow);
    // console.log(props);
    props.showPass(curShow);
  };

  return (
    <div>
      <span style={{ paddingRight: 10 }}>身份证号码</span>
      {show && <EyeOutlined onClick={() => showWord()} />}
      {!show && <EyeInvisibleOutlined onClick={() => showWord()} />}
    </div>
  );
};
const Apis = {
  queryList: data =>
    request(`/ospmSupplierServices/supplierUserRest/query`, {
      method: 'POST',
      body: data,
    }),
  empower: data =>
    request(`/ospmSupplierServices/supplierUserRest/authorized`, {
      method: 'POST',
      body: data,
    }),
  limitStauts: data =>
    request(`/ospmSupplierServices/supplierUserRest/stauts`, {
      method: 'POST',
      body: data,
    }),
};

const PageIndex = props => {
  const { moduleCode } = props;
  const columns = [
    {
      title: '分拨区代码',
      align: 'center',
      ellipsis: true,
      dataIndex: 'allocationAreaCode',
      width: 100,
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      dataIndex: 'allocationArea',
      width: 100,
    },
    {
      title: '中转场代码',
      align: 'center',
      ellipsis: true,
      dataIndex: 'zoneCode',
      width: 120,
    },
    {
      title: '中转场',
      align: 'center',
      ellipsis: true,
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '供应商编码',
      align: 'center',
      ellipsis: true,
      dataIndex: 'supplierCode',
      width: 150,
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      dataIndex: 'supplierName',
      width: 180,
    },
    {
      title: '企业用户',
      align: 'center',
      ellipsis: true,
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '用户ID',
      align: 'center',
      ellipsis: true,
      dataIndex: 'userId',
      width: 150,
    },
    {
      title: <ShowEye showPass={v => showPass(v)} />,
      dataIndex: 'userCertificateNo',
      align: 'center',
      ellipsis: true,
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      ellipsis: true,
      width: 100,
      render: text => ['有效', '失效', '禁用'][text],
    },
    {
      title: '授权生效时间',
      align: 'center',
      ellipsis: true,
      dataIndex: 'startDate',
      width: 180,
      render: timeTrans,
    },
    {
      title: '授权失效时间',
      align: 'center',
      ellipsis: true,
      dataIndex: 'endDate',
      width: 180,
      render: timeTrans,
    },
    {
      title: '账号禁用时间',
      align: 'center',
      ellipsis: true,
      dataIndex: 'disableTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '账号启用时间',
      align: 'center',
      ellipsis: true,
      dataIndex: 'ableTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '创建人',
      align: 'center',
      ellipsis: true,
      dataIndex: 'creator',
      width: 150,
    },
    {
      title: '账号创建时间',
      align: 'center',
      ellipsis: true,
      dataIndex: 'createTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '操作人',
      align: 'center',
      ellipsis: true,
      dataIndex: 'modifier',
      width: 150,
    },
    {
      title: '操作时间',
      align: 'center',
      ellipsis: true,
      dataIndex: 'modifyTime',
      width: 180,
      render: timeTrans,
    },
  ];
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });
  const [decrypt, setDecrypt] = useState(false); // 默认加密
  const [isFirst, setIsFirst] = useState(true);
  const [searchValues, setSearchValues] = useState({}); // 查询入参
  const [selectedRows, setSelectedRows] = useState([]); // 选中数据
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  // 翻页
  const changePage = pages => {
    const { pageSize, current } = pages;
    const data = {
      pageSize,
      pageNum: current,
      ...searchValues,
    };
    queryList(data);
  };
  // 查询
  const handleSearch = (values, isDecrypt) => {
    if (!isDecrypt) {
      const data = {
        ...values,
        decryptCertificates: false,
        pageSize: 10,
        pageNum: 1,
      };
      setSearchValues(values);
      queryList(data);
    } else {
      queryList(values);
    }
  };
  // 加密解密身份
  const showPass = status => {
    setIsFirst(false);
    setDecrypt(status);
  };
  // 授权弹框
  const handleEmpower = () => {
    setVisible(true);
  };
  // 启用/禁用
  const handleStatus = async type => {
    const { id } = selectedRows[0];
    Modal.confirm({
      title: '提示',
      content: `确认${type}`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const res = await Apis.limitStauts({
          id,
          status: type === '启用' ? 0 : 2,
        });
        if (res && res.success) {
          message.success('操作成功');
          const data = {
            pageSize: 10,
            pageNum: 1,
            ...searchValues,
          };
          queryList(data);
        }
      },
      onCancel() {},
    });
  };

  // 选择
  const handleonSelectRow = row => {
    setSelectedRows(row);
  };
  // 隐藏
  const handleHide = () => setVisible(false);
  // 授权信息
  const handleConfirm = values => {
    const { id } = selectedRows[0];
    values.id = id;
    empower(values);
  };
  // 查询
  const queryList = async data => {
    setLoading(true);
    setSelectedRows([]);
    setDatas({
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    });
    const res = await Apis.queryList(data).finally(() => setLoading(false));
    if (res && res.success) {
      const { list = [], total, pageNum, pageSize } = res.obj;
      setDatas({
        list: list || [],
        pagination: {
          pageSize,
          current: pageNum,
          total,
        },
      });
    }
  };

  // 授权
  const empower = async data => {
    const res = await Apis.empower(data).finally(() => setVisible(false));
    if (res && res.success) {
      const { current, pageSize } = datas.pagination;
      const params = {
        ...searchValues,
        pageSize,
        pageNum: current,
      };
      queryList(params);
    }
  };

  useEffect(() => {
    if (!isFirst) {
      const { current, pageSize } = datas.pagination;
      const params = {
        ...searchValues,
        pageSize,
        decryptCertificates: decrypt,
        pageNum: current,
      };
      if (datas.list.length > 0) {
        queryList(params);
      }
    }
  }, [decrypt, isFirst]);
  return (
    <div className="table-list">
      <div className="tableListForm">
        <SearchForm
          handleSearch={handleSearch}
          handleEmpower={handleEmpower}
          handleStatus={handleStatus}
          selectedRows={selectedRows}
          moduleCode={moduleCode}
        />
      </div>

      <StandardTable
        size="small"
        data={datas}
        columns={columns}
        multiple
        loading={loading}
        selectedRows={selectedRows}
        onSelectRow={handleonSelectRow}
        // showSelection={false}
        rowKey="id"
        onChange={changePage}
      />
      {visible && (
        <ModalFrom
          visible={visible}
          handleHide={handleHide}
          handleConfirm={handleConfirm}
        />
      )}
    </div>
  );
};
export default authDecorator(PageIndex);
