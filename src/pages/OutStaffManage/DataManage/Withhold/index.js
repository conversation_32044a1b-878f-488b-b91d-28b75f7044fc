import React, { useState, useEffect } from 'react';
import { Card, message, Modal, Button } from 'antd';
import StandardTable from '@/components/StandardTable';
import moment from 'moment';
import {
  queryList,
  submitItem,
  cancelItem,
  queryHistory,
} from './services/apis';
import SearchForm from './components/search';
// import ModalForm from './components/modal';
import ModalDetail from './components/details';
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const HistoryModal = props => {
  const { visible, handleHide, row } = props;
  const { wbTaskMonthId } = row;
  const [datas, setDatas] = useState({
    list: [],
    pagination: false,
  });
  const [loading, setLoading] = useState(false);

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 50,
    },
    {
      title: '操作项',
      dataIndex: 'opertor',
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作人',
      dataIndex: 'creater',
      width: 150,
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      width: 150,
      render: timeTrans,
    },
  ];
  const getList = async data => {
    setLoading(true);
    const res = await queryHistory(data);
    if (res.success && res.obj) {
      const list = res.obj.map((ele, ind) => {
        ele.index = ind + 1;
        return ele;
      });
      setDatas({
        list,
        pagination: false,
      });
    }
  };
  useEffect(() => {
    if (wbTaskMonthId) {
      getList({ wbTaskMonthId });
    }
  }, [wbTaskMonthId]);
  return (
    <Modal
      visible={visible}
      title="操作记录"
      width={800}
      okText="确定"
      cancelText="取消"
      onOk={() => handleHide()}
      onCancel={() => handleHide()}
    >
      <StandardTable
        size="small"
        data={datas}
        columns={columns}
        showSelection={false}
        loading={loading}
      />
    </Modal>
  );
};

const PageIndex = () => {
  const columns = [
    // {
    //   title: '订单号',
    //   dataIndex: 'wbTaskMonthId',
    //   width: 220,
    //   ellipsis: true,
    // },
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 150,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 150,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '归属月份',
      dataIndex: 'accrueMonth',
      width: 180,
      render: text => (text ? moment(text).format('YYYY-MM') : '-'),
    },

    {
      title: '状态',
      dataIndex: 'withholdingStatusName',
      width: 150,
    },
    // {
    //   title: '预提状态',
    //   dataIndex: 'withholdingStatusName',
    //   width: 150,
    // },
    {
      title: '服务类型',
      dataIndex: 'cooperationModeName',
      width: 150,
    },
    {
      title: '协议组编号',
      dataIndex: 'proTeamNo',
      width: 150,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 150,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '工序',
      dataIndex: 'moduleName',
      width: 150,
    },
    {
      title: '计费单位',
      dataIndex: 'agreemenTypeName',
      width: 150,
    },
    {
      title: '计价方式',
      dataIndex: 'accruePriceAway',
      width: 150,
    },
    {
      title: '合同开始日',
      dataIndex: 'proTeamStartTime',
      width: 150,
      render: timeTrans,
    },
    {
      title: '合同到期日',
      dataIndex: 'proTeamEndTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '业务数量',
      dataIndex: 'totalWeight',
      width: 150,
    },
    {
      title: '考核得分',
      dataIndex: 'acccessScore',
      width: 150,
    },
    {
      title: '考核系数',
      dataIndex: 'acccessNum',
      width: 150,
    },
    {
      title: '补贴',
      dataIndex: 'allowance',
      width: 150,
    },
    {
      title: '扣款',
      dataIndex: 'deduction',
      width: 150,
    },
    {
      title: '奖励',
      dataIndex: 'reward',
      width: 150,
    },
    // {
    //   title: '持续服务奖',
    //   dataIndex: 'serviceReaward',
    //   width: 150,
    // },
    // {
    //   title: '其他费用',
    //   dataIndex: 'otherExpenses',
    //   width: 150,
    // },
    {
      title: '预计预提金额',
      dataIndex: 'withholdingPrice',
      width: 150,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'actions',
      width: 150,
      fixed: 'right',
      render: (text, row) => (
        <div>
          <Button
            size="small"
            type="primary"
            style={{ marginRight: 10 }}
            onClick={() => edit(row)}
            disabled={row.payStatus === 4}
          >
            编辑
          </Button>
        </div>
      ),
    },
  ];
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });

  const [searchValues, setSearchValues] = useState({}); // 查询入参
  const [selectedRows, setSelectedRows] = useState([]); // 选中数据
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [row, setRow] = useState({});
  const [historyModal, setHistoryModal] = useState(false);
  // 翻页
  const changePage = pages => {
    const { pageSize, current } = pages;
    const data = {
      ...searchValues,
      pageSize,
      pageNum: current,
    };
    getList(data);
  };
  // 查询
  const handleSearch = values => {
    const data = {
      ...values,
      pageSize: 10,
      pageNum: 1,
    };
    setSearchValues(values);
    getList(data);
  };

  // 选择
  const handleonSelectRow = item => {
    setSelectedRows(item);
  };

  // 1待提交，2提交审批，3审批中，4审批通过，7失效数据
  // 提交审批
  const handleSumbit = () => {
    if (selectedRows.length > 0) {
      const wbTaskMonthIds = selectedRows.map(ele => ele.wbTaskMonthId);
      const list = selectedRows.filter(ele => ele.payStatus === 4);
      if (list.length > 0) {
        message.error('所选数据存在已结算数据');
        return false;
      }
      Modal.confirm({
        title: '提示',
        content: '确认提交？',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          await submitItem({ wbTaskMonthIds });
          getList({ ...searchValues, pageNum: 1, pageSize: 10 });
          message.success('操作成功');
        },
      });
    } else {
      message.error('请选择数据操作');
    }
  };

  const handleCancel = () => {
    if (selectedRows.length > 0) {
      const wbTaskMonthIds = selectedRows.map(ele => ele.wbTaskMonthId);
      const list = selectedRows.filter(ele => ele.payStatus === 4);
      if (list.length > 0) {
        message.error('所选数据存在已结算数据');
        return false;
      }
      Modal.confirm({
        title: '提示',
        content: '取消预提？',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          await cancelItem({ wbTaskMonthIds });
          getList({ ...searchValues, pageNum: 1, pageSize: 10 });
          message.success('操作成功');
        },
      });
    } else {
      message.error('请选择数据操作');
    }
  };

  // 查询
  const getList = async data => {
    setLoading(true);
    setSelectedRows([]);
    setDatas({
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    });

    const res = await queryList(data).finally(() => setLoading(false));
    if (res && res.success) {
      const { list, total, pageNum, pageSize } = res.obj;
      setDatas({
        list: list || [],
        pagination: {
          pageSize,
          current: pageNum,
          total,
        },
      });
    }
  };
  const edit = item => {
    setRow(item);
    setVisible(true);
  };
  const handleHide = () => setVisible(false);
  const handleConfirm = async () => {
    getList({ ...searchValues, pageNum: 1, pageSize: 10 });
    setVisible(false);
  };
  const handleHideHistory = () => {
    setHistoryModal(false);
  };
  const handleHistory = () => {
    setRow(selectedRows[0]);
    setHistoryModal(true);
  };
  return (
    <Card>
      <SearchForm
        handleSearch={handleSearch}
        handleSumbit={handleSumbit}
        handleCancel={handleCancel}
        selectedRows={selectedRows}
        searchValues={searchValues}
        handleHistory={handleHistory}
        datas={datas}
      />
      <StandardTable
        size="small"
        data={datas}
        columns={columns}
        multiple
        loading={loading}
        selectedRows={selectedRows}
        onSelectRow={handleonSelectRow}
        // showSelection={false}
        rowKey="wbTaskMonthId"
        onChange={changePage}
      />
      {visible && (
        <ModalDetail
          visible={visible}
          handleHide={handleHide}
          handleConfirm={handleConfirm}
          row={row}
        />
      )}
      {historyModal && (
        <HistoryModal
          visible={historyModal}
          handleHide={handleHideHistory}
          row={row}
        />
      )}
    </Card>
  );
};
export default PageIndex;
