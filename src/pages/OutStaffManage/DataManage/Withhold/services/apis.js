import request from '@/utils/request';
// export function queryList(body) {
//   return request(`/ospmAccountService/accountWithholdingRest/query`, {
//     method: 'POST',
//     body,
//   });
// }
// 查询列表
export function queryList(body) {
  return request(`/ospmAccountService/accountWithholdingRest/query`, {
    method: 'POST',
    body,
  });
}
export function queryHistory(body) {
  return request(`/ospmAccountService/accountWithholdingRest/queryOp`, {
    method: 'POST',
    body,
  });
}
// 编辑（旧）
export function editItem(body) {
  return request(`/ospmAccountService/accountWithholdingRest/edit`, {
    method: 'POST',
    body,
  });
}
// 提交预提
export function submitItem(body) {
  return request(
    `/ospmAccountService/accountWithholdingRest/feeConfirmWithholding`,
    {
      method: 'POST',
      body,
    },
  );
}
// 取消预提
export function cancelItem(body) {
  return request(
    `/ospmAccountService/accountWithholdingRest/withholdingCancel`,
    {
      method: 'POST',
      body,
    },
  );
}
