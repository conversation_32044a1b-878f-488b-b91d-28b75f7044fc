import React, { useState, useEffect, useRef, Fragment } from 'react';
import { But<PERSON>, Card, Drawer, message, InputNumber, Input, Form } from 'antd';
import moment from 'moment';
import request from '@/utils/request';
import StandardTable from '@/components/StandardTable';

const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';

const Apis = {
  approval: data =>
    request(`/ospmAccountService/accountWithholdingRest/editNew`, {
      method: 'POST',
      body: data,
    }), // 详情查询
};

const Detail = props => {
  // console.log(props);
  const domRef = useRef();
  const { title, size, row, ...rest } = props;
  const { wbTaskMonthId, visible } = row;
  // const [visible, setVisible] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      pageSize: 10,
      current: 1,
      total: 0,
    },
  });
  // const conditions = [
  //   {
  //     value: 'Z310',
  //     text: '收货,完成对账且收到合格发票7天内付款',
  //   },
  //   {
  //     value: 'Z303',
  //     text: '收货,完成对账且收到合格发票15天内付款',
  //   },
  //   {
  //     value: 'Z305',
  //     text: '收货,完成对账且收到合格发票30天内付款',
  //   },
  //   {
  //     value: 'Z306',
  //     text: '收货,完成对账且收到合格发票45天内付款',
  //   },
  //   {
  //     value: 'Z307',
  //     text: '收货,完成对账且收到合格发票60天内付款',
  //   },
  //   {
  //     value: 'Z311',
  //     text: '收货,完成对账且收到合格发票90天内付款',
  //   },
  //   {
  //     value: 'C2010',
  //     text: '收货,完成对账且开具合格发票7天内付款',
  //   },
  // ];
  // const operateType = {
  //   1: '装车',
  //   2: '卸车',
  //   3: '分拣',
  //   4: '叉车',
  //   5: '木质包装',
  // };
  // const modules = {
  //   0: '装车区',
  //   1: '卸车区',
  //   2: '分拣区',
  //   3: '叉车区',
  //   4: '全流程',
  // };

  // const columns = [
  //   {
  //     title: '分拨区代码',
  //     dataIndex: 'allocationAreaCode',
  //     width: 120,
  //   },
  //   {
  //     title: '分拨区',
  //     dataIndex: 'allocationArea',
  //     width: 180,
  //   },
  //   {
  //     title: '中转场代码',
  //     dataIndex: 'zoneCode',
  //     width: 150,
  //   },
  //   {
  //     title: '中转场',
  //     dataIndex: 'zoneName',
  //     width: 150,
  //   },
  //   {
  //     title: '归属月份',
  //     dataIndex: 'accrueMonth',
  //     width: 180,
  //   },
  //   {
  //     title: '状态',
  //     dataIndex: 'withholdingStatusName',
  //     width: 120,
  //   },
  //   {
  //     title: '甲方公司',
  //     dataIndex: 'aa',
  //     width: 120,
  //   },
  //   {
  //     title: '协议组编码',
  //     dataIndex: 'proTeamNo',
  //     width: 120,
  //   },
  //   {
  //     title: '供应商编码',
  //     dataIndex: 'supplierCode',
  //     width: 120,
  //   },
  //   {
  //     title: '供应商名称',
  //     dataIndex: 'supplierName',
  //     width: 180,
  //   },
  //   {
  //     title: '模块',
  //     dataIndex: 'moduleName',
  //     width: 180,
  //     // render: text => (text ? modules[text] : '-'),
  //   },
  //   {
  //     title: '计费单位',
  //     dataIndex: 'agreemenTypeName',
  //     width: 120,
  //   },
  //   {
  //     title: '计价方式',
  //     dataIndex: 'accruePriceAway',
  //     width: 120,
  //   },
  //   {
  //     title: '合同开始日',
  //     dataIndex: 'proTeamStartTime',
  //     width: 120,
  //     render: timeTrans,
  //   },
  //   {
  //     title: '合同到期日',
  //     dataIndex: 'proTeamEndTime',
  //     width: 120,
  //     render: timeTrans,
  //   },
  //   {
  //     title: '业务数量',
  //     dataIndex: 'totalWeight',
  //     width: 120,
  //   },
  //   {
  //     title: '应结金额（不含扣款）',
  //     dataIndex: 'shouldSettlePrice',
  //     width: 200,
  //   },
  //   {
  //     title: '考核得分',
  //     dataIndex: 'acccessScore',
  //     width: 120,
  //     render: (text, record) => (
  //       <InputNumber
  //         value={text}
  //         size="small"
  //         disabled={record.withholdingStatus }
  //         onChange={e => handleSource(e, 'acccessScore')}
  //       />
  //     ),
  //   },
  //   {
  //     title: '考核扣款金额',
  //     dataIndex: 'acccessDeductPrice',
  //     width: 120,
  //     render: (text, record) => (
  //       <InputNumber
  //         value={text}
  //         size="small"
  //         disabled={record.withholdingStatus }
  //         onChange={e => handleSource(e, 'acccessDeductPrice')}
  //       />
  //     ),
  //   },
  //   {
  //     title: '日常考核扣款（开票前扣款）',
  //     dataIndex: 'timeDeductPrice',
  //     width: 200,
  //     render: (text, record) => (
  //       <InputNumber
  //         value={text}
  //         size="small"
  //         disabled={record.withholdingStatus }
  //         onChange={e => handleSource(e, 'timeDeductPrice')}
  //       />
  //     ),
  //   },
  //   {
  //     title: '奖励',
  //     dataIndex: 'rewardPrice',
  //     width: 120,
  //     render: (text, record) => (
  //       <InputNumber
  //         value={text}
  //         size="small"
  //         disabled={record.withholdingStatus }
  //         onChange={e => handleSource(e, 'rewardPrice')}
  //       />
  //     ),
  //   },
  //   {
  //     title: '补发',
  //     dataIndex: 'reissuePrice',
  //     width: 120,
  //     render: (text, record) => (
  //       <InputNumber
  //         value={text}
  //         size="small"
  //         disabled={record.withholdingStatus }
  //         onChange={e => handleSource(e, 'reissuePrice')}
  //       />
  //     ),
  //   },
  //   {
  //     title: '开票金额',
  //     dataIndex: 'invoicePrice',
  //     width: 120,
  //     render: (text, record) => (
  //       <InputNumber
  //         value={text}
  //         size="small"
  //         disabled={record.withholdingStatus }
  //         onChange={e => handleSource(e, 'invoicePrice')}
  //       />
  //     ),
  //   },
  //   {
  //     title: '3%小规模纳税人费用扣除（开票前扣款）',
  //     dataIndex: 'taxpayerDeductPrice',
  //     width: 300,
  //     render: (text, record) => (
  //       <InputNumber
  //         value={text}
  //         size="small"
  //         disabled={record.withholdingStatus }
  //         onChange={e => handleSource(e, 'taxpayerDeductPrice')}
  //       />
  //     ),
  //   },
  //   {
  //     title: '实际结算金额',
  //     dataIndex: 'actualSettlePrice',
  //     width: 120,
  //   },
  //   {
  //     title: '（预提金额）',
  //     dataIndex: 'withholdingPrice',
  //     width: 150,
  //     render: (text, record) => (
  //       <InputNumber
  //         value={text}
  //         size="small"
  //         disabled={record.withholdingStatus }
  //         onChange={e => handleSource(e, 'withholdingPrice')}
  //       />
  //     ),
  //   },
  //   {
  //     title: '预提与结算差异金额',
  //     dataIndex: 'withholdingSettleDiffPrice',
  //     width: 180,
  //     // render: (text, record) => (
  //     //   <InputNumber
  //     //     value={text}
  //     //     size="small"
  //     //     disabled={record.withholdingStatus !== '4'}
  //     //     onChange={e => handleSource(e, 'withholdingSettleDiffPrice')}
  //     //   />
  //     // ),
  //   },
  //   {
  //     title: '预提与结算差异原因',
  //     dataIndex: 'withholdingSettleDiffDesc',
  //     width: 180,
  //     render: (text, record) => (
  //       <Input
  //         value={text}
  //         size="small"
  //         disabled={record.withholdingStatus }
  //         onChange={e => handleSourceInput(e, 'withholdingSettleDiffDesc')}
  //       />
  //     ),
  //   },
  //   {
  //     title: '税率',
  //     dataIndex: 'taxRate',
  //     width: 180,
  //     render: (text, record) => (
  //       <InputNumber
  //         value={text}
  //         size="small"
  //         disabled={record.withholdingStatus }
  //         onChange={e => handleSource(e, 'taxRate')}
  //       />
  //     ),
  //   },
  //   {
  //     title: '付款条件',
  //     dataIndex: 'payCondition',
  //     width: 120,
  //     render: (text, record) => (
  //       // <Input
  //       //   value={text}
  //       //   size="small"
  //       //   disabled={record.withholdingStatus !== '4'}
  //       //   onChange={e => handleSourceInput(e, 'payCondition')}
  //       // />
  //       <Select
  //         disabled={record.withholdingStatus }
  //         value={text}
  //         onChange={e => handleSourceSelect(e, 'payCondition')}
  //         size="small"
  //         style={{ width: '100%' }}
  //       >
  //         {conditions.map(ele => (
  //           <Option key={ele.value} value={ele.value}>
  //             {ele.text}
  //           </Option>
  //         ))}
  //       </Select>
  //     ),
  //   },
  //   {
  //     title: '备注',
  //     dataIndex: 'remark',
  //     width: 120,
  //     render: (text, record) => (
  //       <Input
  //         value={text}
  //         size="small"
  //         disabled={record.withholdingStatus }
  //         onChange={e => handleSourceInput(e, 'remark')}
  //       />
  //     ),
  //   },
  // ];
  const columns = [
    // {
    //   title: '订单号',
    //   dataIndex: 'wbTaskMonthId',
    //   width: 220,
    //   ellipsis: true,
    // },
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 150,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 150,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '归属月份',
      dataIndex: 'accrueMonth',
      width: 180,
      render: text => (text ? moment(text).format('YYYY-MM') : '-'),
    },

    {
      title: '状态',
      dataIndex: 'payStatusName',
      width: 150,
    },
    // {
    //   title: '预提状态',
    //   dataIndex: 'withholdingStatusName',
    //   width: 150,
    // },
    {
      title: '服务类型',
      dataIndex: 'cooperationModeName',
      width: 150,
    },
    {
      title: '协议组编号',
      dataIndex: 'proTeamNo',
      width: 150,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 150,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '工序',
      dataIndex: 'moduleName',
      width: 150,
    },
    {
      title: '计费单位',
      dataIndex: 'agreemenTypeName',
      width: 150,
    },
    {
      title: '计价方式',
      dataIndex: 'accruePriceAway',
      width: 150,
    },
    {
      title: '合同开始日',
      dataIndex: 'proTeamStartTime',
      width: 150,
      render: timeTrans,
    },
    {
      title: '合同到期日',
      dataIndex: 'proTeamEndTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '业务数量',
      dataIndex: 'totalWeight',
      width: 150,
    },
    {
      title: '考核得分',
      dataIndex: 'acccessScore',
      width: 150,
      render: text => (
        <InputNumber
          value={text}
          size="small"
          min={0}
          onChange={e => handleSource(e, 'acccessScore')}
        />
      ),
    },
    {
      title: '考核系数',
      dataIndex: 'acccessNum',
      width: 150,
    },
    {
      title: '补贴',
      dataIndex: 'allowance',
      width: 150,
      render: text => (
        <InputNumber
          value={text}
          size="small"
          onChange={e => handleSource(e, 'allowance')}
        />
      ),
    },
    {
      title: '扣款',
      dataIndex: 'deduction',
      width: 150,
      render: text => (
        <InputNumber
          value={text}
          size="small"
          onChange={e => handleSource(e, 'deduction')}
        />
      ),
    },
    {
      title: '奖励',
      dataIndex: 'reward',
      width: 150,
      render: text => (
        <InputNumber
          value={text}
          size="small"
          onChange={e => handleSource(e, 'reward')}
        />
      ),
    },
    // {
    //   title: '持续服务奖',
    //   dataIndex: 'serviceReaward',
    //   width: 150,
    //   render: (text, record) => (
    //     <InputNumber
    //       value={text}
    //       size="small"
    //
    //       onChange={e => handleSource(e, 'serviceReaward')}
    //     />
    //   ),
    // },
    // {
    //   title: '其他费用',
    //   dataIndex: 'otherExpenses',
    //   width: 150,
    //   render: (text, record) => (
    //     <InputNumber
    //       value={text}
    //       size="small"
    //
    //       onChange={e => handleSource(e, 'otherExpenses')}
    //     />
    //   ),
    // },
    {
      title: '预计预提金额',
      dataIndex: 'withholdingPrice',
      width: 150,
      // render(text, record) {
      //   console.log(text, record);
      // },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 150,
      render: text => (
        <Input
          value={text}
          size="small"
          onChange={e => handleSourceInput(e, 'remark')}
        />
      ),
    },
  ];
  const handleSource = (e, key) => {
    domRef.current[key] = e;
    const list = [];
    list.push(domRef.current);
    const datasource = {
      list,
      pagination: false,
    };
    setDatas(datasource);
  };
  const handleSourceInput = (e, key) => {
    domRef.current[key] = e.target.value;
    const list = [];
    list.push(domRef.current);
    const datasource = {
      list,
      pagination: false,
    };
    setDatas(datasource);
  };
  const onClose = () => props.handleHide();
  const queryDetail = () => {
    if (row.wbTaskMonthId) {
      domRef.current = row;
      const list = [];
      list.push(row);
      const datasource = {
        list,
        pagination: false,
      };
      setDatas(datasource);
    }
  };
  const handleSubmit = async () => {
    // const data = domRef.current;
    const {
      acccessScore,
      acccessNum,
      allowance,
      deduction,
      reward,
      // serviceReaward,
      // otherExpenses,
      remark,
    } = domRef.current;
    if (!acccessScore) {
      message.error('请填写考核得分');
      return false;
    }
    const data = {
      wbTaskMonthId,
      opertor: 0,
      acccessScore,
      acccessNum,
      allowance,
      deduction,
      reward,
      // serviceReaward,
      // otherExpenses,
      remark,
    };
    setBtnLoading(true);
    const res = await Apis.approval(data).finally(() => setBtnLoading(false));
    if (res && res.success) {
      props.handleConfirm();
      message.success('保存成功');
    }
  };

  useEffect(() => {
    if (wbTaskMonthId) {
      queryDetail();
    }
  }, [wbTaskMonthId]);

  return (
    <Fragment>
      <Drawer
        visible={visible}
        placement="right"
        onClose={onClose}
        title={title}
        width={(document.body.offsetWidth * 4) / 5}
        footer={[
          <Button
            style={{ marginRight: 15 }}
            onClick={() => props.handleHide()}
          >
            取消
          </Button>,
          <Button
            type="primary"
            onClick={() => handleSubmit()}
            loading={btnLoading}
          >
            保存
          </Button>,
        ]}
        footerStyle={{ display: 'flex', justifyContent: 'center' }}
        {...rest}
      >
        <Card>
          <Form>
            <StandardTable
              columns={columns}
              data={datas}
              size="small"
              showSelection={false}
              rowKey="id"
            />
          </Form>
        </Card>
      </Drawer>
    </Fragment>
  );
};

export default Detail;
