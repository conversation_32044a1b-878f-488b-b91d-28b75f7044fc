import React, { useEffect, useState } from 'react';
import { Form, Button, DatePicker, Input, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle } from 'ky-giant';
import NetSearch from 'src/components/NetSelect';
import AsyncExport from 'src/components/AsyncExport';
import AuthButton from 'src/components/AuthButton';
import ExportButton from 'src/components/ExportButton';
import authDecorator from 'src/components/AuthDecorator';
// import SupplierList from '@/components/SuppliersSelect';
import styles from '../style.less';
const { Item } = Form;
const { Option } = Select;

const SearchForm = props => {
  const [form] = Form.useForm();
  const {
    userInfo,
    selectedRows,
    handleSumbit,
    handleCancel,
    datas,
    searchValues,
    moduleCode,
    handleHistory,
    handleSearch,
  } = props;
  const [allocateList, setAllocateList] = useState([]); // 分拨区

  const status = [
    {
      value: 0,
      text: '待结算',
    },
    {
      value: 1,
      text: '已提交',
    },
    {
      value: 2,
      text: '生成账单',
    },
    {
      value: 3,
      text: '确认账单',
    },
    {
      value: 4,
      text: '已结算',
    },
  ];

  const types = [
    { value: 1, text: '装车区' },
    { value: 2, text: '卸车区' },
    { value: 3, text: '叉车区' },
    { value: 4, text: '分拣区' },
    { value: 5, text: '全流程' },
  ];
  const companyAttr = [
    {
      value: 'SF',
      text: '顺丰',
    },
    {
      value: 'SX',
      text: '顺心',
    },
  ];
  const onFinish = () => {
    form.validateFields().then(nameList => {
      if (nameList) {
        // if (nameList.workDate) {
        //   nameList.startDate = nameList.workDate[0].format('x');
        //   nameList.endDate = nameList.workDate[1].format('x');
        // }
        if (nameList.accrueMonth) {
          nameList.accrueMonth = nameList.accrueMonth.format('YYYY-MM');
        }
        // delete nameList.workDate;
        handleSearch(nameList);
      }
    });
  };
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };

  const getValues = () => {
    const wbTaskMonthIds = selectedRows.map(ele => ele.wbTaskMonthId);
    return { wbTaskMonthIds };
  };

  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        sourceType: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
        zoneCode: userInfo.deptCode,
      });
    }
  }, [userInfo.deptCode]);

  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);

  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        accrueMonth: moment(moment(new Date()).format('YYYY-MM')),
      }}
    >
      <Row {...rowStyle}>
        <Col {...colStyle}>
          <Item name="sourceType" label="公司属性">
            {/* disabled={!isRoot} */}
            <Select>
              {companyAttr.map(ele => (
                <Option value={ele.value}>{ele.text}</Option>
              ))}
            </Select>
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="allocationAreaCode" label="分拨区">
            <NetSearch
              extraParam={{
                typeLevels: ['2'],
                hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
              }}
              showSearch={false}
              getList={getList}
              // disabled={!isRoot}
            />
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="zoneCode" label="中转场">
            <NetSearch
              extraParam={{
                excludeOverseasFlag: 1,
              }}
            />
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="module" label="工序">
            <Select allowClear>
              {types.map(ele => (
                <Option key={ele.value} value={ele.value}>
                  {ele.text}
                </Option>
              ))}
            </Select>
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="supplierName" label="供应商">
            <Input placeholder="请输入供应商" />
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="accrueMonth" label="归属月份">
            <DatePicker picker="month" style={{ width: '100%' }} />
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="payStatusName" label="状态">
            <Select allowClear>
              {status.map(ele => (
                <Option key={ele.value} value={ele.value}>
                  {ele.text}
                </Option>
              ))}
            </Select>
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="accruePriceAway" label="结算类型">
            <Select allowClear>
              <Option key="计时" value="计时">
                计时
              </Option>
              <Option key="计重" value="计重">
                计重
              </Option>
            </Select>
          </Item>
        </Col>
      </Row>
      <div style={{ color: '#df2e3f' }}>
        本月预提请在次月1日凌晨前处理完毕,否则影响结算！
      </div>
      <div className={styles.btnCon}>
        <div>
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="withhold-submit"
            style={{ marginRight: 15 }}
            onClick={() => handleSumbit()}
            disabled={selectedRows.length < 1}
          >
            提交预提
          </AuthButton>
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="withhold-recall"
            style={{ marginRight: 15 }}
            onClick={() => handleCancel()}
            disabled={selectedRows.length < 1}
          >
            取消预提
          </AuthButton>

          <ExportButton
            text="导出所选"
            moduleCode={moduleCode}
            code="withhold-export-some"
            disabled={selectedRows.length < 1}
            style={{ marginRight: 15 }}
            options={{
              filename: '列表.xlsx',
              requstParams: [
                `/ospmAccountService/accountWithholdingRest/export`,
                {
                  method: 'POST',
                  body: getValues,
                },
              ],
            }}
          />
          <AsyncExport
            text="导出全部"
            modulecode={moduleCode}
            code="withhold-export-all"
            style={{ marginRight: 15 }}
            disabled={datas.list && datas.list.length < 1}
            options={{
              // total: totalNum,
              filename: '列表.xlsx',
              requstParams: [
                `/ospmAccountService/accountWithholdingRest/exportSync`,
                {
                  method: 'POST',
                  body: { ...searchValues },
                },
              ],
            }}
          />
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="withhold-history"
            style={{ marginRight: 15 }}
            onClick={() => handleHistory()}
            disabled={selectedRows.length !== 1}
          >
            操作记录
          </AuthButton>
        </div>
        <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div>
      </div>
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
