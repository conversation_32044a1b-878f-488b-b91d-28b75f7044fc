import React, { useEffect, useState } from 'react';
import { Form, Row, Col, Modal, Input, InputNumber, Select } from 'antd';
import { connect } from 'dva';
const { TextArea } = Input;
const { Item } = Form;
const { Option } = Select;
const layout = {
  labelCol: { md: 10, sm: 24, xs: 24 },
  wrapperCol: { md: 14, sm: 24, xs: 24 },
};
const ModalForm = props => {
  const [form] = Form.useForm();
  const { visible, title = '修改', handleHide, handleConfirm, row } = props;
  const { remark, floatingAmount, payCondition, taxRate, wbTaskMonthId } = row;
  const [hide, setHide] = useState(false);
  const conditions = [
    {
      value: 'Z310',
      text: '收货,完成对账且收到合格发票7天内付款',
    },
    {
      value: 'Z303',
      text: '收货,完成对账且收到合格发票15天内付款',
    },
    {
      value: 'Z305',
      text: '收货,完成对账且收到合格发票30天内付款',
    },
    {
      value: 'Z306',
      text: '收货,完成对账且收到合格发票45天内付款',
    },
    {
      value: 'Z307',
      text: '收货,完成对账且收到合格发票60天内付款',
    },
    {
      value: 'Z311',
      text: '收货,完成对账且收到合格发票90天内付款',
    },
    {
      value: 'C2010',
      text: '收货,完成对账且开具合格发票7天内付款',
    },
  ];
  const handleOk = () => {
    form.validateFields().then(namelist => {
      if (namelist) {
        const data = {
          ...namelist,
          wbTaskMonthId,
        };
        handleConfirm(data);
      }
    });
  };
  useEffect(() => {
    if (visible) {
      setHide(true);
    }
  }, [visible]);
  return (
    <Modal
      width={600}
      visible={hide}
      title={title}
      onCancel={() => handleHide()}
      onOk={() => handleOk()}
    >
      <Form
        {...layout}
        form={form}
        initialValues={{
          remark,
          floatingAmount,
          taxRate,
          payCondition,
        }}
      >
        <Row gutter={{ md: 4, lg: 12, xl: 12 }}>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="floatingAmount"
              label="浮动金额"
              rules={[{ required: true, message: '请输入内容' }]}
            >
              <Input />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="taxRate"
              label="税率"
              rules={[{ required: true, message: '请输入内容' }]}
            >
              <InputNumber style={{ width: '100%' }} />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="payCondition"
              label="付款条件"
              rules={[{ required: true, message: '请选择' }]}
            >
              <Select>
                {conditions.map(ele => (
                  <Option key={ele.value}>{ele.text}</Option>
                ))}
              </Select>
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="remark"
              label="备注"
              rules={[{ required: true, message: '请输入内容' }]}
            >
              <TextArea maxLength={200} />
            </Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default connect(state => ({
  userInfo: state.global.userInfo,
}))(ModalForm);
