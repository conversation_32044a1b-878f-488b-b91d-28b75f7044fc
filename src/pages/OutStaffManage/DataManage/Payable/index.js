import React, { Component, useEffect, useState } from 'react';
import { Modal, message, Button } from 'antd';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import request from '@/utils/request';
import Search from './components/search';

import ShowDetail from './components/detail';
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';

const Apis = {
  queryList: data =>
    request(`/ospmAccountService/accountWithholdingRest/query`, {
      method: 'POST',
      body: data,
    }),
  queryHistory: data =>
    request(`/ospmAccountService/accountWithholdingRest/queryOp`, {
      method: 'POST',
      body: data,
    }),
  approval: data =>
    request(`/ospmAccountService/wbTaskMonthSettleRest/submitSettle`, {
      method: 'POST',
      body: data,
    }), // 提交至结算
  cancelApproval: data =>
    request(`/ospmAccountService/accountWithholdingRest/feeConfirmCancel`, {
      method: 'POST',
      body: data,
    }), // 提交至结算
};
const HistoryModal = props => {
  const { visible, handleHide, row } = props;
  const { wbTaskMonthId } = row;
  const [datas, setDatas] = useState({
    list: [],
    pagination: false,
  });
  const [loading, setLoading] = useState(false);

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 50,
    },
    {
      title: '操作项',
      dataIndex: 'opertor',
      width: 150,
      ellipsis: true,
    },
    {
      title: '操作人',
      dataIndex: 'creater',
      width: 150,
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      width: 150,
      render: timeTrans,
    },
  ];
  const getList = async data => {
    setLoading(true);
    const res = await Apis.queryHistory(data).finally(() => setLoading(false));
    if (res && res.success && res.obj) {
      const list = res.obj.map((ele, ind) => {
        ele.index = ind + 1;
        return ele;
      });
      setDatas({
        list,
        pagination: false,
      });
    }
  };
  useEffect(() => {
    if (wbTaskMonthId) {
      getList({ wbTaskMonthId });
    }
  }, [wbTaskMonthId]);
  return (
    <Modal
      visible={visible}
      title="操作记录"
      width={800}
      okText="确定"
      cancelText="取消"
      onOk={() => handleHide()}
      onCancel={() => handleHide()}
    >
      <StandardTable
        size="small"
        data={datas}
        columns={columns}
        showSelection={false}
        loading={loading}
      />
    </Modal>
  );
};
class CountWeight extends Component {
  state = {
    selectedRows: [],
    loading: false,
    obj: {
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    },
    btnLoading: false,
    searchValue: '',
    detailModal: false,
    isEdit: false,
    detailRow: {},
    historyModal: false,
  };

  columns = [
    // {
    //   title: '订单号',
    //   dataIndex: 'wbTaskMonthId',
    //   width: 220,
    //   ellipsis: true,
    // },
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 150,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 150,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '归属月份',
      dataIndex: 'accrueMonth',
      width: 180,
      render: text => (text ? moment(text).format('YYYY-MM') : '-'),
    },
    {
      title: '状态',
      dataIndex: 'payStatusName',
      width: 150,
    },
    // {
    //   title: '预提状态',
    //   dataIndex: 'withholdingStatusName',
    //   width: 150,
    // },
    {
      title: '服务类型',
      dataIndex: 'cooperationModeName',
      width: 150,
    },
    {
      title: '协议组编号',
      dataIndex: 'proTeamNo',
      width: 150,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 150,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '工序',
      dataIndex: 'moduleName',
      width: 150,
    },
    {
      title: '计费类型',
      dataIndex: 'agreemenTypeName',
      width: 150,
    },
    {
      title: '计价方式',
      dataIndex: 'accruePriceAway',
      width: 150,
    },
    {
      title: '合同开始日',
      dataIndex: 'proTeamStartTime',
      width: 150,
      render: timeTrans,
    },
    {
      title: '合同到期日',
      dataIndex: 'proTeamEndTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '业务数量',
      dataIndex: 'totalWeight',
      width: 150,
    },
    {
      title: '考核得分',
      dataIndex: 'acccessScore',
      width: 150,
    },
    {
      title: '考核系数',
      dataIndex: 'acccessNum',
      width: 150,
    },
    {
      title: '补贴',
      dataIndex: 'allowance',
      width: 150,
    },
    {
      title: '扣款',
      dataIndex: 'deduction',
      width: 150,
    },
    {
      title: '奖励',
      dataIndex: 'reward',
      width: 150,
    },
    // {
    //   title: '持续服务奖',
    //   dataIndex: 'serviceReaward',
    //   width: 150,
    // },
    // {
    //   title: '其他费用',
    //   dataIndex: 'otherExpenses',
    //   width: 150,
    // },
    {
      title: '预计预提金额',
      dataIndex: 'withholdingPrice',
      width: 150,
    },
    {
      title: '预计应付金额',
      dataIndex: 'amountPayable',
      width: 150,
    },
    {
      title: '付款金额',
      dataIndex: 'paymentAmount',
      width: 150,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 160,
      fixed: 'right',
      render: (text, row) => (
        <div>
          <Button
            size="small"
            onClick={e => this.ShowDetail(e, row, 'edit')}
            style={{ marginRight: 10 }}
            disabled={row.payStatus === 4}
          >
            编辑
          </Button>
          {/* <Button size="small" onClick={e => this.ShowDetail(e, row, 'look')}>
            查看详情
          </Button> */}
        </div>
      ),
    },
  ];

  handleSearch = values => {
    this.setState({
      searchValue: values,
    });
    const data = {
      ...values,
      pageNum: 1,
      pageSize: 10,
    };
    this.getList(data);
  };

  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };

  ShowDetail = (e, row, edit) => {
    e.stopPropagation();
    if (edit === 'edit') {
      this.setState({
        isEdit: true,
      });
    } else {
      this.setState({
        isEdit: false,
      });
    }
    this.setState({
      detailModal: true,
      detailRow: row,
    });
  };

  hideModal = () => {
    this.setState({
      detailModal: false,
    });
  };

  handleConfirm = () => {
    const { searchValue } = this.state;
    this.hideModal();
    this.getList({ ...searchValue, pageNum: 1, pageSize: 10 });
  };

  handleHideHistory = () => {
    this.setState({
      historyModal: false,
    });
  };

  handleHistory = () => {
    this.setState({
      historyModal: true,
    });
  };

  handleApprove = () => {
    const { selectedRows, searchValue } = this.state;
    if (selectedRows.length > 0) {
      const len = selectedRows.filter(ele => ele.payStatus === 4);
      const wbTaskMonthIds = selectedRows.map(ele => ele.wbTaskMonthId);
      if (len.length > 0) {
        message.error('所选数据存在已结算数据');
        return false;
      }
      Modal.confirm({
        title: '提示',
        content: '确认提交至结算',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          await Apis.approval({ wbTaskMonthIds });
          message.success('已提交至外包结算，请知悉');
          const data = {
            ...searchValue,
            pageNum: 1,
            pageSize: 10,
          };
          this.getList(data);
        },
      });
    } else {
      message.error('请选择数据');
    }
  };

  handleCancel = () => {
    const { selectedRows, searchValue } = this.state;
    if (selectedRows.length > 0) {
      const len = selectedRows.filter(ele => ele.payStatus === 4);
      const wbTaskMonthIds = selectedRows.map(ele => ele.wbTaskMonthId);
      if (len.length > 0) {
        message.error('所选数据存在已结算数据');
        return false;
      }
      Modal.confirm({
        title: '提示',
        content: '确认取消结算订单',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          await Apis.cancelApproval({ wbTaskMonthIds });
          message.success('取消操作成功，请知悉');
          const data = {
            ...searchValue,
            pageNum: 1,
            pageSize: 10,
          };
          this.getList(data);
        },
      });
    } else {
      message.error('请选择数据');
    }
  };

  changePage = pages => {
    const { current, pageSize } = pages;
    const { searchValue } = this.state;
    this.setState({
      obj: {
        pagination: {
          pageSize,
          current,
        },
      },
    });
    const data = {
      ...searchValue,
      pageNum: current,
      pageSize,
    };
    this.getList(data);
  };

  // 列表查询
  getList = async data => {
    const { pageNum, pageSize } = data;
    this.setState({
      loading: true,
    });

    const res = await Apis.queryList(data).finally(() =>
      this.setState({
        loading: false,
      }),
    );
    if (res && res.obj) {
      const { list = [], total } = res.obj;
      this.setState({
        obj: {
          list: list || [],
          pagination: {
            pageSize,
            current: pageNum,
            total,
          },
        },
        selectedRows: [],
      });
    }
  };

  render() {
    const {
      btnLoading,
      selectedRows,
      obj,
      loading,
      detailModal,
      isEdit,
      detailRow,
      historyModal,
      searchValue,
    } = this.state;
    return (
      <div className="table-list">
        <div className="tableListForm">
          <Search
            handleSearch={this.handleSearch}
            loading={btnLoading}
            selectedRows={selectedRows}
            datas={obj}
            approveStep={this.handleStep}
            searchValues={searchValue}
            handleApprove={this.handleApprove}
            handleCancel={this.handleCancel}
            handleHistory={this.handleHistory}
          />
        </div>
        <StandardTable
          size="small"
          selectedRows={selectedRows}
          data={obj}
          columns={this.columns}
          multiple
          loading={loading}
          onSelectRow={this.handleSelectRows}
          rowKey="wbTaskMonthId"
          onChange={this.changePage}
        />
        {detailModal && (
          <ShowDetail
            row={detailRow}
            visible={detailModal}
            isEdit={isEdit}
            handleHide={this.hideModal}
            handleConfirm={this.handleConfirm}
          />
        )}
        {historyModal && (
          <HistoryModal
            visible={historyModal}
            handleHide={this.handleHideHistory}
            row={selectedRows[0]}
          />
        )}
      </div>
    );
  }
}
export default CountWeight;
