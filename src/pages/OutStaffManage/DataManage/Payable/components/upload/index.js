/* eslint-disable indent */
import React, { useState, Fragment } from 'react';
import { Button, message, Upload, Modal, Divider } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import ExportButton from '@/components/ExportButton';
import Config from '@/config';
const { Dragger } = Upload;

const UploadFiles = props => {
  const {
    actionUrl,
    modalName,
    modalUrl,
    title = '上传附件',
    headers,
    params,
    handleSyncImport,
    ...rest
  } = props;
  const [visible, setVisible] = useState(false);
  const [fileList, setFileList] = useState([]);
  const showModal = () => {
    setVisible(true);
  };
  const propsUpload = {
    name: 'file',
    multiple: false,
    accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    action: actionUrl,
    headers: {
      credentials: 'include',
      userId: sessionStorage.userid,
      'sgs-userid': sessionStorage.userid,
      systemKey: Config.systemKey,
      'gw-bdus-rid': sessionStorage.roleId || '',
      ...headers,
    },
    fileList,
    data: params,
    onChange(info) {
      const { status, response } = info.file;
      if (status !== 'uploading') {
        // console.log(info.file, info.fileList);
      }
      if (status === 'done') {
        // if (response.success) {
        //   setFileList(info.fileList);
        //   handleSyncImport();
        //   clearFlies();
        //   message.success(`${info.file.name}上传成功`);
        // } else {
        //   message.error(response.errorMessage);
        // }
        if (response.success && response.obj.sync) {
          const {
            countError,
            resultList = [],
            countTotal,
            resultDesc,
            // lifecycle,
          } = response.obj;
          const successCount = countTotal - countError;
          Modal.info({
            title: '导入结果查看',
            okText: '确认',
            content: (
              <div>
                <div>上传明细：</div>
                <div>
                  总数:
                  <span
                    style={{
                      color: 'red',
                    }}
                  >
                    {countTotal}
                  </span>
                  条
                </div>
                <div>
                  导入成功:
                  <span
                    style={{
                      color: 'red',
                    }}
                  >
                    {successCount}
                  </span>
                  条
                </div>
                <div>
                  导入失败:<span className="detail-wrap">{countError}</span>条
                </div>
                {(resultDesc || countError > 0) && (
                  <Fragment>
                    <div className="detail-title">失败明细：</div>
                    <div
                      style={{
                        maxHeight: '400px',
                        color: 'red',
                        overflow: 'scroll',
                      }}
                    >
                      {resultDesc && <div>{resultDesc}</div>}
                      {resultList && resultList.length
                        ? resultList.map(err => (
                            <div
                              key={err.rowNumber}
                            >{`第${err.rowNumber}行，${err.message}`}</div>
                          ))
                        : ''}
                    </div>
                  </Fragment>
                )}
              </div>
            ),
            onOk: () => {
              setFileList(info.fileList);
              handleSyncImport();
              // clearFlies();
            },
          });
        } else {
          message.error(response.errorMessage);
        }
      } else if (status === 'error') {
        message.error(`${info.file.name}上传失败`);
      }
    },
    onRemove(info) {
      console.log(info);
    },
  };
  const clearFlies = () => {
    setVisible(false);
    setVisible(false);
    setFileList([]);
  };
  return (
    <Fragment>
      <Button onClick={showModal} {...rest}>
        {title}
      </Button>

      <Modal
        visible={visible}
        onCancel={() => clearFlies()}
        onOk={() => clearFlies()}
        title={title}
      >
        <Dragger {...propsUpload}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-hint">拖拽或者点击上传Excel附件</p>
        </Dragger>
        <Divider>导入注意事项</Divider>
        <div>
          1.导入时请务必使用页面提供的模版进行导入，导入模版请点击模版下载按钮进行下载。
        </div>
        <div>
          2.在往模版填充数据时请勿改变模版中列的顺序，否则数据将导入错误。
        </div>
        <div>
          3.请勿改变模版中的格式，如模版中最后一列生效日期为日期格式，请勿更改成数据或者文本格式，否则数据将导入失败。
        </div>
        <div style={{ padding: 20, textAlign: 'center' }}>
          <ExportButton
            text="模板下载"
            options={{
              filename: modalName,
              requstParams: [
                modalUrl,
                {
                  method: 'GET',
                },
              ],
            }}
          />
        </div>
      </Modal>
    </Fragment>
  );
};

export default UploadFiles;
