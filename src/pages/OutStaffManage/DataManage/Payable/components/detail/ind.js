import React, { useState, useEffect, useRef, Fragment } from 'react';
import {
  But<PERSON>,
  Card,
  Drawer,
  message,
  InputNumber,
  Input,
  Select,
} from 'antd';
import moment from 'moment';
import request from '@/utils/request';
import StandardTable from '@/components/StandardTable';
import TempUp from '../upload';
const { Option } = Select;
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';

const Apis = {
  queryList: data =>
    request(`/ospmAccountService/wbTaskMonthSettleRest/info`, {
      method: 'POST',
      body: data,
    }), // 详情查询
  approval: data =>
    request(`/ospmAccountService/wbTaskMonthSettleRest/replenish`, {
      method: 'POST',
      body: data,
    }), // 详情查询
};

const Detail = props => {
  // console.log(props);
  const domRef = useRef();
  const { title, size, row, isEdit, ...rest } = props;
  const { wbTaskMonthId, visible } = row;
  // const [visible, setVisible] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      pageSize: 10,
      current: 1,
      total: 0,
    },
  });
  const conditions = [
    {
      value: 'Z310',
      text: '收货,完成对账且收到合格发票7天内付款',
    },
    {
      value: 'Z303',
      text: '收货,完成对账且收到合格发票15天内付款',
    },
    {
      value: 'Z305',
      text: '收货,完成对账且收到合格发票30天内付款',
    },
    {
      value: 'Z306',
      text: '收货,完成对账且收到合格发票45天内付款',
    },
    {
      value: 'Z307',
      text: '收货,完成对账且收到合格发票60天内付款',
    },
    {
      value: 'Z311',
      text: '收货,完成对账且收到合格发票90天内付款',
    },
    {
      value: 'C2010',
      text: '收货,完成对账且开具合格发票7天内付款',
    },
  ];
  // const operateType = {
  //   1: '装车',
  //   2: '卸车',
  //   3: '分拣',
  //   4: '叉车',
  //   5: '木质包装',
  // };
  // const modules = {
  //   0: '装车区',
  //   1: '卸车区',
  //   2: '分拣区',
  //   3: '叉车区',
  //   4: '全流程',
  // };

  const columns = [
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 120,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 180,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '归属月份',
      dataIndex: 'accrueMonth',
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'approvalStatusName',
      width: 120,
    },
    {
      title: '甲方公司',
      dataIndex: 'aa',
      width: 120,
    },
    {
      title: '协议组编码',
      dataIndex: 'proTeamNo',
      width: 120,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 120,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 180,
    },
    {
      title: '模块',
      dataIndex: 'moduleName',
      width: 180,
      // render: text => (text ? modules[text] : '-'),
    },
    {
      title: '计费单位',
      dataIndex: 'agreemenTypeName',
      width: 120,
    },
    {
      title: '计价方式',
      dataIndex: 'accruePriceAway',
      width: 120,
    },
    {
      title: '合同开始日',
      dataIndex: 'proTeamStartTime',
      width: 120,
      render: timeTrans,
    },
    {
      title: '合同到期日',
      dataIndex: 'proTeamEndTime',
      width: 120,
      render: timeTrans,
    },
    {
      title: '业务数量',
      dataIndex: 'totalWeight',
      width: 120,
    },
    {
      title: '应结金额（不含扣款）',
      dataIndex: 'shouldSettlePrice',
      width: 200,
    },
    {
      title: '考核得分',
      dataIndex: 'acccessScore',
      width: 120,
      render: (text, record) => (
        <InputNumber
          value={text}
          size="small"
          disabled={record.approvalStatus !== '4' || !isEdit}
          onChange={e => handleSource(e, 'acccessScore')}
        />
      ),
    },
    {
      title: '考核扣款金额',
      dataIndex: 'acccessDeductPrice',
      width: 120,
      render: (text, record) => (
        <InputNumber
          value={text}
          size="small"
          disabled={record.approvalStatus !== '4' || !isEdit}
          onChange={e => handleSource(e, 'acccessDeductPrice')}
        />
      ),
    },
    {
      title: '日常考核扣款（开票前扣款）',
      dataIndex: 'timeDeductPrice',
      width: 200,
      render: (text, record) => (
        <InputNumber
          value={text}
          size="small"
          disabled={record.approvalStatus !== '4' || !isEdit}
          onChange={e => handleSource(e, 'timeDeductPrice')}
        />
      ),
    },
    {
      title: '奖励',
      dataIndex: 'rewardPrice',
      width: 120,
      render: (text, record) => (
        <InputNumber
          value={text}
          size="small"
          disabled={record.approvalStatus !== '4' || !isEdit}
          onChange={e => handleSource(e, 'rewardPrice')}
        />
      ),
    },
    {
      title: '补发',
      dataIndex: 'reissuePrice',
      width: 120,
      render: (text, record) => (
        <InputNumber
          value={text}
          size="small"
          disabled={record.approvalStatus !== '4' || !isEdit}
          onChange={e => handleSource(e, 'reissuePrice')}
        />
      ),
    },
    {
      title: '开票金额',
      dataIndex: 'invoicePrice',
      width: 120,
      render: (text, record) => (
        <InputNumber
          value={text}
          size="small"
          disabled={record.approvalStatus !== '4' || !isEdit}
          onChange={e => handleSource(e, 'invoicePrice')}
        />
      ),
    },
    {
      title: '3%小规模纳税人费用扣除（开票前扣款）',
      dataIndex: 'taxpayerDeductPrice',
      width: 300,
      render: (text, record) => (
        <InputNumber
          value={text}
          size="small"
          disabled={record.approvalStatus !== '4' || !isEdit}
          onChange={e => handleSource(e, 'taxpayerDeductPrice')}
        />
      ),
    },
    {
      title: '实际结算金额',
      dataIndex: 'actualSettlePrice',
      width: 120,
    },
    {
      title: '（预提金额）',
      dataIndex: 'withholdingPrice',
      width: 150,
      render: (text, record) => (
        <InputNumber
          value={text}
          size="small"
          disabled={record.approvalStatus !== '4' || !isEdit}
          onChange={e => handleSource(e, 'withholdingPrice')}
        />
      ),
    },
    {
      title: '预提与结算差异金额',
      dataIndex: 'withholdingSettleDiffPrice',
      width: 180,
      // render: (text, record) => (
      //   <InputNumber
      //     value={text}
      //     size="small"
      //     disabled={record.approvalStatus !== '4'}
      //     onChange={e => handleSource(e, 'withholdingSettleDiffPrice')}
      //   />
      // ),
    },
    {
      title: '预提与结算差异原因',
      dataIndex: 'withholdingSettleDiffDesc',
      width: 180,
      render: (text, record) => (
        <Input
          value={text}
          size="small"
          disabled={record.approvalStatus !== '4' || !isEdit}
          onChange={e => handleSourceInput(e, 'withholdingSettleDiffDesc')}
        />
      ),
    },
    {
      title: '税率',
      dataIndex: 'taxRate',
      width: 180,
      render: (text, record) => (
        <InputNumber
          value={text}
          size="small"
          disabled={record.approvalStatus !== '4' || !isEdit}
          onChange={e => handleSource(e, 'taxRate')}
        />
      ),
    },
    {
      title: '付款条件',
      dataIndex: 'payCondition',
      width: 120,
      render: (text, record) => (
        // <Input
        //   value={text}
        //   size="small"
        //   disabled={record.approvalStatus !== '4'}
        //   onChange={e => handleSourceInput(e, 'payCondition')}
        // />
        <Select
          disabled={record.approvalStatus !== '4' || !isEdit}
          value={text}
          onChange={e => handleSourceSelect(e, 'payCondition')}
          size="small"
          style={{ width: '100%' }}
        >
          {conditions.map(ele => (
            <Option key={ele.value} value={ele.value}>
              {ele.text}
            </Option>
          ))}
        </Select>
      ),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 120,
      render: (text, record) => (
        <Input
          value={text}
          size="small"
          disabled={record.approvalStatus !== '4' || !isEdit}
          onChange={e => handleSourceInput(e, 'remark')}
        />
      ),
    },
  ];
  const handleSource = (e, key) => {
    domRef.current[key] = e;
    const list = [];
    list.push(domRef.current);
    const datasource = {
      list,
      pagination: false,
    };
    setDatas(datasource);
  };
  const handleSourceInput = (e, key) => {
    domRef.current[key] = e.target.value;
    const list = [];
    list.push(domRef.current);
    const datasource = {
      list,
      pagination: false,
    };
    setDatas(datasource);
  };
  const handleSourceSelect = (e, key) => {
    domRef.current[key] = e;
    const list = [];
    list.push(domRef.current);
    const datasource = {
      list,
      pagination: false,
    };
    setDatas(datasource);
  };
  const onClose = () => props.handleHide();
  // const showDrawer = () => {
  //   setVisible(true);
  // };
  // 详情
  const queryDetail = async data => {
    setDatas({
      list: [],
      pagination: {
        pageSize: 10,
        current: 1,
        total: 0,
      },
    });

    const res = await Apis.queryList(data);
    if (res.obj) {
      domRef.current = res.obj;
      const list = [];
      list.push(res.obj);
      const datasource = {
        list,
        pagination: false,
      };
      setDatas(datasource);
    }
  };

  const handleSubmit = async () => {
    // const data = domRef.current;
    const {
      shouldSettlePrice,
      actualSettlePrice,
      acccessScore,
      acccessDeductPrice,
      timeDeductPrice,
      rewardPrice,
      reissuePrice,
      invoicePrice,
      taxpayerDeductPrice,
      withholdingPrice,
      withholdingSettleDiffPrice,
      withholdingSettleDiffDesc,
      taxRate,
      payCondition,
      remark,
    } = domRef.current;
    const data = {
      wbTaskMonthId,
      shouldSettlePrice,
      actualSettlePrice,
      acccessScore,
      acccessDeductPrice,
      timeDeductPrice,
      rewardPrice,
      reissuePrice,
      invoicePrice,
      taxpayerDeductPrice,
      withholdingPrice,
      withholdingSettleDiffPrice,
      withholdingSettleDiffDesc,
      taxRate,
      payCondition,
      remark,
    };
    setBtnLoading(true);
    await Apis.approval(data).finally(() => setBtnLoading(false));
    props.handleConfirm();
    message.success('已保存考核项数据');
  };

  const handleImport = () => {
    queryDetail({ wbTaskMonthId });
  };

  useEffect(() => {
    if (wbTaskMonthId) {
      queryDetail({ wbTaskMonthId });
    }
  }, [wbTaskMonthId]);

  return (
    <Fragment>
      <Drawer
        visible={visible}
        placement="right"
        onClose={onClose}
        title={title}
        width={(document.body.offsetWidth * 4) / 5}
        footer={
          isEdit
            ? [
                <Button
                  style={{ marginRight: 15 }}
                  onClick={() => props.handleHide()}
                >
                  取消
                </Button>,
                <Button
                  type="primary"
                  onClick={() => handleSubmit()}
                  loading={btnLoading}
                  disabled={
                    domRef.current && domRef.current.approvalStatus !== '4'
                  }
                >
                  保存
                </Button>,
              ]
            : null
        }
        footerStyle={{ display: 'flex', justifyContent: 'center' }}
        {...rest}
      >
        <Card>
          <StandardTable
            columns={columns}
            data={datas}
            size="small"
            showSelection={false}
            rowKey="id"
          />
          {isEdit && (
            <TempUp
              title="考核项数据上传"
              actionUrl="/ospmAccountService/wbTaskMonthSettleRest/upload"
              modalUrl="/ospmAccountService/wbTaskMonthSettleRest/downImportTemplate"
              modalName="考核数据模板.xlsx"
              params={{ wbTaskMonthId }}
              handleSyncImport={handleImport}
              style={{ marginRight: 15 }}
              disabled={domRef.current && domRef.current.approvalStatus !== '4'}
            />
          )}
        </Card>
      </Drawer>
    </Fragment>
  );
};

export default Detail;
