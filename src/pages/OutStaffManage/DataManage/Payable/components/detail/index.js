import React, { useState, useEffect, useRef, Fragment } from 'react';
import { But<PERSON>, Card, Drawer, message, InputNumber, Input, Form } from 'antd';
import moment from 'moment';
import request from '@/utils/request';
import StandardTable from '@/components/StandardTable';

const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';

const Apis = {
  approval: data =>
    request(`/ospmAccountService/accountWithholdingRest/editNew`, {
      method: 'POST',
      body: data,
    }), // 详情查询
};

const Detail = props => {
  // console.log(props);
  const domRef = useRef();
  const {
    title,
    size,
    row,
    isEdit,
    handleHide,
    handleConfirm,
    ...rest
  } = props;
  const { wbTaskMonthId, visible } = row;
  // const [visible, setVisible] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      pageSize: 10,
      current: 1,
      total: 0,
    },
  });
  const columns = [
    // {
    //   title: '订单号',
    //   dataIndex: 'wbTaskMonthId',
    //   width: 220,
    //   ellipsis: true,
    // },
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 150,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 150,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '归属月份',
      dataIndex: 'accrueMonth',
      width: 180,
      render: text => (text ? moment(text).format('YYYY-MM') : '-'),
    },

    {
      title: '状态',
      dataIndex: 'payStatusName',
      width: 150,
    },
    // {
    //   title: '预提状态',
    //   dataIndex: 'withholdingStatusName',
    //   width: 150,
    // },
    {
      title: '服务类型',
      dataIndex: 'cooperationModeName',
      width: 150,
    },
    {
      title: '协议组编号',
      dataIndex: 'proTeamNo',
      width: 150,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 150,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '工序',
      dataIndex: 'moduleName',
      width: 150,
    },
    {
      title: '计费单位',
      dataIndex: 'agreemenTypeName',
      width: 150,
    },
    {
      title: '计价方式',
      dataIndex: 'accruePriceAway',
      width: 150,
    },
    {
      title: '合同开始日',
      dataIndex: 'proTeamStartTime',
      width: 150,
      render: timeTrans,
    },
    {
      title: '合同到期日',
      dataIndex: 'proTeamEndTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '业务数量',
      dataIndex: 'totalWeight',
      width: 150,
    },
    {
      title: '考核得分',
      dataIndex: 'acccessScore',
      width: 150,
      render: text => (
        <InputNumber
          value={text}
          size="small"
          min={0}
          onChange={e => handleSource(e, 'acccessScore')}
        />
      ),
    },
    {
      title: '考核系数',
      dataIndex: 'acccessNum',
      width: 150,
    },
    {
      title: '补贴',
      dataIndex: 'allowance',
      width: 150,
      render: text => (
        <InputNumber
          value={text}
          size="small"
          onChange={e => handleSource(e, 'allowance')}
        />
      ),
    },
    {
      title: '扣款',
      dataIndex: 'deduction',
      width: 150,
      render: text => (
        <InputNumber
          value={text}
          size="small"
          onChange={e => handleSource(e, 'deduction')}
        />
      ),
    },
    {
      title: '奖励',
      dataIndex: 'reward',
      width: 150,
      render: text => (
        <InputNumber
          value={text}
          size="small"
          onChange={e => handleSource(e, 'reward')}
        />
      ),
    },
    // {
    //   title: '持续服务奖',
    //   dataIndex: 'serviceReaward',
    //   width: 150,
    //   render: text => (
    //     <InputNumber
    //       value={text}
    //       size="small"
    //       onChange={e => handleSource(e, 'serviceReaward')}
    //     />
    //   ),
    // },
    // {
    //   title: '其他费用',
    //   dataIndex: 'otherExpenses',
    //   width: 150,
    //   render: text => (
    //     <InputNumber
    //       value={text}
    //       size="small"
    //       onChange={e => handleSource(e, 'otherExpenses')}
    //     />
    //   ),
    // },
    {
      title: '预计应付金额',
      dataIndex: 'amountPayable',
      width: 150,
      // render(text, record) {
      //   console.log(text, record);
      // },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 150,
      render: text => (
        <Input
          value={text}
          size="small"
          onChange={e => handleSourceInput(e, 'remark')}
        />
      ),
    },
  ];
  const handleSource = (e, key) => {
    domRef.current[key] = e;
    const list = [];
    list.push(domRef.current);
    const datasource = {
      list,
      pagination: false,
    };
    setDatas(datasource);
  };
  const handleSourceInput = (e, key) => {
    domRef.current[key] = e.target.value;
    const list = [];
    list.push(domRef.current);
    const datasource = {
      list,
      pagination: false,
    };
    setDatas(datasource);
  };
  const onClose = () => props.handleHide();
  const queryDetail = () => {
    if (row.wbTaskMonthId) {
      domRef.current = row;
      const list = [];
      list.push(row);
      const datasource = {
        list,
        pagination: false,
      };
      setDatas(datasource);
    }
  };
  const handleSubmit = async () => {
    const {
      acccessScore,
      acccessNum,
      allowance,
      deduction,
      reward,
      // serviceReaward,
      // otherExpenses,
      remark,
    } = domRef.current;
    if (!acccessScore) {
      message.error('请填写考核得分');
      return false;
    }
    const data = {
      wbTaskMonthId,
      opertor: 2,
      acccessScore,
      acccessNum,
      allowance,
      deduction,
      reward,
      // serviceReaward,
      // otherExpenses,
      remark,
    };
    setBtnLoading(true);
    await Apis.approval(data).finally(() => setBtnLoading(false));
    handleConfirm();
    message.success('保存成功');
  };

  useEffect(() => {
    if (wbTaskMonthId) {
      queryDetail();
    }
  }, [wbTaskMonthId]);

  return (
    <Fragment>
      <Drawer
        visible={visible}
        placement="right"
        onClose={onClose}
        title={title}
        width={(document.body.offsetWidth * 4) / 5}
        footer={
          isEdit
            ? [
                <Button
                  style={{ marginRight: 15 }}
                  onClick={() => handleHide()}
                >
                  取消
                </Button>,
                <Button
                  type="primary"
                  onClick={() => handleSubmit()}
                  loading={btnLoading}
                  // disabled={domRef.current && domRef.current.withholdingStatus}
                >
                  保存
                </Button>,
              ]
            : null
        }
        footerStyle={{ display: 'flex', justifyContent: 'center' }}
        {...rest}
      >
        <Card>
          <Form>
            <StandardTable
              columns={columns}
              data={datas}
              size="small"
              showSelection={false}
              rowKey="wbTaskMonthId"
            />
          </Form>
        </Card>
      </Drawer>
    </Fragment>
  );
};

export default Detail;
