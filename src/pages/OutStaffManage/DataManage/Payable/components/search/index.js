import React, { useEffect, useState } from 'react';
import { Form, DatePicker, Select, Row, Input, Col } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import NetSearch from '@/components/NetSelect';
// import { userNoToCode } from '@/services/clock';
import AsyncExport from '@/components/AsyncExport';
import AuthButton from '@/components/AuthButton';
import ExportButton from '@/components/ExportButton';
import authDecorator from '@/components/AuthDecorator';
// import styles from '../../style.less';

const { Item } = Form;
const { Option } = Select;

const SearchForm = props => {
  const [form] = Form.useForm();

  const {
    userInfo,
    selectedRows,
    moduleCode,
    datas,
    searchValues,
    handleHistory,
  } = props;
  const [allocateList, setAllocateList] = useState([]); // 分拨区
  // console.log(userInfo.deptCode);
  const status = [
    {
      value: 0,
      text: '待结算',
    },
    {
      value: 1,
      text: '已提交',
    },
    {
      value: 2,
      text: '生成账单',
    },
    {
      value: 3,
      text: '确认账单',
    },
    {
      value: 4,
      text: '已结算',
    },
  ];

  const types = [
    { value: 1, text: '装车区' },
    { value: 2, text: '卸车区' },
    { value: 3, text: '叉车区' },
    { value: 4, text: '分拣区' },
    { value: 5, text: '全流程' },
  ];
  const companyAttr = [
    {
      value: 'SF',
      text: '顺丰',
    },
    {
      value: 'SX',
      text: '顺心',
    },
  ];

  //  查询
  const onFinish = values => {
    // console.log('Success:', values);
    if (values.accrueMonth) {
      values.accrueMonth = values.accrueMonth.format('YYYY-MM');
    }
    props.handleSearch(values);
  };

  //   重置
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };

  const getValues = () => {
    const wbTaskMonthIds = selectedRows.map(ele => ele.wbTaskMonthId);
    return { wbTaskMonthIds };
  };
  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        zoneCode: userInfo.deptCode,
        sourceType: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
      });
    }
  }, [userInfo.deptCode]);
  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);

  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        zoneCode: userInfo && userInfo.deptCode,
        accrueMonth: moment(moment(new Date()).format('YYYY-MM')),
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item name="sourceType" label="公司属性">
                {/* disabled={!isRoot} */}
                <Select>
                  {companyAttr.map(ele => (
                    <Option value={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  placeholder="请选择"
                  extraParam={{
                    typeLevels: ['2'],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                  }}
                  showSearch={false}
                  getList={getList}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="zoneCode" label="中转场">
                <NetSearch />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="module" label="工序">
                <Select placeholder="请输入" allowClear>
                  {types.map(ele => (
                    <Option key={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="supplierName" label="供应商">
                <Input placeholder="请输入" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item label="归属月份" name="accrueMonth">
                <DatePicker
                  picker="month"
                  style={{ width: '100%' }}
                  allowClear={false}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="payStatus" label="状态">
                <Select placeholder="请选择" allowClear>
                  {status.map(ele => (
                    <Option key={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="accruePriceAway" label="结算类型">
                <Select placeholder="请选择" allowClear>
                  <Option key="计时" value="计时">
                    计时
                  </Option>
                  <Option key="计重" value="计重">
                    计重
                  </Option>
                </Select>
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>

      <div className="btn-con">
        <div style={{ color: '#df2e3f' }}>
          本月结算请在次月13日凌晨前处理完毕,否则影响结算！
        </div>
        <div>
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="pay_accout-submit"
            style={{ marginRight: 15 }}
            onClick={() => props.handleApprove()}
            disabled={selectedRows.length < 1}
          >
            提交至结算
          </AuthButton>
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="pay_accout-cancel"
            style={{ marginRight: 15 }}
            onClick={() => props.handleCancel()}
            disabled={selectedRows.length < 1}
          >
            取消结算订单
          </AuthButton>
          <ExportButton
            text="导出所选"
            moduleCode={moduleCode}
            code="pay_accout-export-some"
            disabled={selectedRows.length < 1}
            style={{ marginRight: 15 }}
            options={{
              filename: '列表.xlsx',
              requstParams: [
                `/ospmAccountService/accountWithholdingRest/export`,
                {
                  method: 'POST',
                  body: getValues,
                },
              ],
            }}
          />
          <AsyncExport
            text="导出全部"
            modulecode={moduleCode}
            code="pay_accout-export-all"
            style={{ marginRight: 15 }}
            disabled={datas.list && datas.list.length < 1}
            options={{
              // total: totalNum,
              filename: '列表.xlsx',
              requstParams: [
                `/ospmAccountService/accountWithholdingRest/exportSync`,
                {
                  method: 'POST',
                  body: { ...searchValues },
                },
              ],
            }}
          />
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="pay_accout-history"
            style={{ marginRight: 15 }}
            onClick={() => handleHistory()}
            disabled={selectedRows.length !== 1}
          >
            操作记录
          </AuthButton>
        </div>
        {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
      </div>
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
// export default SearchForm;
