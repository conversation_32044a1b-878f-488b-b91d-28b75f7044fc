/* eslint-disable consistent-return */
/* eslint-disable react/no-unused-state */
/* eslint-disable react/destructuring-assignment */
import React, { Component } from 'react';
import { Modal, message, Button, Form, Input } from 'antd';
import moment from 'moment';
import StandardTable from 'src/components/StandardTable';
// import { connect } from 'dva';
import request from 'src/utils/request';
import Search from './components/search';
import UploadFiles from './components/upload';
import ShowDetail from './components/detail';
import ShowStep from './components/step';
const { Item } = Form;
const { TextArea } = Input;
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const Apis = {
  queryList: data =>
    request(`/ospmAccountService/wbTaskMonthRest/query`, {
      method: 'POST',
      body: data,
    }),
  approval: data =>
    request(`/ospmAccountService/wbTaskMonthRest/submitApproval`, {
      method: 'POST',
      body: data,
    }), // 提交审核
  cancelSubmit: data =>
    request(`/ospmAccountService/wbTaskMonthRest/cancelApproval`, {
      method: 'POST',
      body: data,
    }), // 撤销
};

const Suggest = props => {
  const [form] = Form.useForm();
  const { visible, title = '确认' } = props;
  const handleConfirm = () => {
    // const values = form.getFieldsValue();
    form.validateFields().then(namelist => {
      if (namelist) {
        props.handleConfirm(namelist);
      }
    });
  };
  return (
    <Modal
      visible={visible}
      title={title}
      onOk={handleConfirm}
      onCancel={() => props.handleHide()}
    >
      <Form
        form={form}
        initialValues={{
          approvalWord: '',
        }}
      >
        <Item
          label="意见"
          name="approvalWord"
          rules={[
            {
              required: true,
              message: '请输入内容',
            },
          ]}
        >
          <TextArea maxLength={500} />
        </Item>
      </Form>
    </Modal>
  );
};
class CountWeight extends Component {
  state = {
    selectedRows: [],
    loading: false,
    obj: {
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    },
    btnLoading: false,
    searchValue: '',
    stepModal: false,
    detailModal: false,
    detailRow: {},
    fileModal: false,
    fileRow: {},
    showSuggest: false,
    suggestTitle: '',
  };

  columns = [
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 100,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 180,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '归集月份',
      dataIndex: 'accrueMonth',
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'approvalStatusName',
      width: 100,
    },
    {
      title: '归集开始时间',
      dataIndex: 'collectionStartTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '归集结束时间',
      dataIndex: 'collectionEndTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '协议组编码',
      dataIndex: 'proTeamNo',
      width: 100,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 100,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 180,
    },
    {
      title: '任务环节',
      dataIndex: 'operateTypeName',
      width: 180,
    },
    {
      title: '模块',
      dataIndex: 'moduleName',
      width: 100,
    },
    {
      title: '合同类型',
      dataIndex: 'agreemenTypeName',
      width: 100,
    },
    {
      title: '计价方式',
      dataIndex: 'accruePriceAway',
      width: 100,
    },
    {
      title: '协议开始日',
      dataIndex: 'proTeamStartTime',
      width: 100,
      render: timeTrans,
    },
    {
      title: '协议到期日',
      dataIndex: 'proTeamEndTime',
      width: 100,
      render: timeTrans,
    },
    {
      title: '业务数量',
      dataIndex: 'totalWeight',
      width: 100,
    },
    {
      title: '异常原因',
      dataIndex: 'remark',
      width: 100,
      // render: text => text || '-',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
      fixed: 'right',
      render: (text, row) => (
        <div>
          {this.state.searchValue.accrueType === 0 && (
            <Button
              size="small"
              style={{ marginRight: 10 }}
              onClick={e => this.showFiles(e, row)}
              disabled={
                !(
                  row.approvalStatus === '1' ||
                  row.approvalStatus === '5' ||
                  row.approvalStatus === '6'
                )
              }
            >
              添加附件
            </Button>
          )}

          <Button size="small" onClick={e => this.ShowDetail(e, row)}>
            查看详情
          </Button>
        </div>
      ),
    },
  ];

  handleSearch = values => {
    this.setState({
      searchValue: values,
    });
    const data = {
      ...values,
      pageNum: 1,
      pageSize: 10,
    };
    // const data = {
    //   zoneCode: '755A',
    //   supplierCode: 'K755Y',
    //   accrueType: 0,
    // };
    this.getList(data);
  };

  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };

  handleStep = () => {
    const { selectedRows } = this.state;
    if (selectedRows.length === 1) {
      this.setState({ stepModal: true });
    } else {
      message.error('仅能选择一条数据进行操作');
    }
  };

  ShowDetail = (e, row) => {
    e.stopPropagation();
    this.setState({
      detailModal: true,
      detailRow: row,
    });
  };

  hideModal = () => {
    this.setState({
      detailModal: false,
    });
  };

  showFiles = (e, row) => {
    e.stopPropagation();
    this.setState({
      fileModal: true,
      fileRow: row,
    });
  };

  hideFiles = () => {
    this.setState({
      fileModal: false,
    });
  };

  handleApprove = () => {
    const { selectedRows, searchValue } = this.state;
    if (selectedRows.length > 0) {
      const list = selectedRows.filter(ele => ele.approvalStatus === '8');
      if (list.length > 0) {
        message.error('所选的数据存在已提交审核的数据了，请正确选择');
        return false;
      }
      const wbTaskMonthIds = selectedRows.map(ele => ele.taskMonthId);
      Modal.confirm({
        title: '提示',
        content: '确认提交审核？',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          await Apis.approval({ wbTaskMonthIds });
          this.getList({ ...searchValue, pageNum: 1, pageSize: 10 });
          message.success('已提交审核');
        },
      });
    } else {
      message.error('请选择数据操作');
    }
  };

  // 隐藏意见框
  handleSuggest = () => {
    this.setState({ showSuggest: false });
  };

  handleRecall = () => {
    const { selectedRows } = this.state;
    if (selectedRows.length > 0) {
      const list = selectedRows.filter(
        ele => ele.approvalStatus === '2' || ele.approvalStatus === '3',
      );
      if (list.length === selectedRows.length) {
        this.setState({
          showSuggest: true,
          suggestTitle: '确认驳回',
        });
      }
    } else {
      message.error('请正确选择数据操作');
    }
  };

  handleConfirm = async values => {
    const { selectedRows, searchValue } = this.state;
    if (selectedRows.length > 0) {
      const wbTaskMonthIds = selectedRows.map(ele => ele.taskMonthId);
      await Apis.cancelSubmit({
        wbTaskMonthIds,
        ...values,
      }).finally(() => this.setState({ showSuggest: false }));
      message.success('数据状态已变更为撤回');
      this.getList({ ...searchValue, pageNum: 1, pageSize: 10 });
    }
  };

  handleHide = () => {
    this.setState({ stepModal: false });
  };

  changePage = pages => {
    const { current, pageSize } = pages;
    const { searchValue } = this.state;
    this.setState({
      obj: {
        pagination: {
          pageSize,
          current,
        },
      },
    });
    const data = {
      ...searchValue,
      pageNum: current,
      pageSize,
    };
    this.getList(data);
  };

  // 列表查询
  getList = async data => {
    const { pageNum, pageSize } = data;
    this.setState({
      loading: true,
    });
    const res = await Apis.queryList(data).finally(() =>
      this.setState({
        loading: false,
      }),
    );
    if (res.obj) {
      const { list = [], total } = res.obj;
      this.setState({
        obj: {
          list,
          pagination: {
            pageSize,
            current: pageNum,
            total,
          },
        },
        selectedRows: [],
      });
    }
  };

  render() {
    const {
      btnLoading,
      selectedRows,
      obj,
      loading,
      stepModal,
      detailModal,
      detailRow,
      fileRow,
      fileModal,
      showSuggest,
      suggestTitle,
    } = this.state;
    return (
      <div className="table-list">
        <div className="tableListForm">
          <Search
            handleSearch={this.handleSearch}
            loading={btnLoading}
            selectedRows={selectedRows}
            datas={obj}
            approveStep={this.handleStep}
            handleApprove={this.handleApprove}
            handleRecall={this.handleRecall}
          />
          <StandardTable
            size="small"
            selectedRows={selectedRows}
            data={obj}
            columns={this.columns}
            multiple
            loading={loading}
            onSelectRow={this.handleSelectRows}
            rowKey="id"
            onChange={this.changePage}
          />
        </div>
        {stepModal && (
          <ShowStep
            visible={stepModal}
            title="审核进度"
            row={selectedRows[0]}
            handleHide={this.handleHide}
          />
        )}
        {detailModal && (
          <ShowDetail
            row={detailRow}
            visible={detailModal}
            handleHide={this.hideModal}
          />
        )}
        {fileModal && (
          <UploadFiles
            visible={fileModal}
            handleHide={this.hideFiles}
            actionUrl="/ospmAccountService/wbTaskAttachRest/upload"
            params={{ wbTaskMonthId: fileRow.taskMonthId }}
          />
        )}
        {showSuggest && (
          <Suggest
            visible={showSuggest}
            title={suggestTitle}
            handleConfirm={this.handleConfirm}
            handleHide={this.handleSuggest}
          />
        )}
      </div>
    );
  }
}
export default CountWeight;
