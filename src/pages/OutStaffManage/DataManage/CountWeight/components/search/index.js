import React, { useEffect, useState } from 'react';
import { Form, DatePicker, Input, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import NetSearch from 'src/components/NetSelect';
import AuthButton from 'src/components/AuthButton';
import ExportButton from 'src/components/ExportButton';
import authDecorator from 'src/components/AuthDecorator';
import AsyncExport from 'src/components/AsyncExport';
// import { userNoToCode } from '@/services/clock';
// import styles from '../../style.less';

const { Item } = Form;
const { Option } = Select;

const SearchForm = props => {
  const [form] = Form.useForm();
  const {
    userInfo,
    selectedRows,
    datas,
    moduleCode,
    handleSearch,
    handleApprove,
    handleRecall,
    approveStep,
  } = props;
  const [type, setTpye] = useState(0);
  const [limit, setLimit] = useState(true);
  const [searchVal, setSearchVal] = useState({});
  const currentMonth = moment().format('YYYY-MM');
  const [allocateList, setAllocateList] = useState([]);
  const types = [
    { value: 0, text: '装车区' },
    { value: 1, text: '卸车区' },
    { value: 2, text: '叉车区' },
    { value: 3, text: '分拣区' },
    { value: 4, text: '全流程' },
  ];
  // 1待提交，2审核中，3审核通过，4撤销、5驳回
  // 1待提交，2提交审核，3审核中，4审核通过，5撤销、6驳回，7失效数据8，提交结算
  const status = [
    { value: 1, text: '待提交' },
    { value: 3, text: '审核中' },
    { value: 4, text: '审核通过' },
    { value: 5, text: '撤销' },
    { value: 6, text: '驳回' },
  ];
  const companyAttr = [
    {
      value: 'SF',
      text: '顺丰',
    },
    {
      value: 'SX',
      text: '顺心',
    },
  ];
  //  查询
  const onFinish = () => {
    // console.log('Success:', values);
    form.validateFields().then(namelist => {
      if (namelist) {
        const { accrueType } = namelist;
        setTpye(accrueType);
        if (accrueType) {
          setLimit(false);
        } else {
          setLimit(true);
        }
        if (namelist.accrueMonth) {
          namelist.accrueMonth = namelist.accrueMonth.format('YYYY-MM');
        }
        setSearchVal(namelist);
        handleSearch(namelist);
      }
    });
  };

  //   重置
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };
  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        zoneCode: userInfo.deptCode,
        sourceType: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
      });
    }
  }, [userInfo.deptCode]);
  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);
  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        accrueType: 0,
        accrueMonth: moment(moment(new Date()).format('YYYY-MM')),
        dataType: 0,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item name="sourceType" label="公司属性">
                {/* disabled={!isRoot} */}
                <Select>
                  {companyAttr.map(ele => (
                    <Option value={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  placeholder="请选择"
                  extraParam={{
                    typeLevels: ['2'],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                  }}
                  showSearch={false}
                  getList={getList}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="zoneCode" label="中转场">
                <NetSearch />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="module" label="模块">
                <Select allowClear placeholder="请选择">
                  {types.map(ele => (
                    <Option key={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="supplierCode" label="供应商">
                <Input placeholder="请输入" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="approvalStatus" label="状态">
                <Select placeholder="请选择" allowClear>
                  {status.map(ele => (
                    <Option key={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="dataType" label="是否按照班次">
                <Select placeholder="请选择" allowClear>
                  <Option key={1} value={1}>
                    是
                  </Option>
                  <Option key={0} value={0}>
                    否
                  </Option>
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item
                label="归属月份"
                name="accrueMonth"
                rules={[{ required: true, message: '请选择' }]}
              >
                <DatePicker picker="month" style={{ width: '100%' }} />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="accrueType" label="是否异常数据">
                <Select allowClear placeholder="请选择">
                  <Option key={0} value={0}>
                    否
                  </Option>
                  <Option key={1} value={1}>
                    是
                  </Option>
                </Select>
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div style={{ color: '#df2e3f' }}>
          本月数据请在次月6日凌晨前处理完毕,否则影响结算！
        </div>
        {limit && (
          <AuthButton
            type="primary"
            style={{ marginRight: 15 }}
            moduleCode={moduleCode}
            code="count_weight-submit"
            disabled={
              selectedRows.length < 1 || searchVal.accrueMonth === currentMonth
            }
            onClick={() => handleApprove()}
          >
            提交审核
          </AuthButton>
        )}
        {limit && (
          <AuthButton
            type="primary"
            style={{ marginRight: 15 }}
            onClick={() => handleRecall()}
            moduleCode={moduleCode}
            code="count_weight-recall"
            disabled={
              selectedRows.length < 1 || searchVal.accrueMonth === currentMonth
            }
          >
            撤回
          </AuthButton>
        )}
        {limit && (
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="count_weight-step"
            style={{ marginRight: 15 }}
            onClick={() => approveStep()}
            disabled={selectedRows.length !== 1}
          >
            审核进度
          </AuthButton>
        )}
        <ExportButton
          text="导出所选"
          moduleCode={moduleCode}
          code="count_weight-export-some"
          style={{ marginRight: 15 }}
          disabled={selectedRows.length < 1}
          // icon={<ExportOutlined />}
          options={{
            // total: totalNum,
            filename: '计重业务数据列表.xlsx',
            requstParams: [
              `/ospmAccountService/wbTaskMonthRest/wbTaskMonthSyncExport`,
              {
                method: 'POST',
                body: {
                  idList: selectedRows.map(ele => ele.id),
                  accrueType: type,
                },
              },
            ],
          }}
        />
        <AsyncExport
          text="导出全部"
          modulecode={moduleCode}
          code="count_weight-export-all"
          disabled={datas.list && datas.list.length < 1}
          // icon={<ExportOutlined />}
          options={{
            // total: totalNum,
            requstParams: [
              `/ospmAccountService/wbTaskMonthRest/wbTaskMonthAsynExport`,
              {
                method: 'POST',
                body: { ...searchVal },
              },
            ],
          }}
        />
        {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
      </div>
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
// export default SearchForm;
