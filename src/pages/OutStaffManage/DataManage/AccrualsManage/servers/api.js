import request from '@/utils/request';

// 批量删除
export function approvalList(params) {
  return request(`/tdmsAccrueService/accrueManagementPreRest/approvalList`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
// 查询列表接口
export function search(params) {
  return request(`/tdmsAccrueService/accrueManagementPreRest/query`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
// 根据ID查询单价详情（编辑）
export function searchDetail(params) {
  return request(
    `/tdmsAccrueService/accruePriceInfoService/queryAccruePriceInfoById`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 添加计提单价配置
export function add(params) {
  return request(
    `/tdmsAccrueService/accruePriceInfoService/addAccruePriceInfo`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 更新计提单价配置
export function update(params) {
  return request(
    `/tdmsAccrueService/accruePriceInfoService/updateAccruePriceInfo`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

/**
 * @description: 根据网点查询供应商列表
 * @param {type} orgCode
 * @return:
 */
export function supplierSearch(params) {
  return request(
    `/tdmsAccrueService/supplierRestService/querySupplierByZoneCode`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 增减工作量接口
export function updateDataFluctuate(params) {
  return request(
    `/tdmsAccrueService/accrueManagementPreRest/updateDataFluctuate`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 修改接口
export function updateData(params) {
  return request(`/tdmsAccrueService/accrueManagementPreRest/updateData`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
