import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Form,
  Input,
  message,
  Modal,
  Select,
  DatePicker,
  Tooltip,
  Button,
} from 'antd';
import { ExportOutlined, PlusOutlined, RedoOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import { cloneDeep } from 'lodash';
import { success, error } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import ImportButton from 'src/components/ImportButton';
import authDecorator from 'src/components/AuthDecorator';
import AuthButton from 'src/components/AuthButton';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import SuppliersSearch from 'src/components/SuppliersSearch';
import { approvalList, search } from './servers/api';
import Details from './Comoonents/Details';
import request from '@/utils/request';
import {
  statusList,
  operationList,
  accrueTypeList,
  statusListSe,
} from './status';

import style from './index.less';

const { Item: FormItem } = Form;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const rowKey = 'id';

let condition = {};
const Page = ({
  userInfo,
  logRoleCode,
  areaListSF,
  areaListSX,
  moduleCode,
}) => {
  const [form] = Form.useForm();
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [type, setType] = useState('');
  const [userOrg, setUserOrg] = useState('SF');
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  const [summaryData, setSummaryData] = useState({}); // 汇总数据
  const [refreshCustomerCodeLoading, setRefreshCustomerCodeLoading] = useState(
    false,
  );

  /**
   * 获取汇总数据
   */
  const fetchSummaryData = async () => {
    const { obj } = await request(
      `/tdmsAccrueService/accrueManagementPreRest/querySum`,
      {
        method: 'POST',
        body: condition,
      },
    );
    setSummaryData(obj);
  };

  let approvalWord = '';
  const columns = [
    {
      title: '基础信息',
      children: [
        {
          title: '业务单号',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'flowId',
        },
        {
          title: '归属月份',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'yearMonthQr',
        },
        {
          title: '分拨区代码',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'allocationAreaCode',
        },
        {
          title: '分拨区',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'allocationArea',
        },
        {
          title: '网点代码',
          align: 'center',
          ellipsis: true,
          width: 200,
          dataIndex: 'zoneCode',
        },
        {
          title: '网点名称',
          align: 'center',
          ellipsis: true,
          width: 200,
          dataIndex: 'zoneName',
        },
        {
          title: '法人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'zoneCodeLegalPerson',
        },
      ],
    },
    {
      title: '供应商信息',
      children: [
        {
          title: '供应商名称',
          align: 'center',
          ellipsis: true,
          width: 250,
          dataIndex: 'supplierName',
        },
        {
          title: '供应商编码',
          align: 'center',
          ellipsis: true,
          width: 150,
          dataIndex: 'supplierNo',
        },
        {
          title: '客商编码',
          align: 'center',
          ellipsis: true,
          width: 150,
          dataIndex: 'supplierCode',
        },
        {
          title: '合同生效时间',
          align: 'center',
          ellipsis: true,
          width: 150,
          dataIndex: 'startDate',
          render: v => v && moment(v).format('YYYY-MM-DD'),
        },
        {
          title: '合同失效时间',
          align: 'center',
          ellipsis: true,
          width: 150,
          dataIndex: 'endDate',
          render: v => v && moment(v).format('YYYY-MM-DD'),
        },
      ],
    },
    {
      title: '单价信息',
      children: [
        {
          title: '计价方式',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'accrueType',
          render: value => (accrueTypeList[value] ? accrueTypeList[value] : ''),
        },
        {
          title: '计费单位',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'accrueUnit',
        },
        {
          title: '税率',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'taxRate',
        },
        {
          title: '装车单价',
          align: 'center',
          ellipsis: true,
          width: 120,
          dataIndex: 'loadPrice',
        },
        {
          title: '卸车单价',
          align: 'center',
          ellipsis: true,
          width: 120,
          dataIndex: 'unloadPrice',
        },
        {
          title: '叉车单价',
          align: 'center',
          ellipsis: true,
          width: 120,
          dataIndex: 'forkliftPrice',
        },
      ],
    },
    {
      title: '工作量信息',
      children: [
        {
          title: '装车工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'loadWeight',
          render: v => {
            if (v) {
              const val = v.toString();
              if (val.indexOf('.') > -1) {
                return val.substr(0, val.indexOf('.') + 3);
              }
              return v;
            }
            return v;
          },
        },
        {
          title: '卸车工作量',
          align: 'center',
          ellipsis: true,
          width: 120,
          dataIndex: 'unloadWeight',
          render: v => {
            if (v) {
              const val = v.toString();
              if (val.indexOf('.') > -1) {
                return val.substr(0, val.indexOf('.') + 3);
              }
              return v;
            }
            return v;
          },
        },
        {
          title: '装卸合计工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'loadUnloadWeight',
          render: v => {
            if (v) {
              const val = v.toString();
              if (val.indexOf('.') > -1) {
                return val.substr(0, val.indexOf('.') + 3);
              }
              return v;
            }
            return v;
          },
        },
        {
          title: '叉车工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftWeight',
          render: v => {
            if (v) {
              const val = v.toString();
              if (val.indexOf('.') > -1) {
                return val.substr(0, val.indexOf('.') + 3);
              }
              return v;
            }
            return v;
          },
        },
      ],
    },
    {
      title: '预提费用',
      children: [
        {
          title: '装卸预计预提费用',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'loadUnloadAmount',
        },
        {
          title: '叉车预计预提费用',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftAmount',
        },
        {
          title: '含税预计预提总费用',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'totalAmount',
        },
      ],
    },
    {
      title: '操作记录',
      children: [
        {
          title: '提交人',
          align: 'center',
          ellipsis: true,
          width: 200,
          dataIndex: 'submitter',
        },
        {
          title: '提交时间',
          align: 'center',
          ellipsis: true,
          width: 200,
          dataIndex: 'submitTime',
          render: v => v && moment(v).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          title: '审核人',
          align: 'center',
          ellipsis: true,
          width: 200,
          dataIndex: 'approvalUserNo',
        },
        {
          title: '审核时间',
          align: 'center',
          ellipsis: true,
          width: 170,
          dataIndex: 'approvalTime',
          render: v => v && moment(v).format('YYYY-MM-DD HH:mm:ss'),
        },
      ],
    },
    {
      title: '其他',
      children: [
        {
          title: '备注',
          align: 'center',
          ellipsis: true,
          width: 250,
          dataIndex: 'remark',
          render: value => (
            <Tooltip placement="top" title={value}>
              <div
                style={{
                  width: '250px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {value}
              </div>
            </Tooltip>
          ),
        },
        {
          title: '驳回原因',
          align: 'center',
          ellipsis: true,
          width: 250,
          dataIndex: 'approvalWord',
          render: value => (
            <Tooltip placement="top" title={value}>
              <div
                style={{
                  width: '250px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {value}
              </div>
            </Tooltip>
          ),
        },
        {
          title: '是否手动导入',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'excelImport',
          render: value => (value ? '是' : '否'),
        },
        {
          title: '导入人工号',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'excelSubmitter',
        },
        {
          title: '状态',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'approvalStatus',
          render: value => {
            const data = statusList.find(item => item.value === value);
            return data ? data.label : value;
          },
        },
        {
          title: '推送状态 ',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'sendFina',
          render: value => {
            if (value === 0) {
              return '未推送';
            }
            if (value === 1) {
              return '已推送';
            }
            if (value === 2) {
              return '推送失败';
            }
          },
        },
      ],
    },
    {
      title: '操作',
      align: 'center',
      width: 200,
      fixed: 'right',
      dataIndex: 'a',
      render: (v, record) => (
        <Fragment>
          <Button
            type="link"
            onClick={() => {
              add(record);
              setType('details');
            }}
          >
            详情
          </Button>
          {(record.approvalStatus === 0 || record.approvalStatus === 3) &&
            timeIsOk(record.yearMonthQr) && (
              <AuthButton
                moduleCode={moduleCode}
                code="accruals-submit"
                size="small"
                type="link"
                onClick={() => {
                  add(record);
                  setType('submit');
                }}
              >
                提交
              </AuthButton>
            )}
          {record.approvalStatus === 1 && (
            <AuthButton
              size="small"
              moduleCode={moduleCode}
              code="accruals-adopt"
              type="link"
              onClick={() => {
                add(record);
                setType('submit');
              }}
            >
              审核
            </AuthButton>
          )}
          {/* {record.approvalStatus === 1 && (
            <AuthButton
              moduleCode={moduleCode}
              code="accruals-reject"
              size="small"
              type="link"
              onClick={() => {
                add(record);
                setType('submit');
              }}
            >
              驳回
            </AuthButton>
          )} */}
        </Fragment>
      ),
    },
  ];
  // const [columns, setColumns] = useState(columns);

  const getQueryParams = () => {
    const params = form.getFieldValue();
    condition = cloneDeep(params);
    condition.yearMonthQrEnd = moment(condition.yearMonthQr[1]).format(
      'YYYY-MM',
    );
    condition.yearMonthQr = moment(condition.yearMonthQr[0]).format('YYYY-MM');
    condition.sourceType = userInfo.orgCode;

    return {
      ...condition,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    fetchSummaryData();
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      ...condition,
      pageNum: current,
      pageSize,
    };
    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum, total } = obj;
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: pageNum,
              pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const update = async (e, v, approvalStatus) => {
    e.stopPropagation();
    operationModal([v.id], approvalStatus);
  };

  const add = value => {
    setEditingTarget(value);
    setAddModalVisible(true);
  };

  // 判断时间是否合适
  const timeIsOk = v => {
    if (v) {
      const time = v.replace(/-/g, '/');
      if (
        moment().diff(moment(time), 'month') > 0 &&
        moment().diff(moment(time).add(1, 'month'), 'hour') > 12 &&
        moment().diff(moment(time).add(1, 'month'), 'hour') < 15
      ) {
        return true;
      }
      return false;
    }
    return false;
  };

  // 操作
  const operation = (record, approvalStatus) => {
    const idList = [];
    if (record instanceof Array) {
      for (const item of record) {
        if (approvalStatus === 1 && !timeIsOk(item.yearMonthQr)) {
          return message.error(
            '只能选取上月的数据,本月预提请在次月1日13点后15点前提交',
          );
        }
        if (
          approvalStatus === 1 &&
          !(item.approvalStatus === 0 || item.approvalStatus === 3)
        ) {
          return message.error('只能选未提交或者已驳回状态数据');
        }
        if (approvalStatus === 2 && item.approvalStatus !== 1) {
          return message.error('只能选已提交的数据');
        }
        if (approvalStatus === 3 && item.approvalStatus !== 1) {
          return message.error('只能选已提交状态数据');
        }
        idList.push(item.id);
      }
    } else {
      idList.push(record.id);
    }
    operationModal(idList, approvalStatus);
  };

  const operationModal = (idList, approvalStatus) => {
    Modal.confirm({
      title: '提示',
      content: (
        <div>
          <p>{operationList[approvalStatus]}</p>
          {approvalStatus === 3 && (
            <div style={{ display: 'flex' }}>
              <p>
                <span
                  style={{
                    color: '#ff4d4f',
                    fontSize: '14px',
                    marginRight: '4px',
                    fontFamily: 'SimSun, sans-serif',
                  }}
                >
                  *
                </span>
                请反馈原因:
              </p>
              <TextArea
                showCount
                maxLength={200}
                onChange={e => {
                  approvalWord = e.target.value;
                }}
              />
            </div>
          )}
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const params = { idList, approvalStatus, approvalWord };
        if (approvalStatus === 3 && !approvalWord) {
          message.error('请输入原因');
          return new Promise((resolve, reject) => {
            reject();
          });
        }
        const res = await approvalList(params);
        if (res.success) {
          success('操作成功');
          approvalWord = '';
          refresh();
        } else {
          error(res.errorMessage);
        }
      },
      onCancel() {
        approvalWord = '';
      },
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  /**
   * 刷新客户端编码
   * @param {{ id: string }} list 数据
   */
  const refreshCustomerCode = async list => {
    setRefreshCustomerCodeLoading(true);
    try {
      await request(
        `/tdmsAccrueService/accrueManagementPreRest/refreshSupplierNo`,
        {
          method: 'POST',
          body: {
            idList: list.map(item => item.id),
          },
        },
      );
    } finally {
      setRefreshCustomerCodeLoading(false);
    }
    refresh();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="归属月份"
                name="yearMonthQr"
                rules={[
                  {
                    required: true,
                    message: '请选择月份',
                  },
                ]}
              >
                <RangePicker
                  showToday
                  picker="month"
                  allowClear
                  style={{ width: '100%' }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.zoneCode !== curValues.zoneCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <FormItem name="supplierNo" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      allowClear
                      gysParams={getFieldValue('zoneCode') || userInfo.deptCode}
                    />
                  </FormItem>
                </Col>
              )}
            </FormItem>
            <Col {...colStyle}>
              <FormItem label="状态" name="approvalStatus">
                <Select
                  allowClear
                  options={statusListSe}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem name="flowId" label="业务单号">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div className="margin-bottom-12" style={{ color: '#df2e3f' }}>
          上月预提请在本月1日15点前提交；16点前推送至财务系统，否则影响结算！
        </div>
        <div>
          <AuthButton
            icon={<PlusOutlined />}
            moduleCode={moduleCode}
            code="accruals-submit"
            disabled={!selectedRows.length}
            onClick={() => operation(selectedRows, 1)}
            type="primary"
            style={{ marginRight: 15 }}
          >
            提交
          </AuthButton>
          <AuthButton
            icon={<PlusOutlined />}
            moduleCode={moduleCode}
            code="accruals-adopt"
            disabled={!selectedRows.length}
            onClick={() => operation(selectedRows, 2)}
            type="primary"
            style={{ marginRight: 15 }}
          >
            审批通过
          </AuthButton>
          <AuthButton
            icon={<PlusOutlined />}
            moduleCode={moduleCode}
            code="accruals-reject"
            disabled={!selectedRows.length}
            onClick={() => operation(selectedRows, 3)}
            type="primary"
            style={{ marginRight: 15 }}
          >
            驳回
          </AuthButton>
          <AsyncExport
            text="导出"
            modulecode={moduleCode}
            code="accruals-export"
            style={{ marginRight: 15 }}
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            options={{
              filename: '预提管理.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueManagementPreRest/exportSync`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
          <ImportButton
            danger={false}
            modulecode={moduleCode}
            code="accruals-import"
            style={{ marginRight: 15 }}
            type="primary"
            pagename="预提管理配置"
            title="导入"
            action="/tdmsAccrueService/accrueManagementPreRest/upload/managementPre"
            modalUrl="/tdmsAccrueService/accrueManagementPreRest/downTemplate/managementPreModel"
            modalName="预提管理配置模板.xlsx"
            handleSyncImport={refresh}
          />
          <AuthButton
            icon={<RedoOutlined />}
            moduleCode={moduleCode}
            code="accruals-refresh-customer-code"
            disabled={!selectedRows.length}
            onClick={() => refreshCustomerCode(selectedRows)}
            type="primary"
            loading={refreshCustomerCodeLoading}
            style={{ marginRight: 15 }}
          >
            刷新客商编码
          </AuthButton>
        </div>
        <p className={style.summary}>
          {[
            {
              label: '汇总装卸合计工作量',
              value: summaryData.loadUnloadWeight,
              unit: 'T',
            },
            {
              label: '汇总叉车工作量',
              value: summaryData.forkliftWeight,
              unit: 'T',
            },
            {
              label: '汇总含税预计预提总费用',
              value: summaryData.totalAmount,
              unit: '元',
            },
          ].map(({ label, value, unit }) => (
            <React.Fragment key={label}>
              <span className="label-colon">{label}</span>
              <span className="empty-tip">{value}</span>
              <span>{unit}</span>
            </React.Fragment>
          ))}
        </p>
      </div>
    </Form>
  );

  useEffect(() => {
    if (!userInfo.empCode) {
      return;
    }

    if (logRoleCode.roleCode === 'tp00001') {
      form.setFieldsValue({ zoneCode: userInfo.deptCode });
    }
    form.setFieldsValue({
      yearMonthQr: [moment(new Date()), moment(new Date())],
    });
    if (userInfo && userInfo.empCode) {
      if (/.*SX.*/.test(userInfo.empCode.toUpperCase())) {
        setUserOrg('SX');
      } else {
        setUserOrg('SF');
      }
    }
    getQueryParams();
    refresh({ currentPage: 1 });
  }, [userInfo, logRoleCode]);

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey={rowKey}
        selectedRows={selectedRows}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Details
          initialValues={editingTarget}
          type={type}
          visible={addModalVisible}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh();
          }}
          width={1000}
        />
      )}
    </div>
  );
};
@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
@authDecorator
class Container extends PureComponent {
  render() {
    const {
      moduleCode,
      userInfo,
      logRoleCode,
      areaListSF,
      areaListSX,
    } = this.props;
    return (
      <Page
        moduleCode={moduleCode}
        userInfo={userInfo}
        logRoleCode={logRoleCode}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
      />
    );
  }
}

export default withRouter(Container);
