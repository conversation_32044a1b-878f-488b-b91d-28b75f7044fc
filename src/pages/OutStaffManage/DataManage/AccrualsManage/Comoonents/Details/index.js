import React, { useState, useEffect, Fragment } from 'react';
import { Form, Modal, Col, Row, Input, DatePicker, Button, Select } from 'antd';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { success, error } from 'utils/utils';
import { approvalList } from '../../servers/api';
import { accrueTypeList, statusList, confirmType } from '../../status';
const { Item: FormItem } = Form;
// let overTime = false; // 是否超过当前月12号   true 未超过  false 超过
const { TextArea } = Input;
const Details = props => {
  const {
    onOk,
    visible,
    initialValues,
    sfvisible,
    outvisible,
    type,
    ...rest
  } = props;
  const [form] = Form.useForm();
  // const [isOut, setIsOut] = useState(outvisible);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    const { startDate, endDate } = form;
    form.startDate = startDate ? moment(startDate).format('YYYY-MM-DD') : '';
    form.startDate = endDate ? moment(endDate).format('YYYY-MM-DD') : '';
  }, []);

  // 表单初始值的处理
  const tem = initialValues ? cloneDeep(initialValues) : null;
  if (tem !== null) {
    tem.yearMonthQr = moment(tem.yearMonthQr);
    tem.accrueType = accrueTypeList[tem.accrueType]
      ? accrueTypeList[tem.accrueType]
      : '';
    tem.startDate = tem.startDate
      ? moment(tem.startDate).format('YYYY-MM-DD')
      : '';
    tem.endDate = tem.endDate ? moment(tem.endDate).format('YYYY-MM-DD') : '';
  } else {
    // tem.differenceType = 0;
    form.setFieldsValue({
      differenceType: 0,
      forkliftByDistance: 0,
      forkliftByLimit: 0,
      supplierName: undefined,
    });
  }
  // 表单默认值
  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
    initialValues: {
      ...tem,
    },
  };

  // 提交时请求
  const submit = async () => {
    const { id } = initialValues;
    await form.validateFields();
    const params = {
      idList: [id],
      approvalStatus: form.getFieldValue('a'),
      approvalWord: form.getFieldValue('approvalWord'),
    };
    try {
      setLoading(true);
      const res = await approvalList(params);
      if (res.success) {
        setLoading(false);
        success(`操作成功！`);
        onOk();
      } else {
        error(res.errorMessage);
        setLoading(false);
      }
    } catch {
      setLoading(false);
    }
  };
  const handleCancel = () => {
    form.resetFields();
  };

  return (
    <Modal
      title="顺心预提详情"
      maskClosable={false}
      onCancel={handleCancel}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={
        type === 'submit'
          ? [
              <Button key="back" onClick={rest.onCancel}>
                取消
              </Button>,
              <Button
                key="submit"
                loading={loading}
                type="primary"
                onClick={submit}
              >
                确定
              </Button>,
            ]
          : [
              <Button key="back" onClick={rest.onCancel}>
                取消
              </Button>,
            ]
      }
    >
      <Form {...formProps}>
        <Row gutter={{ md: 4 }} style={{ marginBottom: `10px` }}>
          <Col md={10} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>基础信息</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="业务单号" name="flowId">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem
              label="归属月份"
              name="yearMonthQr"
              rules={[
                {
                  required: true,
                  message: '请选择月份',
                },
              ]}
            >
              <DatePicker
                showToday
                disabled
                picker="month"
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="分拨区代码" name="allocationAreaCode">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="分拨区" name="allocationArea">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="网点代码" name="zoneCode">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="网点名称" name="zoneName">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="法人" name="zoneCodeLegalPerson">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }} style={{ marginBottom: `10px` }}>
          <Col md={10} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>供应商信息</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="供应商名称" name="supplierName">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="供应商编码" name="supplierNo">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="客商编码" name="supplierCode">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="合同生效时间" name="startDate">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="合同失效时间" name="endDate">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }} style={{ marginBottom: `10px` }}>
          <Col md={10} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>单价信息</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="计价类型" name="accrueType">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="计费单位" name="accrueUnit">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="税率" name="taxRate">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="装车单价" name="loadPrice">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="卸车单价" name="unloadPrice">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="叉车单价" name="forkliftPrice">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }} style={{ marginBottom: `10px` }}>
          <Col md={10} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>工作量信息</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="装车工作量" name="loadUnloadWeight">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="卸车工作量" name="loadUnloadWeight">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="装卸合计工作量" name="loadUnloadWeight">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="叉车工作量" name="loadUnloadWeight">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }} style={{ marginBottom: `10px` }}>
          <Col md={10} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>预提费用</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="装卸预计预提费用" name="loadUnloadAmount">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="叉车预计预提费用" name="forkliftAmount">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="含税预计预提总费用" name="totalAmount">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10} sm={24}>
            <FormItem label="状态" name="approvalStatus">
              <Select
                allowClear
                disabled
                options={statusList}
                placeholder="请选择"
              ></Select>
            </FormItem>
          </Col>
        </Row>
        {type === 'submit' && (
          <Fragment>
            <Row gutter={{ md: 4 }}>
              <Col md={10} sm={24}>
                <FormItem
                  label="操作"
                  name="a"
                  rules={[
                    {
                      required: true,
                      message: '请选择',
                    },
                  ]}
                >
                  <Select
                    allowClear
                    options={confirmType[tem.approvalStatus]}
                    placeholder="请选择"
                    onChange={() => {
                      form.setFieldsValue({ approvalWord: undefined });
                    }}
                  ></Select>
                </FormItem>
              </Col>
            </Row>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.a !== curValues.a
              }
              noStyle
            >
              {({ getFieldValue }) =>
                [3].includes(getFieldValue('a')) && (
                  <Row gutter={{ md: 4 }}>
                    <Col md={20} sm={24}>
                      <FormItem
                        label="请反馈原因:"
                        labelCol={{ xl: 4 }}
                        wrapperCol={{ xl: 20 }}
                        name="approvalWord"
                        rules={[
                          {
                            required: true,
                            message: '请输入原因',
                          },
                        ]}
                      >
                        <TextArea
                          showCount
                          maxLength={200}
                          placeholder="请输入"
                          autoSize={{ minRows: 3, maxRows: 5 }}
                        />
                      </FormItem>
                    </Col>
                  </Row>
                )
              }
            </FormItem>
          </Fragment>
        )}
      </Form>
    </Modal>
  );
};

export default Details;
