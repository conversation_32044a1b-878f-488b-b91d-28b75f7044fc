/* eslint-disable consistent-return */
/* eslint-disable react/no-unused-state */
/* eslint-disable react/destructuring-assignment */
import React, { Component } from 'react';
import { Card, Modal, message, Button } from 'antd';
import StandardTable from '@/components/StandardTable';
// import { connect } from 'dva';
import request from '@/utils/request';
import Search from './components/search';

import UploadFiles from './components/upload';
import ShowDetail from './components/detail';
import ShowStep from './components/step';
const Apis = {
  queryList: data =>
    request(`/ospmAccountService/timingMonthRest/query`, {
      method: 'POST',
      body: data,
    }),
  approval: data =>
    request(`/ospmAccountService/approvalRest/submitApproval`, {
      method: 'POST',
      body: data,
    }), // 提交审核
  cancelSubmit: data =>
    request(`/ospmAccountService/approvalRest/cancelApproval`, {
      method: 'POST',
      body: data,
    }), // 撤销
};

class CountTime extends Component {
  state = {
    selectedRows: [],
    loading: false,
    obj: {
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    },
    btnLoading: false,
    searchValue: '',
    stepModal: false,
    detailModal: false,
    detailRow: {},
    fileModal: false,
    fileRow: {},
  };

  columns = [
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 100,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 180,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '归属月份',
      dataIndex: 'accrueMonth',
      width: 180,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 100,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 180,
    },
    {
      title: '结算方式',
      dataIndex: 'accountWayTypeName',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'approvalStatusName',
      width: 150,
    },
    {
      title: '人数',
      dataIndex: 'stationNum',
      width: 180,
    },
    {
      title: '迟到人数',
      dataIndex: 'lateNum',
      width: 100,
    },
    {
      title: '早退人数',
      dataIndex: 'leaveEarlyNum',
      width: 100,
    },
    {
      title: '旷工人数',
      dataIndex: 'absenteeismNum',
      width: 100,
    },
    {
      title: '工作时长（分钟）',
      dataIndex: 'workingHours',
      width: 150,
    },
    {
      title: '补充工时（分钟）',
      dataIndex: 'supplementWorkingHours',
      width: 150,
    },
    {
      title: '扣减工时（分钟）',
      dataIndex: 'deductionWorkingHours',
      width: 160,
    },
    {
      title: '结算工时（分钟）',
      dataIndex: 'accountWorkingHours',
      width: 150,
    },
    {
      title: '异常原因',
      dataIndex: 'remark',
      width: 100,
      // render: text => text || '-',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
      fixed: 'right',
      render: (text, row) => (
        <div>
          <Button
            size="small"
            style={{ marginRight: 10 }}
            onClick={e => this.showFiles(e, row)}
            disabled={
              !(
                row.approvalStatus === 1 ||
                row.approvalStatus === 5 ||
                row.approvalStatus === 6
              )
            }
          >
            添加附件
          </Button>

          <Button size="small" onClick={e => this.ShowDetail(e, row)}>
            查看详情
          </Button>
        </div>
      ),
    },
  ];

  handleSearch = values => {
    this.setState({
      searchValue: values,
    });
    const data = {
      ...values,
      pageNum: 1,
      pageSize: 10,
    };
    // const data = {
    //   zoneCode: '755A',
    //   supplierCode: 'K755Y',
    //   accrueType: 0,
    // };
    this.getList(data);
  };

  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };

  handleStep = () => {
    const { selectedRows } = this.state;
    if (selectedRows.length === 1) {
      this.setState({ stepModal: true });
    } else {
      message.error('仅能选择一条数据进行操作');
    }
  };

  ShowDetail = (e, row) => {
    e.stopPropagation();
    this.setState({
      detailModal: true,
      detailRow: row,
    });
  };

  hideModal = () => {
    this.setState({
      detailModal: false,
    });
  };

  showFiles = (e, row) => {
    e.stopPropagation();
    this.setState({
      fileModal: true,
      fileRow: row,
    });
  };

  hideFiles = () => {
    this.setState({
      fileModal: false,
    });
  };

  handleApprove = () => {
    const { selectedRows, searchValue } = this.state;
    if (selectedRows.length > 0) {
      const list = selectedRows.filter(ele => ele.approvalStatus === 8);
      const arr = selectedRows.filter(ele => ele.approvalStatus === -1);
      if (arr.length > 0) {
        message.error('不是计时供应商，不允许提交');
        return false;
      }
      if (list.length > 0) {
        message.error('所选的数据存在已提交审核的数据了，请正确选择');
        return false;
      }
      const ids = selectedRows.map(({ wbTimingMonthId }) => ({
        wbTimingMonthId,
      }));
      Modal.confirm({
        title: '提示',
        content: '确认提交审核？',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          const res = await Apis.approval({
            ids,
            code: 3,
          });
          if (res && res.success) {
            this.getList({ ...searchValue, pageNum: 1, pageSize: 10 });
            message.success('已提交审核');
          }
        },
      });
    } else {
      message.error('请选择数据操作');
    }
  };

  handleRecall = () => {
    const { selectedRows, searchValue } = this.state;
    if (selectedRows.length > 0) {
      const ids = selectedRows.map(({ wbTimingMonthId }) => ({
        wbTimingMonthId,
      }));
      Modal.confirm({
        title: '提示',
        content: '确认撤回？',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          const res = await Apis.cancelSubmit({
            ids,
            code: 3,
          });
          if (res && res.success) {
            message.success('数据状态已变更为撤回');
            this.getList({ ...searchValue, pageNum: 1, pageSize: 10 });
          }
        },
      });
    } else {
      message.error('仅能选一条数据操作');
    }
  };

  handleHide = () => {
    this.setState({ stepModal: false });
  };

  changePage = pages => {
    const { current, pageSize } = pages;
    const { searchValue } = this.state;
    this.setState({
      obj: {
        pagination: {
          pageSize,
          current,
        },
      },
    });
    const data = {
      ...searchValue,
      pageNum: current,
      pageSize,
    };
    this.getList(data);
  };

  // 列表查询
  getList = async data => {
    const { pageNum, pageSize } = data;
    this.setState({
      loading: true,
    });
    const res = await Apis.queryList(data).finally(() =>
      this.setState({
        loading: false,
      }),
    );
    if (res && res.success) {
      const { list, total } = res.obj;
      this.setState({
        obj: {
          list: list || [],
          pagination: {
            pageSize,
            current: pageNum,
            total,
          },
        },
        selectedRows: [],
      });
    }
  };

  render() {
    const {
      btnLoading,
      selectedRows,
      obj,
      loading,
      stepModal,
      detailModal,
      detailRow,
      fileRow,
      fileModal,
    } = this.state;
    return (
      <Card>
        <div>
          <Search
            handleSearch={this.handleSearch}
            loading={btnLoading}
            selectedRows={selectedRows}
            datas={obj}
            approveStep={this.handleStep}
            handleApprove={this.handleApprove}
            handleRecall={this.handleRecall}
          />
          <StandardTable
            size="small"
            selectedRows={selectedRows}
            data={obj}
            columns={this.columns}
            multiple
            loading={loading}
            onSelectRow={this.handleSelectRows}
            rowKey="id"
            onChange={this.changePage}
          />
        </div>
        {stepModal && (
          <ShowStep
            visible={stepModal}
            title="审核进度"
            row={selectedRows[0]}
            handleHide={this.handleHide}
          />
        )}
        {detailModal && (
          <ShowDetail
            row={detailRow}
            visible={detailModal}
            handleHide={this.hideModal}
          />
        )}
        {fileModal && (
          <UploadFiles
            visible={fileModal}
            handleHide={this.hideFiles}
            actionUrl="/ospmAccountService/wbTaskAttachRest/upload"
            params={{ wbTaskMonthId: fileRow.wbTimingMonthId }}
          />
        )}
      </Card>
    );
  }
}
export default CountTime;
