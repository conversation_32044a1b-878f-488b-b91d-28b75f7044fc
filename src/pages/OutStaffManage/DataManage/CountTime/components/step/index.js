import React, { useState, useEffect } from 'react';
import { Modal } from 'antd';
import moment from 'moment';
import request from '@/utils/request';
import StandardTable from '@/components/StandardTable';
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const Apis = {
  queryStep: data =>
    request(`/ospmAccountService/waListRest/seeApprovalDetails`, {
      method: 'POST',
      body: data,
    }), // 进度查询
};
const ShowStep = props => {
  const { title, visible, handleHide, row } = props;
  const { wbTimingMonthId } = row;
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      pageSize: 10,
      current: 1,
      total: 0,
    },
  });

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 100,
    },
    {
      title: '任务名称',
      dataIndex: 'taskName',
      width: 180,
    },
    {
      title: '执行开始时间',
      dataIndex: 'startTime',
      width: 150,
      render: timeTrans,
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      width: 150,
      render: timeTrans,
    },
    {
      title: '持续时间',
      dataIndex: 'continuedTime',
      width: 180,
    },
    {
      title: '执行人',
      dataIndex: 'executor',
      width: 100,
    },
    {
      title: '审核意见',
      dataIndex: 'approvalWord',
      width: 180,
    },
    {
      title: '审核状态',
      dataIndex: 'approvalStatusName',
      width: 180,
    },
  ];

  const queryStep = async data => {
    const res = await Apis.queryStep(data);
    if (res.success) {
      const list = res.obj.map((ele, ind) => {
        ele.index = ind + 1;
        return ele;
      });
      const sourse = {
        list,
        pagination: false,
      };
      setDatas(sourse);
    }
  };
  useEffect(() => {
    if (wbTimingMonthId) {
      queryStep({ taskId: wbTimingMonthId });
    }
  }, [wbTimingMonthId]);
  return (
    <Modal
      visible={visible}
      title={title}
      width={(document.body.offsetWidth * 3) / 4}
      onCancel={() => handleHide()}
      onOk={() => handleHide()}
    >
      <StandardTable
        showSelection={false}
        size="small"
        columns={columns}
        data={datas}
      />
    </Modal>
  );
};

export default ShowStep;
