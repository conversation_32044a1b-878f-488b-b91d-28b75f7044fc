import React, { useEffect, useState } from 'react';
import { Form, Button, DatePicker, Select, Row, Col, Input } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle } from 'ky-giant';
import NetSearch from '@/components/NetSelect';
import AuthButton from '@/components/AuthButton';
import ExportButton from '@/components/ExportButton';
import AsyncExport from '@/components/AsyncExport';
import authDecorator from '@/components/AuthDecorator';
// import { userNoToCode } from '@/services/clock';
// import { queryNameToCode } from '@/services/supplierApi';
import styles from '../../style.less';

const { Item } = Form;
const { Option } = Select;
// const { RangePicker } = DatePicker;

const SearchForm = props => {
  const [form] = Form.useForm();
  const { userInfo, selectedRows = [], datas, moduleCode } = props;
  const [searchVal, setSearchVal] = useState({});
  const [allocateList, setAllocateList] = useState([]); // 分拨区
  const [limit, setLimit] = useState(true);
  const companyAttr = [
    {
      value: 'SF',
      text: '顺丰',
    },
    {
      value: 'SX',
      text: '顺心',
    },
  ];
  const types = [
    { value: 0, text: '装车区' },
    { value: 1, text: '卸车区' },
    { value: 2, text: '叉车区' },
    { value: 3, text: '分拣区' },
    { value: 4, text: '全流程' },
  ];
  // 1待提交，2审核中，3审核通过，4撤销、5驳回
  // 1待提交，2提交审核，3审核中，4审核通过，5撤销、6驳回，7失效数据8，提交结算
  const status = [
    { value: 1, text: '待提交' },
    { value: 3, text: '审核中' },
    { value: 4, text: '审核通过' },
    { value: 5, text: '撤销' },
    { value: 6, text: '驳回' },
  ];
  const isRegular = [
    { value: 0, text: '否' },
    { value: 1, text: '是' },
  ];
  //  查询
  const onFinish = () => {
    form.validateFields().then(nameList => {
      if (nameList) {
        const { accrueType } = nameList;
        if (accrueType) {
          setLimit(false);
        } else {
          setLimit(true);
        }
        if (nameList.accrueMonth) {
          nameList.accrueMonth = nameList.accrueMonth.format('YYYY-MM');
          // nameList.endDate = nameList.workDate[1].format('x');
        }
        // delete nameList.accrueMonth;
        setSearchVal(nameList);
        props.handleSearch(nameList);
      }
    });
  };

  //   重置
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };
  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        zoneCode: userInfo.deptCode,
        sourceType: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
      });
    }
  }, [userInfo.deptCode]);
  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);
  const getValues = () => {
    const wbTimingMonthIds = selectedRows.map(ele => ele.wbTimingMonthId);
    return { wbTimingMonthIds };
  };
  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        zoneCode: userInfo.deptCode,
        sourceType: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
      });
    }
  }, [userInfo.deptCode]);

  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        accrueMonth: moment(moment(new Date()).format('YYYY-MM')),
        dataType: 0,
        accrueType: 0,
      }}
    >
      <Row {...rowStyle}>
        <Col {...colStyle}>
          <Item name="sourceType" label="公司属性">
            <Select>
              {companyAttr.map(ele => (
                <Option value={ele.value}>{ele.text}</Option>
              ))}
            </Select>
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="allocationAreaCode" label="分拨区">
            <NetSearch
              extraParam={{
                typeLevels: ['2'],
                hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
              }}
              showSearch={false}
              getList={getList}
            />
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="zoneCode" label="中转场">
            <NetSearch />
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="module" label="模块">
            <Select allowClear>
              {types.map(ele => (
                <Option key={ele.value}>{ele.text}</Option>
              ))}
            </Select>
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="supplierCode" label="供应商">
            <Input placeholder="请输入供应商" allowClear />
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item
            label="归属月份"
            name="accrueMonth"
            rules={[{ required: true, message: '请选择' }]}
          >
            <DatePicker picker="month" style={{ width: '100%' }} />
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="approvalStatus" label="状态">
            <Select allowClear>
              {status.map(ele => (
                <Option key={ele.value}>{ele.text}</Option>
              ))}
            </Select>
          </Item>
        </Col>
        <Col {...colStyle}>
          <Item name="accrueType" label="是否异常数据">
            <Select>
              {isRegular.map(ele => (
                <Option key={ele.value} value={ele.value}>
                  {ele.text}
                </Option>
              ))}
            </Select>
          </Item>
        </Col>
      </Row>
      <div style={{ color: '#df2e3f' }}>
        本月数据请在次月6日凌晨前处理完毕,否则影响结算！
      </div>
      <div className={styles.btnCon}>
        <div>
          {limit && (
            <AuthButton
              type="primary"
              moduleCode={moduleCode}
              code="count_time-submit"
              style={{ marginRight: 15 }}
              // || searchVal.accrueMonth === currentMonth
              disabled={selectedRows.length < 1}
              onClick={() => props.handleApprove()}
            >
              提交审核
            </AuthButton>
          )}
          {limit && (
            <AuthButton
              type="primary"
              moduleCode={moduleCode}
              code="count_time-recall"
              style={{ marginRight: 15 }}
              onClick={() => props.handleRecall()}
              // || searchVal.accrueMonth === currentMonth
              disabled={selectedRows.length < 1}
            >
              撤回
            </AuthButton>
          )}
          {limit && (
            <AuthButton
              type="primary"
              moduleCode={moduleCode}
              code="count_time-step"
              style={{ marginRight: 15 }}
              onClick={() => props.approveStep()}
              disabled={selectedRows.length !== 1}
            >
              审核进度
            </AuthButton>
          )}

          <ExportButton
            text="导出所选"
            moduleCode={moduleCode}
            code="count_time-export-some"
            style={{ marginRight: 15 }}
            disabled={selectedRows.length < 1}
            // icon={<ExportOutlined />}
            options={{
              // total: totalNum,
              filename: '计时供应商数据管理列表.xlsx',
              requstParams: [
                `/ospmAccountService/timingMonthRest/export`,
                {
                  method: 'POST',
                  body: getValues,
                },
              ],
            }}
          />
          <AsyncExport
            text="导出全部"
            modulecode={moduleCode}
            code="count_time-export-all"
            disabled={datas.list && datas.list.length < 1}
            // icon={<ExportOutlined />}
            options={{
              // total: totalNum,
              requstParams: [
                `/ospmAccountService/timingMonthRest/exportSync`,
                {
                  method: 'POST',
                  body: { ...searchVal },
                },
              ],
            }}
          />
        </div>
        <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div>
      </div>
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
