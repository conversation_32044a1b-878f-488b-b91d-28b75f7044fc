import React, { useEffect, useState } from 'react';
import { Form, Button, DatePicker, Input, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import NetSearch from '@/components/NetSelect';
import AuthButton from '@/components/AuthButton';
import ExportButton from '@/components/ExportButton';
import AsyncExport from '@/components/AsyncExport';
import authDecorator from '@/components/AuthDecorator';
// import styles from '../../style.less';

const { Item } = Form;
const { Option } = Select;

const SearchForm = props => {
  const [form] = Form.useForm();
  const [searchVal, setSearchVal] = useState({});
  const {
    userInfo,
    selectedRows,
    datas,
    moduleCode,
    handleSearch,
    handleApprove,
    handleRecall,
    approveStep,
  } = props;
  const [allocateList, setAllocateList] = useState([]);
  const types = [
    { value: 0, text: '装车区' },
    { value: 1, text: '卸车区' },
    { value: 2, text: '叉车区' },
    { value: 3, text: '分拣区' },
    { value: 4, text: '全流程' },
  ];
  // 1待提交，2提交审核，3审核中，4审核通过，5撤销、6驳回，7失效数据8，提交结算', 2346
  const status = [
    // {value:2,text:'提交审核'},
    { value: 3, text: '审核中' },
    { value: 4, text: '审核通过' },
    { value: 6, text: '驳回' },
  ];

  const companyAttr = [
    {
      value: 'SF',
      text: '顺丰',
    },
    {
      value: 'SX',
      text: '顺心',
    },
  ];
  //  查询
  const onFinish = values => {
    // console.log('Success:', values);
    if (values.accrueMonth) {
      values.accrueMonth = values.accrueMonth.format('YYYY-MM');
    }
    setSearchVal(values);
    handleSearch(values);
  };

  //   重置
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };

  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        zoneCode: userInfo.deptCode,
        sourceType: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
      });
    }
  }, [userInfo.deptCode]);
  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);

  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        accrueMonth: moment(moment(new Date()).format('YYYY-MM')),
        dataType: 0,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item name="sourceType" label="公司属性">
                {/* disabled={!isRoot} */}
                <Select placeholder="请选择">
                  {companyAttr.map(ele => (
                    <Option value={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  placeholder="请选择"
                  extraParam={{
                    typeLevels: ['2'],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                  }}
                  showSearch={false}
                  getList={getList}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="zoneCode" label="中转场">
                <NetSearch placeholder="请选择" />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="module" label="模块">
                <Select placeholder="请选择" allowClear>
                  {types.map(ele => (
                    <Option key={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="supplierCode" label="供应商">
                <Input placeholder="请选择" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="approvalStatus" label="状态">
                <Select placeholder="请选择" allowClear>
                  {status.map(ele => (
                    <Option key={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="dataType" label="是否按照班次">
                <Select placeholder="请选择">
                  <Option key={1} value={1}>
                    是
                  </Option>
                  <Option key={0} value={0}>
                    否
                  </Option>
                </Select>
              </Item>
            </Col>

            <Col {...colStyle}>
              <Item label="归属月份" name="accrueMonth">
                <DatePicker picker="month" style={{ width: '100%' }} />
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>

      <div className="btn-con">
        <div>
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="business_auditing-submit"
            style={{ marginRight: 15 }}
            onClick={() => handleApprove()}
            disabled={selectedRows.length < 1}
          >
            审核通过
          </AuthButton>
          <AuthButton
            type="primary"
            moduleCode={moduleCode}
            code="business_auditing-recall"
            style={{ marginRight: 15 }}
            onClick={() => handleRecall()}
            disabled={selectedRows.length < 1}
          >
            驳回
          </AuthButton>
          <Button
            type="primary"
            moduleCode={moduleCode}
            code="business_auditing-step"
            style={{ marginRight: 15 }}
            onClick={() => approveStep()}
            disabled={selectedRows.length !== 1}
          >
            审核进度
          </Button>
          <ExportButton
            text="导出所选"
            moduleCode={moduleCode}
            code="business_auditing-export-some"
            style={{ marginRight: 15 }}
            disabled={selectedRows.length < 1}
            // icon={<ExportOutlined />}
            options={{
              // total: totalNum,
              filename: '计重业务数据列表.xlsx',
              requstParams: [
                `/ospmAccountService/wbTaskMonthRest/choiceExport`,
                {
                  method: 'POST',
                  body: { idList: selectedRows.map(ele => ele.id) },
                },
              ],
            }}
          />
          <AsyncExport
            text="导出全部"
            modulecode={moduleCode}
            code="business_auditing-export-all"
            disabled={datas.list && datas.list.length < 1}
            // icon={<ExportOutlined />}
            options={{
              // total: totalNum,
              requstParams: [
                `/ospmAccountService/wbTaskMonthRest/exportSync`,
                {
                  method: 'POST',
                  body: { ...searchVal },
                },
              ],
            }}
          />
        </div>
      </div>
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
