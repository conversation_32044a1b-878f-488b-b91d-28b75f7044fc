import React, { useState, Fragment } from 'react';
import { message, Upload, Modal } from 'antd';
import { InboxOutlined } from '@ant-design/icons';
import Config from '@/config';
import request from '@/utils/request';
const { Dragger } = Upload;
const Apis = {
  delFiles: data =>
    request(`/ospmAccountService/wbTaskAttachRest/delete`, {
      method: 'POST',
      body: data,
    }), // 附件删除
};
const UploadFiles = props => {
  const { actionUrl, headers, params, visible, title = '上传附件' } = props;
  // const [visible, setVisible] = useState(false);
  const [fileList, setFileList] = useState([]);
  // const showModal = () => {
  //   setVisible(true);
  // };
  const propsUpload = {
    name: 'file',
    multiple: true,
    accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    action: actionUrl,
    headers: {
      credentials: 'include',
      userId: sessionStorage.userid,
      'sgs-userid': sessionStorage.userid,
      systemKey: Config.systemKey,
      'gw-bdus-rid': sessionStorage.roleId || '',
      ...headers,
    },
    fileList,
    data: params,
    beforeUpload(info) {
      // const newFile = new File([info], 'aaa.xlsx', {
      //   type:
      //     'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      // });
      params.fileName = encodeURI(info.name);
      return Promise.resolve(info);
    },
    onChange(info) {
      const { status, response } = info.file;
      // console.log(info);
      if (status !== 'uploading') {
        // console.log(info.file, info.fileList);
      }
      if (status === 'done') {
        if (response.success) {
          setFileList(info.fileList);
          message.success(`${info.file.name}上传成功`);
        } else {
          message.error(response.errorMessage);
        }
      } else if (status === 'error') {
        message.error(`${info.file.name}上传失败`);
      }
    },
    onRemove(file) {
      const { obj } = file.response;
      if (obj) {
        const ids = [];
        ids.push(obj);
        const list = fileList.filter(ele => ele.response.obj !== obj);
        setFileList(list);
        Apis.delFiles({ ids }).then(res => {
          if (res.success) {
            message.success('已删除该附件');
          } else {
            message.error(res.errorMessage);
          }
        });
      }
    },
  };
  return (
    <Fragment>
      <Modal
        visible={visible}
        onCancel={() => props.handleHide()}
        onOk={() => props.handleHide()}
        title={title}
      >
        <Dragger {...propsUpload}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-hint">拖拽或者点击上传Excel附件</p>
        </Dragger>
      </Modal>
    </Fragment>
  );
};

export default UploadFiles;
