import React, { useState, useEffect, Fragment } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  Drawer,
  Form,
  Row,
  Col,
  DatePicker,
  message,
  Modal,
} from 'antd';
import moment from 'moment';
import { CloseOutlined } from '@ant-design/icons';
import request from '@/utils/request';
import Download from '@/components/ExportButton';
import StandardTable from '@/components/StandardTable';
import styles from '../../style.less';
const { Item } = Form;
const { RangePicker } = DatePicker;
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const layout = {
  labelCol: { md: 8, sm: 24, xs: 24 },
  wrapperCol: { md: 16, sm: 24, xs: 24 },
};
const Apis = {
  queryList: data =>
    request(`/ospmAccountService/wbTeamRest/getInfo`, {
      method: 'POST',
      body: data,
    }), // 详情查询
  queryList2: data =>
    request(`/ospmAccountService/timingMonthRest/getInfo`, {
      method: 'POST',
      body: data,
    }), // 详情查询
  approval: data =>
    request(`/ospmAccountService/wbTaskMonthRest/submitApproval`, {
      method: 'POST',
      body: data,
    }), // 提交
  approval2: data =>
    request(`/ospmAccountService/approvalRest/submitApproval`, {
      method: 'POST',
      body: data,
    }), // 提交
  queryFiles: data =>
    request(`/ospmAccountService/wbTaskAttachRest/attachList`, {
      method: 'POST',
      body: data,
    }), // 附件查询
  delFiles: data =>
    request(`/ospmAccountService/wbTaskAttachRest/delete`, {
      method: 'POST',
      body: data,
    }), // 附件删除
};
const Search = props => {
  const [form] = Form.useForm();
  const { wbTaskMonthId, type } = props;
  const [searchVal, setSearchVal] = useState('');
  const resetForm = () => form.resetFields();
  //  查询
  const onFinish = values => {
    // console.log('Success:', values);
    if (values.rangeTime) {
      values.startTime = values.rangeTime[0].startOf('day').format('x');
      values.endTime = values.rangeTime[1].endOf('day').format('x');
    }
    delete values.rangeTime;
    // console.log(values);
    setSearchVal(values);
    props.handleSearch(values);
  };

  return (
    <Form {...layout} form={form} onFinish={onFinish}>
      <Row>
        <Col md={12} sm={24} xs={24}>
          <Item
            label="任务创建日期"
            name="rangeTime"
            labelCol={{ md: 6, sm: 24, xs: 24 }}
            wrapperCol={{ md: 18, sm: 24, xs: 24 }}
          >
            <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
          </Item>
        </Col>
        <div className={styles.btnCon}>
          <div>
            {/* <Button type="primary" style={{ marginRight: 15 }}>
              导出
            </Button> */}
            {type === '计时' ? (
              <Download
                text="导出"
                // icon={<ExportOutlined />}
                disabled={!wbTaskMonthId}
                options={{
                  // total: totalNum,
                  filename: '明细列表.xlsx',
                  requstParams: [
                    `/ospmAccountService/timingMonthRest/infoExport`,
                    {
                      method: 'POST',
                      body: { ...searchVal, wbTimingMonthId: wbTaskMonthId },
                    },
                  ],
                }}
              />
            ) : (
              <Download
                text="导出"
                // icon={<ExportOutlined />}
                disabled={!wbTaskMonthId}
                options={{
                  // total: totalNum,
                  filename: '任务明细列表',
                  requstParams: [
                    `/ospmAccountService/wbTeamRest/export`,
                    {
                      method: 'POST',
                      body: { ...searchVal, wbTaskMonthId },
                    },
                  ],
                }}
              />
            )}
          </div>
          <div className={styles.right}>
            <Button
              type="primary"
              style={{ marginRight: 15 }}
              htmlType="submit"
            >
              查询
            </Button>
            <Button style={{ marginRight: 15 }} onClick={resetForm}>
              重置
            </Button>
          </div>
        </div>
      </Row>
    </Form>
  );
};

const Detail = props => {
  // console.log(props);
  const { title, size, row, ...rest } = props;
  const { taskMonthId, visible, approvalStatus, agreemenTypeName } = row;
  const [btnLoading, setBtnLoading] = useState(false);
  const [showType, setShowType] = useState(true);
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      pageSize: 10,
      current: 1,
      total: 0,
    },
  });
  const operateType = {
    1: '装车',
    2: '卸车',
    3: '分拣',
    4: '叉车',
    5: '木质包装',
  };
  // const modules = {
  //   0: '装车区',
  //   1: '卸车区',
  //   2: '分拣区',
  //   3: '叉车区',
  //   4: '全流程',
  // };
  const [fileList, setFileList] = useState([]);
  const [search, setSearch] = useState({});
  const columns = [
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 180,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 180,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '归属日期',
      dataIndex: 'taskSourceDate',
      width: 180,
      render: timeTrans,
    },
    {
      title: '主任务号',
      dataIndex: 'taskId',
      width: 100,
    },
    {
      title: '子任务号',
      dataIndex: 'flowId',
      width: 100,
    },
    {
      title: '任务创建时间',
      dataIndex: 'startTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierNo',
      width: 100,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 180,
    },
    {
      title: '任务环节',
      dataIndex: 'operateType',
      width: 180,
      render: text => (text ? operateType[text] : '-'),
    },
    {
      title: '模块',
      dataIndex: 'moduleName',
      width: 100,
      // render: text => (text ? modules[text] : '-'),
    },
    {
      title: '合同类型',
      dataIndex: 'agreemenTypeName',
      width: 100,
    },
    {
      title: '计价方式',
      dataIndex: 'accruePriceAway',
      width: 100,
      render: () => '元 / KG',
    },
    {
      title: '业务数据',
      dataIndex: 'avgWeight',
      width: 100,
    },
  ];
  const columns2 = [
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 180,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 180,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '归属月份',
      dataIndex: 'accrueMonth',
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'affirmStatusName',
      width: 100,
    },
    {
      title: '归集开始时间',
      dataIndex: 'shiftStartTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '归集结束时间',
      dataIndex: 'shiftEndTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '协议组编码',
      dataIndex: 'proTeamNo',
      width: 180,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 180,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 180,
    },
    {
      title: '任务环节',
      dataIndex: 'operateType',
      width: 180,
      render: text => (text ? operateType[text] : '-'),
    },
    {
      title: '模块',
      dataIndex: 'moduleName',
      width: 100,
      // render: text => (text ? modules[text] : '-'),
    },
    {
      title: '合同类型',
      dataIndex: 'agreemenTypeName',
      width: 100,
    },
    // {
    //   title: '结算方式',
    //   dataIndex: 'accountWayType',
    //   width: 100,
    //   render: text => (text === 1 ? '计重' : '计时'),
    // },
    {
      title: '协议开始日',
      dataIndex: 'proTeamStartTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '协议到期日',
      dataIndex: 'proTeamEndTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '人数',
      dataIndex: 'attendanceNum',
      width: 100,
    },
    {
      title: '迟到人数',
      dataIndex: 'lateNum',
      width: 100,
    },
    {
      title: '早退人数',
      dataIndex: 'leaveEarlyNum',
      width: 100,
    },
    {
      title: '旷工人数',
      dataIndex: 'absenteeismNum',
      width: 100,
    },
    {
      title: '工作时长（分钟）',
      dataIndex: 'workDuration',
      width: 180,
    },
    {
      title: '补充工时（分钟）',
      dataIndex: 'supplementWorkingHours',
      width: 180,
    },
    {
      title: '扣减工时（分钟）',
      dataIndex: 'deductionWorkingHours',
      width: 180,
    },
  ];
  const onClose = () => props.handleHide();
  // 详情
  const queryDetail = async data => {
    setDatas({
      list: [],
      pagination: {
        pageSize: 10,
        current: 1,
        total: 0,
      },
    });
    const queryInfo =
      agreemenTypeName === '计时'
        ? Apis.queryList2(data)
        : Apis.queryList(data);

    const res = await queryInfo;
    if (res && res.success) {
      if (res.obj && res.obj.list) {
        const datasource = {
          list: res.obj.list,
          pagination: {
            pageSize: data.pageSize,
            current: data.pageNum,
            total: res.obj.total,
          },
        };
        setDatas(datasource);
      }
      // setFileList(files);
    }
  };
  // 详情附件
  const queryFiles = async data => {
    const res = await Apis.queryFiles(data);
    if (res && res.success) {
      const files = res.obj || [];
      setFileList(files);
    }
  };
  const handleSearch = val => {
    setSearch(val);
    const data = {
      ...val,
      wbTaskMonthId: taskMonthId,
      pageNum: 1,
      pageSize: 10,
    };
    if (agreemenTypeName === '计时') {
      queryDetail({
        wbTimingMonthId: taskMonthId,
        pageNum: 1,
        pageSize: 10,
        ...val,
      });
    } else {
      queryDetail(data);
    }
    queryFiles({ wbTaskMonthId: taskMonthId });
  };

  // 翻页
  const changePage = pages => {
    const { current, pageSize } = pages;
    const data = {
      ...search,
      pageNum: current,
      pageSize,
      wbTaskMonthId: taskMonthId,
    };
    queryDetail(data);
  };
  const handleSubmit = async () => {
    setBtnLoading(true);
    const queryInfo =
      agreemenTypeName === '计时'
        ? Apis.approval2({
            ids: [
              {
                wbTimingMonthId: taskMonthId,
              },
            ],
            code: 3,
          })
        : Apis.approval({ wbTaskMonthIds: taskMonthId.split() });

    await queryInfo.finally(() => setBtnLoading(false));
    message.success('已提交审核');
  };

  // 删除附件
  const delFile = data => {
    if (
      !(
        approvalStatus === '1' ||
        approvalStatus === '5' ||
        approvalStatus === '6'
      )
    ) {
      message.error('该条数据不在待提交、撤回、驳回状态中，不能再删除附件');
      return false;
    }
    const { id } = data;
    const ids = [];
    ids.push(id);
    Modal.confirm({
      title: '提示',
      content: '确定删除?',
      className: styles.modalCenter,
      cancelText: '取消',
      okText: '确定',
      onOk: async () => {
        const res = await Apis.delFiles({ ids });
        if (res && res.success) {
          message.success('已删除该附件');
          queryFiles({ wbTaskMonthId: taskMonthId });
        }
      },
    });
  };

  useEffect(() => {
    if (taskMonthId) {
      if (agreemenTypeName === '计时') {
        queryDetail({ wbTimingMonthId: taskMonthId, pageNum: 1, pageSize: 10 });
      } else {
        queryDetail({ wbTaskMonthId: taskMonthId, pageNum: 1, pageSize: 10 });
      }

      queryFiles({ wbTaskMonthId: taskMonthId });
    }
  }, [taskMonthId]);
  useEffect(() => {
    if (agreemenTypeName === '计时') {
      setShowType(false);
    } else {
      setShowType(true);
    }
  }, [agreemenTypeName]);
  return (
    <Fragment>
      <Drawer
        visible={visible}
        placement="right"
        onClose={onClose}
        title={title}
        width={(document.body.offsetWidth * 4) / 5}
        footer={[
          <Button
            style={{ marginRight: 15 }}
            onClick={() => props.handleHide()}
          >
            取消
          </Button>,
          <Button
            type="primary"
            onClick={() => handleSubmit()}
            loading={btnLoading}
            disabled={approvalStatus === '8'}
          >
            提交审核
          </Button>,
        ]}
        footerStyle={{ display: 'flex', justifyContent: 'center' }}
        {...rest}
      >
        <Card>
          <Search
            handleSearch={handleSearch}
            wbTaskMonthId={taskMonthId}
            type={agreemenTypeName}
          />
          <h3>任务明细</h3>
          {showType ? (
            <div key={Math.random()}>
              <StandardTable
                columns={columns}
                data={datas}
                size="small"
                showSelection={false}
                rowKey="id"
                onChange={changePage}
              />
            </div>
          ) : (
            <div key={Math.random()}>
              <StandardTable
                columns={columns2}
                data={datas}
                size="small"
                showSelection={false}
                rowKey="id"
                onChange={changePage}
              />
            </div>
          )}

          <h3>附件数据</h3>
          <ul className={styles.filesCon}>
            {fileList.map(ele => (
              <li>
                <div title={ele.fileName}>
                  {ele.fileName.length > 8
                    ? `${ele.fileName.slice(0, 8)}...`
                    : ele.fileName}
                </div>
                <div className={styles.close} onClick={() => delFile(ele)}>
                  <CloseOutlined />
                </div>
                <Download
                  text="导出"
                  // icon={<ExportOutlined />}
                  options={{
                    // total: totalNum,
                    filename: ele.fileName,
                    requstParams: [
                      `/ospmAccountService/wbTaskAttachRest/download`,
                      {
                        method: 'POST',
                        body: { id: ele.id },
                      },
                    ],
                  }}
                />
              </li>
            ))}
          </ul>
        </Card>
      </Drawer>
    </Fragment>
  );
};

export default Detail;
