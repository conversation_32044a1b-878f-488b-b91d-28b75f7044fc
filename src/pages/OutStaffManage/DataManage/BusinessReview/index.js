import React, { Component } from 'react';
import { Modal, message, Button, Form, Input } from 'antd';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import request from '@/utils/request';
import Search from './components/search';

import UploadFiles from './components/upload';
import ShowDetail from './components/detail';
import ShowStep from './components/step';
const { Item } = Form;
const { TextArea } = Input;
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const Apis = {
  queryList: data =>
    request(`/ospmAccountService/wbTaskMonthRest/queryOutsourceList`, {
      method: 'POST',
      body: data,
    }),
  downloadSome: data =>
    request(`/ospmAccountService/wbTaskMonthRest/choiceExport`, {
      method: 'POST',
      body: data,
    }), // 下载所选
  downloadAll: data =>
    request(`/ospmAccountService/wbTaskMonthRest/exportSync`, {
      method: 'POST',
      body: data,
    }), // 下载全部
  approval: data =>
    request(`/ospmAccountService/wbTaskMonthRest/passApproval`, {
      method: 'POST',
      body: data,
    }), // 审核通过
  rejectApproval: data =>
    request(`/ospmAccountService/wbTaskMonthRest/rejectApproval`, {
      method: 'POST',
      body: data,
    }), // 驳回
};
const Suggest = props => {
  const [form] = Form.useForm();
  const { visible, title = '确认', passOrReject } = props;
  const handleConfirm = () => {
    // const values = form.getFieldsValue();
    form.validateFields().then(namelist => {
      if (namelist) {
        props.handleConfirm(namelist);
      }
    });
    // props.handleConfirm(values);
  };
  return (
    <Modal
      visible={visible}
      title={title}
      onOk={handleConfirm}
      onCancel={() => props.handleHide()}
    >
      <Form
        form={form}
        initialValues={{
          approvalWord: '',
        }}
      >
        <Item
          label="意见"
          name="approvalWord"
          rules={[
            {
              required: !passOrReject,
              message: '请输入内容',
            },
          ]}
        >
          <TextArea maxLength={500} />
        </Item>
      </Form>
    </Modal>
  );
};

class CountWeight extends Component {
  state = {
    selectedRows: [],
    loading: false,
    obj: {
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    },
    btnLoading: false,
    searchValue: '',
    stepModal: false,
    detailModal: false,
    detailRow: {},
    fileModal: false,
    fileRow: {},
    passOrReject: false,
    showSuggest: false,
    suggestTitle: '',
  };

  columns = [
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 100,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 180,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '归集月份',
      dataIndex: 'accrueMonth',
      width: 180,
    },
    {
      title: '状态',
      dataIndex: 'approvalStatusName',
      width: 100,
    },
    {
      title: '归集开始时间',
      dataIndex: 'collectionStartTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '归集结束时间',
      dataIndex: 'collectionEndTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '协议组编码',
      dataIndex: 'proTeamNo',
      width: 100,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 100,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 180,
    },
    {
      title: '任务环节',
      dataIndex: 'operateTypeName',
      width: 180,
    },
    {
      title: '模块',
      dataIndex: 'moduleName',
      width: 100,
    },
    {
      title: '合同类型',
      dataIndex: 'agreemenTypeName',
      width: 100,
    },
    {
      title: '计价方式',
      dataIndex: 'accruePriceAway',
      width: 100,
    },
    {
      title: '协议开始日',
      dataIndex: 'proTeamStartTime',
      width: 100,
      render: timeTrans,
    },
    {
      title: '协议到期日',
      dataIndex: 'proTeamEndTime',
      width: 100,
      render: timeTrans,
    },
    {
      title: '业务数量',
      dataIndex: 'totalWeight',
      width: 100,
    },
    {
      title: '申请人',
      dataIndex: 'creator',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
      fixed: 'right',
      render: (text, row) => (
        <div>
          {/* <UploadFiles
            row={row}
            size="small"
            style={{ marginRight: 10 }}
            
            actionUrl="/ospmAccountService/wbTaskAttachRest/upload"
            params={{ wbTaskMonthId: row.taskMonthId }}
          /> */}
          <Button
            size="small"
            style={{ marginRight: 10 }}
            onClick={e => this.showFiles(e, row)}
            disabled={
              !(
                row.approvalStatus === '1' ||
                row.approvalStatus === '5' ||
                row.approvalStatus === '6'
              )
            }
          >
            添加附件
          </Button>
          {/* <ShowDetail row={row} size="small" title="查看详情" /> */}
          <Button size="small" onClick={e => this.ShowDetail(e, row)}>
            查看详情
          </Button>
        </div>
      ),
    },
  ];

  handleSearch = values => {
    this.setState({
      searchValue: values,
    });
    const data = {
      ...values,
      pageNum: 1,
      pageSize: 10,
    };
    // const data = {
    //   supplierCode: 's22000',
    //   allocationAreaCode: '',
    //   zoneCode: '',
    //   module: '',
    //   approvalStatus: '',
    //   dataType: '0',
    //   accrueMonth: '2',
    //   pageNum: '1',
    //   pageSize: '1',
    // };
    this.getList(data);
  };

  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };

  handleStep = () => {
    const { selectedRows } = this.state;
    if (selectedRows.length === 1) {
      this.setState({ stepModal: true });
    } else {
      message.error('仅能选择一条数据进行操作');
    }
  };

  ShowDetail = (e, row) => {
    e.stopPropagation();
    this.setState({
      detailModal: true,
      detailRow: row,
    });
  };

  hideModal = () => {
    this.setState({
      detailModal: false,
    });
  };

  showFiles = (e, row) => {
    e.stopPropagation();
    this.setState({
      fileModal: true,
      fileRow: row,
    });
  };

  hideFiles = () => {
    this.setState({
      fileModal: false,
    });
  };

  // 审核意见展示
  handleConfirm = async values => {
    const { passOrReject, selectedRows, searchValue } = this.state;
    this.setState({ showSuggest: false });
    if (passOrReject) {
      const wbTaskMonthIds = selectedRows.map(ele => ele.taskMonthId);
      const res = await Apis.approval({ wbTaskMonthIds, ...values });
      if (res && res.success) {
        this.getList({ ...searchValue, pageSize: 10, pageNum: 1 });
        message.success('已提交审核');
      }
    } else {
      const wbTaskMonthIds = selectedRows.map(ele => ele.taskMonthId);
      const res = await Apis.rejectApproval({ wbTaskMonthIds, ...values });
      if (res && res.success) {
        this.getList({ ...searchValue, pageSize: 10, pageNum: 1 });
        message.success('数据状态已变更为驳回');
      }
    }
  };

  // 隐藏意见框
  handleSuggest = () => {
    this.setState({ showSuggest: false });
  };

  handlePass = () => {
    const { selectedRows } = this.state;
    if (selectedRows.length > 0) {
      const list = selectedRows.filter(ele => ele.approvalStatus === '8');
      if (list.length > 0) {
        message.error('所选的数据存在已审核通过的数据了，请正确选择');
      } else {
        this.setState({
          showSuggest: true,
          passOrReject: true,
          suggestTitle: '确认审核',
        });
      }
    } else {
      message.error('请选择数据操作');
    }
  };

  handleRecall = () => {
    const { selectedRows } = this.state;
    if (selectedRows.length > 0) {
      this.setState({
        showSuggest: true,
        passOrReject: false,
        suggestTitle: '确认驳回',
      });
    } else {
      message.error('请选择数据操作');
    }
  };

  handleHide = () => {
    this.setState({ stepModal: false });
  };

  changePage = pages => {
    const { current, pageSize } = pages;
    const { searchValue } = this.state;
    this.setState({
      obj: {
        pagination: {
          pageSize,
          current,
        },
      },
    });
    const data = {
      ...searchValue,
      pageNum: current,
      pageSize,
    };
    this.getList(data);
  };

  // 列表查询
  getList = async data => {
    const { pageNum, pageSize } = data;
    this.setState({
      loading: true,
    });

    const res = await Apis.queryList(data).finally(() =>
      this.setState({
        loading: false,
      }),
    );
    if (res && res.success) {
      const { list = [], total } = res.obj;
      this.setState({
        obj: {
          list: list || [],
          pagination: {
            pageSize,
            current: pageNum,
            total,
          },
        },
        selectedRows: [],
      });
    }
  };

  render() {
    const {
      btnLoading,
      selectedRows,
      obj,
      loading,
      stepModal,
      detailModal,
      detailRow,
      fileRow,
      fileModal,
      showSuggest,
      suggestTitle,
      passOrReject,
    } = this.state;
    return (
      <div className="table-list">
        <div className="tableListForm">
          <Search
            handleSearch={this.handleSearch}
            datas={obj}
            selectedRows={selectedRows}
            loading={btnLoading}
            approveStep={this.handleStep}
            handleApprove={this.handlePass}
            handleRecall={this.handleRecall}
          />
        </div>
        <StandardTable
          size="small"
          selectedRows={selectedRows}
          data={obj}
          columns={this.columns}
          multiple
          loading={loading}
          onSelectRow={this.handleSelectRows}
          rowKey="id"
          onChange={this.changePage}
        />
        {stepModal && (
          <ShowStep
            visible={stepModal}
            title="审核进度"
            row={selectedRows[0]}
            handleHide={this.handleHide}
          />
        )}
        {detailModal && (
          <ShowDetail
            row={detailRow}
            visible={detailModal}
            handleHide={this.hideModal}
          />
        )}
        {fileModal && (
          <UploadFiles
            visible={fileModal}
            handleHide={this.hideFiles}
            actionUrl="/ospmAccountService/wbTaskAttachRest/upload"
            params={{ wbTaskMonthId: fileRow.taskMonthId }}
          />
        )}
        {showSuggest && (
          <Suggest
            visible={showSuggest}
            title={suggestTitle}
            required={passOrReject}
            handleConfirm={this.handleConfirm}
            handleHide={this.handleSuggest}
          />
        )}
      </div>
    );
  }
}
export default CountWeight;
