import classNames from 'classnames';
import React, { useRef } from 'react';
import style from './index.less';

export function Html2canvasTextArea({
  className,
  placeholder = '请输入',
  bordered = true,
  maxLength,
  showCount = maxLength >= 0,
  autoSize = false,
  disabled = false,
  value,
}) {
  const editNodeRef = useRef(null);
  const { minRows, maxRows } =
    typeof autoSize === 'boolean'
      ? autoSize
        ? { minRows: 1 }
        : { minRows: 3, maxRows: 3 }
      : autoSize;

  return (
    <section
      className={classNames(style.root, className, {
        [style['show-count']]: showCount,
      })}
      data-count={`${value?.length ?? 0}${
        maxLength != null ? ` / ${maxLength}` : ''
      }`}
    >
      <div
        ref={editNodeRef}
        className={classNames(style.textarea, {
          [style.bordered]: bordered,
          [style.disabled]: disabled,
        })}
        style={{
          minHeight: minRows >= 0 ? `${minRows * 14 * 1.5715}px` : 'auto',
          maxHeight: maxRows >= 0 ? `${maxRows * 14 * 1.5715}px` : 'auto',
        }}
        placeholder={placeholder}
      >
        {value}
      </div>
    </section>
  );
}
