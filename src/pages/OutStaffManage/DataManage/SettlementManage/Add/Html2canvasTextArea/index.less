.root {
  position: relative;

  &.show-count::after {
    display: block;
    color: #666666;
    text-align: right;
    content: attr(data-count);
  }

  .clear-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.25);

    &:hover {
      color: #666666;
    }
  }

  .textarea {
    padding: 4px 11px;
    color: #333333;
    font-size: 14px;
    line-height: 1.5715;
    background-color: #fff;
    transition: all 0.3s;
    overflow-y: auto;
    word-break: break-all;
    outline: 0;

    &:empty::after {
      content: attr(placeholder);
      color: #b2b2b2;
    }

    &.bordered {
      border: 1px solid #DDDDDD;
      border-radius: 4px;

      &:not(.disabled):hover {
        border-color: #FF4659;
      }
  
      &:not(.disabled):focus-visible, &:not(.disabled):focus {
        border-color: #e84650;
        box-shadow: 0 0 0 2px rgba(220, 30, 50, 0.2);
      }
    }

    &.disabled {
      color: #BBBBB1;
      background-color: #F5F5F5;
      cursor: not-allowed;
    }
  }
}