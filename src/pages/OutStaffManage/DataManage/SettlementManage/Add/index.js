import React, { useState, Fragment } from 'react';
import {
  Form,
  Modal,
  Col,
  Row,
  Input,
  DatePicker,
  Button,
  Select,
  message,
} from 'antd';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { success, error } from 'utils/utils';
import html2canvas from 'html2canvas';
import dayjs from 'dayjs';
import {
  updateData,
  updateDataFluctuate,
  approvalPassList,
  approvalSendFinaList,
} from '../servers/api';
import { accrueTypeList, statusList, addTitle, confirmType } from '../status';
import { Html2canvasTextArea } from './Html2canvasTextArea';
// import { getOrderLatestStatus } from '../CancellationOrderModal';
const { Item: FormItem } = Form;
const { TextArea } = Input;
// let overTime = false; // 是否超过当前月12号   true 未超过  false 超过

const Add = props => {
  const {
    onOk,
    visible,
    initialValues,
    sfvisible,
    outvisible,
    type,
    ...rest
  } = props;
  const [form] = Form.useForm();
  // const [isOut, setIsOut] = useState(outvisible);
  const [loading, setLoading] = useState(false);

  const price2 = [
    { required: true, message: '请输入' },
    {
      pattern: /^([-]?[0-9]\d{0,4})(\.[0-9]{1,4})?$|^([-]?100000)$|^0\.[0-9]{0,4}$/,
      message: '请输入正负100000之间的数字，最多保留四位小数',
    },
  ];

  const rs1 = [
    { required: true, message: '请输入' },
    {
      pattern: /^([-]?[0-9]\d{0,1})(\.[0-9]{1,2})?$|^0\.[0-9]{0,2}$/,
      message: '请输入0-68数字，最多2位小数',
    },
    {
      validator: async (_, names) => {
        if (names < 0 || names > 68) {
          return Promise.reject(new Error('请输入0-68数字，最多2位小数'));
        }
      },
    },
  ];

  const rs2 = [
    { required: true, message: '请输入' },
    {
      pattern: /^([-]?[0-9]\d{0,1})(\.[0-9]{1,2})?$|^0\.[0-9]{0,2}$/,
      message: '请输入0-40数字，最多2位小数',
    },
    {
      validator: async (_, names) => {
        if (names < 0 || names > 40) {
          return Promise.reject(new Error('请输入0-40数字，最多2位小数'));
        }
      },
    },
  ];

  const rs3 = [
    { required: true, message: '请输入' },
    {
      pattern: /^([0-9]\d{0,4})(\.[0-9]{1,2})?$|^(100000)$|^0\.[0-9]{0,2}$/,
      message: '请输入0-100000之间的数字，最多保留2位小数',
    },
  ];

  const rs4 = [
    { required: true, message: '请输入' },
    {
      pattern: /^([-]?[0-9]\d{0,4})(\.[0-9]{1,2})?$|^([-]?100000)$|^0\.[0-9]{0,2}$/,
      message: '请输入6位数以下数字，最多保留2位小数',
    },
  ];

  // 表单初始值的处理
  const tem = initialValues ? cloneDeep(initialValues) : null;
  if (tem !== null) {
    tem.yearMonthQr = moment(tem.yearMonthQr);
    tem.accrueType = accrueTypeList[tem.accrueType]
      ? accrueTypeList[tem.accrueType]
      : '';
    tem.startDate = tem.startDate
      ? moment(tem.startDate).format('YYYY-MM-DD')
      : '';
    tem.endDate = tem.endDate ? moment(tem.endDate).format('YYYY-MM-DD') : '';
  } else {
    // tem.differenceType = 0;
    form.setFieldsValue({
      differenceType: 0,
      forkliftByDistance: 0,
      forkliftByLimit: 0,
      supplierName: undefined,
    });
  }
  // 表单默认值
  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
    initialValues: {
      ...tem,
    },
  };

  // 提交时请求
  const submit = async () => {
    // debugger;
    if (type === 'confirm') {
      if (!form.getFieldValue('confirm')) {
        message.error('请选择操作');
        return;
      }
      if (
        [4, 6].includes(form.getFieldValue('confirm')) &&
        !form.getFieldValue('approvalWord')
      ) {
        message.error('请输入原因');
        return;
      }

      // 后端校验
      // // 中转场总部负责人【推送】操作时，需要校验这条数据在财务系统的结算状态：
      // // ①如果是【对账中】、【结算中】、【结算完成】则推送失败，回滚到旧数据
      // //   提示文案：本条数据已进入财务对账结算环节，不支持修改，将还原数据到修改前
      // //   【关闭】按钮，是关闭整个修改页面
      // // ②如果是【新数据】、【冻结】、【未对账】则推送成功，把此条数据推给财务系统做红冲处理
      // const { settleStatus } = await getOrderLatestStatus({
      //   flowId: initialValues.flowId,
      // });
      // if ([2, 3, 4].includes(settleStatus)) {
      //   Modal.warn({
      //     title:
      //       '本条数据已进入财务对账结算环节，不支持修改，将还原数据到修改前',
      //     okText: '关闭',
      //     onOk() {
      //       rest.onCancel?.();
      //     },
      //   });
      //   return;
      // }

      const pam = {
        idList: [tem.id],
        approvalStatus: form.getFieldValue('confirm'),
        approvalWord: form.getFieldValue('approvalWord'),
      };
      try {
        setLoading(true);
        const res = [1, 3, 4].includes(form.getFieldValue('confirm'))
          ? await approvalPassList(pam)
          : await approvalSendFinaList(pam);
        if (res.success) {
          setLoading(false);
          success(`操作成功！`);
          onOk();
        } else {
          error(res.errorMessage);
          setLoading(false);
        }
      } catch {
        setLoading(false);
      }
      return;
    }

    const formData = await form.validateFields();
    let param = {};
    if (type === 'edit') {
      param = {
        id: tem.id,
        fixLoadWeight: formData.fixLoadWeight,
        fixUnloadWeight: formData.fixUnloadWeight,
        fixForkliftWeight: formData.fixForkliftWeight,
        fixRemark: formData.fixRemark,
        topKpi: formData.topKpi,
        provincialKpi: formData.provincialKpi,
        positiveExcitation1: formData.positiveExcitation1,
        positiveExcitation2: formData.positiveExcitation2,
        negativeExcitation1: formData.negativeExcitation1,
        negativeExcitation2: formData.negativeExcitation2,
        negativeExcitation3: formData.negativeExcitation3,
        negativeExcitation4: formData.negativeExcitation4,
        negativeExcitation5: formData.negativeExcitation5,
        otherAmount: formData.otherAmount,
        remark: formData.remark,
        approvalStatus: formData.approvalStatus,
      };
    } else {
      param = {
        id: tem.id,
        fixLoadWeight: formData.fixLoadWeight,
        fixUnloadWeight: formData.fixUnloadWeight,
        fixForkliftWeight: formData.fixForkliftWeight,
        fixRemark: formData.fixRemark,
      };
    }
    try {
      setLoading(true);
      const res =
        type === 'edit'
          ? await updateData(param)
          : await updateDataFluctuate(param);
      if (res.success) {
        setLoading(false);
        success(`${type === 'edit' ? '修改' : '增减工作量'}成功！`);
        onOk();
      } else {
        error(res.errorMessage);
        setLoading(false);
      }
    } catch {
      setLoading(false);
    }
  };
  const handleCancel = () => {
    form.resetFields();
  };

  const [downloading, setDownloading] = useState(false);

  /**
   * 下载图片
   */
  const handleDownloadImg = async () => {
    const containerNode = document.querySelector(
      '.SettlementManage__Add__root .ant-modal-body',
    );
    if (!containerNode) {
      message.warning('系统出错，请联系管理员');
      return;
    }

    setDownloading(true);
    setTimeout(async () => {
      try {
        const canvas = await html2canvas(containerNode);
        const href = canvas.toDataURL();

        let a = document.createElement('a');
        a.style.display = 'none';
        a.href = href;
        a.download = initialValues.flowId || 'unknown-id';
        document.body.appendChild(a);
        a.click();
        a.remove();
        a = null;
      } catch (e) {
        //
      }

      setDownloading(false);
    });
  };

  return (
    <Modal
      className="SettlementManage__Add__root"
      title={addTitle[type]}
      maskClosable={false}
      onCancel={handleCancel}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={
        type === 'details'
          ? [
              <Button
                key="download"
                loading={downloading}
                onClick={handleDownloadImg}
              >
                下载图片
              </Button>,
              <Button key="back" onClick={rest.onCancel}>
                取消
              </Button>,
            ]
          : [
              <Button
                key="download"
                loading={downloading}
                onClick={handleDownloadImg}
              >
                下载图片
              </Button>,
              <Button key="back" onClick={rest.onCancel}>
                取消
              </Button>,
              <Button
                key="submit"
                loading={loading}
                type="primary"
                onClick={submit}
              >
                确定
              </Button>,
            ]
      }
    >
      <Form {...formProps}>
        <Row gutter={{ md: 4 }} style={{ marginBottom: `10px` }}>
          <Col md={11} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>基础信息</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="业务单号" name="flowId">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem
              label="归属月份"
              name="yearMonthQr"
              rules={[
                {
                  required: true,
                  message: '请选择月份',
                },
              ]}
            >
              <DatePicker
                showToday
                disabled
                picker="month"
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="分拨区代码" name="allocationAreaCode">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem label="分拨区" name="allocationArea">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="网点代码" name="zoneCode">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem label="网点名称" name="zoneName">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="法人" name="zoneCodeLegalPerson">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }} style={{ marginBottom: `10px` }}>
          <Col md={11} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>供应商信息</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="供应商名称" name="supplierName">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem label="供应商编码" name="supplierNo">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="客商编码" name="supplierCode">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="合同生效时间" name="startDate">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem label="合同失效时间" name="endDate">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }} style={{ marginBottom: `10px` }}>
          <Col md={11} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>单价信息</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="计价类型" name="accrueType">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem label="计费单位" name="accrueUnit">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="税率" name="taxRate">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem label="装车单价" name="loadPrice">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="卸车单价" name="unloadPrice">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem label="叉车单价" name="forkliftPrice">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }} style={{ marginBottom: `10px` }}>
          <Col md={11} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>工作量信息</div>
          </Col>
          <Col md={11} sm={24}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>增减工作量</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="装车工作量" name="loadWeight">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.fixLoadWeight !== curValues.fixLoadWeight
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <FormItem
                  label="增减装车工作量"
                  name="fixLoadWeight"
                  rules={[...price2]}
                >
                  <Input
                    placeholder="请输入"
                    style={
                      Math.abs(getFieldValue('fixLoadWeight')) > 100
                        ? { color: 'red' }
                        : {}
                    }
                    disabled={!['edit', 'add'].includes(type)}
                    onChange={v => {
                      let num = '';
                      if (v.target.value && getFieldValue('fixUnloadWeight')) {
                        num =
                          Number(v.target.value) +
                          Number(getFieldValue('fixUnloadWeight'));
                      }
                      if (typeof num === 'number') {
                        if (num.toString().indexOf('.') > -1) {
                          num = num
                            .toString()
                            .substr(0, num.toString().indexOf('.') + 5);
                        }
                        return form.setFieldsValue({
                          fixLoadUnloadWeight: Number(num),
                        });
                      }
                      form.setFieldsValue({ fixLoadUnloadWeight: '' });
                    }}
                  />
                </FormItem>
              )}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="卸车工作量" name="unloadWeight">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.fixUnloadWeight !== curValues.fixUnloadWeight
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <FormItem
                  label="增减卸车工作量"
                  name="fixUnloadWeight"
                  rules={[...price2]}
                >
                  <Input
                    placeholder="请输入"
                    style={
                      Math.abs(getFieldValue('fixUnloadWeight')) > 100
                        ? { color: 'red' }
                        : {}
                    }
                    disabled={!['edit', 'add'].includes(type)}
                    onChange={v => {
                      let num = '';
                      if (v.target.value && getFieldValue('fixLoadWeight')) {
                        num =
                          Number(v.target.value) +
                          Number(getFieldValue('fixLoadWeight'));
                      }
                      if (typeof num === 'number') {
                        if (num.toString().indexOf('.') > -1) {
                          num = num
                            .toString()
                            .substr(0, num.toString().indexOf('.') + 5);
                        }
                        return form.setFieldsValue({
                          fixLoadUnloadWeight: Number(num),
                        });
                      }
                      form.setFieldsValue({ fixLoadUnloadWeight: '' });
                    }}
                  />
                </FormItem>
              )}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="装卸合计工作量" name="loadUnloadWeight">
              <Input disabled />
            </FormItem>
          </Col>

          <Col md={11} sm={24}>
            <FormItem label="装卸增减合计" name="fixLoadUnloadWeight">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="叉车工作量" name="forkliftWeight">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.fixForkliftWeight !== curValues.fixForkliftWeight
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <FormItem
                  label="增减叉车工作量"
                  name="fixForkliftWeight"
                  rules={[...price2]}
                >
                  <Input
                    placeholder="请输入"
                    style={
                      Math.abs(getFieldValue('fixForkliftWeight')) > 100
                        ? { color: 'red' }
                        : {}
                    }
                    disabled={!['edit', 'add'].includes(type)}
                  />
                </FormItem>
              )}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={22} sm={24} style={{ marginTop: 25 }}>
            <FormItem
              label="增减工作量备注"
              labelCol={{ xl: 4 }}
              wrapperCol={{ xl: 20 }}
              name="fixRemark"
              rules={[
                {
                  required: true,
                  message: '请输入备注',
                },
              ]}
            >
              {downloading ? (
                <Html2canvasTextArea
                  showCount
                  maxLength={200}
                  placeholder="请输入"
                  autoSize={{ minRows: 3, maxRows: 5 }}
                  disabled={type === 'confirm' || type === 'details'}
                />
              ) : (
                <TextArea
                  showCount
                  maxLength={200}
                  placeholder="请输入"
                  autoSize={{ minRows: 3, maxRows: 5 }}
                  disabled={type === 'confirm' || type === 'details'}
                />
              )}
            </FormItem>
          </Col>
        </Row>

        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>KPI考核</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="总部KPI考核得分" name="topKpi" rules={[...rs1]}>
              <Input placeholder="请输入" disabled={type !== 'edit'} />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem
              label="省区KPI考核得分"
              name="provincialKpi"
              rules={[...rs2]}
            >
              <Input placeholder="请输入" disabled={type !== 'edit'} />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>正激励</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.positiveExcitation1 !== curValues.positiveExcitation1
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <FormItem
                  label="超重超方"
                  name="positiveExcitation1"
                  rules={[...rs3]}
                >
                  <Input
                    placeholder="请输入"
                    style={
                      Math.abs(getFieldValue('positiveExcitation1')) > 1000
                        ? { color: 'red' }
                        : {}
                    }
                    disabled={type !== 'edit'}
                  />
                </FormItem>
              )}
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.positiveExcitation2 !== curValues.positiveExcitation2
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <FormItem
                  label="其他（差错上报等）"
                  name="positiveExcitation2"
                  rules={[...rs3]}
                >
                  <Input
                    placeholder="请输入"
                    style={
                      Math.abs(getFieldValue('positiveExcitation2')) > 1000
                        ? { color: 'red' }
                        : {}
                    }
                    disabled={type !== 'edit'}
                  />
                </FormItem>
              )}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>负激励</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.negativeExcitation1 !== curValues.negativeExcitation1
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <FormItem
                  label="遗失理赔金额"
                  name="negativeExcitation1"
                  rules={[...rs3]}
                >
                  <Input
                    placeholder="请输入"
                    style={
                      Math.abs(getFieldValue('negativeExcitation1')) > 1000
                        ? { color: 'red' }
                        : {}
                    }
                    disabled={type !== 'edit'}
                  />
                </FormItem>
              )}
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.negativeExcitation2 !== curValues.negativeExcitation2
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <FormItem
                  label="野蛮操作金额"
                  name="negativeExcitation2"
                  rules={[...rs3]}
                >
                  <Input
                    placeholder="请输入"
                    style={
                      Math.abs(getFieldValue('negativeExcitation2')) > 1000
                        ? { color: 'red' }
                        : {}
                    }
                    disabled={type !== 'edit'}
                  />
                </FormItem>
              )}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.negativeExcitation3 !== curValues.negativeExcitation3
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <FormItem
                  label="货损理赔金额"
                  name="negativeExcitation3"
                  rules={[...rs3]}
                >
                  <Input
                    placeholder="请输入"
                    style={
                      Math.abs(getFieldValue('negativeExcitation3')) > 1000
                        ? { color: 'red' }
                        : {}
                    }
                    disabled={type !== 'edit'}
                  />
                </FormItem>
              )}
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.negativeExcitation4 !== curValues.negativeExcitation4
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <FormItem
                  label="业务差错金额"
                  name="negativeExcitation4"
                  rules={[...rs3]}
                >
                  <Input
                    placeholder="请输入"
                    style={
                      Math.abs(getFieldValue('negativeExcitation4')) > 1000
                        ? { color: 'red' }
                        : {}
                    }
                    disabled={type !== 'edit'}
                  />
                </FormItem>
              )}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.negativeExcitation5 !== curValues.negativeExcitation5
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <FormItem
                  label="乱走货金额"
                  name="negativeExcitation5"
                  rules={[...rs3]}
                >
                  <Input
                    placeholder="请输入"
                    style={
                      Math.abs(getFieldValue('negativeExcitation5')) > 1000
                        ? { color: 'red' }
                        : {}
                    }
                    disabled={type !== 'edit'}
                  />
                </FormItem>
              )}
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>其他</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="其他费用" name="otherAmount" rules={[...rs4]}>
              <Input placeholder="请输入" disabled={type !== 'edit'} />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={22} sm={24} style={{ marginTop: 25 }}>
            <FormItem
              label="修改原因备注"
              labelCol={{ xl: 4 }}
              wrapperCol={{ xl: 20 }}
              name="remark"
              //   rules={[
              //     {
              //       required: true,
              //       message: '请输入备注',
              //     },
              //   ]}
            >
              {downloading ? (
                <Html2canvasTextArea
                  showCount
                  maxLength={200}
                  placeholder="请输入"
                  autoSize={{ minRows: 3, maxRows: 5 }}
                  disabled={type !== 'edit'}
                />
              ) : (
                <TextArea
                  showCount
                  maxLength={200}
                  placeholder="请输入"
                  autoSize={{ minRows: 3, maxRows: 5 }}
                  disabled={type !== 'edit'}
                />
              )}
            </FormItem>
          </Col>
        </Row>

        {/* 操作记录 */}
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>操作记录</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="场地确认人" name="submitter">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem
              label="场地确认时间"
              name="submitTime"
              getValueProps={val => ({ value: val ? dayjs(val) : undefined })}
            >
              <DatePicker
                placeholder=""
                showTime
                disabled
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem label="分拨区审核人" name="approvalUserNo">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem
              label="分拨区审核时间"
              name="approvalTime"
              getValueProps={val => ({ value: val ? dayjs(val) : undefined })}
            >
              <DatePicker
                placeholder=""
                showTime
                disabled
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
        </Row>

        <Row gutter={{ md: 4 }} style={{ marginBottom: `10px` }}>
          <Col md={11} sm={24} offset={1}>
            <div style={{ fontSize: '20px', fontWeight: 600 }}>实际应付</div>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="结算费用" name="totalNeedAmount">
              <Input disabled />
            </FormItem>
          </Col>
          <Col md={11} sm={24}>
            <FormItem label="含税预计预提总费用" name="totalPreAmount">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="偏离率" name="deviationRate">
              <Input disabled />
            </FormItem>
          </Col>
        </Row>

        <Row gutter={{ md: 4 }}>
          <Col md={11} sm={24}>
            <FormItem label="状态" name="approvalStatus">
              <Select
                allowClear
                disabled
                options={statusList}
                placeholder="请选择"
              ></Select>
            </FormItem>
          </Col>
        </Row>

        {type === 'confirm' && (
          <Fragment>
            <Row gutter={{ md: 4 }}>
              <Col md={11} sm={24}>
                <FormItem
                  label="操作"
                  name="confirm"
                  rules={[
                    {
                      required: true,
                      message: '请选择',
                    },
                  ]}
                >
                  <Select
                    allowClear
                    options={confirmType[tem.approvalStatus]}
                    placeholder="请选择"
                    onChange={() => {
                      form.setFieldsValue({ approvalWord: undefined });
                    }}
                  ></Select>
                </FormItem>
              </Col>
            </Row>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.confirm !== curValues.confirm
              }
              noStyle
            >
              {({ getFieldValue }) =>
                [4, 6].includes(getFieldValue('confirm')) && (
                  <Row gutter={{ md: 4 }}>
                    <Col md={22} sm={24}>
                      <FormItem
                        label="请反馈原因:"
                        labelCol={{ xl: 4 }}
                        wrapperCol={{ xl: 20 }}
                        name="approvalWord"
                        rules={[
                          {
                            required: true,
                            message: '请输入原因',
                          },
                        ]}
                      >
                        {downloading ? (
                          <Html2canvasTextArea
                            showCount
                            maxLength={200}
                            placeholder="请输入"
                            autoSize={{ minRows: 3, maxRows: 5 }}
                          />
                        ) : (
                          <TextArea
                            showCount
                            maxLength={200}
                            placeholder="请输入"
                            autoSize={{ minRows: 3, maxRows: 5 }}
                          />
                        )}
                      </FormItem>
                    </Col>
                  </Row>
                )
              }
            </FormItem>
          </Fragment>
        )}
      </Form>
    </Modal>
  );
};

export default Add;
