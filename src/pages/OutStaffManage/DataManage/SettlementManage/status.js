export const statusList = [
  {
    label: '工作量待固化',
    value: 0,
  },
  {
    label: '工作量已固化',
    value: 1,
  },
  {
    label: '待确认',
    value: 2,
  },
  {
    label: '场地已确认',
    value: 3,
  },
  {
    label: '分拨区已确认',
    value: 4,
  },
  {
    label: '已推送',
    value: 5,
  },
  {
    label: '待修改',
    value: 6,
  },
  // {
  //   label: '已付款', // 已付款调到了付款状态里面的结算完成
  //   value: 7,
  // },
  {
    label: '已作废',
    value: 11,
  },
];

export const operationList = {
  1: '是否确认已选中数据',
  3: '是否确认审核已选中数据',
  4: '是否不通过已选中数据',
  5: '是否确认推送已选中数据',
  6: '是否确认驳回已选数据不推送至财务系统',
};

export const accrueTypeList = {
  1: '计费重量',
  2: '实际重量',
  3: '操作货量',
  4: '计件',
  5: '计板',
  6: '按天',
  7: '按时',
  8: '全场均摊',
};

export const addTitle = {
  edit: '顺心结算详情-修改',
  add: '顺心结算详情-增减工作量',
  details: '顺心结算详情',
  confirm: '顺心结算详情',
};

export const confirmType = {
  4: [
    {
      label: '推送',
      value: 5,
    },
    {
      label: '不推送',
      value: 6,
    },
  ],
  3: [
    {
      label: '审核通过',
      value: 3,
    },
    {
      label: '审核不通过',
      value: 4,
    },
  ],
  2: [
    {
      label: '确认',
      value: 1,
    },
  ],
};

/** 付款状态 obj */
export const PAYMENT_STATUS_OBJ = {
  1: { label: '新数据', value: 1 },
  2: { label: '冻结', value: 2 },
  3: { label: '未对账', value: 3 },
  4: { label: '对账中', value: 4 },
  5: { label: '结算中', value: 5 },
  6: { label: '结算完成', value: 6 },
  7: { label: '作废', value: 7 },
};

/** 付款状态 options */
export const paymentStatusOptions = Object.values(PAYMENT_STATUS_OBJ);
