import request from '@/utils/request';

// 查询列表接口
export function search(params) {
  return request(`/tdmsAccrueService/accrueManagementPreRest/queryFinaList`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

/**
 * @description: 根据网点查询供应商列表
 * @param {type} orgCode
 * @return:
 */
export function supplierSearch(params) {
  return request(
    `/tdmsAccrueService/supplierRestService/querySupplierByZoneCode`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 确认、审核通过、审核不通过接口
export function approvalPassList(params) {
  return request(
    `/tdmsAccrueService/accrueManagementPreRest/approvalPassList`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 推送、不推送接口
export function approvalSendFinaList(params) {
  return request(
    `/tdmsAccrueService/accrueManagementPreRest/approvalSendFinaList`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 增减工作量接口
export function updateDataFluctuate(params) {
  return request(
    `/tdmsAccrueService/accrueManagementPreRest/updateDataFluctuate`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 修改接口
export function updateData(params) {
  return request(`/tdmsAccrueService/accrueManagementPreRest/updateData`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
