import { Button, Form, Input, Modal } from 'antd';
import classNames from 'classnames';
import React, { useState } from 'react';
import style from './index.less';
import request from '@/utils/request';

export const CancellationOrderModal = ({
  className,
  visible,
  data,
  onCancel: parentOnCancel,
  afterClose,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  /**
   * 关闭前清理数据
   */
  const onCancel = () => {
    parentOnCancel?.();

    // 重置数据
    form.resetFields();
  };

  /**
   * 作废订单
   * @param {{ remark: string }} body modal content
   */
  const handleFinish = async body => {
    // 为防止页面显示的付款状态不是最新的，在提交作废操作时，需要校验这条数据在财务系统的结算状态
    // 如果是【对账中】、【结算中】、【结算完成】则提交作废操作失败
    setLoading(true);

    const { settleStatus } = await getOrderLatestStatus({
      flowId: data.flowId,
    });
    if ([2, 3, 4].includes(settleStatus)) {
      Modal.warn({
        title: '数据已进入财务对账结算环节，不支持作废操作',
        okText: '关闭',
        onOk() {
          onCancel();
        },
      });
      return;
    }

    try {
      // 作废
      await request(`/tdmsAccrueService/accrueManagementPreRest/cancelData`, {
        method: 'POST',
        body: {
          id: data.id,
          ...body,
        },
      });
    } finally {
      setLoading(false);
    }

    // 关闭模态框
    onCancel();
  };

  return (
    <Modal
      className={classNames(style.root, className)}
      title="作废后不能再修改提交结算，是否确认作废所选数据？"
      footer={null}
      visible={visible}
      onCancel={onCancel}
      afterClose={afterClose}
      maskClosable={false}
    >
      <Form form={form} onFinish={handleFinish}>
        <Form.Item
          label="原因"
          name="remark"
          rules={[{ required: true, message: '请输入作废原因' }]}
        >
          <Input.TextArea
            placeholder="请输入作废原因"
            maxLength={50}
            showCount
          />
        </Form.Item>
        <Form.Item noStyle>
          <div className={style.btnGroup}>
            <Button onClick={onCancel} disabled={loading}>
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              确定
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

/**
 * 获取订单最新状态
 * @param {{ flowId: string; }} params 参数
 * @returns {
 *   新数据、作废、冻结 -> 不可结算 核准 -> 未对账、对账中、结算中、结算完成
 *   settleStatus: 0 | 1 | 2 | 3 | 4; // 0 不可结算 1 未对账 2 对账中 3 结算中 4 结算完成
 *   settleStatusValue: string;
 * }
 */
export const getOrderLatestStatus = async params => {
  const { obj } = await request(
    `/tdmsAccrueService/accrueManagementPreRest/queryPayableDetailStatus`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
  return obj;
};
