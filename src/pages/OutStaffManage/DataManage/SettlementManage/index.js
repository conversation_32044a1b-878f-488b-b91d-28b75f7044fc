import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Form,
  Input,
  Tooltip,
  Modal,
  Select,
  DatePicker,
  message,
  Button,
} from 'antd';
import { ExportOutlined, PlusOutlined, RedoOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import { cloneDeep } from 'lodash';
import dayjs from 'dayjs';
import { success, error } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import ImportButton from 'src/components/ImportButton';
import AuthButton from 'src/components/AuthButton';
import authDecorator from 'src/components/AuthDecorator';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import SuppliersSearch from 'src/components/SuppliersSearch';
import { OverflowText } from 'src/components/OverflowText';
import { search, approvalPassList, approvalSendFinaList } from './servers/api';
// eslint-disable-next-line import/extensions
import { querySuppliers } from '@/services/supplierApi';
import Add from './Add';
import {
  statusList,
  operationList,
  accrueTypeList,
  paymentStatusOptions,
} from './status';
import request from '@/utils/request';
import style from './index.less';

import './index.scss';
import {
  CancellationOrderModal,
  getOrderLatestStatus,
} from './CancellationOrderModal';

const { Item: FormItem } = Form;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const rowKey = 'id';

let condition = {};
const Page = ({
  userInfo,
  logRoleCode,
  areaListSF,
  areaListSX,
  moduleCode,
}) => {
  const [form] = Form.useForm();
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 当前操作行的数据
  const [supplierNameList, setSupplierNameList] = useState([]);
  const [userOrg, setUserOrg] = useState('SF');
  const [type, setType] = useState('');
  const [
    cancellationOrderModalVisible,
    setCancellationOrderModalVisible,
  ] = useState(false);
  const [summaryData, setSummaryData] = useState({}); // 汇总数据
  const [refreshCustomerCodeLoading, setRefreshCustomerCodeLoading] = useState(
    false,
  );

  /**
   * 获取汇总数据
   */
  const fetchSummaryData = async () => {
    const { obj } = await request(
      `/tdmsAccrueService/accrueManagementPreRest/queryFinaSum`,
      {
        method: 'POST',
        body: condition,
      },
    );
    setSummaryData(obj);
  };

  let approvalWord = '';

  const showNum = v => {
    if (v) {
      const val = v.toString();
      if (val.indexOf('.') > -1) {
        return val.substr(0, val.indexOf('.') + 3);
      }
      return v;
    }
    return v;
  };

  const columns = [
    {
      title: '基础信息',
      children: [
        {
          title: '业务单号',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'flowId',
        },
        {
          title: '归属月份',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'yearMonthQr',
        },
        {
          title: '分拨区代码',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'allocationAreaCode',
        },
        {
          title: '分拨区',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'allocationArea',
        },
        {
          title: '网点代码',
          align: 'center',
          ellipsis: true,
          width: 200,
          dataIndex: 'zoneCode',
        },
        {
          title: '网点名称',
          align: 'center',
          ellipsis: true,
          width: 150,
          dataIndex: 'zoneName',
        },
        {
          title: '法人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'zoneCodeLegalPerson',
        },
      ],
    },
    {
      title: '供应商信息',
      children: [
        {
          title: '供应商名称',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'supplierName',
        },
        {
          title: '供应商编码',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'supplierNo',
        },
        {
          title: '客商编码',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'supplierCode',
        },
        {
          title: '合同生效时间',
          align: 'center',
          ellipsis: true,
          width: 150,
          dataIndex: 'startDate',
          render: v => v && moment(v).format('YYYY-MM-DD'),
        },
        {
          title: '合同失效时间',
          align: 'center',
          ellipsis: true,
          width: 150,
          dataIndex: 'endDate',
          render: v => v && moment(v).format('YYYY-MM-DD'),
        },
      ],
    },
    {
      title: '单价信息',
      children: [
        {
          title: '计价方式',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'accrueType',
          render: value => (accrueTypeList[value] ? accrueTypeList[value] : ''),
        },
        {
          title: '计费单位',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'accrueUnit',
        },
        {
          title: '税率',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'taxRate',
        },
        {
          title: '装车单价',
          align: 'center',
          ellipsis: true,
          width: 120,
          dataIndex: 'loadPrice',
        },
        {
          title: '卸车单价',
          align: 'center',
          ellipsis: true,
          width: 120,
          dataIndex: 'unloadPrice',
        },
        {
          title: '叉车单价',
          align: 'center',
          ellipsis: true,
          width: 120,
          dataIndex: 'forkliftPrice',
        },
      ],
    },
    {
      title: '工作量信息',
      children: [
        {
          title: '装车工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'loadWeight',
          render: v => showNum(v),
        },
        {
          title: '卸车工作量',
          align: 'center',
          ellipsis: true,
          width: 120,
          dataIndex: 'unloadWeight',
          render: v => showNum(v),
        },
        {
          title: '装卸合计工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'loadUnloadWeight',
          render: v => showNum(v),
        },
        {
          title: '叉车工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftWeight',
          render: v => showNum(v),
        },
      ],
    },
    {
      title: '增减工作量信息',
      children: [
        {
          title: '增减装车工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'fixLoadWeight',
          render: v => {
            if (Math.abs(v) > 100) {
              return <span style={{ color: 'red' }}>{showNum(v)}</span>;
            }
            return showNum(v);
          },
        },
        {
          title: '增减卸车工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'fixUnloadWeight',
          render: v => {
            if (Math.abs(v) > 100) {
              return <span style={{ color: 'red' }}>{showNum(v)}</span>;
            }
            return showNum(v);
          },
        },
        {
          title: '装卸增减合计',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'fixLoadUnloadWeight',
          render: v => {
            if (Math.abs(v) > 100) {
              return <span style={{ color: 'red' }}>{showNum(v)}</span>;
            }
            return showNum(v);
          },
        },
        {
          title: '增减叉车工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'fixForkliftWeight',
          render: v => {
            if (Math.abs(v) > 100) {
              return <span style={{ color: 'red' }}>{showNum(v)}</span>;
            }
            return showNum(v);
          },
        },
        {
          title: '增减工作量备注',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'fixRemark',
          render: value => (
            <Tooltip placement="top" title={value}>
              <div
                style={{
                  width: '250px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {value}
              </div>
            </Tooltip>
          ),
        },
      ],
    },
    {
      title: '总工作量信息',
      children: [
        {
          title: '装车总工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'totalLoadWeight',
          render: v => showNum(v),
        },
        {
          title: '卸车总工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'totalUnloadWeight',
          render: v => showNum(v),
        },
        {
          title: '装卸合计总工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'totalLoadUnloadWeight',
          render: v => showNum(v),
        },
        {
          title: '叉车总工作量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'totalForkliftWeight',
          render: v => showNum(v),
        },
      ],
    },
    {
      title: '应付工作量金额',
      children: [
        {
          title: '装车应付金额',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'loadAmount',
        },
        {
          title: '卸车应付金额',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'unloadAmount',
        },
        {
          title: '装卸应付金额',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'loadUnloadAmount',
        },
        {
          title: '叉车应付金额',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftAmount',
        },
        {
          title: '合计应付',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'totalAmount',
        },
      ],
    },
    {
      title: 'KPI考核',
      children: [
        {
          title: '总部KPI考核得分',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'topKpi',
        },
        {
          title: '省区KPI考核得分',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'provincialKpi',
        },
        {
          title: 'KPI费率',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'kpiRate',
        },
        {
          title: 'KPI调整金额',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'kpiFix',
        },
      ],
    },
    {
      title: '正激励',
      children: [
        {
          title: '正激励-超重超方',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'positiveExcitation1',
          render: v => {
            if (Math.abs(v) > 1000) {
              return <span style={{ color: 'red' }}>{v}</span>;
            }
            return v;
          },
        },
        {
          title: '正激励-其他（差错上报等）',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'positiveExcitation2',
          render: v => {
            if (Math.abs(v) > 1000) {
              return <span style={{ color: 'red' }}>{v}</span>;
            }
            return v;
          },
        },
      ],
    },
    {
      title: '负激励',
      children: [
        {
          title: '负激励-遗失理赔金额',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'negativeExcitation1',
          render: v => {
            if (Math.abs(v) > 1000) {
              return <span style={{ color: 'red' }}>{v}</span>;
            }
            return v;
          },
        },
        {
          title: '负激励-野蛮操作金额',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'negativeExcitation2',
          render: v => {
            if (Math.abs(v) > 1000) {
              return <span style={{ color: 'red' }}>{v}</span>;
            }
            return v;
          },
        },
        {
          title: '负激励-货损理赔金额',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'negativeExcitation3',
          render: v => {
            if (Math.abs(v) > 1000) {
              return <span style={{ color: 'red' }}>{v}</span>;
            }
            return v;
          },
        },
        {
          title: '负激励-业务差错金额',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'negativeExcitation4',
          render: v => {
            if (Math.abs(v) > 1000) {
              return <span style={{ color: 'red' }}>{v}</span>;
            }
            return v;
          },
        },
        {
          title: '负激励-乱走货金额',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'negativeExcitation5',
          render: v => {
            if (Math.abs(v) > 1000) {
              return <span style={{ color: 'red' }}>{v}</span>;
            }
            return v;
          },
        },
      ],
    },
    {
      title: '其他',
      children: [
        {
          title: '其他费用',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'otherAmount',
        },
      ],
    },
    {
      title: '实际应付',
      children: [
        {
          title: '结算费用',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'totalNeedAmount',
        },
      ],
    },
    {
      title: '预计预提总费用',
      children: [
        {
          title: '含税预计预提总费用',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'totalPreAmount',
        },
      ],
    },
    {
      title: '偏离率',
      children: [
        {
          title: '偏离率',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'deviationRate',
          render: v => v && `${v}%`,
        },
      ],
    },
    {
      title: '操作记录',
      children: [
        {
          title: '导入操作人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'excelSubmitter',
        },
        {
          title: '导入操作时间',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'excelSubmitTime',
          render: v => v && moment(v).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          title: '增减工作量操作人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'fixSubmitter',
        },
        {
          title: '增减工作量操作时间',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'fixSubmitTime',
          render: v => v && moment(v).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          title: '修改操作人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'fixModifier',
        },
        {
          title: '修改操作时间',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'fixModifyTime',
          render: v => v && moment(v).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          title: '场地确认人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'submitter',
        },
        {
          title: '场地确认时间',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'submitTime',
          render: v => v && moment(v).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          title: '分拨区审核人',
          align: 'center',
          ellipsis: true,
          width: 120,
          dataIndex: 'approvalUserNo',
        },
        {
          title: '分拨区审核时间',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'approvalTime',
          render: v => v && moment(v).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          title: '推送操作人',
          align: 'center',
          ellipsis: true,
          width: 170,
          dataIndex: 'leaderUserNo',
        },
        {
          title: '推送操作时间',
          align: 'center',
          ellipsis: true,
          width: 200,
          dataIndex: 'leaderApprovalTime',
          render: v => v && moment(v).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          title: '不推送原因',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'approvalWord',
          render: value => (
            <Tooltip placement="top" title={value}>
              <div
                style={{
                  width: '250px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {value}
              </div>
            </Tooltip>
          ),
        },
        {
          title: '修改原因备注',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'remark',
          render: value => (
            <Tooltip placement="top" title={value}>
              <div
                style={{
                  width: '250px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {value}
              </div>
            </Tooltip>
          ),
        },
        {
          title: '是否手动导入',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'excelImport',
          render: value => (value ? '是' : '否'),
        },
        {
          title: '导入人工号',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'excelHisotrySubmitter',
        },
        {
          title: '作废原因',
          align: 'center',
          ellipsis: true,
          dataIndex: 'delRemark',
          render(v) {
            return <OverflowText width={200}>{v}</OverflowText>;
          },
        },
        {
          title: '作废人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'delModifier',
        },
        {
          title: '作废时间',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'delModifyTime',
          render(v) {
            return v ? dayjs(v).format('YYYY-MM-DD HH:mm:ss') : '';
          },
        },
      ],
    },
    {
      title: '状态',
      children: [
        {
          title: '状态',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'approvalStatus',
          render: value => {
            const data = statusList.find(item => item.value === value);
            return data ? data.label : value;
          },
        },
        {
          title: '付款流程号',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'payFlowId',
        },
        {
          title: '应付金额',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'payTotals',
        },
      ],
    },
    {
      title: '付款状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      fixed: 'right',
      dataIndex: 'payMessage',
    },
    {
      title: '操作',
      align: 'center',
      width: 220,
      fixed: 'right',
      dataIndex: 'a',
      render: (v, record) => (
        <Fragment>
          <Button
            size="small"
            type="link"
            onClick={() => {
              setType('details');
              add(record);
            }}
          >
            详情
          </Button>
          {record.approvalStatus === 1 && (
            <AuthButton
              moduleCode={moduleCode}
              code="settlement-fluctuate"
              size="small"
              type="link"
              onClick={() => {
                setType('add');
                add(record);
              }}
            >
              增减工作量
            </AuthButton>
          )}
          {record.approvalStatus === 2 && (
            <AuthButton
              moduleCode={moduleCode}
              code="settlement-confirm"
              size="small"
              type="link"
              onClick={() => {
                setType('confirm');
                add(record);
              }}
            >
              确认
            </AuthButton>
          )}
          {record.approvalStatus === 3 && (
            <AuthButton
              moduleCode={moduleCode}
              code="settlement-examine-ok"
              size="small"
              type="link"
              onClick={() => {
                setType('confirm');
                add(record);
              }}
            >
              审核
            </AuthButton>
          )}
          {/* {record.approvalStatus === 3 && (
            <AuthButton
              moduleCode={moduleCode}
              code="settlement-examine-no"
              size="small"
              type="link"
              onClick={() => {
                setType('confirm');
                add(record);
              }}
            >
              审核不通过
            </AuthButton>
          )} */}
          {record.approvalStatus === 4 && (
            <AuthButton
              moduleCode={moduleCode}
              code="settlement-push"
              size="small"
              type="link"
              onClick={() => {
                setType('confirm');
                add(record);
              }}
            >
              推送
            </AuthButton>
          )}
          {/* {record.approvalStatus === 4 && !record.excelImport && (
            <AuthButton
              moduleCode={moduleCode}
              code="settlement-unpush"
              size="small"
              type="link"
              onClick={() => {
                setType('confirm');
                add(record);
              }}
            >
              不推送
            </AuthButton>
          )} */}

          {/* 5 已推送 6 待修改 */}
          {/* 状态是【已推送】+ 付款状态是【新数据】/【冻结】/【未对账】 */}
          {((record.approvalStatus === 5 &&
            [1, 2, 3].includes(record.payStatus)) ||
            record.approvalStatus === 6) && (
            <AuthButton
              moduleCode={moduleCode}
              code="settlement-modify"
              size="small"
              type="link"
              onClick={async () => {
                // 点提交按钮时，需要校验这条数据在财务系统的结算状态，是【新数据】、【冻结】、【未对账】则修改提交成功；
                // 是【对账中】、【结算中】、【结算完成】则修改提交失败
                const { settleStatus } = await getOrderLatestStatus({
                  flowId: record.flowId,
                });
                if ([2, 3, 4].includes(settleStatus)) {
                  Modal.warn({
                    title: '本条数据已进入财务对账结算环节，不支持修改',
                    okText: '关闭',
                    onOk() {
                      refresh();
                    },
                  });
                  return;
                }

                setType('edit');
                add(record);
              }}
            >
              修改
            </AuthButton>
          )}

          {/* 权限控制：中转场总部负责人 */}
          {/* 除了付款状态是【对账中】/【结算中】/【结算完成】时，其他都可以操作 */}
          {/* 状态为已作废(11)时也不能显示作废按钮，因为已经作废过了 */}
          {![4, 5, 6].includes(record.payStatus) &&
            record.approvalStatus !== 11 && (
              <AuthButton
                moduleCode={moduleCode}
                code="settlement-cancellation-order"
                size="small"
                type="link"
                onClick={() => {
                  setEditingTarget(record);
                  setCancellationOrderModalVisible(true);
                }}
              >
                作废
              </AuthButton>
            )}
        </Fragment>
      ),
    },
  ];
  // const [columns, setColumns] = useState(columns);

  const getQueryParams = () => {
    const params = form.getFieldValue();
    condition = cloneDeep(params);
    condition.yearMonthQrEnd = moment(condition.yearMonthQr[1]).format(
      'YYYY-MM',
    );
    condition.yearMonthQr = moment(condition.yearMonthQr[0]).format('YYYY-MM');
    condition.sourceType = userInfo.orgCode;

    return {
      ...condition,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    fetchSummaryData();
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      ...condition,
      pageNum: current,
      pageSize,
    };
    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum, total } = obj;
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: pageNum,
              pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const update = async (e, v, approvalStatus) => {
    e.stopPropagation();
    operationModal([v.id], approvalStatus);
  };

  // 操作
  const operation = (record, approvalStatus) => {
    const idList = [];
    if (record instanceof Array) {
      for (const item of record) {
        if (approvalStatus === 6 && item.excelImport) {
          return message.error('批量推送不能选择人工导入数据');
        }
        idList.push(item.id);
      }
    } else {
      idList.push(record.id);
    }
    operationModal(idList, approvalStatus);
  };

  const operationModal = (idList, approvalStatus) => {
    Modal.confirm({
      title: '提示',
      content: (
        <div>
          <p>{operationList[approvalStatus]}</p>
          {[4, 6].includes(approvalStatus) && (
            <div style={{ display: 'flex' }}>
              <p style={{ width: '32%' }}>
                <span
                  style={{
                    color: '#ff4d4f',
                    fontSize: '14px',
                    marginRight: '4px',
                    fontFamily: 'SimSun, sans-serif',
                  }}
                >
                  *
                </span>
                请反馈原因:
              </p>
              <TextArea
                showCount
                maxLength={200}
                onChange={e => {
                  approvalWord = e.target.value;
                }}
              />
            </div>
          )}
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const params = { idList, approvalStatus, approvalWord };
        if ([4, 6].includes(approvalStatus) && !approvalWord) {
          message.error('请输入原因');
          return new Promise((resolve, reject) => {
            reject();
          });
        }
        const res = [1, 3, 4].includes(approvalStatus)
          ? await approvalPassList(params)
          : await approvalSendFinaList(params);
        if (res.success) {
          success('操作成功');
          approvalWord = '';
          refresh();
        } else {
          error(res.errorMessage);
        }
      },
      onCancel() {
        approvalWord = '';
      },
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  const add = value => {
    setEditingTarget(value);
    setAddModalVisible(true);
  };

  // 获取供应商列表
  const handleQuerySuppliers = async () => {
    const args = {
      orgCode: form.getFieldsValue().zoneCode || userInfo.deptCode,
      srcCode: userOrg,
    };
    const res = await querySuppliers(args);
    if (res.success) {
      const list =
        res.obj.map(({ companyCode, companyName }) => ({
          label: companyName,
          value: companyCode,
        })) || [];
      setSupplierNameList(list);
    }
  };

  /**
   * 刷新客商编码和支付状态
   * @param {{ id: string }} list 数据
   */
  const refreshCustomerCodeAndPayStatus = async list => {
    // 付款状态=结算完成，不能刷新客商编码和支付状态
    const canNotRefreshPayStatus = list.some(item => item.payStatus === 6);
    if (canNotRefreshPayStatus) {
      Modal.info({ title: '付款状态为【结算完成】时不能进行刷新操作' });
      return;
    }

    setRefreshCustomerCodeLoading(true);
    try {
      await request(
        `/tdmsAccrueService/accrueManagementPreRest/refreshSupplierNoAndAccount`,
        {
          method: 'POST',
          body: {
            idList: list.map(item => item.id),
          },
        },
      );
    } finally {
      setRefreshCustomerCodeLoading(false);
    }
    refresh();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="归属月份"
                name="yearMonthQr"
                rules={[
                  {
                    required: true,
                    message: '请选择月份',
                  },
                ]}
              >
                <RangePicker
                  showToday
                  picker="month"
                  allowClear
                  style={{ width: '100%' }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  onChange={handleQuerySuppliers}
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.zoneCode !== curValues.zoneCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <FormItem name="supplierNo" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      allowClear
                      gysParams={getFieldValue('zoneCode') || userInfo.deptCode}
                    />
                  </FormItem>
                </Col>
              )}
            </FormItem>
            <Col {...colStyle}>
              <FormItem label="状态" name="approvalStatus">
                <Select
                  allowClear
                  options={statusList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem name="flowId" label="业务单号">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="付款状态" name="payStatus">
                <Select
                  allowClear
                  options={paymentStatusOptions}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div className="margin-bottom-12" style={{ color: '#df2e3f' }}>
          上月结算请在本月15日12点前确认，否则影响结算！
        </div>
        <div className="btn-con__btn--margin">
          <ImportButton
            danger={false}
            modulecode={moduleCode}
            code="settlement-import"
            style={{ marginRight: 15 }}
            type="primary"
            pagename="KPI配置"
            title="批量导入"
            action="/tdmsAccrueService/accrueManagementPreRest/upload/managementKpi"
            modalUrl="/tdmsAccrueService/accrueManagementPreRest/downTemplate/managementKpiModel"
            modalName="KPI配置模板.xlsx"
            handleSyncImport={refresh}
          />
          <AuthButton
            icon={<PlusOutlined />}
            moduleCode={moduleCode}
            code="settlement-push-more"
            disabled={!selectedRows.length}
            onClick={() => operation(selectedRows, 5)}
            type="primary"
            style={{ marginRight: 15 }}
          >
            批量推送
          </AuthButton>
          <AuthButton
            icon={<PlusOutlined />}
            moduleCode={moduleCode}
            code="settlement-push-more-no"
            disabled={!selectedRows.length}
            onClick={() => operation(selectedRows, 6)}
            type="primary"
            style={{ marginRight: 15 }}
          >
            批量不推送
          </AuthButton>
          <AsyncExport
            text="导出"
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="settlement-export"
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            options={{
              filename: '结算管理.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueManagementPreRest/exportSyncManagementFina`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
          <ImportButton
            danger={false}
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="settlement-import-history"
            type="primary"
            pagename="结算管理导入历史记录配置"
            title="导入历史记录"
            action="/tdmsAccrueService/accrueManagementPreRest/upload/managementFina"
            modalUrl="/tdmsAccrueService/accrueManagementPreRest/downTemplate/managementFinaModel"
            modalName="结算管理导入历史记录配置模板.xlsx"
            handleSyncImport={refresh}
          />
          <AuthButton
            icon={<RedoOutlined />}
            moduleCode={moduleCode}
            code="settlement-refresh-customer-code-and-pay-status"
            disabled={!selectedRows.length}
            onClick={() => refreshCustomerCodeAndPayStatus(selectedRows)}
            type="primary"
            loading={refreshCustomerCodeLoading}
            style={{ marginRight: 15 }}
          >
            刷新客商编码和支付状态
          </AuthButton>
          <AsyncExport
            text="导出每日明细"
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="settlement-export"
            // code="settlement-export-daily"
            icon={<ExportOutlined />}
            // disabled={datas.list && datas.list.length < 1}
            options={{
              filename: '顺心结算管理每日明细.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueManagementPreRest/exportSyncSupplierDetail`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
        </div>
        <p className={style.summary}>
          {[
            {
              label: '汇总装卸合计总工作量',
              value: summaryData.totalLoadUnloadWeight,
              unit: 'T',
            },
            {
              label: '汇总叉车总工作量',
              value: summaryData.totalForkliftWeight,
              unit: 'T',
            },
            {
              label: '汇总各项正负激励',
              value: summaryData.positiveExcitation,
              unit: '元',
            },
            {
              label: '汇总结算费用',
              value: summaryData.totalNeedAmount,
              unit: '元',
            },
          ].map(({ label, value, unit }) => (
            <React.Fragment key={label}>
              <span className="label-colon">{label}</span>
              <span className="empty-tip">{value}</span>
              <span>{unit}</span>
            </React.Fragment>
          ))}
        </p>
      </div>
    </Form>
  );

  useEffect(() => {
    if (!userInfo.empCode) {
      return;
    }

    if (logRoleCode.roleCode === 'tp00001') {
      form.setFieldsValue({ zoneCode: userInfo.deptCode });
    }
    form.setFieldsValue({
      yearMonthQr: [
        moment(new Date()).subtract(1, 'month'),
        moment(new Date()).subtract(1, 'month'),
      ],
    });
    if (userInfo && userInfo.empCode) {
      if (/.*SX.*/.test(userInfo.empCode.toUpperCase())) {
        setUserOrg('SX');
      } else {
        setUserOrg('SF');
      }
    }
    getQueryParams();
    refresh({ currentPage: 1 });
  }, [userInfo, logRoleCode]);

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey={rowKey}
        selectedRows={selectedRows}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          initialValues={editingTarget}
          type={type}
          visible={addModalVisible}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh();
          }}
          width={1000}
        />
      )}

      {/* 作废模态框 */}
      <CancellationOrderModal
        visible={cancellationOrderModalVisible}
        data={editingTarget}
        onCancel={() => setCancellationOrderModalVisible(false)}
        afterClose={refresh}
      />
    </div>
  );
};
@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
@authDecorator
class Container extends PureComponent {
  render() {
    const {
      moduleCode,
      userInfo,
      logRoleCode,
      areaListSF,
      areaListSX,
    } = this.props;
    return (
      <Page
        moduleCode={moduleCode}
        userInfo={userInfo}
        logRoleCode={logRoleCode}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
      />
    );
  }
}

export default withRouter(Container);
