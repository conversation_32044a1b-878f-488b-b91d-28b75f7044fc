import React, { Component } from 'react';
import { Card } from 'antd';
import { queryClockList } from '@/services/clock';
import Search from './components/search';
import TableData from './components/tableData';

class DailyAttendance extends Component {
  state = {
    selectedRows: [],
    loading: false,
    obj: {
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    },
    searchValue: '',
    decryptPhone: false,
    decryptCertificates: false,
    showTable: true,
  };

  handleSearch = (values, isWord) => {
    //   console.log(values)
    const { belongSite, ...res } = values;
    const { obj } = this.state;
    const { current, pageSize } = obj.pagination;
    this.setState({ searchValue: values });
    if (!isWord) {
      this.setState(
        {
          decryptPhone: false,
          decryptCertificates: false,
          showTable: false,
        },
        () => {
          this.setState({
            showTable: true,
          });
        },
      );
    }
    const data = {
      belongSite,
      ...res,
      pageNum: isWord ? current : 1,
      pageSize: isWord ? pageSize : 10,
    };
    this.getClockList(data);
  };

  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
    // console.info(rows)
  };

  changePage = pages => {
    const { current, pageSize } = pages;
    // this.setState({});
    this.setState(
      {
        showTable: false,
        decryptPhone: false,
        decryptCertificates: false,
        obj: {
          pagination: {
            pageSize,
            current,
          },
        },
      },
      () => {
        this.setState({
          showTable: true,
        });
        const { searchValue } = this.state;
        const { belongCode, ...res } = searchValue;
        const data = {
          belongCode,
          ...res,
          decryptPhone: false,
          decryptCertificates: false,
          pageNum: current,
          pageSize,
        };
        this.getClockList(data);
      },
    );
  };

  handleParams = () => {
    const { searchValue, decryptPhone, decryptCertificates } = this.state;
    const data = {
      ...searchValue,
      decryptPhone,
      decryptCertificates,
    };
    return data;
  };

  // 打卡列表查询
  getClockList = async data => {
    const { pageSize, pageNum } = data;
    this.setState({
      obj: {
        list: [],
        pagination: {
          pageSize,
          current: pageNum,
          total: 0,
        },
      },
      selectedRows: [],
      loading: true,
    });

    const res = await queryClockList(data).finally(() =>
      this.setState({ loading: false }),
    );
    if (res && res.success) {
      const { list = [], total } = res.obj;
      this.setState({
        obj: {
          list,
          pagination: {
            pageSize,
            current: pageNum,
            total,
          },
        },
        selectedRows: [],
      });
    }
  };

  // 加密与解密
  showPass = type => {
    const flag = type === '手机号' ? 'decryptPhone' : 'decryptCertificates';
    this.setState(
      prevState => ({
        [flag]: !prevState[flag],
      }),
      () => {
        const {
          searchValue,
          decryptPhone,
          decryptCertificates,
          obj,
        } = this.state;
        const values = { ...searchValue, decryptPhone, decryptCertificates };
        // console.log(values);
        if (obj.list.length > 0) {
          this.handleSearch(values, true);
        }
      },
    );
  };

  render() {
    const { selectedRows, obj, decryptPhone, decryptCertificates } = this.state;
    return (
      <div className="table-list">
        <div className="tableListForm">
          <Search
            handleSearch={this.handleSearch}
            selectedRows={selectedRows}
            datas={obj}
            decryptPhone={decryptPhone}
            decryptCertificates={decryptCertificates}
            handleParams={this.handleParams}
          />
          <TableData
            propsState={this.state}
            handleSelectRows={this.handleSelectRows}
            changePage={this.changePage}
            showPass={this.showPass}
          />
        </div>
      </div>
    );
  }
}

export default DailyAttendance;
