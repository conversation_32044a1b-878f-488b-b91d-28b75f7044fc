/* eslint-disable react/destructuring-assignment */
import React, { Component, useState, Fragment } from 'react';
import moment from 'moment';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import StandardTable from 'src/components/StandardTable';

const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const ShowEye = props => {
  const [show, setShow] = useState(false);
  const showPass = () => {
    setShow(!show);
    props.showPass(props.title);
  };

  return (
    <div>
      <span style={{ paddingRight: 10 }}>{props.title}</span>
      {show && <EyeOutlined onClick={() => showPass()} />}
      {!show && <EyeInvisibleOutlined onClick={() => showPass()} />}
    </div>
  );
};
export default class TableData extends Component {
  columns = [
    {
      title: '公司属性',
      dataIndex: 'orgId',
      width: 100,
      render: text => (text ? '顺心' : '顺丰'),
    },
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 180,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 180,
    },
    {
      title: '中转场代码',
      dataIndex: 'belongCode',
      width: 180,
    },
    {
      title: '中转场',
      dataIndex: 'belongSite',
      width: 180,
    },
    {
      title: '供应商',
      dataIndex: 'outBelongShortName',
      width: 180,
      render: (text, record) => text || record.outBelongName,
    },
    {
      title: '工号',
      dataIndex: 'userNo',
      width: 150,
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '考勤机姓名',
      dataIndex: 'bpoName',
      width: 150,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: 80,
    },
    {
      title: <ShowEye showPass={this.props.showPass} title="证件号码" />,
      dataIndex: 'certificateNo',
      width: 200,
    },
    // {
    //   title: '手机号',
    //   dataIndex: 'userPhone',
    //   width: 150,
    // },
    {
      title: <ShowEye showPass={this.props.showPass} title="手机号" />,
      dataIndex: 'userPhone',
      width: 150,
    },
    {
      title: '是否协助打卡',
      dataIndex: 'status',
      width: 150,
      render: text => (text ? '是' : '否'),
    },
    {
      title: '打卡标识',
      dataIndex: 'clockType',
      width: 150,
      render: text => (text ? '下班' : '上班'),
    },
    {
      title: '打卡时间',
      dataIndex: 'clockTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '打卡状态',
      dataIndex: 'clockStatus',
      width: 150,
      render: text => (text ? '正常' : '异常'),
    },
    // {
    //   title: '打卡地址',
    //   dataIndex: 'clockSite',
    //   width: 180,
    // },
    {
      title: '设备来源',
      dataIndex: 'clockSource',
      width: 150,
    },
    {
      title: '异常原因',
      dataIndex: 'errorMsg',
      width: 150,
    },
  ];

  handleStandardTableChange = pages => {
    this.props.changePage(pages);
  };

  handleSelectRows = rows => {
    this.props.handleSelectRows(rows);
  };

  render() {
    const { selectedRows, obj, loading, showTable } = this.props.propsState;
    return (
      <Fragment>
        {showTable && (
          <StandardTable
            size="small"
            selectedRows={selectedRows}
            data={obj}
            columns={this.columns}
            multiple
            loading={loading}
            onSelectRow={this.handleSelectRows}
            rowKey={(ele, index) => index}
            onChange={this.handleStandardTableChange}
          />
        )}
      </Fragment>
    );
  }
}
