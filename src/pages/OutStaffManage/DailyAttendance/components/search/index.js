/* eslint-disable arrow-body-style */
import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Form, Input, DatePicker, message, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import DeptSearch from 'src/components/DeptSearch';
import NetSearch from 'src/components/NetSelect';
import ExportButton from 'src/components/ExportButton';
import AsyncExport from 'src/components/AsyncExport';
import authDecorator from 'src/components/AuthDecorator';
import SuppliersSearch from 'src/components/SuppliersSearch';
// import styles from '../../style.less';

const { Item } = Form;
const { Option } = Select;
const { RangePicker } = DatePicker;
const SearchForm = props => {
  const [form] = Form.useForm();
  const [allocateList, setAllocateList] = useState([]); // 分拨区
  const orgAttr = [
    { value: 0, lable: '顺丰' },
    { value: 1, lable: '顺心' },
  ];
  const types = [
    { value: 1, lable: '是' },
    { value: 0, lable: '否' },
  ];
  const sourceTypes = [
    { value: 'attend', lable: '大件计提' },
    { value: 'staff', lable: '快伙伴' },
    { value: 'BPO', lable: '考勤机' },
  ];
  const {
    userInfo,
    selectedRows,
    decryptPhone = false,
    decryptCertificates = false,
    datas,
    handleParams,
    moduleCode,
    handleSearch,
  } = props;

  //  查询
  const onFinish = values => {
    // console.log('Success:', values);
    if (values.startTime) {
      values.clockBeginTime = values.startTime[0].startOf('day').format('x');
      values.clockEndTime = values.startTime[1].endOf('day').format('x');
      const rangTime =
        values.clockEndTime - values.clockBeginTime > 60 * 60 * 24 * 7 * 1000;
      if (rangTime) {
        message.error('请选择7天内的时间查询');
        return false;
      }
    }
    delete values.startTime;
    handleSearch(values);
  };

  //   重置
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };

  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        belongCode: userInfo.deptCode,
        orgId: userInfo.orgCode === 'SX' ? 1 : 0,
      });
    }
  }, [userInfo.deptCode]);

  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);

  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        startTime: [
          moment(moment().startOf('day')),
          moment(moment().endOf('day')),
        ],
        orgId: userInfo && userInfo.orgCode === 'SX' ? 1 : 0,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item name="orgId" label="公司属性">
                {/* disabled={!isRoot} */}
                <Select>
                  {orgAttr.map(ele => (
                    <Option key={ele.value} value={ele.value}>
                      {ele.lable}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  placeholder="请输入分拨区名称或代码"
                  extraParam={{
                    typeLevels: [2],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                    excludeOverseasFlag: true,
                  }}
                  showSearch
                  getList={getList}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              {/* disabled={!limit} */}
              <Item name="belongCode" label="中转场">
                <DeptSearch />
              </Item>
            </Col>
            <Item
              shouldUpdate={(prevValues, curValues) =>
                prevValues.belongCode !== curValues.belongCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <Item name="outBelongName" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      allowClear
                      isName
                      gysParams={
                        getFieldValue('belongCode') || userInfo.deptCode
                      }
                    />
                  </Item>
                </Col>
              )}
            </Item>
            <Col {...colStyle}>
              <Item name="userNo" label="工号">
                <Input placeholder="请输入工号" />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item label="打卡时间" name="startTime">
                <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="status" label="是否协助打卡">
                <Select placeholder="请选择" allowClear>
                  {types.map(ele => (
                    <Option key={ele.value} value={ele.value}>
                      {ele.lable}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="clockSource" label="设备来源">
                <Select placeholder="请选择" allowClear>
                  {sourceTypes.map(ele => (
                    <Option key={ele.value} value={ele.value}>
                      {ele.lable}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <ExportButton
          text="导出所选"
          style={{ marginRight: 15 }}
          moduleCode={moduleCode}
          code="daily_attendance-export-some"
          disabled={selectedRows.length < 1}
          options={{
            // total: totalNum,
            filename: '打卡列表.xlsx',
            requstParams: [
              `/sdmCoreStaffServices/clockQuery/export`,
              {
                method: 'POST',
                body: {
                  idList: selectedRows.map(ele => ({
                    infoId: ele.infoId,
                    logId: ele.logId,
                  })),
                  decryptPhone,
                  decryptCertificates,
                },
              },
            ],
          }}
        />
        <AsyncExport
          text="导出全部"
          disabled={datas.list && datas.list.length < 1}
          modulecode={moduleCode}
          code="daily_attendance-export-all"
          options={{
            // total: datas.pagination && datas.pagination.total,
            requstParams: [
              `/sdmCoreStaffServices/clockQuery/exportSync`,
              {
                method: 'POST',
                body: {
                  ...handleParams(),
                },
              },
            ],
          }}
        />
      </div>
      {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div>
      </div> */}
    </Form>
  );
};
export default connect(state => {
  return {
    userInfo: state.global.userInfo,
  };
})(authDecorator(SearchForm));
// export default SearchForm;
