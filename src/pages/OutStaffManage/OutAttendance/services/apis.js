import request from '@/utils/request';
// 处理考勤查询列表
export function queryDealList(body) {
  return request(`/ospmAccountService/timingAttendanceDetailsRest/query`, {
    method: 'POST',
    body,
  });
}
// 处理考勤提交审核(计时)
export function submitDealList(body) {
  return request(`/ospmAccountService/approvalRest/submitApproval`, {
    method: 'POST',
    body,
  });
}
// 处理考勤查看审核进度(计时)
export function queryStep(body) {
  return request(`/ospmAccountService/waListRest/seeApprovalDetails`, {
    method: 'POST',
    body,
  });
}

// 处理考勤作废工时
export function delWorkHours(body) {
  return request(`/ospmAccountService/attendanceHandleRest/abolishHours`, {
    method: 'POST',
    body,
  });
}
// 处理考勤查取消作废工时
export function cancelWorkHours(body) {
  return request(
    `/ospmAccountService/attendanceHandleRest/cancelAbolishHours`,
    {
      method: 'POST',
      body,
    },
  );
}
// 处理考勤查取消作废工时
export function deductHours(body) {
  return request(`/ospmAccountService/attendanceHandleRest/deductionHours`, {
    method: 'POST',
    body,
  });
}

// 考勤审核列表查询
export function queryTestList(body) {
  return request(`/ospmAccountService/timingAttendanceDetailsRest/queryArea`, {
    method: 'POST',
    body,
  });
}
// 考勤审核审核通过
export function passTestList(body) {
  return request(`/ospmAccountService/approvalRest/passApproval`, {
    method: 'POST',
    body,
  });
}
// 考勤审核审核通过
export function rejectTestList(body) {
  return request(`/ospmAccountService/approvalRest/rejectApproval`, {
    method: 'POST',
    body,
  });
}

// 查询考勤确认结果列表(计时)
export function queryConfirmList(body) {
  return request(
    `/ospmAccountService/timingAttendanceDetailsRest/groupByQuery`,
    {
      method: 'POST',
      body,
    },
  );
}
// 查询考勤确认(计时)
export function confirmTestList(body) {
  return request(`/ospmAccountService/timingAttendanceDetailsRest/notarize`, {
    method: 'POST',
    body,
  });
}
// 查询考勤取消确认(计时)
export function cancelConfirmTestList(body) {
  return request(`/ospmAccountService/timingAttendanceDetailsRest/unNotarize`, {
    method: 'POST',
    body,
  });
}
// 考勤处理管理补充,补扣工时
export function updateAttendItem(body) {
  return request(
    `/ospmAccountService/timingAttendanceDetailsRest/updateAttendItem`,
    {
      method: 'POST',
      body,
    },
  );
}
