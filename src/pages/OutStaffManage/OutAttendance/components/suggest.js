import React from 'react';
import { Form, Input, Modal } from 'antd';
const { Item } = Form;
const Suggest = props => {
  const [form] = Form.useForm();
  const { visible, title = '确认' } = props;
  const handleConfirm = () => {
    form.validateFields().then(nameList => {
      if (nameList) {
        props.handleConfirm(nameList);
      }
    });
  };
  return (
    <Modal
      visible={visible}
      title={title}
      onOk={handleConfirm}
      onCancel={() => props.handleHide()}
    >
      <Form
        form={form}
        initialValues={{
          approvalWord: '',
        }}
      >
        <Item
          label="意见"
          name="approvalWord"
          rules={[
            {
              required: true,
              message: '请输入',
            },
          ]}
        >
          <Input />
        </Item>
      </Form>
    </Modal>
  );
};
export default Suggest;
