import React, { useState } from 'react';
import { message, Modal, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import StandardTable from '@/components/StandardTable';
import { minutesToHours } from '@/utils/utils';
import {
  queryConfirmList,
  confirmTestList,
  cancelConfirmTestList,
} from '../services/apis';
import SearchForm from './components/search';

const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const tooltips = {
  planWorkHours: '规定工作时长=计划上班时数-休息时长',
  attendanceNum: '累加该班次人员数据',
  lateNum: '累加该班次该类型异常单据',
  supplementWorkingHours: '该班次补充工时累加，单位小时',
  deductionWorkingHours: '该班次扣减工时累加，单位分钟',
  // accountWorkingHours: '该班次下所有正常人员结算工时累加',
  accountWorkingHours: (
    <span>
      ①上班卡和下班卡都在有效区间内，则结算工时等于班次结束-班次开始-休息时长+补充时长-扣减工时
      <br />
      ②有上班卡，上班卡迟到，下班卡正常，结算工时等于班次结束时间-上班卡-休息时长+补充时长-扣减工时
      <br />
      ③有上班卡，上班卡正常，下班卡早退，结算工时工时等于下班卡-班次开始时间-休息时长+补充时长-扣减工时
      <br />
      ④有上下班卡，上班卡迟到，下班卡早退，结算工时等于下班卡-上班卡-休息时长+补充时长-扣减工时
      <br />
      ⑤有上班卡，下班卡缺失 ，结算工时为0​
    </span>
  ),
};
const PopTip = props => {
  const { title, tipKey } = props;
  return (
    <div>
      <span style={{ paddingRight: 10 }}>{title}</span>
      <Tooltip title={tooltips[tipKey]}>
        <QuestionCircleOutlined />
      </Tooltip>
    </div>
  );
};

const PageIndex = () => {
  const columns = [
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 150,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 150,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '归属月份',
      dataIndex: 'accrueMonth',
      width: 180,
      render: text => (text ? moment(text).format('YYYY-MM') : '-'),
    },

    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 150,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'affirmStatusName',
      width: 150,
    },
    {
      title: '考勤日期',
      dataIndex: 'timingTime',
      width: 150,
      render: text => (text ? moment(text).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '班次号',
      dataIndex: 'shiftName',
      width: 150,
    },
    {
      title: '班次开始时间',
      dataIndex: 'shiftStartTime',
      width: 150,
      render: timeTrans,
    },
    {
      title: '班次结束时间',
      dataIndex: 'shiftEndTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: <PopTip title="规定工作时长(小时)" tipKey="planWorkHours" />,
      dataIndex: 'planWorkHours',
      width: 150,
      render: text => minutesToHours(text),
    },
    {
      title: '需求人数',
      dataIndex: 'requireNum',
      width: 150,
    },
    {
      title: <PopTip title="实到人数" tipKey="attendanceNum" />,
      dataIndex: 'attendanceNum',
      width: 150,
    },
    {
      title: <PopTip title="迟到人数" tipKey="lateNum" />,
      dataIndex: 'lateNum',
      width: 150,
    },
    {
      title: '早退人数',
      dataIndex: 'leaveEarlyNum',
      width: 150,
    },
    // {
    //   title: '旷工人数',
    //   dataIndex: 'absenteeismNum',
    //   width: 150,
    // },
    {
      title: <PopTip title="补充工时" tipKey="supplementWorkingHours" />,
      dataIndex: 'supplementWorkingHours',
      width: 180,
    },
    {
      title: <PopTip title="扣减工时" tipKey="deductionWorkingHours" />,
      dataIndex: 'deductionWorkingHours',
      width: 180,
    },
    {
      title: <PopTip title="结算工时" tipKey="accountWorkingHours" />,
      dataIndex: 'accountWorkingHours',
      width: 150,
    },
    {
      title: '确认人',
      dataIndex: 'operator',
      width: 150,
    },
    {
      title: '确认时间',
      dataIndex: 'operatorTime',
      width: 150,
      render: timeTrans,
    },
  ];
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });

  const [searchValues, setSearchValues] = useState({}); // 查询入参
  const [selectedRows, setSelectedRows] = useState([]); // 选中数据
  const [loading, setLoading] = useState(false);
  // 翻页
  const changePage = pages => {
    const { pageSize, current } = pages;
    const data = {
      ...searchValues,
      pageSize,
      pageNum: current,
    };
    getList(data);
  };
  // 查询
  const handleSearch = values => {
    const data = {
      ...values,
      pageSize: 10,
      pageNum: 1,
    };
    setSearchValues(values);
    getList(data);
  };

  // 选择
  const handleonSelectRow = row => {
    setSelectedRows(row);
  };

  // 1待提交，2提交审核，3审核中，4审核通过，7失效数据
  // 提交审核
  const handleConfirm = () => {
    if (selectedRows.length > 0) {
      const arr = selectedRows.filter(ele => ele.affirmStatus === 1);
      if (arr.length > 0) {
        message.error('所选的数据存在已确认的数据了，请正确选择');
        return false;
      }
      const list = selectedRows.map(ele => ({
        zoneCode: ele.zoneCode,
        supplierCode: ele.supplierCode,
        shiftName: ele.shiftName,
        accrueMonth: ele.accrueMonth,
        shiftStartTime: ele.shiftStartTime,
      }));
      Modal.confirm({
        title: '提示',
        content: '请确保该班次下人员数据完整，是否进行确认？',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          const res = await confirmTestList(list);
          if (res && res.success) {
            getList({ ...searchValues, pageNum: 1, pageSize: 10 });
            message.success('确认考勤操作成功');
          }
        },
      });
    } else {
      message.error('请选择数据操作');
    }
  };
  const handleCancel = () => {
    if (selectedRows.length > 0) {
      const arr = selectedRows.filter(ele => ele.affirmStatus === 0);
      if (arr.length > 0) {
        message.error('所选的数据存在未确认的数据了，请正确选择');
        return false;
      }
      const list = selectedRows.map(ele => ({
        zoneCode: ele.zoneCode,
        supplierCode: ele.supplierCode,
        shiftName: ele.shiftName,
        accrueMonth: ele.accrueMonth,
        shiftStartTime: ele.shiftStartTime,
      }));
      Modal.confirm({
        title: '提示',
        content: '确认取消已确认的班次单据？',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          const res = await cancelConfirmTestList(list);
          if (res && res.success) {
            getList({ ...searchValues, pageNum: 1, pageSize: 10 });
            message.success('取消确认考勤操作成功');
          }
        },
      });
    } else {
      message.error('请选择数据操作');
    }
  };
  // 查询
  const getList = async data => {
    setLoading(true);
    setSelectedRows([]);
    setDatas({
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    });

    const res = await queryConfirmList(data).finally(() => setLoading(false));
    if (res && res.success && res.obj) {
      const { list, total, pageNum, pageSize } = res.obj;
      setDatas({
        list: list || [],
        pagination: {
          pageSize,
          current: pageNum,
          total,
        },
      });
    }
  };

  return (
    <div className="table-list">
      <div className="tableListForm">
        <SearchForm
          handleSearch={handleSearch}
          handleConfirm={handleConfirm}
          handleCancel={handleCancel}
          selectedRows={selectedRows}
          searchValues={searchValues}
          datas={datas}
        />
        <StandardTable
          size="small"
          data={datas}
          columns={columns}
          multiple
          loading={loading}
          selectedRows={selectedRows}
          onSelectRow={handleonSelectRow}
          // showSelection={false}
          rowKey="timingUuid"
          onChange={changePage}
        />
      </div>
    </div>
  );
};
export default PageIndex;
