import React, { useEffect, useState } from 'react';
import { Form, DatePicker, Input, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import SuppliersSearch from 'src/components/SuppliersSearch';
import NetSearch from '@/components/NetSelect';
import AuthButton from '@/components/AuthButton';
import AsyncExport from '@/components/AsyncExport';
import ExportButton from '@/components/ExportButton';
import authDecorator from '@/components/AuthDecorator';
// import styles from '../style.less';
const { Item } = Form;
const { RangePicker } = DatePicker;
const { Option } = Select;

const SearchForm = props => {
  const [form] = Form.useForm();
  // const { getFieldValue } = form;

  const {
    userInfo,
    selectedRows,
    handleConfirm,
    handleCancel,
    datas,
    searchValues,
    moduleCode,
    handleSearch,
  } = props;
  const [allocateList, setAllocateList] = useState([]); // 分拨区
  const [formatVal, setFormatVal] = useState({});
  const status = [
    {
      value: 1,
      text: '已确认',
    },
    {
      value: 0,
      text: '未确认',
    },
  ];
  const companyAttr = [
    {
      value: 'SF',
      text: '顺丰',
    },
    {
      value: 'SX',
      text: '顺心',
    },
  ];
  const onFinish = () => {
    form.validateFields().then(nameList => {
      if (nameList) {
        if (nameList.accrueMonth) {
          nameList.accrueMonth = nameList.accrueMonth.format('YYYY-MM');
        }
        if (nameList.timingTime) {
          nameList.timingTimeStart = nameList.timingTime[0]
            .startOf('day')
            .format('x');
          nameList.timingTimeEnd = nameList.timingTime[1]
            .endOf('day')
            .format('x');
          delete nameList.timingTime;
        }
        handleSearch(nameList);
      }
    });
  };
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };
  const getValues = () => {
    const list = selectedRows.map(ele => ({
      zoneCode: ele.zoneCode,
      supplierCode: ele.supplierCode,
      shiftName: ele.shiftName,
      accrueMonth: ele.accrueMonth,
      shiftStartTime: ele.shiftStartTime,
    }));
    return list;
  };
  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        sourceType: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
        zoneCode: userInfo.deptCode,
      });
    }
  }, [userInfo.deptCode]);

  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);

  useEffect(() => {
    const filterVal = {};
    for (const key in searchValues) {
      if (searchValues[key]) {
        filterVal[key] = searchValues[key];
      }
    }
    setFormatVal(filterVal);
  }, [searchValues]);
  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        accrueMonth: moment(moment(new Date()).format('YYYY-MM')),
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item name="sourceType" label="公司属性">
                {/* disabled={!isRoot} */}
                <Select>
                  {companyAttr.map(ele => (
                    <Option value={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  extraParam={{
                    typeLevels: ['2'],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                  }}
                  showSearch
                  getList={getList}
                  placeholder="请输入分拨区名称或代码"
                  // disabled={!isRoot}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="zoneCode" label="中转场">
                <NetSearch
                  extraParam={{
                    excludeOverseasFlag: 1,
                  }}
                />
              </Item>
            </Col>
            <Item
              shouldUpdate={(prevValues, curValues) =>
                prevValues.zoneCode !== curValues.zoneCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <Item name="supplierName" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      isName
                      allowClear
                      gysParams={getFieldValue('zoneCode') || userInfo.deptCode}
                    />
                  </Item>
                </Col>
              )}
            </Item>
            <Col {...colStyle}>
              <Item name="affirmStatus" label="状态">
                <Select placeholder="请选择" allowClear>
                  {status.map(ele => (
                    <Option key={ele.value} value={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              {/* <Item
            name="workDate"
            label="归属日期"
            labelCol={{ md: 4, sm: 24, xs: 24 }}
            wrapperCol={{ md: 20, sm: 24, xs: 24 }}
          >
            <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
          </Item> */}
              <Item label="归属月份" name="accrueMonth">
                <DatePicker picker="month" style={{ width: '100%' }} />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item label="考勤日期" name="timingTime">
                <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </Item>
            </Col>
            {/* <Col {...colStyle}>
          <Item label="考勤日期结束" name="timingTimeEnd">
            <DatePicker
              picker="day"
              // disabledDate={disabledInvalidDate}
              style={{ width: '100%' }}
            />
          </Item>
        </Col> */}
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div style={{ color: '#df2e3f' }}>
          本月数据请在次月6日凌晨前处理完毕,否则影响结算！
        </div>

        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="confirm_attendance-confirm"
          style={{ marginRight: 15 }}
          onClick={() => handleConfirm()}
          disabled={selectedRows.length < 1}
        >
          确认
        </AuthButton>
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="confirm_attendance-cancel"
          style={{ marginRight: 15 }}
          onClick={() => handleCancel()}
          disabled={selectedRows.length < 1}
        >
          取消确认
        </AuthButton>

        <ExportButton
          text="导出所选"
          moduleCode={moduleCode}
          code="confirm_attendance-export-some"
          disabled={selectedRows.length < 1}
          style={{ marginRight: 15 }}
          options={{
            filename: '考勤确认列表.xlsx',
            requstParams: [
              `/ospmAccountService/timingAttendanceDetailsRest/confirmExport`,
              {
                method: 'POST',
                body: getValues,
              },
            ],
          }}
        />
        <AsyncExport
          text="导出全部"
          modulecode={moduleCode}
          code="confirm_attendance-export-all"
          style={{ marginRight: 15 }}
          disabled={datas.list && datas.list.length < 1}
          options={{
            // total: totalNum,
            filename: '考勤确认列表.xlsx',
            requstParams: [
              `/ospmAccountService/timingAttendanceDetailsRest/confirmExportSync`,
              {
                method: 'POST',
                body: { ...formatVal },
              },
            ],
          }}
        />
      </div>
      {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
      {/* </div> */}
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
