import React, { useEffect, useState, Fragment } from 'react';
import {
  Button,
  Tabs,
  Modal,
  message,
  DatePicker,
  Select,
  Input,
  Tooltip,
  Upload,
} from 'antd';
import { QuestionCircleOutlined, PlusOutlined } from '@ant-design/icons';
import moment from 'moment';
import { pick, cloneDeep } from 'lodash';
import { connect } from 'dva';
import StandardTable from '@/components/StandardTable';
import ImportButton from '@/components/ImportButton';
import authDecorator from '@/components/AuthDecorator';
import { updateAttendItem } from '../../services/apis';
import Config from '../../../../../config'; // 项目中放systemKey的那个配置文件

const { TabPane } = Tabs;
const { Option } = Select;

const Retroactive = props => {
  const {
    moduleCode,
    searchValues,
    selectedRows,
    originData,
    refreshList,
  } = props;

  const [cloneSelectedRows, setCloneSelectedRows] = useState([]);
  const [formatVal, setFormatVal] = useState({});
  const [visible, setVisible] = useState(false);
  const [tabsActive, setTabs] = useState('edit');
  const [fileList, setFileList] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const timeFormat = 'YYYY-MM-DD HH:mm:ss';
  const formatTime = timeStamp => (timeStamp ? moment(timeStamp) : '');

  const options = {
    pushReason: ['忘记打卡', '停电', '设备故障', '操作失误', '其他'],
  };
  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  const columns = [
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 100,
    },
    {
      title: '考勤日期',
      dataIndex: 'timingTime',
      width: 100,
      render: text => (text ? moment(text).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '工号',
      dataIndex: 'userNo',
      width: 100,
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 180,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 100,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 100,
    },
    {
      title: '上班卡',
      dataIndex: 'startWorkTime',
      width: 180,
      render: (text, row) => (
        <DatePicker
          showTime
          defaultValue={formatTime(text)}
          format={timeFormat}
          disabled={row.disabledStartWorkTime}
          onChange={e => changeCell('startWorkTime', row, e)}
        />
      ),
    },
    {
      title: '下班卡',
      dataIndex: 'endWorkTime',
      width: 180,
      render: (text, row) => (
        <DatePicker
          showTime
          defaultValue={formatTime(text)}
          format={timeFormat}
          disabled={row.disabledEndWorkTime}
          onChange={e => changeCell('endWorkTime', row, e)}
        />
      ),
    },
    {
      title: '补签卡原因',
      dataIndex: 'pushReason',
      width: 180,
      render: (text, row) => (
        <Select
          placeholder="请选择原因"
          defaultValue={text}
          style={{ width: '100%' }}
          disabled={row.disabledStartWorkTime && row.disabledEndWorkTime}
          onChange={e => changeCell('pushReason', row, e)}
        >
          {options.pushReason.map(item => (
            <Option key={item} value={item}>
              {item}
            </Option>
          ))}
        </Select>
      ),
    },
    {
      title: '备注',
      dataIndex: 'pushRemark',
      width: 200,
      render: (text, row) => (
        <Input
          defaultValue={text}
          placeholder="补签原因为其他时，备注必填"
          disabled={row.disabledStartWorkTime && row.disabledEndWorkTime}
          onChange={e => changeCell('pushRemark', row, e)}
        />
      ),
    },
    {
      title: (
        <>
          <span style={{ paddingRight: 10 }}>出入场图片</span>
          <Tooltip title="提供人员出入场的监控画面或照片，支持扩展名 .jpg .jpeg .png，不超过10MB，最多2张">
            <QuestionCircleOutlined />
          </Tooltip>
        </>
      ),
      dataIndex: 'inOutGroundPicList',
      width: 250,
      render: (text, row, index) => (
        <Upload
          accept=".jpg,.jpeg,.png"
          action="/ospmAccountService/timingAttendanceDetailsRest/uploadPic"
          withCredentials
          headers={{
            systemKey: Config.systemKey,
            userId: sessionStorage.userid,
            'sgs-userid': sessionStorage.userid,
            'gw-bdus-rid': sessionStorage.roleId || '',
          }}
          listType="picture-card"
          beforeUpload={beforeUpload}
          onPreview={handlePreview}
          onChange={info => {
            handleChangePicList(info, index);
          }}
          fileList={fileList[index]}
          disabled={row.diableUploadPic}
        >
          {fileList[index].length >= 2 || row.diableUploadPic
            ? null
            : uploadButton}
        </Upload>
      ),
    },
  ];
  const getBase64 = file =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);

      reader.onload = () => resolve(reader.result);

      reader.onerror = error => reject(error);
    });
  const handleCancelPreView = () => {
    setPreviewVisible(false);
  };
  const handlePreview = async file => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }

    setPreviewImage(file.url || file.preview);
    setPreviewVisible(true);
  };
  const handleChangePicList = (info, index) => {
    if (!(info.file.size / 1024 / 1024 < 10)) {
      info.fileList = info.fileList.filter(file => file.uid !== info.file.uid);
    }
    const fileType = info.file.type;
    if (
      !(
        fileType === 'image/jpeg' ||
        fileType === 'image/jpg' ||
        fileType === 'image/png'
      )
    ) {
      info.fileList = info.fileList.filter(file => file.uid !== info.file.uid);
    }

    switch (info.file.status) {
      case 'uploading':
        setIsLoading(true);
        break;
      case 'error':
        setIsLoading(false);
        message.error('上传失败');
        info.fileList = info.fileList.filter(
          file => file.uid !== info.file.uid,
        );
        break;
      default:
        setIsLoading(false);
        break;
    }
    // 把上传后返回的 name 赋值给对应 file 里面的 name
    const res = info.file.response;
    if (res) {
      if (!res.success) {
        message.error(res.errorMessage);
        info.fileList = info.fileList.filter(
          file => file.uid !== info.file.uid,
        );
      } else {
        info.fileList.map(_file => {
          if (_file.uid === info.file.uid) {
            _file.name = res.obj;
          }
          return _file;
        });
        info.file.name = res.obj;
      }
    }
    // console.log('info.fileList', info.fileList);
    // console.log('info.file', info.file);
    // 把第 index 个 file 修改，重新赋值 fileList
    const temp = fileList.map((item, _index) =>
      _index === index ? info.fileList : item,
    );
    setFileList(temp);
  };
  // 校验图片大小和类型
  const beforeUpload = file => {
    const isJpgOrPng =
      file.type === 'image/jpeg' ||
      file.type === 'image/jpg' ||
      file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('上传失败，仅支持扩展名 .jpg .jpeg .png');
    }
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('上传失败，图片大小不超过10M');
    }
    return isJpgOrPng && isLt10M;
  };

  const changeCell = (key, row, e) => {
    if (['startWorkTime', 'endWorkTime'].includes(key)) {
      row[key] = e ? +e.$d : '';
    }
    if (['pushReason'].includes(key)) {
      row[key] = e;
    }
    if (['pushRemark'].includes(key)) {
      row[key] = e.target.value;
    }
  };

  const handleSyncImport = () => {
    refreshList({
      ...searchValues,
      pageNum: 1,
      pageSize: originData.pagination.pageSize,
    });
  };
  const showModal = () => {
    const picFileList = [];
    let picFile = [];
    selectedRows.forEach(item => {
      if (item.startWorkTime) {
        item.disabledStartWorkTime = true;
      }
      if (item.endWorkTime) {
        item.disabledEndWorkTime = true;
      }
      if (item.pic1 !== null || item.pic2 !== null) {
        item.diableUploadPic = true;
      }
      // 构造符合上传组件需要的数据结构
      picFile = [];
      item.picList.forEach((pic, index) => {
        const tempName = index === 0 ? item.pic1 : item.pic2;
        if (pic !== '') {
          picFile.push({
            uid: `${index}`,
            name: tempName,
            status: 'done',
            url: pic,
          });
        }
      });
      picFileList.push(picFile);
    });
    setFileList(picFileList);

    setCloneSelectedRows(cloneDeep(selectedRows));

    setVisible(true);
  };
  const handleOk = async () => {
    if (!cloneSelectedRows.length) {
      return message.warning('请选择要补签的数据');
    }

    for (let index = 0; index < cloneSelectedRows.length; index++) {
      const item = cloneSelectedRows[index];

      const picList = fileList[index].map(file => file.name);
      [item.pic1, item.pic2] = picList;
      if (item.pic1 === undefined) {
        return message.warning(`第${index + 1}条需要上传出入场图片`);
      }
      if (item.pic2 === undefined) item.pic2 = '';
      // console.log(item.pic1, item.pic2);
      if (
        item.disabledStartWorkTime &&
        item.disabledEndWorkTime &&
        item.diableUploadPic
      ) {
        return message.warning(`第${index + 1}条不用补签，请勿提交`);
      }
      if (!item.startWorkTime || !item.endWorkTime) {
        return message.warning(`第${index + 1}条的上、下班卡不能留空`);
      }
      if (!item.pushReason) {
        return message.warning(`第${index + 1}条请选择补签原因！`);
      }
      if (item.pushReason === '其他' && !item.pushRemark) {
        return message.warning(
          `第${index + 1}条选了补签原因为“其他”，需在备注中详细说明`,
        );
      }
    }

    const body = { dataType: 3 };
    body.dataList = cloneSelectedRows.map(item => {
      const cur = pick(item, [
        'id',
        'timingTime',
        'startWorkTime',
        'endWorkTime',
        'pushReason',
        'pushRemark',
        'pic1',
        'pic2',
      ]);
      return cur;
    });

    const res = await updateAttendItem(body);

    if (res && res.success) {
      message.success('提交成功');

      const { current: pageNum, pageSize } = originData.pagination;
      refreshList({ ...searchValues, pageNum, pageSize });
      handleCancel();
    }
  };
  const handleCancel = () => {
    setVisible(false);
  };
  const changeTabs = e => {
    setTabs(e);
  };

  useEffect(() => {
    const filterVal = {};
    for (const key in searchValues) {
      if (searchValues[key] !== null && searchValues[key] !== undefined) {
        filterVal[key] = searchValues[key];
      }
    }
    setFormatVal(filterVal);
  }, [searchValues]);

  return (
    <Fragment>
      <Button
        type="primary"
        style={{ marginRight: 10 }}
        onClick={showModal}
        disabled={!originData.list.length}
      >
        补签卡
      </Button>

      <Modal
        title="补签卡"
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose
        width={1800}
        footer={[
          <Button key="back" onClick={handleCancel}>
            取消
          </Button>,
          tabsActive === 'edit' && (
            <Button
              key="submit"
              type="primary"
              onClick={handleOk}
              loading={isLoading}
            >
              确定
            </Button>
          ),
        ]}
      >
        <Tabs defaultActiveKey={tabsActive} onChange={changeTabs}>
          <TabPane tab="补签卡编辑" key="edit">
            <StandardTable
              size="small"
              data={{ list: cloneSelectedRows, pagination: false }}
              showSelection={false}
              columns={columns}
              footer={null}
              rowKey="id"
              scroll={{ y: 500 }}
            />
          </TabPane>
          <TabPane tab="导入" key="import">
            <ImportButton
              title="补签卡"
              moduleCode={moduleCode}
              code="deal_attendance_manage-sign-hours"
              action="/ospmAccountService/attendanceHandleRest/uploadRepairAll"
              modalUrl="/ospmAccountService/attendanceHandleRest/downImportCardRepTemplate"
              modalName="补签卡模板.xlsx"
              params={{ documentType: 2, ...formatVal }}
              disabled={originData.list && originData.list.length < 1}
              handleSyncImport={handleSyncImport}
            />
            <div style={{ color: 'red', marginTop: '5px' }}>
              导入补签卡后，请在页面上勾选导入数据，选择“补签卡”上传出入场图片，否则无法提交审核
            </div>
          </TabPane>
        </Tabs>
      </Modal>
      <Modal
        visible={previewVisible}
        footer={null}
        destroyOnClose
        onCancel={handleCancelPreView}
      >
        <img
          alt="example"
          style={{
            width: '100%',
          }}
          src={previewImage}
        />
      </Modal>
    </Fragment>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(Retroactive));
