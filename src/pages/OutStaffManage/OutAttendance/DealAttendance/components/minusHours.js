import React, { useEffect, useState, Fragment } from 'react';
import { Button, InputNumber, Input, Tabs, Modal, message } from 'antd';
import StandardTable from '@/components/StandardTable';
import { connect } from 'dva';
import ImportButton from '@/components/ImportButton';
import authDecorator from '@/components/AuthDecorator';
import moment from 'moment';
import { updateAttendItem } from '../../services/apis';
import { pick } from 'lodash';

const { TabPane } = Tabs;

const MinusHours = props => {
  const {
    moduleCode,
    searchValues,
    selectedRows,
    originData,
    refreshList,
  } = props;

  const [formatVal, setFormatVal] = useState({});
  const [visible, setVisible] = useState(false);
  const [tabsActive, setTabs] = useState('edit');

  const columns = [
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 100,
    },
    {
      title: '考勤日期',
      dataIndex: 'timingTime',
      width: 100,
      render: text => (text ? moment(text).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '工号',
      dataIndex: 'userNo',
      width: 100,
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 100,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 100,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 100,
    },
    {
      title: '扣减前结算工时',
      dataIndex: 'accountWorkingHours',
      width: 150,
    },
    {
      title: '扣减工时(分钟)',
      dataIndex: 'minutes',
      width: 180,
      fixed: 'right',
      render: (text, row) => (
        <InputNumber
          precision="0"
          min={0}
          parser={value => {
            if (isNaN(+value)) return 0;
            return value;
          }}
          defaultValue={row.minutes}
          onChange={changeHour.bind(this, row)}
        />
      ),
    },
    {
      title: '扣减原因',
      dataIndex: 'deductionReason',
      width: 180,
      fixed: 'right',
      render: (text, row) => (
        <Input
          defaultValue=""
          placeholder="请输入"
          onChange={e => changeReason(row, e)}
        />
      ),
    },
  ];

  const handleSyncImport = () => {
    refreshList({
      ...searchValues,
      pageNum: 1,
      pageSize: originData.pagination.pageSize,
    });
  };
  const showModal = () => {
    selectedRows.forEach(item => {
      item.minutes = 0;
      item.deductionReason = '';
    });
    setVisible(true);
  };
  const handleOk = async () => {
    if (!selectedRows.length) {
      return message.warning('请选择要编辑的数据');
    }

    if (selectedRows.some(item => !item.deductionReason)) {
      return message.warning('扣减原因不能为空！');
    }

    const body = { dataType: 2, dataList: [] };
    body.dataList = selectedRows.map(item => {
      return pick(item, ['id', 'minutes', 'timingTime', 'deductionReason']);
    });

    const res = await updateAttendItem(body);
    if (res && res.success) {
      message.success('提交成功');

      const { current: pageNum, pageSize } = originData.pagination;
      refreshList({ ...searchValues, pageNum, pageSize });
      handleCancel();
    }
  };
  const handleCancel = () => {
    setVisible(false);
  };
  const changeTabs = e => {
    setTabs(e);
  };
  const changeHour = (row, e) => {
    row.minutes = e;
  };
  const changeReason = (row, e) => {
    row.deductionReason = e.target.value;
  };

  useEffect(() => {
    const filterVal = {};
    for (const key in searchValues) {
      if (searchValues[key] !== null && searchValues[key] !== undefined) {
        filterVal[key] = searchValues[key];
      }
    }
    setFormatVal(filterVal);
  }, [searchValues]);

  return (
    <Fragment>
      <Button
        type="primary"
        style={{ marginRight: 10 }}
        onClick={showModal}
        disabled={!originData.list.length}
      >
        扣减工时
      </Button>

      <Modal
        title="扣减工时"
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnClose={true}
        width={1000}
        footer={[
          <Button key="back" onClick={handleCancel}>
            取消
          </Button>,
          tabsActive === 'edit' && (
            <Button key="submit" type="primary" onClick={handleOk}>
              确定
            </Button>
          ),
        ]}
      >
        <Tabs defaultActiveKey={tabsActive} onChange={changeTabs}>
          <TabPane tab="扣减编辑" key="edit">
            <StandardTable
              size="small"
              data={{ list: selectedRows, pagination: false }}
              showSelection={false}
              columns={columns}
              footer={null}
              rowKey="id"
              scroll={{ y: 500 }}
            />
          </TabPane>
          <TabPane tab="导入" key="import">
            <ImportButton
              title="扣减工时"
              moduleCode={moduleCode}
              code="deal_attendance_manage-reduce-hours"
              action="/ospmAccountService/attendanceHandleRest/uploadDeductionHours"
              modalUrl="/ospmAccountService/attendanceHandleRest/downDeductionHoursTemplate"
              modalName="扣减工时模板.xlsx"
              disabled={originData.list && originData.list.length < 1}
              style={{ marginRight: 15 }}
              handleSyncImport={handleSyncImport}
            />
          </TabPane>
        </Tabs>
      </Modal>
    </Fragment>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(MinusHours));
