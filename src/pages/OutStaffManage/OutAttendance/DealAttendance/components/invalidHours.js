import React, { useEffect, useState, Fragment } from 'react';
import { Button, Input, Modal, message } from 'antd';
import { connect } from 'dva';
import authDecorator from '@/components/AuthDecorator';
import AuthButton from '@/components/AuthButton';
const { TextArea } = Input;

import { delWorkHours } from '../../services/apis';

const InvalidHours = props => {
  const { moduleCode, selectedRows, refreshList } = props;

  const [visible, setVisible] = useState(false);
  const [invalidReason, setInvalidReason] = useState('');

  // 作废（status:1正常，2作废）
  const handleOk = async () => {
    if (!invalidReason) {
      return message.error('作废原因不能为空');
    }

    if (selectedRows.some(item => item.status == 2)) {
      return message.error('所选的数据存在已作废工时的数据了，请正确选择');
    }

    if (selectedRows.some(item => item.affirmStatus == 1)) {
      return message.error('已确认数据不允许修改，请知悉');
    }

    const ids = selectedRows.map(item => item.id);

    const res = await delWorkHours({ ids, invalidReason });

    if (res && res.success) {
      message.success('作废成功！');
      closeModal();
      refreshList();
    }
  };
  const handleInput = e => {
    setInvalidReason(e.target.value);
  };
  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    setInvalidReason('');
    setVisible(false);
  };

  return (
    <Fragment>
      <AuthButton
        type="primary"
        moduleCode={moduleCode}
        code="deal_attendance_manage-del-hours"
        style={{ marginRight: 15 }}
        disabled={selectedRows.length < 1}
        onClick={() => showModal()}
      >
        作废工时
      </AuthButton>

      <Modal
        title="作废工时"
        visible={visible}
        onOk={handleOk}
        onCancel={closeModal}
        width={500}
        footer={[
          <Button key="back" onClick={closeModal}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={handleOk}>
            确定
          </Button>,
        ]}
      >
        <p>确定作废工时？请输入作废原因：</p>
        <TextArea
          maxLength={50}
          showCount={true}
          value={invalidReason}
          onChange={e => handleInput(e)}
        ></TextArea>
      </Modal>
    </Fragment>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(InvalidHours));
