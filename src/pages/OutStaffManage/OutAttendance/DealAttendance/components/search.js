import React, { useEffect, useState, memo } from 'react';
import {
  Form,
  // Button,
  DatePicker,
  Input,
  Select,
  Row,
  Col,
  Cascader,
} from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import { cloneDeep } from 'lodash';
import NetSearch from 'src/components/NetSelect';
import ImportButton from 'src/components/ImportButton';
import ExportButton from 'src/components/ExportButton';
import AuthButton from 'src/components/AuthButton';
import AsyncExport from 'src/components/AsyncExport';
import authDecorator from 'src/components/AuthDecorator';
import SuppliersSearch from 'src/components/SuppliersSearch';
// import styles from '../style.less';
import AddHours from './addHours';
import MinusHours from './minusHours';
import Retroactive from './retroactive';
import InvalidHours from './invalidHours';

const { Item } = Form;
const { Option } = Select;
const { RangePicker } = DatePicker;
const SearchForm = memo(props => {
  const [form] = Form.useForm();
  const {
    userInfo,
    selectedRows,
    handleSubmit,
    showStep,
    datas,
    searchValues,
    // handleDel,
    handleCancelDel,
    // handleDeductModal,
    moduleCode,
    refreshList,
  } = props;

  const [allocateList, setAllocateList] = useState([]); // 分拨区
  const [formatVal, setFormatVal] = useState({});
  const dataStatus = [
    {
      value: 1,
      text: '正常',
    },
    {
      value: 2,
      text: '异常',
      children: [
        {
          value: 21,
          text: '迟到',
        },
        {
          value: 22,
          text: '早退',
        },
        {
          value: 31,
          text: '缺卡',
        },
        {
          value: 26,
          text: '作废',
        },
        {
          value: 32,
          text: '导入班次',
        },
        {
          value: 33,
          text: '补签卡',
        },
        // {
        //   value: 34,
        //   text: '班次重叠',
        // },
      ],
    },
  ];
  const approvalStatus = [
    {
      value: 1,
      text: '待提交',
    },
    {
      value: 3,
      text: '审批中',
    },
    {
      value: 4,
      text: '审批通过',
    },
    {
      value: 6,
      text: '驳回',
    },
  ];
  const companyAttr = [
    {
      value: 'SF',
      text: '顺丰',
    },
    {
      value: 'SX',
      text: '顺心',
    },
  ];
  const onFinish = () => {
    form.validateFields().then(searchParams => {
      const params = cloneDeep(searchParams);
      if (params) {
        if (params.workDate) {
          params.timingTimeStart = params.workDate[0].format('x');
          params.timingTimeEnd = params.workDate[1].format('x');
        }
        if (params.dataStatus) {
          params.dataStatus = params.dataStatus.length
            ? params.dataStatus.pop()
            : '';
        }
        delete params.workDate;
        props.handleSearch(params);
      }
    });
  };
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };
  const getValues = () => {
    const idList = selectedRows.map(ele => ele.id);
    return { idList };
  };
  const handleSyncImport = () => {
    onFinish();
  };
  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        sourceType: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
        zoneCode: userInfo.deptCode,
      });
    }
  }, [userInfo.deptCode]);

  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);

  useEffect(() => {
    const filterVal = {};
    for (const key in searchValues) {
      if (searchValues[key] !== null && searchValues[key] !== undefined) {
        filterVal[key] = searchValues[key];
      }
    }
    setFormatVal(filterVal);
  }, [searchValues]);
  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        workDate: [
          moment(
            moment()
              .add(-6, 'days')
              .startOf('day'),
          ),
          moment(moment().endOf('day')),
        ],
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item name="sourceType" label="公司属性">
                <Select>
                  {companyAttr.map(ele => (
                    <Option value={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  extraParam={{
                    typeLevels: [2],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                    excludeOverseasFlag: true,
                  }}
                  showSearch
                  getList={getList}
                  placeholder="请输入分拨区名称或代码"
                  // disabled={!isRoot}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="zoneCode" label="中转场">
                <NetSearch
                  extraParam={{
                    excludeOverseasFlag: 1,
                  }}
                />
              </Item>
            </Col>
            <Item
              shouldUpdate={(prevValues, curValues) =>
                prevValues.zoneCode !== curValues.zoneCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <Item name="supplierName" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      isName
                      allowClear
                      gysParams={getFieldValue('zoneCode') || userInfo.deptCode}
                    />
                  </Item>
                </Col>
              )}
            </Item>
            <Col {...colStyle}>
              <Item name="userName" label="员工姓名">
                <Input placeholder="请输入" />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="userNo" label="员工工号">
                <Input placeholder="请输入" />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item
                name="workDate"
                label="考勤日期"
                // labelCol={{ md: 4, sm: 24, xs: 24 }}
                // wrapperCol={{ md: 20, sm: 24, xs: 24 }}
                rules={[{ required: true, message: '请选择' }]}
              >
                <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="dataStatus" label="状态">
                <Cascader
                  options={dataStatus}
                  fieldNames={{ label: 'text' }}
                  changeOnSelect
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="approvalStatus" label="审核状态">
                <Select allowClear placeholder="请选择">
                  {approvalStatus.map(ele => (
                    <Option key={ele.value} value={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="affirmStatus" label="是否确认">
                <Select allowClear placeholder="请选择">
                  <Option key={0} value={0}>
                    未确认
                  </Option>
                  <Option key={1} value={1}>
                    已确认
                  </Option>
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="shiftNo" label="班次名称">
                <Input placeholder="请输入" />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="status" label="是否作废">
                <Select allowClear placeholder="请选择">
                  <Option key={1} value={1}>
                    正常
                  </Option>
                  <Option key={2} value={2}>
                    作废
                  </Option>
                </Select>
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div style={{ color: '#df2e3f' }}>
          本月数据请在次月6日凌晨前处理完毕,否则影响结算！(因国庆假期，原时间节点后延至9日)
        </div>
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="deal_attendance_manage-submit"
          style={{ marginRight: 15 }}
          onClick={() => handleSubmit()}
          disabled={selectedRows.length < 1}
        >
          提交审核
        </AuthButton>
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="deal_attendance_manage-step"
          style={{ marginRight: 15 }}
          onClick={() => showStep()}
          disabled={selectedRows.length !== 1}
        >
          查看审核进度
        </AuthButton>
        {/* 补充工时 */}
        <AddHours
          setSelectedRows={props.setSelectedRows}
          searchValues={searchValues}
          userInfo={userInfo}
          selectedRows={selectedRows}
          originData={datas}
          refreshList={refreshList}
        ></AddHours>
        {/* 补签卡 */}
        <Retroactive
          searchValues={searchValues}
          userInfo={userInfo}
          selectedRows={selectedRows}
          originData={datas}
          refreshList={refreshList}
        ></Retroactive>
        {/* 作废工时 */}
        <InvalidHours
          userInfo={userInfo}
          moduleCode={moduleCode}
          selectedRows={selectedRows}
          refreshList={onFinish}
        ></InvalidHours>
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="deal_attendance_manage-cancel-hours"
          style={{ marginRight: 15 }}
          disabled={selectedRows.length < 1}
          onClick={() => handleCancelDel()}
        >
          取消作废工时
        </AuthButton>
        {/* 扣减工时 */}
        <MinusHours
          searchValues={searchValues}
          userInfo={userInfo}
          selectedRows={selectedRows}
          originData={datas}
          refreshList={onFinish}
        ></MinusHours>
        <ImportButton
          title="导入班次考勤人员"
          moduleCode={moduleCode}
          code="deal_attendance_manage-add-users"
          action="/ospmAccountService/attendanceHandleRest/uploadAddUser"
          modalUrl="/ospmAccountService/attendanceHandleRest/downAddUserTemplate"
          modalName="班次考勤人员模板.xlsx"
          // params={{ documentType: 1, ...formatVal }}
          // disabled={datas.list && datas.list.length < 1}
          style={{ marginRight: 15 }}
          handleSyncImport={handleSyncImport}
        />
        <AsyncExport
          text="导出所选"
          modulecode={moduleCode}
          code="deal_attendance_manage-export-some"
          disabled={selectedRows.length < 1}
          style={{ marginRight: 15 }}
          options={{
            filename: '考勤处理列表.xlsx',
            requstParams: [
              `/ospmAccountService/timingAttendanceDetailsRest/export`,
              {
                method: 'POST',
                body: getValues,
              },
            ],
          }}
        />
        <AsyncExport
          text="导出全部"
          modulecode={moduleCode}
          code="deal_attendance_manage-export-all"
          style={{ marginRight: 15 }}
          disabled={datas.list && datas.list.length < 1}
          options={{
            // total: totalNum,
            filename: '考勤处理列表.xlsx',
            requstParams: [
              `/ospmAccountService/timingAttendanceDetailsRest/exportSync`,
              {
                method: 'POST',
                body: { ...formatVal },
              },
            ],
          }}
        />
        {/* </div> */}
        {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
      </div>
    </Form>
  );
});
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
