import React, { useState, memo, useCallback } from 'react';
import { Button, message, Modal, Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import Viewer from 'react-viewer';
import StandardTable from '@/components/StandardTable';
import { minutesToHours } from '@/utils/utils';
import {
  queryDealList,
  submitDealList,
  delWorkHours,
  cancelWorkHours,
  // deductHours,
} from '../services/apis';
import SearchForm from './components/search';
import ShowStep from '../components/step';
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
// const { Item } = Form;

const tooltips = {
  workDuration: '计划上班时数-休息时长',
  lateMinute: '上班时间-班次开始时间',
  leaveEarlyMinute: '下班时间-班次结束时间',
  // accountWorkingHours: '结算工时=工作时长+补充时长-扣减工时',
  accountWorkingHours: (
    <span>
      ①上班卡和下班卡都在有效区间内，则结算工时等于班次结束-班次开始-休息时长+补充时长-扣减工时
      <br />
      ②有上班卡，上班卡迟到，下班卡正常，结算工时等于班次结束时间-上班卡-休息时长+补充时长-扣减工时
      <br />
      ③有上班卡，上班卡正常，下班卡早退，结算工时工时等于下班卡-班次开始时间-休息时长+补充时长-扣减工时
      <br />
      ④有上下班卡，上班卡迟到，下班卡早退，结算工时等于下班卡-上班卡-休息时长+补充时长-扣减工时
      <br />
      ⑤有上班卡，下班卡缺失 ，结算工时为0​
    </span>
  ),
  errorMsg: '迟到、早退、缺少下班卡，上下班都缺失  缺上班，作废',
  attendanceDuration: '签退时间-签到时间',
};
const PopTip = props => {
  const { title, tipKey } = props;
  return (
    <div>
      <span style={{ paddingRight: 10 }}>{title}</span>
      <Tooltip title={tooltips[tipKey]}>
        <QuestionCircleOutlined />
      </Tooltip>
    </div>
  );
};

const PageIndex = memo(() => {
  const dataStatus = {
    1: '正常',
    2: '异常',
  };
  const columns = [
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 150,
    },
    // {
    //   title: '分拨区',
    //   dataIndex: 'allocationArea',
    //   width: 150,
    // },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    // {
    //   title: '中转场',
    //   dataIndex: 'zoneName',
    //   width: 150,
    // },
    {
      title: '考勤日期',
      dataIndex: 'timingTime',
      width: 180,
      render: text => (text ? moment(text).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '工号',
      dataIndex: 'userNo',
      width: 180,
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: 150,
    },
    {
      title: '性别',
      dataIndex: 'sex',
      width: 150,
    },
    {
      title: '上班时间',
      dataIndex: 'startWorkTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '下班时间',
      dataIndex: 'endWorkTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 150,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '班次名称',
      dataIndex: 'shiftNo',
      width: 150,
    },
    {
      title: '班次开始时间',
      dataIndex: 'shiftStartTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '班次结束时间',
      dataIndex: 'shiftEndTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '计划上班时数',
      dataIndex: 'planWorkHours',
      width: 150,
      render: text => minutesToHours(text),
    },
    {
      title: '休息时长',
      dataIndex: 'restTime',
      width: 150,
    },
    {
      title: <PopTip title="结算工时" tipKey="accountWorkingHours" />,
      dataIndex: 'accountWorkingHours',
      width: 150,
    },
    {
      title: <PopTip title="出勤时长(小时)" tipKey="attendanceDuration" />,
      dataIndex: 'attendanceDuration',
      width: 180,
      render: text => minutesToHours(text),
    },
    {
      title: '补充工时(小时)',
      dataIndex: 'supplementWorkingHours',
      width: 180,
      render: text => minutesToHours(text),
    },
    {
      title: '扣减工时(分钟)',
      dataIndex: 'deductionWorkingHours',
      width: 180,
    },
    {
      title: <PopTip title="迟到(分钟)" tipKey="lateMinute" />,
      dataIndex: 'lateMinute',
      width: 180,
    },
    {
      title: <PopTip title="早退(分钟)" tipKey="leaveEarlyMinute" />,
      dataIndex: 'leaveEarlyMinute',
      width: 180,
    },
    {
      title: '排休时间',
      dataIndex: 'restTimeCode',
      width: 350,
    },
    {
      title: '班次号',
      dataIndex: 'shiftName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'dataStatus',
      width: 150,
      render: text => dataStatus[text],
    },
    {
      title: <PopTip title="异常原因" tipKey="errorMsg" />,
      dataIndex: 'errorMsg',
      width: 180,
    },
    {
      title: '单据类型',
      dataIndex: 'documentTypeName',
      width: 180,
    },
    {
      title: '审核状态',
      dataIndex: 'approvalStatusName',
      width: 150,
    },
    {
      title: '作废状态',
      dataIndex: 'statusName', // 1正常，2作废
      width: 150,
    },
    {
      title: '是否确认',
      dataIndex: 'affirmStatus',
      width: 150,
      render: text => ['否', '是'][text],
    },
    {
      title: '补签原因',
      dataIndex: 'pushReason',
      width: 150,
      render: (text, row) =>
        [row.pushReason, row.pushRemark].filter(item => item).join('，'),
    },
    {
      title: '作废原因',
      dataIndex: 'invalidReason',
      width: 150,
    },
    {
      title: '扣减原因',
      dataIndex: 'deductionReason',
      width: 150,
    },
    {
      title: '出入场图片',
      dataIndex: 'picList',
      width: 100,
      render: (text, row) => (
        <>
          <Button
            type="link"
            disabled={
              (row.pic1 === null && row.pic2 === null) || row.documentType !== 2
            }
            onClick={() => handlePreviewPic(row.picList)}
          >
            查看
          </Button>
        </>
      ),
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      width: 150,
    },
    {
      title: '操作时间',
      dataIndex: 'operatorTime',
      width: 180,
      render: timeTrans,
    },
  ];
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });

  const [searchValues, setSearchValues] = useState({}); // 查询入参
  const [selectedRows, setSelectedRows] = useState([]); // 选中数据
  const [loading, setLoading] = useState(false);
  const [stepModal, setStepModal] = useState(false);
  const [picVisible, setPicVisible] = useState(false); // 图片预览
  const [picSrc, setPicSrc] = useState([]); // 图片源
  // const [deductModal, setDeductModal] = useState(false);
  // 查看图片
  const handlePreviewPic = useCallback(picList => {
    const tmp = [];
    picList.forEach(pic => {
      if (pic !== '') {
        tmp.push({
          src: pic,
          alt: '出入场图片',
        });
      }
    });
    setPicSrc(tmp);
    setPicVisible(true);
  });
  // 翻页
  const changePage = pages => {
    const { pageSize, current } = pages;
    const data = {
      ...searchValues,
      pageSize,
      pageNum: current,
    };
    getList(data);
  };
  // 查询
  const handleSearch = values => {
    const data = {
      ...values,
      pageSize: 10,
      pageNum: 1,
    };
    setSearchValues(values);
    getList(data);
  };

  // 选择
  const handleonSelectRow = row => {
    setSelectedRows(row);
  };

  // 展开进度弹框
  const handleShowStep = () => {
    setStepModal(true);
  };
  // 隐藏进度弹框
  const handleHideStep = () => {
    setStepModal(false);
  };
  // 取消作废
  const handleCancelDel = () => {
    const ids = selectedRows.map(ele => ele.id);
    const arr = selectedRows.filter(ele => ele.status === 1);
    const confirmArr = selectedRows.filter(ele => ele.affirmStatus === 1);
    if (confirmArr.length > 0) {
      message.error('正常数据不允许提交审核，请知悉​');
      return false;
    }
    // status:1正常，2作废
    if (arr.length > 0) {
      message.error('所选的数据存在不作废工时的数据了，请正确选择');
      return false;
    }

    // cancelWorkHours
    Modal.confirm({
      title: '提示',
      content: '确认取消作废？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const res = await cancelWorkHours({ ids });
        if (res && res.success) {
          getList({ ...searchValues, pageNum: 1, pageSize: 10 });
          message.success('已取消作废');
        }
      },
    });
  };

  // const handleDeductModal = () => {
  //   const confirmArr = selectedRows.filter(ele => ele.affirmStatus === 1);
  //   if (confirmArr.length > 0) {
  //
  //     return false;
  //   }
  //   setDeductModal(true);
  // };
  // const handleHideDeduct = () => {
  //   setDeductModal(false);
  // };

  // const confirmDeduct = async values => {
  //   // console.log(values);
  //   const ids = selectedRows.map(ele => ele.id);
  //   try {
  //     const res = await deductHours({ ...values, ids });
  //     if (res.success) {
  //       message.success('已扣减工时');
  //       getList({ ...searchValues, pageNum: 1, pageSize: 10 });
  //       setDeductModal(false);
  //     } else {
  //       message.error(res.errorMessage);
  //     }
  //   } catch (e) {
  //     setDeductModal(false);
  //   }
  // };
  // 1待提交，2提交审核，3审核中，4审核通过，7失效数据
  // 当前审批状态：0 不需要审批的数据，1待提交，2提交审批，3审批中，4审批通过，5撤回， 6驳回
  // 提交审核
  const handleSubmit = () => {
    if (selectedRows.length > 0) {
      const soneDate = selectedRows.filter(ele => ele.approvalStatus === 0);
      if (soneDate.length > 0) {
        message.error('所选的数据存在不需要审核的数据，请正确选择');
        return false;
      }
      const list = selectedRows.filter(
        ele =>
          ele.approvalStatus === 1 ||
          ele.approvalStatus === 5 ||
          ele.approvalStatus === 6,
      );
      // const datalist = selectedRows.filter(ele => ele.dataStatus === 1);
      // const confirmArr = selectedRows.filter(ele => ele.affirmStatus === 1);
      // if (confirmArr.length > 0) {
      //   return false;
      // }
      if (list.length !== selectedRows.length) {
        message.error('所选的数据存在已提交审核的数据了，请正确选择');
        return false;
      }
      // if (datalist.length > 0) {
      //   message.error('所选的数据存在已存在正常的数据了，请正确选择');
      //   return false;
      // }
      const unUpLoadPicList = [];
      selectedRows.forEach(row => {
        if (row.documentType === 2 && row.pic1 === null && row.pic2 === null) {
          unUpLoadPicList.push(row.userNo);
        }
      });
      if (unUpLoadPicList.length !== 0) {
        message.error(
          `所选工号${unUpLoadPicList.toString()}的数据出入场图片未上传，请通过补签卡上传图片`,
        );
        return false;
      }
      const ids = selectedRows.map(ele => ele.timingUuid);
      Modal.confirm({
        title: '提示',
        content: '确认提交审核？',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          const res = await submitDealList({ ids, code: 2 });
          if (res && res.success) {
            getList({ ...searchValues, pageNum: 1, pageSize: 10 });
            message.success('已提交审核');
          }
        },
      });
    } else {
      message.error('请选择数据操作');
    }
  };
  // 查询
  const getList = async data => {
    setLoading(true);
    setSelectedRows([]);
    setDatas({
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    });

    const res = await queryDealList(data).finally(() => setLoading(false));

    if (res && res.success) {
      const { list, total, pageNum, pageSize } = res.obj;
      setDatas({
        list: list || [],
        pagination: {
          pageSize,
          current: pageNum,
          total,
        },
      });
    }
  };

  return (
    <div className="table-list">
      <div className="tableListForm">
        <SearchForm
          handleSearch={handleSearch}
          handleSubmit={handleSubmit}
          selectedRows={selectedRows}
          setSelectedRows={setSelectedRows}
          showStep={handleShowStep}
          handleCancelDel={handleCancelDel}
          // handleDeductModal={handleDeductModal}
          searchValues={searchValues}
          datas={datas}
          refreshList={getList}
        />
        <StandardTable
          size="small"
          data={datas}
          columns={columns}
          multiple
          loading={loading}
          selectedRows={selectedRows}
          onSelectRow={handleonSelectRow}
          // showSelection={false}
          rowKey="id"
          onChange={changePage}
        />
        {stepModal && (
          <ShowStep
            visible={stepModal}
            handleHide={handleHideStep}
            row={selectedRows[0]}
          />
        )}
        <Viewer
          visible={picVisible}
          onClose={() => {
            setPicVisible(false);
          }}
          drag={false}
          images={picSrc}
        />
        {/* {deductModal && (
        <DeductModal
          visible={deductModal}
          confirmDeduct={confirmDeduct}
          handleHide={handleHideDeduct}
        />
      )} */}
      </div>
    </div>
  );
});
export default PageIndex;
