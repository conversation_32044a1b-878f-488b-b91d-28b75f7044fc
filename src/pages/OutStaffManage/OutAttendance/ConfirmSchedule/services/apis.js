import request from '@/utils/request';
export function queryList(body) {
  return request(`/fpmTmgtServices/supplierOrderConfirm/listInPage`, {
    method: 'POST',
    body,
  });
}

// 编辑前的校验接口
export function checkEdit(body) {
  return request(`/fpmTmgtServices/supplierOrderConfirm/allowUpdate`, {
    method: 'POST',
    body,
  });
}

// 编辑和详情的查询接口
export function queryTableList(body) {
  return request(`/fpmTmgtServices/customerOrderConfirm/listCustomer`, {
    method: 'POST',
    body,
  });
}
export function customerConfirmList(body) {
  return request(`/fpmTmgtServices/customerOrderConfirm/confirm`, {
    method: 'POST',
    body,
  });
}
export function supplierConfirmList(body) {
  return request(`/fpmTmgtServices/supplierOrderConfirm/confirm`, {
    method: 'POST',
    body,
  });
}
