import React, { Fragment, useState } from 'react';
import { connect } from 'dva';
import { Button } from 'antd';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import StandardTable from 'src/components/StandardTable';
import authDecorator from 'src/components/AuthDecorator';
import { queryList, checkEdit } from './services/apis';
// import { queryList, supplierConfirmList } from './services/apis';

import SearchForm from './components/search';
import ConfirmModal from './components/modal';
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';

const PageIndex = ({ areaListSF, areaListSX }) => {
  // const { moduleCode } = props;
  const columns = [
    // {
    //   title: '战123区代码',
    //   dataIndex: 'areaCode',
    //   align: 'center',
    //   width: 100,
    // },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: value => {
        const areaList = areaListSF.concat(areaListSX);
        const date = areaList.find(item => item.value === value);
        return date ? date.label : value;
      },
    },
    {
      title: '中转场代码',
      dataIndex: 'deptCode',
      align: 'center',
      width: 100,
    },
    {
      title: '中转场名称',
      dataIndex: 'deptName',
      width: 150,
      align: 'center',
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 170,
      align: 'center',
    },
    {
      title: '供应商编码',
      ellipsis: true,
      dataIndex: 'supplierCode',
      width: 120,
      align: 'center',
    },
    {
      title: '班次号',
      dataIndex: 'shiftName',

      width: 200,
      align: 'center',
    },
    {
      title: '预计工时(小时)',
      dataIndex: 'totalAccountConfirmHours',
      width: 150,
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      align: 'center',
      render: text =>
        text === -1
          ? '全部'
          : text === 0
          ? '待确认'
          : text === 1
          ? '已确认'
          : text === 2
          ? '超时未确认'
          : '',
    },
    {
      title: '考勤时间',
      dataIndex: 'beginTime',
      width: 180,
      align: 'center',
      render: value => (value ? moment(value).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '班次开始时间',
      dataIndex: 'beginTime',
      width: 180,
      align: 'center',
      render: timeTrans,
    },
    {
      title: '班次结束时间',
      dataIndex: 'endTime',
      width: 180,
      align: 'center',
      render: timeTrans,
    },
    {
      title: '确认/需求/通过人数',
      dataIndex: 'confirmNum',
      width: 150,
      align: 'center',
      render: (text, record) =>
        `${record.confirmNum} / ${record.requireNum} / ${record.signUpNum}`,
    },
    {
      title: '班次预计货量(吨)',
      dataIndex: 'shiftWeight',
      width: 150,
      align: 'center',
      render: text => (text ? (text / 1000000).toFixed(2) : ''),
    },
    // {
    //   title: '班次确认货量(吨)',
    //   dataIndex: 'totalWeight',
    //   width: 150,
    //   align: 'center',
    //   render: text => (text ? (text / 1000000).toFixed(2) : ''),
    // },
    {
      title: '确认人',
      align: 'center',
      dataIndex: 'confirmName',
      width: 100,
    },
    {
      title: '确认时间',
      dataIndex: 'confirmTime',
      width: 180,
      align: 'center',
      render: timeTrans,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 150,
      align: 'center',
      fixed: 'right',
      render: (text, record) => (
        <Fragment>
          {/* <AuthButton
            type="primary"
            size="small"
            style={{ marginRight: 8 }}
            disabled={record.status === 1}
            onClick={e => confirmItem(e, record)}
            moduleCode={moduleCode}
            code="confirm_supp-confirm"
          >
            确认
          </AuthButton> */}
          <Button
            type="primary"
            size="small"
            style={{ marginRight: 10 }}
            // disabled={record.status !== 1}
            onClick={e => confirmModal(e, record)}
            // moduleCode={moduleCode}
            // code="confirm_supp-detail"
          >
            详情
          </Button>
          {/* 单据归属单月,当前时间是次月6日2359后，则不展示编辑入口 */}
          {/* {moment() <
            moment(record.beginTime)
              .add(1, 'months')
              .startOf('month')
              .add(5, 'day')
              .endOf('day')
              .format('x') && (
            <Button
              type="primary"
              size="small"
              onClick={e => editModal(e, record)}
            >
              编辑
            </Button>
          )} */}
          {record.allowUpdate && (
            <Button
              type="primary"
              size="small"
              onClick={e => editModal(e, record)}
            >
              编辑
            </Button>
          )}
        </Fragment>
      ),
    },
  ];
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });
  const [row, setRow] = useState({});
  const [searchVals, setSearchVals] = useState({});
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [selectedRows, setSelectedRows] = useState([]); // 选中数据
  // 翻页
  const changePage = pages => {
    const { current, pageSize } = pages;
    getList({ ...searchVals, pageNum: current, pageSize });
  };
  const handleonSelectRow = rows => {
    setSelectedRows(rows);
  };
  // 查询
  const handleSearch = values => {
    // console.log(values);
    const data = {
      ...values,
      pageSize: 10,
      pageNum: 1,
    };
    setSearchVals(values);
    getList(data);
  };

  // 查询
  const getList = async data => {
    const { pageNum, pageSize } = data;
    setLoading(true);
    setDatas({
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    });
    setSelectedRows([]);

    const res = await queryList(data).finally(() => setLoading(false));
    if (res && res.success && res.obj) {
      const { list, total } = res.obj;
      setDatas({
        list: list || [],
        pagination: {
          pageSize,
          current: pageNum,
          total,
        },
      });
    }
  };
  // 详情
  const confirmModal = async (e, record) => {
    e.stopPropagation();
    setVisible(true);
    const rowValue = cloneDeep(record);
    rowValue.type = 'detail';
    setRow(rowValue);
  };

  const editModal = async (e, record) => {
    e.stopPropagation();

    try {
      checkEdit({
        zoneCode: record.deptCode,
        supplierCode: record.supplierCode,
        reqTime: moment(record.beginTime).format('YYYY-MM-DD'),
        shiftNo: record.shiftName,
      }).then(res => {
        // console.log(res, 'res');
        if (res.success) {
          const rowValue = cloneDeep(record);
          rowValue.type = 'edit';
          setRow(rowValue);
          setVisible(true);
        }
      });
    } catch (err) {
      throw err;
    }
  };
  const handleConfirm = () => {
    getList({ ...searchVals, pageNum: 1, pageSize: 10 });
    setVisible(false);
  };
  const handleHide = () => {
    setVisible(false);
  };
  return (
    <div className="table-list">
      <div className="tableListForm">
        <SearchForm
          handleSearch={handleSearch}
          datas={datas}
          searchValues={searchVals}
          selectedRows={selectedRows}
        />
        <StandardTable
          size="small"
          data={datas}
          columns={columns}
          multiple
          loading={loading}
          selectedRows={selectedRows}
          onSelectRow={handleonSelectRow}
          // showSelection={false}
          rowKey="id"
          onChange={changePage}
        />
      </div>
      {visible && (
        <ConfirmModal
          visible={visible}
          handleConfirm={handleConfirm}
          handleHide={handleHide}
          row={row}
        />
      )}
    </div>
  );
};

export default connect(state => ({
  userInfo: state.global.userInfo,
  systemRole: state.global.roles,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))(authDecorator(PageIndex));
