import React, { useEffect, useState } from 'react';
import { Form, DatePicker, Input, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import NetSearch from 'src/components/NetSelect';
import authDecorator from 'src/components/AuthDecorator';
import AsyncExport from 'src/components/AsyncExport';
import SuppliersSearch from 'src/components/SuppliersSearch';
import '../style.scss';
const { Item } = Form;
const { Option } = Select;
const { RangePicker } = DatePicker;

const SearchForm = props => {
  const [form] = Form.useForm();
  const {
    userInfo,
    datas,
    searchValues,
    selectedRows,
    moduleCode,
    handleSearch,
  } = props;
  const [allocateList, setAllocateList] = useState([]); // 分拨区
  const status = [
    {
      value: -1,
      text: '全部',
    },
    {
      value: 0,
      text: '待确认',
    },
    {
      value: 1,
      text: '已确认',
    },
    {
      value: 2,
      text: '超时未确认',
    },
  ];

  const onFinish = () => {
    form.validateFields().then(nameList => {
      if (nameList) {
        if (nameList.rangeTime) {
          nameList.beginTime = nameList.rangeTime[0].startOf('day').format('x');
          nameList.endTime = nameList.rangeTime[1].endOf('day').format('x');
          delete nameList.rangeTime;
        }
        handleSearch(nameList);
      }
    });
  };
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };

  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        deptCode: userInfo.deptCode,
      });
    }
  }, [userInfo.deptCode]);

  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          areaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);

  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        rangeTime: [
          moment(
            moment()
              .add(-6, 'days')
              .startOf('day'),
          ),
          moment(moment().endOf('day')),
        ],
        deptCode: '755A',
        status: -1,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item
                name="areaCode"
                label="分拨区"
                dependencies={['deptCode']}
                rules={[
                  // {
                  //   required:
                  //     !form.getFieldValue('deptCode') &&
                  //     !form.getFieldValue('areaCode'),
                  //   message: '请选择',
                  // },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (getFieldValue('deptCode') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区或者中转场必填写其一'),
                      );
                    },
                  }),
                ]}
              >
                <NetSearch
                  extraParam={{
                    typeLevels: ['2'],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                  }}
                  showSearch
                  placeholder="请输入分拨区名称或代码"
                  getList={getList}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item
                name="deptCode"
                label="中转场"
                dependencies={['areaCode']}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (getFieldValue('areaCode') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区或者中转场必填写其一'),
                      );
                    },
                  }),
                ]}
              >
                <NetSearch
                  extraParam={{
                    excludeOverseasFlag: 1,
                  }}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="shiftName" label="班次号">
                <Input placeholder="请输入班次号" />
              </Item>
            </Col>
            <Item
              shouldUpdate={(prevValues, curValues) =>
                prevValues.deptCode !== curValues.deptCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <Item name="supplierName" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      isName
                      allowClear
                      gysParams={getFieldValue('deptCode') || userInfo.deptCode}
                    />
                  </Item>
                </Col>
              )}
            </Item>
            <Col {...colStyle}>
              <Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请输入选择' }]}
              >
                <Select allowClear>
                  {status.map(ele => (
                    <Option key={ele.value} value={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item
                label="考勤日期"
                name="rangeTime"
                className="classTime"
                rules={[{ required: true, message: '请输入选择' }]}
              >
                <RangePicker style={{ width: '100%' }} />
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div style={{ color: '#df2e3f' }}>
          次月6日前可操作上个月超时未确认数据
        </div>
        <AsyncExport
          text="导出所选"
          modulecode={moduleCode}
          code="confirm_supp-export-some"
          style={{ marginRight: 15 }}
          disabled={selectedRows.length < 1}
          options={{
            // total: totalNum,
            filename: '供应商考勤班次确认列表.xlsx',
            requstParams: [
              `/fpmTmgtServices/supplierOrderConfirm/exportChooseList`,
              {
                method: 'POST',
                body: selectedRows,
              },
            ],
          }}
        />
        <AsyncExport
          text="导出全部"
          modulecode={moduleCode}
          code="confirm_supp-export-all"
          style={{ marginRight: 15 }}
          disabled={datas.list && datas.list.length < 1}
          options={{
            // total: totalNum,
            filename: '供应商考勤班次确认列表.xlsx',
            requstParams: [
              `/fpmTmgtServices/supplierOrderConfirm/exportList`,
              {
                method: 'POST',
                body: { ...searchValues },
              },
            ],
          }}
        />
        {/* <div>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
      </div>
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
