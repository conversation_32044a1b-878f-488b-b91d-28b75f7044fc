/* eslint-disable no-return-assign */
import React, { useEffect, Fragment, useState, useRef } from 'react';
import {
  Form,
  Row,
  Col,
  Modal,
  Input,
  Button,
  Select,
  message,
  notification,
} from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import StandardTable from 'src/components/StandardTable';
import authDecorator from 'src/components/AuthDecorator';
import AsyncExport from 'src/components/AsyncExport';
import AuthButton from 'src/components/AuthButton';
import {
  queryTableList,
  customerConfirmList,
  supplierConfirmList,
} from '../services/apis';
import './index.scss';
const { Item } = Form;
const layout = {
  labelCol: { md: 6, sm: 24, xs: 24 },
  wrapperCol: { md: 14, sm: 24, xs: 24 },
};
const { Option } = Select;
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const ModalForm = props => {
  const [form] = Form.useForm();
  const { visible, handleHide, handleConfirm, row, moduleCode } = props;

  const { supplierId, orderNo, status, type } = row;
  const dataType = -1;
  const [loading, setLoading] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);

  const [searchVals, setSearchVals] = useState({}); // 查询的条件缓存
  // const [btnDisabled, setBtnDisabled] = useState(false);
  const [shiftWeight, setShiftWeight] = useState(0.0);
  const [shiftHours, setShiftHours] = useState(0.0);
  const columns = [
    {
      title: '姓名',
      dataIndex: 'name',
      width: 80,
      align: 'center',
    },
    {
      title: '工号',
      dataIndex: 'staffId',
      align: 'center',
      width: 100,
    },
    {
      title: '工时(小时)',
      dataIndex: 'accountHours',
      width: 150,
      // render: text => (text >= 0 ? (text / 60).toFixed(2) : '-'),
    },
    {
      title: '班次操作货量(T)',
      dataIndex: 'avgWeight',
      align: 'center',
      width: 150,
      render: text => (text >= 0 ? (text / 1000000).toFixed(2) : '-'),
    },
    {
      title: '装车货量(T)',
      dataIndex: 'a',
      align: 'center',
      width: 120,
      render: text => (text >= 0 ? (text / 1000000).toFixed(2) : '-'),
    },
    {
      title: '卸车货量(T)',
      dataIndex: 'b',
      align: 'center',
      width: 120,
      render: text => (text >= 0 ? (text / 1000000).toFixed(2) : '-'),
    },
    {
      title: '分拣货量(T)',
      dataIndex: 'c',
      align: 'center',
      width: 120,
      render: text => (text >= 0 ? (text / 1000000).toFixed(2) : '-'),
    },

    {
      title: '上班时间',
      dataIndex: 'onlineAttendTime',
      align: 'center',
      width: 170,
      render: timeTrans,
    },
    {
      title: '下班时间',
      dataIndex: 'offlineAttendTime',
      align: 'center',
      width: 170,
      render: timeTrans,
    },
    {
      title: '考勤状态',
      dataIndex: 'dataType',
      align: 'center',
      width: 100,
      render: text => (text ? { 1: '正常数据', 2: '异常数据' }[text] : '-'),
    },
    {
      title: '异常原因',
      dataIndex: 'attendanceErrorMsg',
      align: 'center',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      width: 80,
      render: text => (text === 1 ? '已确认' : '待确认'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: (text, record) => (
        <Fragment>
          <AuthButton
            type="primary"
            size="small"
            disabled={record.status !== 1}
            onClick={e => cancleConfirm(e, record)}
            moduleCode={moduleCode}
            code="confirm_supp-detail"
          >
            取消确认
          </AuthButton>
        </Fragment>
      ),
    },
  ];
  const [datas, setDatas] = useState({
    list: [],
    pagination: false,
  });
  const [selectedRows, setSelectedRows] = useState([]);
  const [scrollHeight, setScrollHeight] = useState(0);
  const dom = useRef();
  const dataTypes = [
    {
      value: -1,
      text: '全部数据',
    },
    {
      value: 1,
      text: '正常数据',
    },
    {
      value: 2,
      text: '异常数据',
    },
  ];
  // 重置
  const resetForm = () => {
    form.resetFields();
  };

  // 取消确认
  const cancleConfirm = async (e, record) => {
    e.stopPropagation();
    const data = {
      supplierId,
      orderNo,
      list: [{ ...record }],
      confirmName: sessionStorage.username,
      status: 0,
      sourceType: 'FOP-OSPM-CORE',
    };
    Modal.confirm({
      title: '提示',
      content: '确认取消',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const res = await customerConfirmList(data);
        if (res && res.success) {
          message.success('取消确认成功！');
          onFinish();
        }
      },
    });
  };

  const onFinish = values => {
    setSearchVals(values);
    queryList({ supplierId, orderNo, dataType, ...values });
  };
  const handleSelect = items => {
    setSelectedRows(items);
  };

  const handleOk = async () => {
    try {
      setLoadingBtn(true);
      const list = selectedRows.filter(ele => ele.status !== 1);
      if (list.length < 1) {
        message.error('请选择待确认的数据');
        return false;
      }

      const data = {
        supplierId,
        orderNo,
        list,
        confirmName: sessionStorage.username,
        status: 1,
        sourceType: 'FOP-OSPM-CORE',
        aipFlag: 1,
      };
      const res = await customerConfirmList(data);
      const { obj } = res || {};
      if (obj && obj.msgData) {
        setLoadingBtn(false);
        notification.open({
          message: <span style={{ fontSize: 18 }}>操作提示</span>,
          description: (
            <div>
              {obj.msgData.map(item => (
                <div>
                  <span className="wordStyle">{item.name}</span>已被
                  <span className="wordStyle">{item.zoneCode}</span>
                  场地
                  <span className="wordStyle">{item.supplierName}</span>
                  确认
                </div>
              ))}
            </div>
          ),
          duration: null,
          style: {
            width: 500,
          },
        });
      } else {
        setLoadingBtn(false);
        message.success('已确认所选数据');
        queryList({ supplierId, orderNo, dataType, ...searchVals });
      }
    } catch {
      setLoadingBtn(false);
    }
  };
  const confirmSuppliers = async () => {
    const res = await supplierConfirmList({
      supplierId,
      orderNo,
      confirmName: sessionStorage.username,
      status: 1,
      sourceType: 'FOP-OSPM-CORE',
    }).finally(() => {
      handleConfirm();
    });
    if (res && res.success) {
      message.success('已确认整个班次');
    } else {
      message.error(res.errorMessage);
    }
  };
  // const handleBack = () => {
  //   if (status === 1) {
  //     handleConfirm();
  //     return false;
  //   }
  //   const len = datas.list.filter(ele => ele.status !== 1);
  //   if (len.length > 0) {
  //     Modal.confirm({
  //       title: '提示',
  //       content: '确认整个班次？确认后则单据状态变更为已确认',
  //       okText: '确认',
  //       cancelText: '取消',
  //       onOk: async () => {
  //         confirmSuppliers();
  //       },
  //       onCancel: () => {
  //         handleConfirm();
  //         // confirmSelect();
  //       },
  //     });
  //   } else if (datas.list.length < 1) {
  //     handleConfirm();
  //   } else if (datas.list.length > 0 && datas.list.length > len) {
  //     confirmSuppliers();
  //   }
  // };
  // const reduceSum = (arr, id) =>
  //   arr.reduce((pre, cur) => {
  //     if (cur[id] >= 0) {
  //       return pre + cur[id];
  //     }
  //     return pre;
  //   }, 0);
  // 预计结算工时  accountHours;
  // 确认工时 accountConfirmHours;
  // 预计货量  accountWeights;
  // 确认货量  accountConfirmWeights;
  const queryList = async data => {
    setLoading(true);
    setSelectedRows([]);
    const res = await queryTableList(data).finally(() => setLoading(false));
    if (res.obj && res.success) {
      const bol = res.obj.list.some(item => item.status === 1);
      setShiftWeight(
        bol ? res.obj.accountConfirmWeights : res.obj.accountWeights,
      );
      setShiftHours(bol ? res.obj.accountConfirmHours : res.obj.accountHours);
      const arrList = res.obj.list.map(ele => {
        const obj = {};
        ele.accrueItems.map(item => {
          if (item.jobType === '装车工') {
            obj.a = item.weight;
          } else if (item.jobType === '卸车工') {
            obj.b = item.weight;
          } else if (item.jobType === '分拣') {
            obj.c = item.weight;
          } else if (item.jobType === '叉车工') {
            obj.d = item.weight;
          }
          return { ...item, ...obj };
        });
        return { ...ele, ...obj };
      });
      setDatas({
        list: arrList,
        pagination: false,
      });
    }
  };

  useEffect(() => {
    if (supplierId && orderNo && dataType) {
      queryList({ supplierId, orderNo, dataType });
    }
  }, [supplierId]);
  useEffect(() => {
    if (dom.current) {
      const height =
        document.body.clientHeight -
        dom.current.getBoundingClientRect().top -
        200;
      setScrollHeight(height);
    }
  }, [dom.current]);

  // const getCheckboxProps = record => ({
  //   disabled: record.status === 1,
  //   name: record.name,
  // });

  const onCancelHandel = () => {
    // setShiftWeight(0.0);
    // setShiftHours(0.0);
    if (type === 'detail' || datas.list.length === 0) {
      handleHide();
    } else {
      if (status === 1) {
        handleHide();
        handleConfirm();
        return false;
      }
      // 列表中待确认的数据list
      if (datas.list.some(ele => ele.status === 1)) {
        Modal.confirm({
          title: '提示',
          content: '确认整个班次？确认后则单据状态变更为已确认',
          okText: '确认',
          cancelText: '取消',
          onOk: async () => {
            confirmSuppliers();
            handleHide();
          },
          onCancel: () => {
            handleConfirm();
            handleHide();
          },
        });
      } else {
        handleHide();
        handleConfirm();
      }

      // const len = datas.list.filter(ele => ele.status !== 1);
      // if (len && len.length > 0) {
      //   Modal.confirm({
      //     title: '提示',
      //     content: '确认整个班次？确认后则单据状态变更为已确认',
      //     okText: '确认',
      //     cancelText: '取消',
      //     onOk: async () => {
      //       confirmSuppliers();
      //       handleHide();
      //     },
      //     onCancel: () => {
      //       handleConfirm();
      //       handleHide();
      //       // confirmSelect();
      //     },
      //   });
      // } else if (datas.list.length < 1) {
      //   handleConfirm();
      // } else if (datas.list.length > 0 && datas.list.length > len) {
      //   confirmSuppliers();
      // }
    }
  };
  return (
    <Modal
      width={1300}
      visible={visible}
      title={`考勤班次${type === 'detail' ? '详情' : '编辑'}`}
      onCancel={onCancelHandel}
      footer={null}
      // footer={
      //   type === 'detail'
      //     ? null
      //     : [
      //         // <AuthButton
      //         //   key="back"
      //         //   onClick={() => handleBack()}
      //         //   moduleCode={moduleCode}
      //         //   code="confirm_cust-back"
      //         // >
      //         //   返回
      //         // </AuthButton>,
      //         <AuthButton
      //           key="submit"
      //           type="primary"
      //           disabled={selectedRows.length < 1}
      //           onClick={() => handleOk()}
      //           moduleCode={moduleCode}
      //           code="confirm_cust-submit"
      //         >
      //           人员确认
      //         </AuthButton>,
      //       ]
      // }
    >
      <Form
        {...layout}
        form={form}
        onFinish={onFinish}
        initialValues={{ dataType: -1 }}
      >
        <Row gutter={{ md: 4, lg: 12, xl: 12 }}>
          <Col md={8} sm={24} xs={24}>
            <Item name="name" label="姓名">
              <Input placeholder="请输入姓名" allowClear />
            </Item>
          </Col>
          <Col md={8} sm={24} xs={24}>
            <Item name="staffId" label="工号">
              <Input placeholder="请输入工号" allowClear />
            </Item>
          </Col>
          <Col md={8} sm={24} xs={24}>
            <Item name="dataType" label="考勤状态">
              <Select>
                {dataTypes.map(ele => (
                  <Option key={ele.value} value={ele.value}>
                    {ele.text}
                  </Option>
                ))}
              </Select>
            </Item>
          </Col>
          <div className="btnCon">
            <div>
              <AsyncExport
                text="导出所选"
                modulecode={moduleCode}
                code="confirm_cust-export-some"
                style={{ marginRight: 15 }}
                handleCancel={() => handleHide()}
                disabled={selectedRows.length < 1}
                options={{
                  // total: totalNum,
                  filename: '供应商考勤班次确认列表.xlsx',
                  requstParams: [
                    `/fpmTmgtServices/customerOrderConfirm/exportChooseList`,
                    {
                      method: 'POST',
                      body: selectedRows,
                    },
                  ],
                }}
              />
              <AsyncExport
                text="导出全部"
                modulecode={moduleCode}
                code="confirm_cust-export-all"
                style={{ marginRight: 15 }}
                disabled={datas.list && datas.list.length < 1}
                handleCancel={() => handleHide()}
                options={{
                  // total: totalNum,
                  filename: '供应商考勤班次确认列表.xlsx',
                  requstParams: [
                    `/fpmTmgtServices/customerOrderConfirm/exportList`,
                    {
                      method: 'POST',
                      body: { supplierId, orderNo, dataType, ...searchVals },
                    },
                  ],
                }}
              />
              {type === 'edit' && (
                <AuthButton
                  key="submit"
                  type="primary"
                  loading={loadingBtn}
                  style={{ marginRight: 15 }}
                  disabled={
                    selectedRows.length < 1 ||
                    selectedRows.some(item => item.status === 1)
                  }
                  onClick={() => handleOk()}
                  moduleCode={moduleCode}
                  code="confirm_cust-submit"
                >
                  批量确认
                </AuthButton>
              )}
            </div>
            <div>
              <Button
                type="primary"
                style={{ marginRight: 15 }}
                htmlType="submit"
              >
                查询
              </Button>
              <Button style={{ marginRight: 15 }} onClick={resetForm}>
                重置
              </Button>
            </div>
          </div>
        </Row>
      </Form>
      <div style={{ fontSize: '16px' }} ref={dom}>
        <span>预计货量/工时： </span>
        <span style={{ color: '#DC1E32' }}>{`${shiftWeight}T/${shiftHours ||
          '0.00'}小时`}</span>
      </div>
      <StandardTable
        size="small"
        data={datas}
        columns={
          type === 'detail'
            ? columns.filter(item => item.dataIndex !== 'action')
            : columns
        }
        loading={loading}
        selectedRows={selectedRows}
        onSelectRow={handleSelect}
        // showSelection={false}
        footer={null}
        rowKey="staffId"
        scroll={{ y: scrollHeight }}
        showSelection
        // getCheckboxProps={type === 'edit' ? getCheckboxProps : null}
        onRow={null}
      />
    </Modal>
  );
};

export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(ModalForm));
