import React, { useState, useCallback } from 'react';
import { message, Tooltip, Button } from 'antd';
import moment from 'moment';
import { QuestionCircleOutlined } from '@ant-design/icons';
import Viewer from 'react-viewer';
import StandardTable from '@/components/StandardTable';
import { queryTestList, passTestList, rejectTestList } from '../services/apis';
import SearchForm from './components/search';
import ShowStep from '../components/step';
import Suggest from '../components/suggest';
import { minutesToHours } from '@/utils/utils';

const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const tooltips = {
  planWorkHours: '规定工作时长=计划上班时数-休息时长',
  attendanceNum: '累加该班次人员数据',
  lateNum: '累加该班次该类型异常单据',
  supplementWorkingHours: '该班次补充工时累加，单位小时',
  deductionWorkingHours: '该班次扣减工时累加，单位小时',
  accountWorkingHours: '该班次下所有正常人员结算工时累加',
};
const PopTip = props => {
  const { title, tipKey } = props;
  return (
    <div>
      <span style={{ paddingRight: 10 }}>{title}</span>
      <Tooltip title={tooltips[tipKey]}>
        <QuestionCircleOutlined />
      </Tooltip>
    </div>
  );
};

const PageIndex = () => {
  const columns = [
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 150,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 150,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '考勤日期',
      dataIndex: 'timingTime',
      width: 180,
      render: text => (text ? moment(text).format('YYYY-MM-DD') : '-'),
    },
    {
      title: '工号',
      dataIndex: 'userNo',
      width: 180,
    },
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '单据类型',
      dataIndex: 'documentTypeName',
      width: 150,
    },
    {
      title: '合同类型',
      dataIndex: 'agreemenTypeName',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'approvalStatusName',
      width: 150,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 150,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '班次号',
      dataIndex: 'shiftName',
      width: 150,
    },
    {
      title: '班次开始时间',
      dataIndex: 'shiftStartTime',
      width: 150,
      render: timeTrans,
    },
    {
      title: '班次结束时间',
      dataIndex: 'shiftEndTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '计划上班时数(小时)',
      dataIndex: 'planWorkHours',
      width: 150,
      render: text => minutesToHours(text),
    },
    {
      title: '休息时长(小时)',
      dataIndex: 'restTime',
      width: 150,
      render: text => minutesToHours(text),
    },
    {
      title: '上班时间',
      dataIndex: 'startWorkTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '下班时间',
      dataIndex: 'endWorkTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '补充工时(小时)',
      dataIndex: 'supplementWorkingHours',
      width: 180,
      render: text => minutesToHours(text),
    },
    {
      title: '扣减工时(分钟)',
      dataIndex: 'deductionWorkingHours',
      width: 180,
    },
    {
      title: <PopTip title="结算工时" tipKey="accountWorkingHours" />,
      dataIndex: 'accountWorkingHours',
      width: 180,
    },
    {
      title: '提交人',
      dataIndex: 'operator',
      width: 150,
    },
    {
      title: '操作时间',
      dataIndex: 'operatorTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '出入场图片',
      dataIndex: 'picList',
      width: 100,
      fixed: 'right',
      render: (text, row) => (
        <>
          {row.picList.find(i => i !== '') && (
            <Button type="link" onClick={() => handlePreviewPic(row.picList)}>
              查看
            </Button>
          )}
        </>
      ),
    },
  ];
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });

  const [searchValues, setSearchValues] = useState({}); // 查询入参
  const [selectedRows, setSelectedRows] = useState([]); // 选中数据
  const [loading, setLoading] = useState(false);
  const [stepModal, setStepModal] = useState(false);
  const [suggestModal, setSuggestModal] = useState(false); // 审核意见弹框
  const [suggestTitle, setSuggestTitle] = useState('审核确认'); // 审核意见弹框
  const [picVisible, setPicVisible] = useState(false); // 图片预览
  const [picSrc, setPicSrc] = useState([]); // 图片源

  const handlePreviewPic = useCallback(picList => {
    const tmp = [];
    picList.forEach(pic => {
      if (pic !== '') {
        tmp.push({
          src: pic,
          alt: '出入场图片',
        });
      }
    });
    setPicSrc(tmp);
    setPicVisible(true);
  });
  // 翻页
  const changePage = pages => {
    const { pageSize, current } = pages;
    const data = {
      ...searchValues,
      pageSize,
      pageNum: current,
    };
    getList(data);
  };
  // 查询
  const handleSearch = values => {
    const data = {
      ...values,
      pageSize: 10,
      pageNum: 1,
    };
    setSearchValues(values);
    getList(data);
  };

  // 选择
  const handleonSelectRow = row => {
    setSelectedRows(row);
  };

  // 展开进度弹框
  const handleShowStep = () => {
    setStepModal(true);
  };
  // 隐藏进度弹框
  const handleHideStep = () => {
    setStepModal(false);
  };
  // 1待提交，2提交审核，3审核中，4审核通过，7失效数据
  // 提交审核

  // 隐藏审核意见弹框
  const handleHideSuggest = () => {
    setSuggestModal(false);
  };
  const handleConfirm = async values => {
    const ids = selectedRows.map(ele => ele.timingUuid);
    if (suggestTitle === '审核确认') {
      const res = await passTestList({ ids, code: 2, ...values }).finally(() =>
        setSuggestModal(false),
      );
      if (res.success) {
        getList({ ...searchValues, pageNum: 1, pageSize: 10 });
        message.success('审核操作成功');
      }
    } else {
      const res = await rejectTestList({
        ids,
        code: 2,
        ...values,
      }).finally(() => setSuggestModal(false));
      if (res && res.success) {
        getList({ ...searchValues, pageNum: 1, pageSize: 10 });
        message.success('驳回操作成功');
      }
    }
  };
  const handlePass = () => {
    if (selectedRows.length > 0) {
      setSuggestModal(true);
      setSuggestTitle('审核确认');
    } else {
      message.error('请选择数据操作');
    }
  };
  const handleReject = () => {
    if (selectedRows.length > 0) {
      setSuggestModal(true);
      setSuggestTitle('驳回确认');
      // const list = selectedRows.filter(ele => ele.approvalStatus === '8');
      // if (list.length > 0) {
      //   message.error('所选的数据存在已提交审核的数据了，请正确选择');
      //   return false;
      // }
      // const ids = selectedRows.map(ele => ele.timingUuid);
      // Modal.confirm({
      //   title: '提示',
      //   content: '确认驳回？',
      //   okText: '确认',
      //   cancelText: '取消',
      //   onOk: async () => {
      //     const res = await rejectTestList({ ids, code: 2 });
      //     if (res.success) {
      //       getList({ ...searchValues, pageNum: 1, pageSize: 10 });
      //       message.success('考核驳回操作成功');
      //     } else {
      //       message.error(res.errorMessage);
      //     }
      //   },
      // });
    } else {
      message.error('请选择数据操作');
    }
  };
  // 查询
  const getList = async data => {
    setLoading(true);
    setSelectedRows([]);
    setDatas({
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    });

    const res = await queryTestList(data).finally(() => setLoading(false));
    if (res.success) {
      const { list, total, pageNum, pageSize } = res.obj;
      setDatas({
        list: list || [],
        pagination: {
          pageSize,
          current: pageNum,
          total,
        },
      });
    }
  };

  return (
    <div className="table-list">
      <div className="tableListForm">
        <SearchForm
          handleSearch={handleSearch}
          handlePass={handlePass}
          handleReject={handleReject}
          selectedRows={selectedRows}
          showStep={handleShowStep}
          searchValues={searchValues}
          datas={datas}
        />
        <StandardTable
          size="small"
          data={datas}
          columns={columns}
          multiple
          loading={loading}
          selectedRows={selectedRows}
          onSelectRow={handleonSelectRow}
          // showSelection={false}
          rowKey="id"
          onChange={changePage}
        />
        <Viewer
          visible={picVisible}
          onClose={() => {
            setPicVisible(false);
          }}
          drag={false}
          images={picSrc}
        />
        {stepModal && (
          <ShowStep
            visible={stepModal}
            handleHide={handleHideStep}
            row={selectedRows[0]}
          />
        )}
        {suggestModal && (
          <Suggest
            visible={suggestModal}
            handleHide={handleHideSuggest}
            handleConfirm={handleConfirm}
            title={suggestTitle}
          />
        )}
      </div>
    </div>
  );
};
export default PageIndex;
