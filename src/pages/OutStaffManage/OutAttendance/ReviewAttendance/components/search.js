import React, { useEffect, useState } from 'react';
import { Form, DatePicker, Input, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import SuppliersSearch from 'src/components/SuppliersSearch';
import NetSearch from '@/components/NetSelect';
import AuthButton from '@/components/AuthButton';
import ExportButton from '@/components/ExportButton';
import AsyncExport from '@/components/AsyncExport';
import authDecorator from '@/components/AuthDecorator';
// import SupplierList from '@/components/SuppliersSelect';
// import styles from '../style.less';
const { Item } = Form;
const { Option } = Select;
const { RangePicker } = DatePicker;

const SearchForm = props => {
  const [form] = Form.useForm();
  const {
    userInfo,
    selectedRows,
    handleReject,
    handlePass,
    showStep,
    searchValues,
    datas,
    moduleCode,
    handleSearch,
  } = props;
  const [allocateList, setAllocateList] = useState([]); // 分拨区
  const [formatVal, setFormatVal] = useState({});
  const approvalStatus = [
    { value: 3, text: '审核中' },
    { value: 4, text: '审核通过' },
    { value: 6, text: '驳回' },
  ];
  const docType = [
    {
      value: 2,
      text: '补签卡',
    },
    {
      value: 3,
      text: '补工时',
    },
  ];
  const companyAttr = [
    {
      value: 'SF',
      text: '顺丰',
    },
    {
      value: 'SX',
      text: '顺心',
    },
  ];
  const onFinish = () => {
    form.validateFields().then(nameList => {
      if (nameList) {
        if (nameList.workDate) {
          nameList.timingTimeStart = nameList.workDate[0].format('x');
          nameList.timingTimeEnd = nameList.workDate[1].format('x');
        }
        delete nameList.workDate;
        handleSearch(nameList);
      }
    });
  };
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };
  const getValues = () => {
    const timingUuids = selectedRows.map(ele => ele.timingUuid);
    return { timingUuids };
  };
  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        sourceType: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
        zoneCode: userInfo.deptCode,
      });
    }
  }, [userInfo.deptCode]);

  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);
  useEffect(() => {
    const filterVal = {};
    for (const key in searchValues) {
      if (searchValues[key]) {
        filterVal[key] = searchValues[key];
      }
    }
    setFormatVal(filterVal);
  }, [searchValues]);
  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{}}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item name="sourceType" label="公司属性">
                {/* disabled={!isRoot} */}
                <Select>
                  {companyAttr.map(ele => (
                    <Option value={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  extraParam={{
                    typeLevels: ['2'],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                  }}
                  showSearch
                  getList={getList}
                  // disabled={!isRoot}
                  placeholder="请输入分拨区名称或代码"
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="zoneCode" label="中转场">
                <NetSearch
                  extraParam={{
                    excludeOverseasFlag: 1,
                  }}
                />
              </Item>
            </Col>
            <Item
              shouldUpdate={(prevValues, curValues) =>
                prevValues.zoneCode !== curValues.zoneCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <Item name="supplierName" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      isName
                      allowClear
                      gysParams={getFieldValue('zoneCode') || userInfo.deptCode}
                    />
                  </Item>
                </Col>
              )}
            </Item>
            <Col {...colStyle}>
              <Item name="userName" label="员工姓名">
                <Input placeholder="请输入" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="userNo" label="员工工号">
                <Input placeholder="请输入" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="workDate" label="考勤日期">
                <RangePicker
                  allowClear
                  format="YYYY-MM-DD"
                  style={{ width: '100%' }}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="documentType" label="单据类型">
                <Select placeholder="请选择" allowClear>
                  {docType.map(ele => (
                    <Option key={ele.value} value={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="approvalStatus" label="审核状态">
                <Select placeholder="请选择" allowClear>
                  {approvalStatus.map(ele => (
                    <Option key={ele.value} value={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>

      <div className="btn-con">
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="review_attendance_manage-submit"
          style={{ marginRight: 15 }}
          disabled={selectedRows.length < 1}
          onClick={() => handlePass()}
        >
          审核通过
        </AuthButton>
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="review_attendance_manage-recall"
          style={{ marginRight: 15 }}
          disabled={selectedRows.length < 1}
          onClick={() => handleReject()}
        >
          驳回
        </AuthButton>
        {/* <Button
            type="primary"
            style={{ marginRight: 15 }}
            disabled={selectedRows.length !== 1}
          >
            导出
          </Button> */}
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="review_attendance_manage-step"
          style={{ marginRight: 15 }}
          disabled={selectedRows.length !== 1}
          onClick={() => showStep()}
        >
          审核进度
        </AuthButton>
        <AsyncExport
          text="导出所选"
          modulecode={moduleCode}
          code="review_attendance_manage-export-some"
          disabled={selectedRows.length < 1}
          style={{ marginRight: 15 }}
          options={{
            filename: '考勤审核列表.xlsx',
            requstParams: [
              `/ospmAccountService/timingApprovalRest/export`,
              {
                method: 'POST',
                body: getValues,
              },
            ],
          }}
        />
        <AsyncExport
          text="导出全部"
          modulecode={moduleCode}
          code="review_attendance_manage-export-all"
          style={{ marginRight: 15 }}
          disabled={datas.list && datas.list.length < 1}
          options={{
            // total: totalNum,
            filename: '考勤审核列表.xlsx',
            requstParams: [
              `/ospmAccountService/timingApprovalRest/exportSync`,
              {
                method: 'POST',
                body: { ...formatVal },
              },
            ],
          }}
        />
        {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
      </div>
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
