import React, { useState } from 'react';
// import { Card } from 'antd';
import StandardTable from '@/components/StandardTable';
import { queryList } from './services/apis';
import SearchForm from './components/search';

const PageIndex = () => {
  const columns = [
    {
      title: '年月',
      dataIndex: 'accrueMonth',
      width: 150,
    },
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 150,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 150,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 150,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 150,
      ellipsis: true,
    },
    {
      title: '应出勤天数',
      dataIndex: 'requireDay',
      width: 150,
    },
    {
      title: '稳定人员出勤次数',
      dataIndex: 'attendanceNum',
      width: 200,
    },
    {
      title: '总出勤次数',
      dataIndex: 'normalAttendanceNum',
      width: 200,
    },
    {
      title: '人员稳定率（%）',
      dataIndex: 'personnelStabilityPec',
      width: 200,
    },
  ];
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });

  const [searchValues, setSearchValues] = useState({}); // 查询入参
  const [selectedRows, setSelectedRows] = useState([]); // 选中数据
  const [loading, setLoading] = useState(false);
  // 翻页
  const changePage = pages => {
    const { pageSize, current } = pages;
    const data = {
      ...searchValues,
      pageSize,
      pageNum: current,
    };
    getList(data);
  };
  // 查询
  const handleSearch = values => {
    const data = {
      ...values,
      pageSize: 10,
      pageNum: 1,
    };
    setSearchValues(values);
    getList(data);
  };

  // 选择
  const handleonSelectRow = row => {
    setSelectedRows(row);
  };

  // 查询
  const getList = async data => {
    setLoading(true);
    setSelectedRows([]);
    setDatas({
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    });

    const res = await queryList(data).finally(() => setLoading(false));
    if (res && res.success) {
      const { list, total, pageNum, pageSize } = res.obj;
      setDatas({
        list: list || [],
        pagination: {
          pageSize,
          current: pageNum,
          total,
        },
      });
    }
  };

  return (
    <div className="table-list">
      <div className="tableListForm">
        <SearchForm
          handleSearch={handleSearch}
          selectedRows={selectedRows}
          searchValues={searchValues}
          datas={datas}
        />
        <StandardTable
          size="small"
          data={datas}
          columns={columns}
          multiple
          loading={loading}
          selectedRows={selectedRows}
          onSelectRow={handleonSelectRow}
          showSelection={false}
          rowKey="id"
          onChange={changePage}
        />
      </div>
    </div>
  );
};
export default PageIndex;
