import React, { useEffect, useState } from 'react';
import { Form, DatePicker, Row, Col, Modal, Select } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
const { Option } = Select;

const { Item } = Form;
const layout = {
  labelCol: { md: 10, sm: 24, xs: 24 },
  wrapperCol: { md: 14, sm: 24, xs: 24 },
};
const ModalForm = props => {
  const [form] = Form.useForm();
  const {
    visible,
    title = '确定',
    handleHide,
    handleConfirm,
    userInfo,
  } = props;
  const [hide, setHide] = useState(false);
  const handleOk = () => {
    form.validateFields().then(namelist => {
      if (namelist.startDate) {
        namelist.startDate = namelist.startDate.startOf('day').format('x');
      }
      if (namelist.endDate) {
        namelist.endDate = namelist.endDate.endOf('day').format('x');
      }
      namelist.authorizedPerson = userInfo.empName;
      namelist.authorizedNo = userInfo.empNum;
      handleConfirm(namelist);
    });
  };
  const disabledDate = current => current && current < moment().endOf('day');
  useEffect(() => {
    if (visible) {
      setHide(true);
    }
  }, [visible]);
  return (
    <Modal
      width={600}
      visible={hide}
      title={title}
      onCancel={() => handleHide()}
      onOk={() => handleOk()}
    >
      <Form
        {...layout}
        form={form}
        initialValues={{
          startDate: moment(),
          authorizedFun: 1,
        }}
      >
        <Row gutter={{ md: 4, lg: 12, xl: 12 }}>
          <Col md={20} sm={24} xs={24}>
            <Item
              name="authorizedFun"
              label="授权的功能"
              rules={[{ required: true, message: '请选择' }]}
            >
              <Select>
                <Option value={1} key={1}>
                  确认考勤班次（默认)
                </Option>
              </Select>
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              label="生效时间"
              name="startDate"
              extra="（起始时间00:00:01）"
              rules={[{ required: true, message: '请选择' }]}
            >
              <DatePicker
                disabled
                style={{ width: '100%' }}
                format="YYYY-MM-DD"
              />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item
              label="失效时间"
              name="endDate"
              extra="（终止时间23:59:59）"
              rules={[{ required: true, message: '请选择' }]}
            >
              <DatePicker
                style={{ width: '100%' }}
                disabledDate={disabledDate}
                format="YYYY-MM-DD"
              />
            </Item>
          </Col>
          {/* <Col md={20} sm={24} xs={24}>
            <Item name="authorizedPerson" label="授权人">
              <Input />
            </Item>
          </Col>
          <Col md={20} sm={24} xs={24}>
            <Item name="authorizedNo" label="授权工号">
              <Input />
            </Item>
          </Col> */}
        </Row>
      </Form>
    </Modal>
  );
};

export default connect(state => ({
  userInfo: state.global.userInfo,
}))(ModalForm);
