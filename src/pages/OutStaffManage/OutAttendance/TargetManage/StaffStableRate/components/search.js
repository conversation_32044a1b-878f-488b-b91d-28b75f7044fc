import React, { useEffect, useState } from 'react';
import { Form, Button, DatePicker, Input, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import NetSearch from '@/components/NetSelect';
import AsyncExport from '@/components/AsyncExport';
import ExportButton from '@/components/ExportButton';
import authDecorator from '@/components/AuthDecorator';
// import styles from '../style.less';
const { Item } = Form;
const { Option } = Select;

const SearchForm = props => {
  const [form] = Form.useForm();
  const {
    userInfo,
    selectedRows,
    // handleConfirm,
    // handleCancel,
    datas,
    searchValues,
    moduleCode,
  } = props;
  const [allocateList, setAllocateList] = useState([]); // 分拨区
  const [formatVal, setFormatVal] = useState({});
  const companyAttr = [
    {
      value: 'SF',
      text: '顺丰',
    },
    {
      value: 'SX',
      text: '顺心',
    },
  ];
  const onFinish = () => {
    form.validateFields().then(nameList => {
      if (nameList) {
        if (nameList.accrueMonth) {
          nameList.accrueMonth = nameList.accrueMonth.format('YYYY-MM');
        }
        props.handleSearch(nameList);
      }
    });
  };
  const resetForm = () => {
    form.resetFields();
  };
  const getList = list => {
    setAllocateList(list);
  };
  const getValues = () => {
    const timingUuids = selectedRows.map(ele => ele.timingUuid);
    return { timingUuids };
  };

  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        sourceType: userInfo.orgCode === 'SX' ? 'SX' : 'SF',
        zoneCode: userInfo.deptCode,
      });
    }
  }, [userInfo.deptCode]);

  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    }
  }, [userInfo.areaCode, allocateList]);

  useEffect(() => {
    const filterVal = {};
    for (const key in searchValues) {
      if (searchValues[key]) {
        filterVal[key] = searchValues[key];
      }
    }
    setFormatVal(filterVal);
  }, [searchValues]);
  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        accrueMonth: moment(moment(new Date()).format('YYYY-MM')),
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <Item name="sourceType" label="公司属性">
                {/* disabled={!isRoot} */}
                <Select>
                  {companyAttr.map(ele => (
                    <Option value={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  extraParam={{
                    typeLevels: ['2'],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                  }}
                  showSearch={false}
                  getList={getList}
                  // disabled={!isRoot}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="zoneCode" label="中转场">
                <NetSearch
                  extraParam={{
                    excludeOverseasFlag: 1,
                  }}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="supplierCode" label="供应商">
                <Input placeholder="请输入供应商" />
              </Item>
            </Col>

            <Col {...colStyle}>
              <Item label="归属月份" name="accrueMonth">
                <DatePicker picker="month" style={{ width: '100%' }} />
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>

      <div className="btn-con">
        <ExportButton
          text="导出所选"
          moduleCode={moduleCode}
          code="confirm_attendance-export-some"
          disabled={selectedRows.length < 1}
          style={{ marginRight: 15 }}
          options={{
            filename: '考勤确认列表.xlsx',
            requstParams: [
              `/ospmAccountService/timingAttendanceDetailsRest/confirmExport`,
              {
                method: 'POST',
                body: getValues,
              },
            ],
          }}
        />
        <AsyncExport
          text="导出"
          moduleCode={moduleCode}
          // code="confirm_attendance-export-all"
          style={{ marginRight: 15 }}
          disabled={datas.list && datas.list.length < 1}
          options={{
            // total: totalNum,
            filename: '人员稳定率报表.xlsx',
            requstParams: [
              `/ospmAccountService/timingAttendanceDetailsRest/personnelStabilityReportExportSync`,
              {
                method: 'POST',
                body: { ...formatVal },
              },
            ],
          }}
        />

        {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
      </div>
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(SearchForm));
