.itemName {
  margin-bottom: 15px !important;
  label {
    display: inline-block !important;
    line-height: 32px;
    width: 100px;
  }
}
.btnCon {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 8px 6px 20px 0;
}
.filesCon {
  display: flex;
  align-items: center;
  padding-left: 10px;
  flex-wrap: wrap;
  li {
    width: 200px;
    height: 100px;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-direction: column;
  }
  li + li {
    margin-left: 15px;
  }
}
