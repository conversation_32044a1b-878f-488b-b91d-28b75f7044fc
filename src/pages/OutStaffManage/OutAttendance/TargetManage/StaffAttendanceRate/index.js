import React, { useState } from 'react';
import { Card } from 'antd';
import StandardTable from '@/components/StandardTable';
import { queryList } from './services/apis';
import SearchForm from './components/search';

const PageIndex = () => {
  const columns = [
    {
      title: '日期',
      dataIndex: 'accrueMonth',
      width: 150,
    },
    {
      title: '分拨区代码',
      dataIndex: 'allocationAreaCode',
      width: 150,
    },
    {
      title: '分拨区',
      dataIndex: 'allocationArea',
      width: 150,
    },
    {
      title: '中转场代码',
      dataIndex: 'zoneCode',
      width: 150,
    },
    {
      title: '中转场',
      dataIndex: 'zoneName',
      width: 150,
    },
    {
      title: '供应商编码',
      dataIndex: 'supplierCode',
      width: 150,
    },
    {
      title: '供应商名称',
      dataIndex: 'supplierName',
      width: 150,
    },
    {
      title: '业务类型',
      dataIndex: 'agreemenTypeName',
      width: 150,
    },
    {
      title: '排班人次',
      dataIndex: 'requireNum',
      width: 150,
    },
    {
      title: '正常打卡人次',
      dataIndex: 'normalClockNum',
      width: 150,
    },
    {
      title: '协助打卡人次',
      dataIndex: 'assistClockNum',
      width: 180,
    },
    {
      title: '缺卡人次',
      dataIndex: 'missingClockNum',
      width: 150,
    },
    {
      title: '迟到早退人次',
      dataIndex: 'lateEarlyNum',
      width: 150,
    },
    {
      title: '补签卡人次',
      dataIndex: 'pushSignatureNum',
      width: 150,
    },
    {
      title: '总出勤人次',
      dataIndex: 'attendanceNum',
      width: 150,
    },
    {
      title: '补签卡率（%）',
      dataIndex: 'pushSignaturePec',
      width: 150,
    },
    {
      title: '人员满足率（%）',
      dataIndex: 'personnelSatisfactionPec',
      width: 150,
    },
    {
      title: '出勤率（%）',
      dataIndex: 'attendancePec',
      width: 150,
    },
    {
      title: '协助打卡率（%）',
      dataIndex: 'assistPec',
      width: 150,
    },
  ];
  const [datas, setDatas] = useState({
    list: [],
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
  });

  const [searchValues, setSearchValues] = useState({}); // 查询入参
  const [selectedRows, setSelectedRows] = useState([]); // 选中数据
  const [loading, setLoading] = useState(false);
  // 翻页
  const changePage = pages => {
    const { pageSize, current } = pages;
    const data = {
      ...searchValues,
      pageSize,
      pageNum: current,
    };
    getList(data);
  };
  // 查询
  const handleSearch = values => {
    const data = {
      ...values,
      pageSize: 10,
      pageNum: 1,
    };
    setSearchValues(values);
    getList(data);
  };

  // 选择
  const handleonSelectRow = row => {
    setSelectedRows(row);
  };

  // 查询
  const getList = async data => {
    setLoading(true);
    setSelectedRows([]);
    setDatas({
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    });

    const res = await queryList(data).finally(() => setLoading(false));

    if (res && res.success) {
      const { list, total, pageNum, pageSize } = res.obj;
      setDatas({
        list: list || [],
        pagination: {
          pageSize,
          current: pageNum,
          total,
        },
      });
    }
  };

  return (
    <div className="table-list">
      <div className="tableListForm">
        <SearchForm
          handleSearch={handleSearch}
          selectedRows={selectedRows}
          searchValues={searchValues}
          datas={datas}
        />
        <StandardTable
          size="small"
          data={datas}
          columns={columns}
          multiple
          loading={loading}
          selectedRows={selectedRows}
          onSelectRow={handleonSelectRow}
          showSelection={false}
          rowKey="id"
          onChange={changePage}
        />
      </div>
    </div>
  );
};
export default PageIndex;
