/* eslint-disable no-empty */
/* eslint-disable no-unused-expressions */
/* eslint-disable indent */
import React, { Component, useState, Fragment } from 'react';
// import { connect } from 'dva';
import { Modal, message, Input } from 'antd';
import AuthButton from 'src/components/AuthButton';
import moment from 'moment';
import { EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import StandardTable from 'components/StandardTable';
import {
  querySupplierList,
  addSupplier,
  editSupplierList,
  // exportSupplierList,
} from 'src/services/supplierApi';
import { success, error } from 'src/utils/utils';
import authDecorator from 'src/components/AuthDecorator';
import { approvalList } from './servers/api';
import { statusList, operationList } from './Components/status';
import CreateAndEdit from './Components/createAndEdit';
import SearchForm from './Components/search';

// import ImportModal from './components/importModal'

// import history from '../../../history';
const { TextArea } = Input;

const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';
const ShowEye = props => {
  const { showPass } = props;
  const [show, setShow] = useState(false);
  const showPassNum = () => {
    setShow(!show);
    // console.log(props);
    showPass();
  };

  return (
    <div>
      <span style={{ paddingRight: 10 }}>联系电话</span>
      {show && <EyeOutlined onClick={() => showPassNum()} />}
      {!show && <EyeInvisibleOutlined onClick={() => showPassNum()} />}
    </div>
  );
};
let approvalWord = '';
@authDecorator
class UserManage extends Component {
  state = {
    orgCode: '',
    selectedRows: [],
    loading: false,
    datas: {
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    },
    searchValues: '',
    showModal: false, // 是否show弹窗
    title: '',
    editStatus: false,
    editObj: '',
    // isRoot: false,
    confirmLoading: false, // 弹窗确认loading
    siteInfo: '', // 网点携带的分拨区与大区
    isWord: false,
    showTable: true,
  };

  columns = [
    {
      title: '公司属性',
      dataIndex: 'srcCode',
      width: 100,
      render: text => (text === 'SX' ? '顺心' : '顺丰'),
    },
    {
      title: '省区',
      dataIndex: 'dictName',
      width: 150,
    },
    {
      title: '分拨区',
      dataIndex: 'provinceAreaCode',
      width: 120,
    },
    {
      title: '中转场代码',
      dataIndex: 'orgCode',
      width: 120,
    },
    {
      title: '中转场',
      dataIndex: 'orgName',
      width: 150,
    },
    {
      title: '是否直营场站',
      dataIndex: 'typeCode',
      width: 150,
    },
    {
      title: '供应商全称',
      dataIndex: 'companyName',
      width: 220,
    },
    {
      title: '供应商编码',
      dataIndex: 'companyCode',
      width: 150,
    },
    {
      title: '供应商简称',
      dataIndex: 'companyNameShort',
      width: 150,
    },
    {
      title: '联系人',
      dataIndex: 'contackPerson',
      width: 150,
    },
    {
      title: <ShowEye showPass={() => this.showPass()} />,
      dataIndex: 'phoneNumber',
      width: 150,
    },
    // {
    //   title: '地址',
    //   dataIndex: 'address',
    //   width: 150,
    // },
    {
      title: '账号状态',
      dataIndex: 'statusName',
      width: 100,
    },
    {
      title: '合同编码',
      dataIndex: 'agreemenNo',
      ellipsis: true,
      width: 150,
    },
    {
      title: '协议组编码',
      dataIndex: 'proTeamNo',
      ellipsis: true,
      width: 150,
    },
    {
      title: '合同类型',
      dataIndex: 'accountModelCodeName',
      width: 100,
    },
    {
      title: '模块',
      dataIndex: 'scol5Name',
      width: 100,
    },
    {
      title: '协议生效时间',
      dataIndex: 'startDate',
      width: 180,
      render: timeTrans,
    },
    {
      title: '协议失效时间',
      dataIndex: 'endDate',
      width: 180,
      render: timeTrans,
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '修改人',
      dataIndex: 'modifier',
      width: 100,
    },
    {
      title: '修改时间',
      dataIndex: 'modifyTime',
      width: 180,
      render: timeTrans,
    },
    {
      title: '审核人',
      dataIndex: 'approvalUserNo',
      width: 180,
    },
    {
      title: '审核时间',
      dataIndex: 'approvalTime',
      width: 180,
    },
    {
      title: '审核状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'approvalStatus',
      render: value => {
        const data = statusList.find(item => item.value === value);
        return data ? data.label : '';
      },
    },
    {
      title: '备注',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'approvalRemark',
    },
    {
      title: '操作',
      align: 'center',
      width: 160,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          {record.approvalStatus === 0 && record.srcCode === 'SX' && (
            <AuthButton
              size="small"
              // eslint-disable-next-line react/destructuring-assignment
              moduleCode={this.props.moduleCode}
              code="supplier_maintain-adopt"
              type="link"
              onClick={e => this.update(e, record, 1)}
            >
              审核通过
            </AuthButton>
          )}
          {record.approvalStatus === 0 && record.srcCode === 'SX' && (
            <AuthButton
              // eslint-disable-next-line react/destructuring-assignment
              moduleCode={this.props.moduleCode}
              code="supplier_maintain-reject"
              size="small"
              type="link"
              onClick={e => this.update(e, record, 2)}
            >
              驳回
            </AuthButton>
          )}
        </Fragment>
      ),
    },
  ];

  update = async (e, v, approvalStatus) => {
    e.stopPropagation();
    this.operationModal([v.id], approvalStatus);
  };

  operationModal = (idList, approvalStatus) => {
    Modal.confirm({
      title: '提示',
      content: (
        <div>
          <p>{operationList[approvalStatus]}</p>
          {approvalStatus === 2 && (
            <div style={{ display: 'flex' }}>
              <p>请反馈原因:</p>
              <TextArea
                showCount
                maxLength={200}
                onChange={e => {
                  approvalWord = e.target.value;
                }}
              />
            </div>
          )}
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const params = { idList, approvalStatus, approvalWord };
        const res = await approvalList(params);
        if (res.success) {
          const { searchValues } = this.state;
          success('操作成功');
          approvalWord = '';
          this.handleSearch(searchValues);
        } else {
          error(res.errorMessage);
        }
      },
      onCancel() {
        approvalWord = '';
      },
    });
  };

  // 加密与解密
  showPass = () => {
    const { isWord, searchValues, datas } = this.state;
    this.setState({ isWord: !isWord });
    const values = {
      ...searchValues,
      isContackPhone: !isWord,
    };
    if (datas.list.length > 0) {
      this.handleSearch(values, true);
    }
  };

  // 所属网点传code
  handleInit = (code, siteInfo) => {
    this.setState({ siteInfo });
  };

  // 获取默认的公司属性
  handleInitOrgCode = orgCode => {
    this.setState({ orgCode });
  };

  // 修改网点
  handleChangeSite = val => {
    this.setState({ siteInfo: val });
  };

  handleSearch = (values, isWord) => {
    this.setState({ searchValues: values });
    const { datas } = this.state;
    const { current, pageSize } = datas.pagination;
    if (!isWord) {
      this.setState(
        {
          isWord: false,
          showTable: false,
        },
        () => {
          this.setState({
            showTable: true,
          });
        },
      );
    }
    const data = {
      ...values,
      pageSize: isWord ? pageSize : 10,
      pageNum: isWord ? current : 1,
    };
    this.getlist(data);
  };

  // 新增
  handleCreate = () => {
    this.setState({
      showModal: true,
      title: '新增',
      editStatus: false,
      editObj: '',
      confirmLoading: false,
    });
  };

  // 修改
  handleEdit = () => {
    const { selectedRows } = this.state;
    if (selectedRows.length === 1) {
      this.setState({
        showModal: true,
        title: '修改',
        editStatus: true,
        confirmLoading: false,
        editObj: selectedRows[0],
      });
    } else if (selectedRows.length < 1) {
      message.error('请选择一条数据后，再修改');
    } else {
      message.error('仅能选择一条数据，在修改');
    }
  };

  // 关闭弹窗
  handleHide = () => {
    this.setState({
      showModal: false,
    });
  };

  // 确定修改或者新增
  handleConfirm = values => {
    const { editStatus, siteInfo } = this.state;
    if (editStatus) {
      const { selectedRows } = this.state;
      const { provinceAreaCode, orgName, orgCode, id } = selectedRows[0];
      values.provinceAreaCode =
        values.orgCode === orgCode ? provinceAreaCode : siteInfo?.hqCode;
      values.orgName =
        values.orgCode === orgCode ? orgName : siteInfo?.deptName;
      values.id = id;
      this.editSupplier({ ...values });
    } else {
      values.provinceAreaCode = siteInfo?.hqCode;
      values.orgName = siteInfo?.deptName;
      this.addUser({ ...values });
    }
  };

  // 选择数据
  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };

  // 导出
  handleExport = type => {
    const { searchValues, selectedRows } = this.state;
    let data;
    if (type === 'some') {
      if (selectedRows.length < 1) {
        Modal.error({
          content: '请选择数据',
        });
        return;
      }
      const idList = selectedRows.map(ele => ele.id);
      data = { idList, srcCode: searchValues.srcCode };
    } else {
      const { datas } = this.state;
      const { list } = datas;
      if (list.length < 1) {
        message.error(`无数据可导`);
        return;
      }
      data = { ...searchValues };
    }
    this.exportList(data, type);
  };

  downloadBlob = (blob_, fileName) => {
    const blobUrl = window.URL.createObjectURL(blob_);
    const a = document.createElement('a');
    a.href = blobUrl;
    a.target = '_blank';
    a.style.display = 'none';
    document.body.appendChild(a);
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(blobUrl);
    document.body.removeChild(a);
  };

  // 翻页
  handleStandardTableChange = pages => {
    const { pageSize, current } = pages;
    const { searchValues } = this.state;
    this.setState(
      {
        isWord: false,
        showTable: false,
        datas: {
          pagination: {
            current,
            pageSize,
          },
        },
      },
      () => {
        this.setState({
          showTable: true,
        });
        const data = {
          ...searchValues,
          pageSize,
          isContackPhone: false,
          pageNum: current,
        };

        this.getlist(data);
      },
    );
  };

  // 查询列表
  getlist = async data => {
    this.setState({ loading: true });
    const { pageNum, pageSize } = data;
    const res = await querySupplierList(data).finally(() =>
      this.setState({ loading: false }),
    );
    this.setState({
      datas: {
        list: [],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
        },
      },
      selectedRows: [],
    });
    if (res && res.success && res.obj) {
      const { list, total } = res.obj;
      if (list && list.length > 0) {
        list.forEach(ele => {
          if (ele.accountId && !ele.id) {
            ele.id = ele.accountId;
          }
        });
      }
      // console.log(list);
      this.setState({
        datas: {
          list,
          pagination: {
            current: pageNum,
            pageSize,
            total,
          },
        },
        selectedRows: [],
      });
    }
  };

  // 修改
  editSupplier = async data => {
    const { searchValues } = this.state;
    this.setState({
      confirmLoading: true,
    });

    const res = await editSupplierList(data).finally(() =>
      this.setState({
        confirmLoading: false,
      }),
    );

    if (res && res.success) {
      message.success('修改成功');
      this.handleHide();
      this.getlist({
        pageSize: 10,
        pageNum: 1,
        ...searchValues,
      });
    }
  };

  // 新增
  addUser = async data => {
    const { searchValues, siteInfo, orgCode } = this.state;
    this.setState({
      confirmLoading: true,
    });

    const res = await addSupplier(data).finally(() =>
      this.setState({
        confirmLoading: false,
      }),
    );

    if (res && res.success) {
      message.success('新增成功');
      this.handleHide();
      this.getlist({
        pageSize: 10,
        pageNum: 1,
        ...searchValues,
        orgCode: siteInfo?.deptCode,
        srcCode: orgCode,
      });
    }
  };

  // 导入
  handleImport = () => {
    const { searchValues, siteInfo, orgCode } = this.state;
    this.getlist({
      pageSize: 10,
      pageNum: 1,
      orgCode: siteInfo?.deptCode,
      srcCode: orgCode,
      ...searchValues,
    });
  };

  handleParams = () => {
    const { searchValues, isWord } = this.state;
    const data = {
      ...searchValues,
      isContackPhone: isWord,
    };
    return data;
  };

  handleSomeParams = () => {
    const { searchValues, isWord, selectedRows } = this.state;
    const { srcCode } = searchValues;
    const data = { srcCode, isContackPhone: isWord };
    if (srcCode === 'SX') {
      data.idList = selectedRows.map(ele => ele.id);
    } else {
      data.sfIdList = selectedRows.map(ele => ele.id);
    }
    return data;
  };

  render() {
    const {
      selectedRows,
      loading,
      datas,
      showModal,
      title,
      editStatus,
      editObj,
      confirmLoading,
      showTable,
      isWord,
    } = this.state;
    return (
      <div className="table-list">
        <div className="tableListForm">
          <SearchForm
            handleCreate={this.handleCreate}
            handleEdit={this.handleEdit}
            handleSearch={this.handleSearch}
            selectedRows={selectedRows}
            handleSomeParams={this.handleSomeParams}
            handleParams={this.handleParams}
            initCode={this.handleInit}
            datas={datas}
            isContackPhone={isWord}
            initOrgCode={this.handleInitOrgCode}
            handleImport={this.handleImport}
          />
          {showTable && (
            <StandardTable
              size="small"
              selectedRows={selectedRows}
              data={datas}
              columns={this.columns}
              multiple
              loading={loading}
              onSelectRow={this.handleSelectRows}
              rowKey="id"
              onChange={this.handleStandardTableChange}
            />
          )}

          {showModal && (
            <CreateAndEdit
              showModal={showModal}
              handleHide={this.handleHide}
              handleConfirm={this.handleConfirm}
              editStatus={editStatus}
              editObj={editObj}
              title={title}
              confirmLoading={confirmLoading}
              changeSite={this.handleChangeSite}
            />
          )}
        </div>
      </div>
    );
  }
}

export default UserManage;
