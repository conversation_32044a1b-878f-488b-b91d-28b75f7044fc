import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  DatePicker,
  Select,
  Modal,
  Button,
  Card,
  message,
} from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import moment from 'moment';
import DeptSearch from '@/components/DeptSearch';
// import { getDept } from '@/services/api';

import { checkUserRole } from '@/services/clock';
import { queryNameToCode } from '@/services/supplierApi';
import styles from '../../style.less';

const { Item } = Form;
const { Option } = Select;

const SearchForm = props => {
  const [form] = Form.useForm();
  const {
    userInfo,
    editStatus,
    editObj,
    showModal,
    handleHide,
    title,
    confirmLoading,
  } = props;
  // const [isRoot, setIsRoot] = useState(true);
  const [comProperty, setComProperty] = useState(true);
  const [timeId, setTimeId] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [showComs, setShowComs] = useState(false);
  const [showFirst, setShowFirst] = useState(false);
  const [focusName, setFocusName] = useState('');
  const agreementStatus = [
    {
      value: 1,
      text: '生效',
    },
    {
      value: 0,
      text: '失效',
    },
  ];
  const companyAttr = [
    {
      value: 'SX',
      text: '顺心',
    },
  ];

  const handleConfirm = () => {
    form.validateFields().then(nameList => {
      if (nameList) {
        if (nameList.endDate) {
          nameList.endDate = nameList.endDate.format('x');
        }
        if (nameList.startDate) {
          nameList.startDate = nameList.startDate.format('x');
        }
        if (nameList.phoneNumber) {
          const reg = /^1[3456789]\d{9}$/;
          if (nameList.phoneNumber.includes('***')) {
            if (props.editObj.phoneNumber === nameList.phoneNumber) {
              nameList.contackPhone = props.editObj.contackPhone;
            } else if (!reg.test(nameList.phoneNumber)) {
              message.error('请正确输入手机号码');
              return false;
            }
          } else {
            if (!reg.test(nameList.phoneNumber)) {
              message.error('请正确输入手机号码');
              return false;
            }
            nameList.contackPhone = nameList.phoneNumber;
          }
        } else {
          nameList.contackPhone = '';
        }
        delete nameList.phoneNumber;
        if (nameList.srcCode !== 'SX') {
          message.error(`当前只支持${props.title}顺心场地的供应商信息`);
          return false;
        }
        // console.log(nameList)
        props.handleConfirm(nameList);
      }
    });
    //
  };

  const getItem = list => {
    const siteCode = form.getFieldValue('orgCode');
    const site = list
      ? list
          .filter(ele => ele.value === siteCode)
          .map(({ areaCode, areaName, deptCode, deptName }) => ({
            areaCode,
            areaName,
            deptCode,
            deptName,
          }))[0]
      : '';
    if (site) {
      form.setFieldsValue({
        provinceAreaName: site.areaName,
      });
    }
    props.changeSite(site);
  };
  const getSrcVal = value => {
    // console.log(value);
    if (value === 'SF') {
      message.error(`当前只支持${props.title}顺心场地的供应商信息`);
      setComProperty(true);
    } else {
      setComProperty(false);
    }
  };
  const searchSupperlier = e => {
    const { value } = e.target;
    if (value.length >= 1) {
      const srcCode = form.getFieldValue('srcCode');
      clearTimeout(timeId);
      setShowFirst(true);
      setTimeId(
        setTimeout(() => {
          queryNameToCode({ companyName: value, srcCode }).then(res => {
            if (res.success) {
              // console.log(res);
              const list = res.obj.map(
                ({ companyName, companyNameShort, companyCode }) => ({
                  companyName,
                  companyNameShort,
                  companyCode,
                }),
              );
              // console.log(list);
              setCompanies(list);
              if (list.length > 0) {
                setShowComs(true);
              } else {
                setShowComs(false);
              }
            } else {
              message.error(res.errorMessage);
            }
          });
        }, 300),
      );
    }
  };
  // 选择供应商
  const selectItem = item => {
    // console.log(item);
    setShowComs(false);
    setShowFirst(false);
    form.setFieldsValue({
      companyName: item.companyName,
      companyNameShort: item.companyNameShort,
      companyCode: item.companyCode,
    });
  };
  // 失去焦点
  const handleBlur = e => {
    const str = e.target.value;
    setFocusName(str || focusName);
    form.setFieldsValue({
      companyName: e.target.value || focusName,
    });

    setTimeout(() => {
      setShowFirst(false);
      setShowComs(false);
      // if (!form.getFieldValue('companyName')) {
      //   form.setFieldsValue({
      //     companyNameShort: '',
      //     companyCode: '',
      //   });
      // }
    }, 200);
  };
  const handleFocus = () => {
    form.setFieldsValue({
      companyName: focusName,
    });
    setShowFirst(true);
    setShowComs(true);
  };
  // 角色权限
  // const checkRoot = async () => {
  //   const res = await checkUserRole();
  //   if (res && res.success) {
  //     setIsRoot(res.obj);
  //   }
  // };
  useEffect(() => {
    if (editStatus) {
      if (editObj.srcCode === 'SX') {
        setComProperty(false);
      } else {
        setComProperty(true);
      }
    } else if (userInfo && userInfo.deptCode) {
      setComProperty(false);
      form.setFieldsValue({
        orgCode: userInfo.deptCode,
        srcCode: 'SX',
      });
    }
  }, [userInfo.deptCode]);

  // useEffect(() => {
  //   checkRoot();
  // }, []);
  return (
    <Modal
      visible={showModal}
      onCancel={handleHide}
      title={title}
      width={850}
      footer={[
        <Button key="back" onClick={handleHide}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={confirmLoading}
          onClick={handleConfirm}
        >
          确定
        </Button>,
      ]}
    >
      <Form
        layout="inline"
        form={form}
        initialValues={{
          srcCode: editStatus
            ? editObj.srcCode || 'SF'
            : userInfo && userInfo.orgCode === 'SX'
            ? 'SX'
            : 'SF',
          orgCode: editStatus ? editObj.orgCode : userInfo && userInfo.deptCode,
          provinceAreaName: editStatus ? editObj.provinceAreaName : '',
          dictName: editStatus ? editObj.dictName : '',
          companyName: editStatus ? editObj.companyName : '',
          companyNameShort: editStatus ? editObj.companyNameShort : '',
          companyCode: editStatus ? editObj.companyCode : '',
          contackPerson: editStatus ? editObj.contackPerson : '',
          // contackPhone: props.editStatus ? props.editObj.contackPhone : '',
          phoneNumber: editStatus ? editObj.phoneNumber : '',
          address: editStatus ? editObj.address : '',
          startDate: editStatus ? moment(editObj.startDate) : '',
          endDate: editStatus ? moment(editObj.endDate) : '',
          status: editStatus ? editObj.status : '',
        }}
      >
        <Card
          title="网点信息"
          bordered={false}
          className={styles.cardContainer}
        >
          <div className={styles.cardContent}>
            <Item name="srcCode" label="公司归属" className={styles.itemName}>
              <Select
                style={{ width: 250 }}
                onChange={getSrcVal}
                disabled={editStatus}
              >
                {companyAttr.map(ele => (
                  <Option key={ele.value}>{ele.text}</Option>
                ))}
              </Select>
            </Item>
            <Item name="orgCode" label="中转场" className={styles.itemName}>
              <DeptSearch
                style={{ width: 250 }}
                getItem={getItem}
                onlySx={true}
                disabled={editStatus}
                allowClear={false}
              ></DeptSearch>
            </Item>
            {comProperty && (
              <Item
                name="provinceAreaName"
                label="分拨区"
                className={styles.itemName}
              >
                <Input
                  style={{ width: 250 }}
                  rules={[{ required: true }]}
                  disabled={editStatus}
                />
              </Item>
            )}
            {!comProperty && (
              <Item
                name="provinceAreaName"
                label="大区"
                className={styles.itemName}
              >
                <Input
                  style={{ width: 250 }}
                  rules={[{ required: true }]}
                  disabled={editStatus}
                />
              </Item>
            )}
            {!comProperty && (
              <Item name="dictName" label="省区" className={styles.itemName}>
                <Input
                  style={{ width: 250 }}
                  rules={[{ required: true }]}
                  disabled={editStatus}
                />
              </Item>
            )}
          </div>
        </Card>
        <Card
          title="供应商信息"
          bordered={false}
          className={styles.cardContainer}
        >
          <div className={styles.cardContent}>
            {!editStatus && showFirst && (
              <Item
                name="companyName"
                label="供应商全称"
                className={styles.itemName}
                rules={[{ required: true, message: '请输入' }]}
              >
                <Input
                  style={{ width: 250 }}
                  onChange={searchSupperlier}
                  disabled={editStatus}
                  onBlur={handleBlur}
                  suffix={
                    <DownOutlined size="small" style={{ color: '#ccc' }} />
                  }
                />
                {showComs && (
                  <ul className={styles.ulList}>
                    {companies.map(ele => (
                      <li
                        title={ele.companyName}
                        key={ele.companyCode}
                        onClick={() => selectItem(ele)}
                      >
                        {ele.companyName}
                      </li>
                    ))}
                  </ul>
                )}
              </Item>
            )}
            {!editStatus && !showFirst && (
              <Item
                name="companyName"
                label="供应商全称"
                className={styles.itemName}
                rules={[{ required: true, message: '请输入' }]}
              >
                <Input
                  style={{ width: 250 }}
                  onChange={searchSupperlier}
                  disabled={editStatus}
                  suffix={
                    <DownOutlined size="small" style={{ color: '#ccc' }} />
                  }
                  onFocus={handleFocus}
                />
              </Item>
            )}
            {editStatus && (
              <Item
                name="companyName"
                label="供应商全称"
                className={styles.itemName}
                rules={[{ required: true, message: '请输入' }]}
              >
                <Input style={{ width: 250 }} disabled={editStatus} />
              </Item>
            )}
            <Item
              name="companyNameShort"
              label="供应商简称"
              className={styles.itemName}
              rules={[{ required: true, message: '请输入' }]}
            >
              <Input style={{ width: 250 }} disabled={editStatus} />
            </Item>
            <Item
              name="companyCode"
              label="供应商编码"
              className={styles.itemName}
            >
              <Input style={{ width: 250 }} disabled={!comProperty} />
            </Item>
            <Item
              name="contackPerson"
              label="联系人"
              className={styles.itemName}
            >
              <Input style={{ width: 250 }} />
            </Item>
            <Item
              // name="contackPhone"
              name="phoneNumber"
              label="联系电话"
              className={styles.itemName}
              // rules={[
              //   {
              //     pattern: new RegExp(/^1[3456789]\d{9}$/, 'g'),
              //     message: '请输入正确的手机号码 !',
              //   },
              // ]}
            >
              <Input style={{ width: 250 }} maxLength={11} />
            </Item>
            {/* <Item name="address" label="地址" className={styles.itemName}>
              <Input style={{ width: 250 }} />
            </Item> */}
          </div>
        </Card>
        <Card
          title="合同信息"
          bordered={false}
          className={styles.cardContainer}
        >
          <div className={styles.cardContent}>
            <Item
              name="startDate"
              label="生效时间"
              className={styles.itemName}
              rules={[{ required: true }]}
            >
              <DatePicker style={{ width: 250 }} format="YYYY-MM-DD" />
            </Item>
            <Item
              name="endDate"
              label="失效时间"
              className={styles.itemName}
              rules={[{ required: true }]}
            >
              <DatePicker style={{ width: 250 }} format="YYYY-MM-DD" />
            </Item>
          </div>
        </Card>
      </Form>
    </Modal>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
}))(SearchForm);
