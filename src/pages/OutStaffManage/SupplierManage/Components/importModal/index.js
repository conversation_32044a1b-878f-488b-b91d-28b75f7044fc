/* eslint-disable indent */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable prettier/prettier */
import React from 'react';
import { message, Modal, Button, Upload } from 'antd';
import { CloudUploadOutlined, DownloadOutlined } from '@ant-design/icons';
// domnTemplate
import { domnTemplate } from '@/services/supplierApi';
import styles from '../../style.less';
const { Dragger } = Upload;

const ImportModal = props => {
  const domnLoadTemplate = async ()=>{
    const res = await domnTemplate()
    const blob = new Blob([res.blob],{ type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"})
    downloadBlob(blob,'供应商商模板')
  }
  const downloadBlob=(blob_, fileName)=> {
    const blobUrl = window.URL.createObjectURL(blob_);
    const a = document.createElement('a');
    a.href = blobUrl;
    a.target = '_blank';
    a.style.display = 'none';
    document.body.appendChild(a);
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(blobUrl);
    document.body.removeChild(a);
  }
  const importProps = {
    name: 'file',
    multiple: true,
    action: `/fopFpmCoeSupplier/supplierRest/import`,
    headers: {
      credentials: 'include',
      userId: sessionStorage.userid,
      'sgs-userid': sessionStorage.userid,
      systemKey: require('@/config').default.systemKey,
      'gw-bdus-rid': sessionStorage.roleId || '',
    },
    data: {
      operator: sessionStorage.getItem('username'),
    },
    onChange(info) {
      const { status } = info.file;
      if (status !== 'uploading') {
        // console.log(info.file, info.fileList);
      }
      if (status === 'done') {
        message.success(`${info.file.name} file uploaded successfully.`);
      } else if (status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
  };
  return (
    <Modal
      visible={props.visible}
      title="导入"
      width={700}
      footer={false}
      onCancel={props.hideModal}
    >
      <div>
        <div>
          <div style={{width:360,height:180,margin:'0 auto'}}>
            <Dragger {...importProps}>
              <CloudUploadOutlined  style={{fontSize:50,color:'#DC1E32'}}/>
              <div>将文件拖到此处，或 点击上传</div>
            </Dragger>
            <p className="draggerTip">只能上传excel文件</p>
          </div>
          <div className={styles.downBtn}>
            <Button icon={<DownloadOutlined />}  type="primary" size="large" onClick={domnLoadTemplate}>下载模板</Button>
          </div>
        <dl className={styles.inportTip}>
          <dt>导入注意事项：</dt>
          <dd>导入时请务必使用页面提供的模版进行导入，导入模版请点击模版下载按钮进行下载。</dd>
          <dd>在往模版填充数据时请勿改变模版中列的顺序，否则数据将导入错误。</dd>
          <dd>请勿改变模版中的格式，如模版中最后一列生效日期为日期格式，请勿更改成数据或者文本格式，否则数据将导入失败。</dd>
        </dl>
      </div>
      </div>
    </Modal>
  )
};

export default ImportModal;
