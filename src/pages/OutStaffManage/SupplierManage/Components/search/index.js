import React, { useEffect } from 'react';
import { Form, Input, Select, Row, Col, DatePicker } from 'antd';
import { connect } from 'dva';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import DeptSearch from 'src/components/DeptSearch';
import ImportButton from 'src/components/ImportButton';
import ExportButton from 'src/components/ExportButton';
import AuthButton from 'src/components/AuthButton';
import authDecorator from 'src/components/AuthDecorator';
import { checkUserRole } from 'src/services/clock';
import SuppliersSearch from 'src/components/SuppliersSearch';

// import { queryNameToCode } from '@/services/supplierApi';
// import styles from '../../style.less';

const { Item } = Form;
const { Option } = Select;
const { RangePicker } = DatePicker;

const SearchForm = props => {
  const [form] = Form.useForm();
  const {
    handleSearch,
    handleCreate,
    handleEdit,
    handleImport,
    initOrgCode,
    initCode,
  } = props;
  // const [isRoot, setIsRoot] = useState(true);
  const {
    userInfo,
    selectedRows,
    handleSomeParams,
    handleParams,
    datas,
    moduleCode,
    areaListSF,
    areaListSX,
  } = props;

  const companyAttr = [
    {
      value: 'SX',
      text: '顺心',
    },
  ];

  //  查询
  const onFinish = () => {
    form.validateFields().then(nameList => {
      if (nameList) {
        if (nameList.rangeDate) {
          nameList.startDate = nameList.rangeDate[0].startOf('day').format('x');
          nameList.endDate = nameList.rangeDate[1].endOf('day').format('x');
        }
        delete nameList.rangeDate;
        handleSearch(nameList);
      }
    });
  };

  // 新增
  // const handleCreate = () => {

  //   handleCreate();
  // };
  // 修改
  // const handleEdit = () => {
  //   props.handleEdit();
  // };

  //   重置
  const resetForm = () => {
    form.resetFields();
  };
  // 导入
  // const handleImport = () => {
  //   props.handleImport();
  // };

  // 公司切换
  const handleChange = type => {
    initOrgCode(type);
  };
  const getItem = list => {
    const siteCode = form.getFieldValue('orgCode');
    const site = list
      ? list
          .filter(ele => ele.value === siteCode)
          .map(({ areaCode, areaName, deptCode, deptName }) => ({
            areaCode,
            areaName,
            deptCode,
            deptName,
          }))[0]
      : '';
    initCode(siteCode, site);
  };

  useEffect(() => {
    if (userInfo.deptCode) {
      const type = userInfo.orgCode === 'SX' ? 'SX' : 'SF';
      initOrgCode(type);
      initCode(userInfo.deptCode);
      form.setFieldsValue({
        srcCode: 'SX',
        orgCode: userInfo.deptCode,
      });
    }
  }, [userInfo.deptCode]);

  // useEffect(() => {
  //   if (initVal) {
  //     searchSupperlier(initVal);
  //   }
  // }, [initVal]);
  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        srcCode: userInfo && userInfo.orgCode,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              {/* disabled={!isRoot} */}
              <Item name="srcCode" label="公司属性">
                <Select placeholder="请选择" onChange={handleChange}>
                  {companyAttr.map(ele => (
                    <Option value={ele.value} key={ele.value}>
                      {ele.text}
                    </Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item label="分拨区" name="provinceAreaCode">
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              {/* disabled={!isRoot} */}
              <Item name="orgCode" label="中转场">
                <DeptSearch getItem={getItem}></DeptSearch>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="dictName" label="省区">
                <Input placeholder="请输入省区" allowClear />
              </Item>
            </Col>
            <Item
              shouldUpdate={(prevValues, curValues) =>
                prevValues.orgCode !== curValues.orgCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <Item name="companyName" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      isName
                      allowClear
                      gysParams={getFieldValue('orgCode') || userInfo.deptCode}
                    />
                  </Item>
                </Col>
              )}
            </Item>
            <Col {...colStyle}>
              <Item
                name="rangeDate"
                label="合同日期"
                // labelCol={{ md: 4, sm: 24, xs: 24 }}
                // wrapperCol={{ md: 20, sm: 24, xs: 24 }}
              >
                <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </Item>
            </Col>
            {/* <Col {...colStyle}>
          <Item name="companyCode" label="供应商">
            <Select
              placeholder="请输入供应商名称"
              showSearch
              onSearch={searchSupperlier}
              defaultActiveFirstOption={false}
              filterOption={false}
              value={initVal}
              allowClear
            >
              {suppliers.map(item => (
                <Option value={item.companyCode} key={item.companyCode}>
                  <Tooltip placement="rightTop" title={item.label}>
                    {item.companyName}
                  </Tooltip>
                </Option>
              ))}
            </Select>
          </Item>
        </Col> */}

            <Col {...colStyle}>
              <Item name="status" label="账号状态">
                <Select>
                  <Option value="" key="">
                    全部
                  </Option>
                  <Option value={1} key={1}>
                    生效
                  </Option>
                  <Option value={0} key={0}>
                    失效
                  </Option>
                  <Option value={-1} key={-1}>
                    待生效
                  </Option>
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item label="审核状态" name="approvalStatus">
                <Select
                  allowClear
                  options={[
                    { label: '待审核', value: 0 },
                    { label: '审核通过', value: 1 },
                    { label: '已驳回', value: 2 },
                  ]}
                  placeholder="请选择审核状态"
                ></Select>
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="supplier_maintain-add"
          style={{ marginRight: 15 }}
          onClick={handleCreate}
        >
          新增
        </AuthButton>
        <AuthButton
          type="primary"
          moduleCode={moduleCode}
          code="supplier_maintain-edit"
          style={{ marginRight: 15 }}
          onClick={handleEdit}
        >
          修改
        </AuthButton>

        {/* <Button
            type="primary"
            style={{ marginRight: 15 }}
            onClick={() => exportList('some')}
            loading={props.btnLoading[0]}
          >
            导出所选
          </Button> */}
        <ExportButton
          text="导出所选"
          style={{ marginRight: 15 }}
          disabled={selectedRows.length < 1}
          moduleCode={moduleCode}
          code="supplier_maintain-export-some"
          options={{
            // total: totalNum,
            filename: '供应商列表.xlsx',
            requstParams: [
              `/tdmsAccrueService/supplierRest/export`,
              {
                method: 'POST',
                body: {
                  ...handleSomeParams(),
                },
              },
            ],
          }}
        />
        <ExportButton
          text="导出全部"
          style={{ marginRight: 15 }}
          disabled={datas.list && datas.list.length < 1}
          moduleCode={moduleCode}
          code="supplier_maintain-export-all"
          options={{
            // total: totalNum,
            filename: '供应商列表.xlsx',
            requstParams: [
              `/tdmsAccrueService/supplierRest/export`,
              {
                method: 'POST',
                body: {
                  ...handleParams(),
                },
              },
            ],
          }}
        />
        {/* <Button
            type="primary"
            loading={props.btnLoading[1]}
            style={{ marginRight: 15 }}
            onClick={() => exportList('all')}
          >
            导出全部
          </Button> */}

        <ImportButton
          title="供应商导入"
          action="/tdmsAccrueService/supplierRest/import"
          modalUrl="/tdmsAccrueService/supplierRest/import/download/tmp"
          modalName="供应商模板.xlsx"
          moduleCode={moduleCode}
          code="supplier_maintain-import-suppliers"
          handleSyncImport={handleImport}
          style={{ marginRight: 15 }}
        />
        <ImportButton
          title="合同信息导入"
          action="/ospmSupplierServices/supplierRest/importContract"
          modalUrl="/ospmSupplierServices/supplierRest/import/download/supplierContract"
          modalName="合同信息模板.xlsx"
          moduleCode={moduleCode}
          code="supplier_maintain-import-info"
          handleSyncImport={handleImport}
        />
      </div>
      {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
      {/* </div> */}
    </Form>
  );
};
export default connect(state => ({
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))(authDecorator(SearchForm));
