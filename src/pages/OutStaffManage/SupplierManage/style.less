.itemName {
  margin-bottom: 15px !important;
  label {
    display: inline-block !important;
    line-height: 32px;
    width: 100px;
  }
}
.btnCon {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 8px 6px 20px 0;
}
.outerName {
  margin-bottom: 15px !important;
  label {
    display: inline-block !important;
    line-height: 32px;
    width: 128px;
  }
}

.partTitle {
  font-size: 18px;
  display: flex;
  width: 100%;
  padding-left: 20px;
  // color: #DC1E32;
}
.cardContent {
  display: flex;
  flex-wrap: wrap;
}
.draggerTip {
  padding-top: 6px;
}
.downBtn {
  padding-top: 40px;
  text-align: center;
}
.inportTip {
  padding: 0 10px;
  dd {
    padding-left: 2em;
    position: relative;
    color: #606266;
    margin-bottom: 0;
  }
  dd:nth-child(2)::before {
    position: absolute;
    left: 0;
    content: '1';
  }
  dd:nth-child(3)::before {
    position: absolute;
    left: 0;
    content: '2';
  }
  dd:nth-child(4)::before {
    position: absolute;
    left: 0;
    content: '3';
  }
}
.ulList {
  width: 250px;
  padding: 0;
  max-height: 200px;
  overflow-y: scroll;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  li {
    height: 30px;
    text-overflow: ellipsis;
    line-height: 30px;
    text-align: left;
    padding-left: 11px;
    overflow: hidden;
    white-space: nowrap;
  }
  li:hover {
    background-color: #ddd;
  }
}
.cardContainer {
  // background: #f00;
  :global {
    .ant-card-head-title {
      font-weight: 600;
      font-size: 18px;
    }
  }
}
