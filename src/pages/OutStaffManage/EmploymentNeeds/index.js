import React, { Component } from 'react';
import { Modal } from 'antd';
import { connect } from 'dva';
import StandardTable from 'src/components/StandardTable';
// import Detail from './components/detail';
import request from 'src/utils/request';
import history from 'src/history';
import Search from './components/search';
import columns from './columns';

const Apis = {
  queryList: data =>
    request(`/ospmSupplierServices/supplierRequireInfoRest/query`, {
      method: 'POST',
      body: data,
    }),
  downloadSome: data =>
    request(`/ospmSupplierServices/supplierRequireInfoRest/export`, {
      method: 'POST',
      body: data,
      responseType: 'blob',
    }),
  downloadAll: data =>
    request(`/ospmSupplierServices/supplierRequireInfoRest/exportSync`, {
      method: 'POST',
      body: data,
    }),
};
@connect(state => ({
  state,
}))
class DailyAttendance extends Component {
  state = {
    selectedRows: [],
    loading: false,
    obj: {
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
    },
    btnLoading: false,
    searchValue: '',
    // detailModal: {
    //   visible: false,
    //   info: {},
    // },
  };

  columns = [
    ...columns,
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   width: 80,
    //   fixed: 'right',
    //   render: (text, row) => (
    //     <Button
    //       type="primary"
    //       size="small"
    //       onClick={e => this.showDetail(e, row)}
    //     >
    //       详情
    //     </Button>
    //   ),
    // },
  ];

  handleSearch = values => {
    // console.log(values);
    this.setState({
      searchValue: values,
    });
    const data = {
      ...values,
      pageNum: 1,
      pageSize: 10,
    };
    this.getList(data);
  };

  handleSelectRows = rows => {
    this.setState({
      selectedRows: rows,
    });
  };

  changePage = pages => {
    const { current, pageSize } = pages;
    const { searchValue } = this.state;
    this.setState({
      obj: {
        pagination: {
          pageSize,
          current,
        },
      },
    });
    const data = {
      ...searchValue,
      pageNum: current,
      pageSize,
    };
    this.getList(data);
  };

  // 异步导出
  exportAsync = async data => {
    const res = await Apis.downloadAll(data);
    if (res && res.success && res.obj && res.obj.taskId) {
      this.toExportTab();
    }
  };

  // 异步导出跳转
  toExportTab = () => {
    const { dispatch, state } = this.props;
    const { tabHistory } = state;
    // debugger;
    const path = '/download/list';
    const moduleCode = 'base_download';
    Modal.confirm({
      title: '提示',
      content: '是否跳转到下载列表查看导出详情',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        if (!tabHistory.routes.find(route => route.moduleCode === moduleCode)) {
          dispatch({
            type: 'tabHistory/custom',
            data: {
              // path,
              moduleName: `下载列表`,
              moduleCode,
              moduleIcon: 'home',
              // sort: 1,
            },
          });
        } else {
          dispatch({
            type: 'tabHistory/setActive',
            data: moduleCode,
          });
        }
        dispatch({
          type: 'global/updateListKey',
          data: 'export',
        });
        history.push(path);
      },
    });
  };

  downloadBlob = (blob_, fileName) => {
    const blobUrl = window.URL.createObjectURL(blob_);
    const a = document.createElement('a');
    a.href = blobUrl;
    a.target = '_blank';
    a.style.display = 'none';
    document.body.appendChild(a);
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(blobUrl);
    document.body.removeChild(a);
  };

  // 打卡列表查询
  getList = async data => {
    const { pageSize, pageNum } = data;
    this.setState({
      loading: true,
      obj: {
        list: [],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
        },
      },
      selectedRows: [],
    });

    const res = await Apis.queryList(data).finally(() =>
      this.setState({
        loading: false,
      }),
    );

    // console.log(res);
    if (res && res.success) {
      const { list = [], total } = res.obj;
      this.setState({
        obj: {
          list: list || [],
          pagination: {
            pageSize,
            current: pageNum,
            total,
          },
        },
        selectedRows: [],
      });
    }
  };

  // showDetail(e, row) {
  //   e.stopPropagation();
  //   this.setState({
  //     detailModal: {
  //       visible: true,
  //       info: row,
  //     },
  //   });
  // }

  // hideDetail() {
  //   this.setState({
  //     detailModal: {
  //       visible: false,
  //     },
  //   });
  // }

  render() {
    const {
      btnLoading,
      selectedRows,
      obj,
      loading,
      searchValue,
      // detailModal,
    } = this.state;
    return (
      <div className="table-list">
        <div className="tableListForm">
          <Search
            handleSearch={this.handleSearch}
            loading={btnLoading}
            selectedRows={selectedRows}
            datas={obj}
            searchValue={searchValue}
          />
          <StandardTable
            size="small"
            selectedRows={selectedRows}
            data={obj}
            columns={this.columns}
            multiple
            loading={loading}
            onSelectRow={this.handleSelectRows}
            rowKey="id"
            onChange={this.changePage}
          />
          {/* {detailModal.visible && (
            <Detail
              visible={detailModal.visible}
              info={detailModal.info}
              hideDetail={this.hideDetail.bind(this)}
            />
          )} */}
        </div>
      </div>
    );
  }
}
export default DailyAttendance;
