/* eslint-disable arrow-body-style */
import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Form, Input, DatePicker, Select, Row, Col } from 'antd';
import { connect } from 'dva';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import NetSearch from 'src/components/NetSelect';
import ExportButton from 'src/components/ExportButton';
import AsyncExport from 'src/components/AsyncExport';
import authDecorator from 'src/components/AuthDecorator';
// import styles from '../../style.less';

const { Item } = Form;
const { Option } = Select;

const { RangePicker } = DatePicker;
const SearchForm = props => {
  const { handleSearch } = props;
  const [form] = Form.useForm();
  const [allocateList, setAllocateList] = useState([]); // 分拨区
  const { userInfo, moduleCode, selectedRows, datas, searchValue } = props;
  const types = [
    { value: 1, text: '装车' },
    { value: 2, text: '卸车' },
    { value: 3, text: '叉车' },
    { value: 4, text: '分拣' },
    { value: 5, text: '其它' },
  ];
  const status = [
    { value: 1, text: '新订单' },
    { value: 2, text: '执行中' },
    { value: 3, text: '已完成' },
  ];
  //  查询
  const onFinish = values => {
    // console.log('Success:', values);
    if (values.startTime) {
      values.reqTime = values.startTime[0].startOf('day').format('x');
      values.reqTimeEnd = values.startTime[1].endOf('day').format('x');
    }
    delete values.startTime;
    // console.log(values);
    handleSearch(values);
  };

  //   重置
  const resetForm = () => {
    form.resetFields();
  };

  useEffect(() => {
    // console.log(userInfo, 'userInfo');
    if (userInfo && userInfo.orgCode) {
      form.setFieldsValue({
        orgId: userInfo.orgCode === 'SX' ? 1 : 0,
      });
    }
  }, []);

  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        deptCode: userInfo.deptCode,
        srcCode: userInfo.orgCode,
      });
    }
  }, [userInfo.deptCode]);
  const getList = list => {
    setAllocateList(list);
  };
  useEffect(() => {
    if (userInfo.areaCode) {
      const filter = allocateList.filter(
        ele => ele.value === userInfo.areaCode,
      );
      if (filter.length > 0) {
        form.setFieldsValue({
          allocationAreaCode: userInfo.areaCode,
        });
      }
    } else {
      // setAllocate(false);
    }
  }, [userInfo.areaCode, allocateList]);

  return (
    <Form
      {...formItemLayout}
      onFinish={onFinish}
      form={form}
      initialValues={{
        startTime: [
          moment()
            .subtract(1, 'day')
            .startOf('day'),
          moment().endOf('day'),
        ],
        srcCode: userInfo && userInfo.orgCode === 'SX' ? 'SX' : 'SF',
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              {/* disabled={!limit} */}
              <Item name="srcCode" label="公司属性">
                <Select>
                  <Option value="SF" key="SF">
                    顺丰
                  </Option>
                  <Option value="SX" key="SX">
                    顺心
                  </Option>
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="allocationAreaCode" label="分拨区">
                <NetSearch
                  placeholder="请输入分拨区名称或代码"
                  extraParam={{
                    typeLevels: ['2'],
                    hqCodes: ['CN36', 'CN01', 'CN02', 'CN03', 'CN08', 'HQOP'],
                  }}
                  showSearch
                  // disabled={!isRoot}
                  getList={getList}
                />
              </Item>
            </Col>
            <Col {...colStyle}>
              {/* disabled={!limit} */}
              <Item name="deptCode" label="中转场">
                <NetSearch placeholder="请选择" />
              </Item>
            </Col>

            <Col {...colStyle}>
              <Item name="jobType" label="工序">
                <Select placeholder="请选择" allowClear>
                  {types.map(ele => (
                    <Option key={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="supplierName" label="供应商">
                {/* <Select
              placeholder="请选择供应商"
              options={suppliers}
              allowClear
            ></Select> */}
                <Input placeholder="请输入" allowClear />
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item name="status" label="状态">
                <Select placeholder="请选择" allowClear>
                  {status.map(ele => (
                    <Option key={ele.value}>{ele.text}</Option>
                  ))}
                </Select>
              </Item>
            </Col>
            <Col {...colStyle}>
              <Item label="用工日期" name="startTime">
                <RangePicker format="YYYY-MM-DD" style={{ width: '100%' }} />
              </Item>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <ExportButton
          text="导出所选"
          style={{ marginRight: 15 }}
          moduleCode={moduleCode}
          code="needs_details-export-some"
          disabled={selectedRows.length < 1}
          options={{
            // total: totalNum,
            filename: '用工列表.xlsx',
            requstParams: [
              `/ospmSupplierServices/supplierRequireInfoRest/export`,
              {
                method: 'POST',
                body: {
                  idList: selectedRows.map(ele => ele.id),
                },
              },
            ],
          }}
        />
        <AsyncExport
          text="导出全部"
          disabled={datas.list && datas.list.length < 1}
          modulecode={moduleCode}
          code="needs_details-export-all"
          options={{
            // total: datas.pagination && datas.pagination.total,
            requstParams: [
              `/ospmSupplierServices/supplierRequireInfoRest/exportSync`,
              {
                method: 'POST',
                body: {
                  ...searchValue,
                },
              },
            ],
          }}
        />
      </div>
      {/* <div className={styles.right}>
          <Button type="primary" style={{ marginRight: 15 }} htmlType="submit">
            查询
          </Button>
          <Button style={{ marginRight: 15 }} onClick={resetForm}>
            重置
          </Button>
        </div> */}
      {/* </div> */}
    </Form>
  );
};
export default connect(state => {
  return {
    userInfo: state.global.userInfo,
  };
})(authDecorator(SearchForm));
// export default SearchForm;
