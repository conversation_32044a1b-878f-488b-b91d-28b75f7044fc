import React, { useEffect, useState, useRef } from 'react';
// component
import { Modal, Button, Form, Row, Col, Select, Input } from 'antd';
import { pick } from 'lodash';
import { connect } from 'dva';
import moment from 'moment';
import AsyncExport from '@/components/AsyncExport';
import StandardTable from '@/components/StandardTable';
import ExportButton from '@/components/ExportButton';
import authDecorator from '@/components/AuthDecorator';
import { queryDetail } from '../services/apis';
import { filterSearchParams } from '@/utils/utils';
import styles from '../../style.less';

const { Item } = Form;
const layout = {
  labelCol: { md: 6, sm: 24, xs: 24 },
  wrapperCol: { md: 14, sm: 24, xs: 24 },
};
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';

const Detail = props => {
  const { visible, info, hideDetail, moduleCode } = props;
  const [form] = Form.useForm();
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    list: [],
    pagination: false,
  });
  const status = { 0: '否', 1: '是' };

  useEffect(() => {
    if (visible) {
      queryList();
    }
  }, [visible]);

  const columns = [
    {
      title: '姓名',
      dataIndex: 'userName',
      width: 150,
    },
    {
      title: '工号',
      dataIndex: 'userNo',
      width: 150,
    },
    {
      title: '已报名',
      dataIndex: 'isSigned',
      width: 150,
      render: text => status[text],
    },
    {
      title: '已审核',
      dataIndex: 'isAudited',
      width: 150,
      render: text => status[text],
    },
    {
      title: '已到岗',
      dataIndex: 'isOnDuty',
      width: 150,
      render: text => status[text],
    },
    {
      title: '上班卡',
      dataIndex: 'startWorkTime',
      width: 150,
      render: text => timeTrans(text),
    },
    {
      title: '下班卡',
      dataIndex: 'endWorkTime',
      width: 150,
      render: text => timeTrans(text),
    },
  ];

  const handleOk = () => {
    console.log('ok');
  };

  const resetForm = () => {
    form.resetFields();
  };

  const onFinish = () => {
    queryList();
  };

  const handleSelect = items => {
    setSelectedRows(items);
  };

  const getParams = (type = 'search') => {
    let params = pick(info, [
      'allocationAreaCode',
      'deptCode',
      'reqTime',
      'beginTime',
      'endTime',
      'jobType',
      'supplierCode',
    ]);
    params = {
      ...params,
      ...form.getFieldsValue(['userName', 'userNo']),
      timingTime: params.reqTime,
      zoneCode: params.deptCode,
      operateType: params.jobType,
    };
    delete params.reqTime;
    delete params.deptCode;
    delete params.jobType;

    if (type == 'exportSelected') {
      params.idList = selectedRows.map(item => item.id);
    }

    if (type == 'exportAll') {
      params.idList = datas.list.map(item => item.id);
    }

    return filterSearchParams(params);
  };

  const queryList = async () => {
    const res = await queryDetail(getParams());
    if (res && res.success) {
      setDatas({
        list: res.obj,
        pagination: false,
      });
    }
  };

  return (
    <Modal
      title="详情信息"
      visible={visible}
      onOk={handleOk}
      onCancel={hideDetail}
      width={1000}
    >
      <Form {...layout} form={form} onFinish={onFinish}>
        <Row gutter={{ md: 4, lg: 12, xl: 12 }}>
          <Col md={8} sm={24} xs={24}>
            <Item name="userName" label="姓名">
              <Input />
            </Item>
          </Col>
          <Col md={8} sm={24} xs={24}>
            <Item name="userNo" label="工号">
              <Input />
            </Item>
          </Col>
        </Row>
        <div className={styles.flexShow}>
          <div>
            需求人数：<span className={styles.sign}>{info.requireNum}</span>
          </div>
          <div>
            报名人数：<span className={styles.sign}>{info.signUpNum}</span>
          </div>
          <div>
            通过人数：<span className={styles.sign}>{info.examineNum}</span>
          </div>
          <div>
            实到人数：<span className={styles.sign}>{info.areadyUpNum}</span>
          </div>
        </div>
        <div className={styles.btnCon}>
          <div>
            <ExportButton
              text="导出所选123"
              type="primary"
              moduleCode={moduleCode}
              // disabled={!selectedRows.length}
              code="needs_details-export-some"
              style={{ marginRight: 15 }}
              options={{
                filename: '外包用工需求详情明细导出.xls',
                requstParams: [
                  `/ospmClockServices/empattend/exportDetail`,
                  {
                    method: 'POST',
                    body: { ...getParams('exportSelected') },
                  },
                ],
              }}
            />
            <ExportButton
              text="导出全部"
              type="primary"
              // moduleCode={moduleCode}
              // code="needs_details-export-all"
              options={{
                filename: '外包用工需求详情明细导出.xls',
                requstParams: [
                  `/ospmClockServices/empattend/exportDetail`,
                  {
                    method: 'POST',
                    body: { ...getParams('exportAll') },
                  },
                ],
              }}
            />
          </div>
          <div>
            <Button
              type="primary"
              style={{ marginRight: 15 }}
              htmlType="submit"
            >
              查询
            </Button>
            <Button style={{ marginRight: 15 }} onClick={resetForm}>
              重置
            </Button>
          </div>
        </div>
      </Form>
      <StandardTable
        size="small"
        data={datas}
        columns={columns}
        loading={loading}
        selectedRows={selectedRows}
        onSelectRow={handleSelect}
        multiple
        rowKey="id"
        scroll={{ y: 200 }}
      />
    </Modal>
  );
};

export default connect(state => ({
  userInfo: state.global.userInfo,
}))(authDecorator(Detail));
