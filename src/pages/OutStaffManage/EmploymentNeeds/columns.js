import moment from 'moment';
const timeTrans = value =>
  value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '-';

const types = {
  1: '装车',
  2: '卸车',
  3: '叉车',
  4: '分拣',
  5: '其它',
};
const status = {
  1: '新订单',
  2: '执行中',
  3: '已完成',
};
export default [
  {
    title: '分拨区代码',
    dataIndex: 'allocationAreaCode',
    width: 100,
  },
  {
    title: '分拨区',
    dataIndex: 'allocationArea',
    width: 180,
  },
  {
    title: '中转场代码',
    dataIndex: 'deptCode',
    width: 150,
  },
  {
    title: '中转场',
    dataIndex: 'deptName',
    width: 150,
  },
  {
    title: '用工日期',
    dataIndex: 'reqTime',
    width: 180,
    render: text => (text ? moment(text).format('YYYY-MM-DD') : '-'),
    sorter: (a, b) => a.reqTime - b.reqTime,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    render: text => (text < 1 ? '新订单' : status[text]),
  },
  {
    title: '班次编码',
    dataIndex: 'shiftName',
    width: 180,
  },
  {
    title: '班次名称',
    dataIndex: 'shiftNo',
    width: 180,
  },
  {
    title: '班次开始时间',
    dataIndex: 'beginTime',
    width: 180,
    render: timeTrans,
  },
  {
    title: '班次结束时间',
    dataIndex: 'endTime',
    width: 180,
    render: timeTrans,
  },
  {
    title: '工序',
    dataIndex: 'jobType',
    width: 100,
    render: text => (text ? types[text] : '-'),
  },
  {
    title: '供应商编码',
    dataIndex: 'supplierCode',
    width: 180,
  },
  {
    title: '供应商名称',
    dataIndex: 'supplierName',
    width: 180,
  },
  {
    title: '需求人数',
    dataIndex: 'requireNum',
    width: 100,
  },
  {
    title: '报名人数',
    dataIndex: 'signUpNum',
    width: 100,
  },
  // {
  //   title: '通过人数',
  //   dataIndex: 'examineNum',
  //   width: 100,
  // },
  {
    title: '实到人数',
    dataIndex: 'areadyUpNum',
    width: 100,
  },
  {
    title: '订单执行人',
    dataIndex: 'orderExcCreator',
    width: 100,
  },
  {
    title: '订单执行时间',
    dataIndex: 'orderExcTime',
    width: 180,
    render: timeTrans,
  },
  {
    title: '申请人',
    dataIndex: 'creator',
    width: 100,
  },
  {
    title: '申请时间',
    dataIndex: 'createTime',
    width: 180,
    render: timeTrans,
  },
];
