import request from '@/utils/request';

// 查询
export function search(params) {
  return request(
    `/tdmsAccrueService/largeSingleWeightConfigRestService/page-query`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 新增
export function add(params) {
  return request(`/tdmsAccrueService/largeSingleWeightConfigRestService/add`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 修改
export function update(params) {
  return request(
    `/tdmsAccrueService/largeSingleWeightConfigRestService/modify`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}
