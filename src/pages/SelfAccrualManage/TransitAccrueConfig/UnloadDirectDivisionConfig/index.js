import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Select } from 'antd';
import { PlusOutlined, ExportOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  formItemLayout,
  colStyle,
  rowStyle,
  AuthButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import AsyncExport from 'src/components/AsyncExport';
import { search } from './servers/api';
import { timeTypeList } from './status';
import Add from './Components/Add';
const { Item: FormItem } = Form;

let inputParam = {};
const Page = ({ userInfo, areaListSF, areaListSX, logRoleCode }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'zoneCode',
    },
    {
      title: '网点名称',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'zoneName',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'areaCode',
      render: (v, record) => (
        <Fragment>
          <span>{record.areaCode}</span>
          <span style={{ marginLeft: '10px' }}>{record.areaName}</span>
        </Fragment>
      ),
    },
    // 👇 新增：配置维度
    {
      title: '配置维度',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'configDimension',
      render: v => {
        const dimensionMap = {
          0: '按重量归类',
          1: '按体积归类',
          2: '按月结卡号剔除',
        };
        return dimensionMap[v] || '';
      },
    },
    {
      title: '大单件重量标准（KG）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'largeSingeWeight',
      render: (text, record) => {
        if (record.configDimension === 0) {
          return `${record.largeSingeWeight || 0}`;
        }
        return '';
      },
    },
    // 👇 新增：大单件体积标准
    {
      title: '大单件体积标准（m³）',
      align: 'center',
      ellipsis: true,
      width: 200,
      render: (text, record) => {
        if (record.configDimension === 1) {
          return `${record.length || 0} × ${record.width || 0} × ${record.height || 0}`;
        }
        return '';
      },
    },
    // 👇 新增：白名单月结卡号
    {
      title: '白名单月结卡号',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'accountNo',
    },
    {
      title: '生效时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'validTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm') : ''),
    },
    {
      title: '失效时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'invalidTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm') : ''),
    },
    {
      title: '创建人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'createUserNo',
    },
    {
      title: '创建时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'createTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'modifyUserNo',
      render: text => text || '',
    },

    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '状态',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'status',
      render: v => timeTypeList[v]?.label || '',
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          <AuthButton
            modulecode={moduleCode}
            code="unloadDirectDivisionConfig-edit"
            size="small"
            type="link"
            onClick={e => update(e, record)}
            disabled={record.status === 2}
          >
            编辑
          </AuthButton>
        </Fragment>
      ),
    },
  ];

  const getQueryParams = () => {
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      condition: { ...inputParam },
      current: current,
      size: pageSize,
    };
    search(param)
      .then(res => {
        const { success, obj } = res;
        if (success) {
          const { list, total } = obj;
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.pageNum,
              pageSize: obj.pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 新增
  const add = () => {
    setAddModalVisible(true);
  };

  // 修改
  const update = async (e, row) => {
    e.stopPropagation();
    // row.type = type;
    // row.effectTime = moment(row.effectTime);
    // row.expireTime = moment(row.expireTime);
    setEditingTarget(row);
    setAddModalVisible(true);
  };

  useEffect(() => {
    if (userInfo && userInfo.deptCode && logRoleCode && logRoleCode.roleCode) {
      form.setFieldsValue({
        areaCode:
          logRoleCode.roleCode === '88888888888' ||
          logRoleCode.roleCode === 'tp00001'
            ? userInfo.areaCode
            : undefined,
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
      });
    }
    getQueryParams();
    refresh({ currentPage: 1 });
  }, [logRoleCode, userInfo]);

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={
        {
          // zoneCode: userInfo.deptCode,
        }
      }
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <DeptSearch
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  placeholder="请输入中转场名称或者代码"
                  addAllZoneCode={true}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="areaCode">
                <Select
                  disabled={
                    logRoleCode.roleCode === '88888888888' ||
                    logRoleCode.roleCode === 'tp00001'
                  }
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="配置维度" name="configDimension">
                <Select
                  allowClear
                  placeholder="请选择配置维度"
                  options={[
                    { value: 0, label: '按重量归类' },
                    { value: 1, label: '按体积归类' },
                    { value: 2, label: '按月结卡号剔除' },
                  ]}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="生效状态" name="status">
                <Select
                  allowClear
                  options={timeTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AuthButton
            modulecode={moduleCode}
            code="unloadDirectDivisionConfig-add"
            icon={<PlusOutlined />}
            style={{ marginRight: 15 }}
            type="primary"
            onClick={() => add()}
          >
            新增
          </AuthButton>
          <AsyncExport
            text="导出"
            modulecode={moduleCode}
            code="unloadDirectDivisionConfig-export"
            icon={<ExportOutlined />}
            style={{ marginRight: 15 }}
            disabled={datas.list && datas.list.length < 1}
            options={{
              filename: '大单件卸车直分配置.xlsx',
              requstParams: [
                `/tdmsAccrueService/largeSingleWeightConfigRestService/asyncExport`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        selectedRows={selectedRows}
        showSelection={false}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          areaListSF={areaListSF}
          initialValues={editingTarget}
          roleCode={logRoleCode.roleCode}
          userInfo={userInfo}
          visible={addModalVisible}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh({ currentPage: editingTarget ? undefined : 1 });
          }}
          width={850}
        />
      )}
    </div>
  );
};
@connect(state => ({
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
  logRoleCode: state.global.logRoleCode,
}))
class Container extends PureComponent {
  render() {
    const { userInfo, areaListSF, areaListSX, logRoleCode } = this.props;
    return (
      <Page
        userInfo={userInfo}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
        logRoleCode={logRoleCode}
      />
    );
  }
}

export default withRouter(Container);
