import React, { useState, useEffect, useMemo } from 'react';
import { Form, Modal, Col, Row, Button, DatePicker, Input, Select, InputNumber } from 'antd';
import moment from 'moment';
import { success, error } from 'src/utils/utils';
import DeptSearch from 'src/components/DeptSearch';
import { add, update } from '../../servers/api';

const { Item: FormItem } = Form;
const { Option } = Select;
const Add = props => {
  const {
    onOk,
    visible,
    initialValues,
    areaListSF,
    userInfo,
    roleCode,
    ...rest
  } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 表单默认值
  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
  };

  // 提交时请求
  const submit = async () => {
    const param = await form.validateFields();
    param.configDimension = Number(param.configDimension)
    param.validTime = moment(
      `${moment(param.validTime).format('YYYY-MM-DD HH')}:00`,
    ).valueOf();
    param.invalidTime = moment(
      `${moment(param.invalidTime).format('YYYY-MM-DD HH')}:00`,
    ).valueOf();

    if (initialValues?.id && initialValues?.status === 1) {
      // 校验一下失效时间-修改-已生效
      const invalidTimeIsSixHoursLater = moment(param.invalidTime).isAfter(
        moment().add(6, 'hours'),
      );
      if (!invalidTimeIsSixHoursLater) {
        error('失效时间只能选择当前时间的6小时以后的时间');
        return;
      }
    } else {
      // 校验一下生效和失效时间-新增和修改-未生效
      const validTimeIsSixHoursLater = moment(param.validTime).isAfter(
        moment().add(6, 'hours'),
      );
      if (!validTimeIsSixHoursLater) {
        error('生效时间只能选择当前时间的6小时以后的时间');
        return;
      }
      const validTimeIsValid = moment(param.validTime).isBefore(
        moment(param.invalidTime),
      );
      if (!validTimeIsValid) {
        error('失效时间需大于生效时间');
        return;
      }
    }

    setLoading(true);
    try {
      const res = initialValues
        ? await update({ ...param, id: initialValues.id })
        : await add(param);
      if (res.success) {
        setLoading(false);
        success(`${initialValues ? '修改' : '新增'}成功！`);
        onOk();
      } else {
        setLoading(false);
        error(res.errorMessage);
      }
    } catch {
      setLoading(false);
    }
  };
  const handleCancel = () => {
    form.resetFields();
    setZoneCode('');
    setConfigDimension('');
  };

  const disabledDate = current => {
    return current && current < moment().startOf('day');
  };

  // const range = (start, end) => {
  //   const result = [];
  //   for (let i = start; i < end; i++) {
  //     result.push(i);
  //   }
  //   return result;
  // };

  const disabledDateTime = data => {
    // 禁用当前时间往后6小时范围内的时间
    const now = moment();
    const sixHoursLater = moment().add(6, 'hours');
    const selectedDate = moment(data);
    
    // 如果选择的是今天
    if (selectedDate.isSame(now, 'day')) {
      return {
        disabledHours: () => {
          const disabledHours = [];
          
          // 禁用从0点到6小时后的小时范围
          for (let i = 0; i <= sixHoursLater.hour(); i++) {
            disabledHours.push(i);
          }
          
          return disabledHours;
        },
      };
    }
    
    // 如果选择的是明天，且6小时后跨到了明天
    if (selectedDate.isSame(moment().add(1, 'day'), 'day') && sixHoursLater.isAfter(now.clone().endOf('day'))) {
      return {
        disabledHours: () => {
          const disabledHours = [];
          
          // 禁用从0点到6小时后在明天的小时数
          for (let i = 0; i <= sixHoursLater.hour(); i++) {
            disabledHours.push(i);
          }
          
          return disabledHours;
        },
      };
    }
    
    // 其他情况返回空，表示不禁用任何时间
    return {};
  };

  useEffect(() => {
    if (initialValues) {
      setZoneCode(initialValues.zoneCode)
      setConfigDimension(`${initialValues.configDimension}`)
      form.setFieldsValue({
        zoneCode: initialValues.zoneCode,
        configDimension: `${initialValues.configDimension}`,
        length: initialValues.length,
        width: initialValues.width,
        height: initialValues.height,
        accountNo: initialValues.accountNo,
        validTime: initialValues.validTime
          ? moment(initialValues.validTime)
          : undefined,
        invalidTime: initialValues.invalidTime
          ? moment(initialValues.invalidTime)
          : undefined,
        largeSingeWeight: initialValues.largeSingeWeight,
      });
    } else if (roleCode === 'tp00001') {
      setZoneCode(userInfo.deptCode);
      form.setFieldsValue({ zoneCode: userInfo.deptCode });
    }
  }, [visible, initialValues]);

  const [zoneCode, setZoneCode] = useState('');
  const handleDeptChange = (value) => {
    setZoneCode(value);
    form.setFieldsValue({ 
      configDimension: undefined,
      largeSingeWeight: undefined,
      length: undefined,
      width: undefined,
      height: undefined,
      accountNo: undefined 
    });
    setConfigDimension('');
  };

  const [configDimension, setConfigDimension] = useState('');
  const configDimensionArr = useMemo(() => {
    return [{
      text:"按重量归类",
      value: '0',
      disable: zoneCode && zoneCode === "001",
      remark: "大于配置重量后判断为大单件",
      fields: ['largeSingeWeight'],
    },{
      text:"按体积归类",
      value: '1',
      disable: zoneCode && zoneCode !== "001",
      remark: "均大于配置体积后判断为大单件",
      fields: ['length', 'width', 'height'],
    },{
      text:"按月结卡号剔除",
      value: '2',
      disable: zoneCode && zoneCode !== "001",
      remark: "命中配置白名单月结卡号后不判断为大单件",
      fields: ['accountNo'],
    }]
  }, [zoneCode]);

  const handleDimensionChange = (value) => {
    setConfigDimension(value);
    form.setFieldsValue({ configDimension: value });
    // 清空其他字段值
    if (value !== '0') {
      form.setFieldsValue({ largeSingeWeight: undefined });
    }
    if (value !== '1') {
      form.setFieldsValue({ length: undefined, width: undefined, height: undefined });
    }
    if (value !== '2') {
      form.setFieldsValue({ accountNo: undefined });
    }
  };

  const ConfigDimensionSelect = (props) => (
    <div>
      <Select
        {...props}
        placeholder="请选择配置维度"
        key={zoneCode}
        onChange={handleDimensionChange}
        allowClear
        disabled={initialValues?.id}
      >
        {configDimensionArr.map(ele => (
          <Option key={ele.value} disabled={ele.disable}>{ele.text}</Option>
        ))}
      </Select>
      {configDimension && (
        <p style={{ color: '#DC1E32', fontSize: '12px', marginBottom: 0 }}>{configDimensionArr.find(i => i.value === configDimension)?.remark}</p>
      )}
    </div>
  );
  
  const VolumeNumber = (props) => (
    <div style={{ display: 'flex' }}>
      <InputNumber
        {...props}
        placeholder="请输入"
        min={0}
        max={1.8}
        step={0.01}
        precision={2}
        formatter={value => `${value}`.replace(/^(\d+)(\.\d{0,2})?$/, '$1$2')}
        parser={value => value.replace(/[^\d.]/g, '')}
        style={{ width: '100%', marginRight: '5px' }}
        suffix="m"
        disabled={initialValues?.id}
      />
       m 
    </div>
  );

  return (
    <Modal
      title={initialValues ? '编辑' : '新增'}
      maskClosable={false}
      onCancel={handleCancel}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={[
        <Button key="back" onClick={rest.onCancel}>
          取消
        </Button>,
        <Button key="submit" loading={loading} type="primary" onClick={submit}>
          确定
        </Button>,
      ]}
    >
      <Form {...formProps}>
        <Row>
          <Col md={18}>
            <FormItem
              label="网点"
              name="zoneCode"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DeptSearch
                disabled={initialValues?.id || roleCode === 'tp00001'}
                placeholder="请输入中转场名称或者代码"
                addAllZoneCode={true}
                allowClear
                onChange={handleDeptChange}
              />
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem
              label="配置维度"
              name="configDimension"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <ConfigDimensionSelect />
            </FormItem>
          </Col>
          {configDimension === '0' && <Col md={18}>
            <FormItem
              label="大单件重量标准"
              name="largeSingeWeight"
              rules={[
                {
                  required: true,
                  message: '请输入',
                },
                {
                  pattern: /^(?:[1-9]\d{0,2}|0)$/,
                  message: '请输入0-999整数',
                },
              ]}
            >
              <Input
                placeholder="请输入"
                maxLength={3}
                allowClear
                style={{ width: '100%' }}
                addonAfter="KG"
                disabled={initialValues?.id}
              />
            </FormItem>
          </Col>}
          {configDimension === '1' && (
            <>
              <Col md={18}>
                <FormItem label="大单件体积标准 - 长" name="length" rules={[{ required: true, message: '请输入' }]}>
                  <VolumeNumber />
                </FormItem>
              </Col>
              <Col md={18}>
                <FormItem label="大单件体积标准 - 宽" name="width" rules={[{ required: true, message: '请输入' }]}>
                  <VolumeNumber />
                </FormItem>
              </Col>
              <Col md={18}>
                <FormItem label="大单件体积标准 - 高" name="height" rules={[{ required: true, message: '请输入' }]}>
                  <VolumeNumber />
                </FormItem>
              </Col>
            </>
          )}
          {configDimension === '2' && (
            <Col md={18}>
              <FormItem label="白名单月结卡号" name="accountNo" rules={[{ required: true, message: '请输入' }]}>
                <Input placeholder="请输入月结卡号" disabled={initialValues?.id}/>
              </FormItem>
            </Col>
          )}
          <Col md={18}>
            <FormItem
              label="生效时间"
              name="validTime"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DatePicker
                disabled={initialValues && initialValues.status === 1}
                disabledDate={disabledDate}
                disabledTime={disabledDateTime}
                showTime={{ format: 'HH' }}
                format="YYYY-MM-DD HH"
                showNow={false}
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem
              label="失效时间"
              name="invalidTime"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DatePicker
                disabledDate={disabledDate}
                disabledTime={disabledDateTime}
                showTime={{ format: 'HH' }}
                format="YYYY-MM-DD HH"
                showNow={false}
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default Add;
