import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Select } from 'antd';
import {
  // SearchOutlined,
  PlusOutlined,
  // RollbackOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  AuthButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import AsyncExport from 'src/components/AsyncExport';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询

import { search } from './servers/api';
import Add, { ratioOptions, ratioOptionsObj } from './Components/Add';
import './index.scss';
const { Item: FormItem } = Form;

let inputParam = {};
const Page = ({ userInfo, logRoleCode, areaListSF, areaListSX }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  const [selectedRows, setSelectedRows] = useState([]);
  // const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '归属组织',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'sourceType',
    },
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'zoneCode',
    },
    {
      title: '网点名称',
      align: 'center',
      ellipsis: true,
      // width: 120,
      dataIndex: 'zoneName',
    },
    {
      title: '分拨区代码',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'allocationAreaCode',
    },
    {
      title: '分拨区名称',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'allocationArea',
    },
    {
      title: '系数类型',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'coefficientType',
      render(v) {
        return ratioOptionsObj[v];
      },
    },
    {
      title: '折算系数',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'coefficient',
    },
    {
      title: '生效状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'effectiveStatus',
    },
    {
      title: '生效时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'startTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '失效时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'endTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '创建人',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'creator',
    },
    {
      title: '创建时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'createTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'modifier',
      render: text => text || '',
    },

    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '操作',
      align: 'center',
      // width: 100,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          <AuthButton
            modulecode={moduleCode}
            code="cargoVolumeConfig-edit"
            size="small"
            type="link"
            onClick={e => update(e, record)}
          >
            编辑
          </AuthButton>
        </Fragment>
      ),
    },
  ];

  const getQueryParams = () => {
    // const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);

    if (inputParam.time && inputParam.time.length > 0) {
      inputParam.startTime = moment(inputParam.time[0]).format('x');
      inputParam.endTime = moment(inputParam.time[1]).format('x');
    }
    delete inputParam.time;
    // refresh({ pageNum, pageSize });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      ...inputParam,
      pageNum: current,
      pageSize,
    };
    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum, total } = obj;
          // setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: pageNum,
              pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 新增
  const add = () => {
    setAddModalVisible(true);
  };

  // 修改
  const update = async (e, row, type) => {
    e.stopPropagation();
    row.type = type;
    row.effectTime = moment(row.effectTime);
    row.expireTime = moment(row.expireTime);
    setEditingTarget(row);
    setAddModalVisible(true);
  };

  useEffect(() => {
    form.setFieldsValue({
      zoneCode:
        logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
    });
    getQueryParams();
    refresh({ currentPage: 1 });
  }, [userInfo, logRoleCode]);

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={
        {
          // zoneCode: userInfo.deptCode,
        }
      }
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="网点"
                name="zoneCode"
                // rules={[
                //   {
                //     required: true,
                //     message: '请选择',
                //   },
                // ]}
              >
                <DeptSearch
                  // disabled={logRoleCode.roleCode === 'tp00001'}
                  placeholder="请输入中转场名称或者代码"
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="生效状态" name="effectiveStatus">
                <Select
                  allowClear
                  options={[
                    { label: '待生效', value: '待生效' },
                    { label: '已生效', value: '已生效' },
                    { label: '已过期', value: '已过期' },
                  ]}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="系数类型" name="coefficientType">
                <Select
                  allowClear
                  options={ratioOptions}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AuthButton
            modulecode={moduleCode}
            code="cargoVolumeConfig-add"
            icon={<PlusOutlined />}
            style={{ marginRight: 15 }}
            type="primary"
            onClick={() => add()}
          >
            新增
          </AuthButton>
          <AsyncExport
            text="导出"
            modulecode={moduleCode}
            code="cargoVolumeConfig-export"
            icon={<ExportOutlined />}
            style={{ marginRight: 15 }}
            disabled={datas.list && datas.list.length < 1}
            options={{
              filename: '折算系数.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueCoefficientRestService/exportSync`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        selectedRows={selectedRows}
        showSelection={false}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          initialValues={editingTarget}
          roleCode={logRoleCode.roleCode}
          userInfo={userInfo}
          visible={addModalVisible}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh({ currentPage: editingTarget ? undefined : 1 });
          }}
          width={850}
        />
      )}
    </div>
  );
};
@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const { logRoleCode, userInfo, areaListSF, areaListSX } = this.props;
    return (
      <Page
        logRoleCode={logRoleCode}
        userInfo={userInfo}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
      />
    );
  }
}

export default withRouter(Container);
