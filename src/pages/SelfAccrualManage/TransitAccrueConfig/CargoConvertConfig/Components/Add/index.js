import React, { useState, useEffect } from 'react';
import { Form, Modal, Col, Row, Input, Button, DatePicker, Select } from 'antd';
import moment from 'moment';
import { success, error } from 'src/utils/utils';
import DeptSearch from 'src/components/DeptSearch';
import { add, update } from '../../servers/api';
// import { operateTypeList } from '../status';

/** 系数类型 */
export const ratioOptions = [
  { label: '线路折算系数', value: 0 },
  { label: 'L0外包装车折算系数', value: 1 },
  { label: 'L0外包卸车折算系数', value: 2 },
  { label: '标快零担卸车折算系数', value: 3 },
  { label: '标快零担装车折算系数', value: 4 },
  { label: '陆运港到港卸车折算系数', value: 5 },
  { label: '陆运港到港装车折算系数', value: 6 },
];

/** 系数类型对象 */
export const ratioOptionsObj = ratioOptions.reduce((obj, cur) => {
  obj[cur.value] = cur.label;
  return obj;
}, {});

const { Item: FormItem } = Form;
const Add = props => {
  const {
    onOk,
    visible,
    initialValues,
    // areaListSF,
    // areaListSX,
    userInfo,
    roleCode,
    ...rest
  } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  // 表单默认值
  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
    // initialValues,
  };

  // 提交时请求
  const submit = async () => {
    const param = await form.validateFields();
    setLoading(true);
    param.startTime = moment(param.startTime).format('x');
    param.endTime = moment(param.endTime).format('x');
    setLoading(false);
    const res = initialValues
      ? await update({ ...param, id: initialValues.id })
      : await add(param);
    if (res.success) {
      success(`${initialValues ? '修改' : '新增'}成功！`);
      onOk();
    } else {
      error(res.errorMessage);
    }
  };
  const handleCancel = () => {
    form.resetFields();
  };

  //   不包含0： \b0(.\d{1,2})\b
  // 包含0： \b0(.\d{1,2})?\b
  // 包含0和1： \b(0(.\d{1,2})?)|1\b
  // /^(1|0(\.\d{1,2})?)$/
  // ^[0|1](\.[0-9]{1,2}){0,1}$

  const price = [
    {
      // ^[0|1](\.[0-9]{1,2}){0,1}$
      pattern: /^(1|0(\.[1-9]{0,2})?)$/,
      message: '请输入0-1之间的数字，最多保留两位小数',
    },
  ];

  useEffect(() => {
    if (!initialValues && roleCode === 'tp00001') {
      form.setFieldsValue({ zoneCode: userInfo.deptCode });
    }
    if (initialValues) {
      form.setFieldsValue({
        zoneCode: initialValues.zoneCode,
        coefficient: initialValues.coefficient,
        startTime: initialValues.startTime
          ? moment(initialValues.startTime)
          : undefined,
        endTime: initialValues.endTime
          ? moment(initialValues.endTime)
          : undefined,
        coefficientType: initialValues.coefficientType,
      });
    }
  }, [visible]);

  return (
    <Modal
      title={initialValues ? '编辑' : '新增'}
      maskClosable={false}
      onCancel={handleCancel}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={[
        <Button key="back" onClick={rest.onCancel}>
          取消
        </Button>,
        <Button key="submit" loading={loading} type="primary" onClick={submit}>
          确定
        </Button>,
      ]}
    >
      <Form {...formProps}>
        <Row>
          <Col md={18} sm={24}>
            <FormItem
              label="网点"
              name="zoneCode"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DeptSearch
                disabled={!!initialValues}
                placeholder="请输入中转场名称或者代码"
                allowClear
              />
            </FormItem>
          </Col>
          {/* <Col md={18} sm={24}>
            <FormItem label="分拨区" name="allocationAreaCode">
              <Select
                allowClear
                options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                placeholder="请选择"
              ></Select>
            </FormItem>
          </Col> */}
          <Col md={18} sm={24}>
            <FormItem
              label="系数类型"
              name="coefficientType"
              rules={[{ required: true, message: '请选择系数类型' }]}
            >
              <Select
                allowClear
                options={ratioOptions}
                placeholder="请选择"
                disabled={!!initialValues}
              />
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem label="折算系数" name="coefficient" rules={[...price]}>
              <Input placeholder="请输入折算系数" />
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem
              label="生效时间"
              name="startTime"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DatePicker showTime allowClear style={{ width: '100%' }} />
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem
              label="失效时间"
              name="endTime"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DatePicker showTime allowClear style={{ width: '100%' }} />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default Add;
