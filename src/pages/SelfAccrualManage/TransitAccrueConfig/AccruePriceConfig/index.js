import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Input, Button, Modal, Select } from 'antd';
import {
  // SearchOutlined,
  PlusOutlined,
  // RollbackOutlined,
  ExportOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
  // ImportButton,
} from 'ky-giant';
import { success, error } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import ImportButton from 'src/components/ImportButton';
import AuthButton from 'src/components/AuthButton';
import authDecorator from 'src/components/AuthDecorator';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import SuppliersSearch from 'src/components/SuppliersSearch';
import {
  deleteAccruePrice,
  search,
  searchDetail,
  approvalList,
} from './servers/api';
import Add from './Components/Add';
import {
  accrueTypeList,
  statusList,
  employTypeList,
  forkliftTypeList,
  shiftTypeList,
  calculationRulesList,
  operationList,
  operationStatusList,
} from './Components/status';
// import './style.less';

const { Item: FormItem } = Form;
const { TextArea } = Input;
const rowKey = 'id';

let condition = {};
const Page = ({
  userInfo,
  logRoleCode,
  moduleCode,
  areaListSF,
  areaListSX,
}) => {
  const [form] = Form.useForm();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  const [sfVisible, setSfvisible] = useState(false);
  const [outVisible, setOutvisible] = useState(false);
  // const [roleCode, setRoleCode] = useState(null); // 角色编码
  const [intZoneCode, setIntZoneCode] = useState(null); // 网点代码
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  let approvalWord = '';
  // 权限查询
  // const getCurrentRoleCode = () => {
  //   if (logRoleCode) {
  //     setRoleCode(logRoleCode.roleCode);
  //     setIntZoneCode(userInfo.deptCode);
  //     getQueryParams();
  //     refresh({ currentPage: 1 });
  //   }
  // };

  const columns = [
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'zoneCode',
    },
    {
      title: '网点名称',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'zoneName',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: (values, row) => `${row.allocationAreaCode} ${values}`,
      // const areaList = areaListSF.concat(areaListSX);
      // const date = areaList.find(item => item.value === values);
      // return date ? date.label : values;
    },
    {
      title: '网点归属',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'orgid',
    },
    {
      title: '供应商名称',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'supplierName',
      render: text => text || '',
    },
    {
      title: '供应商编码',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierCode',
      render: text => text || '',
    },
    {
      title: '用工类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'employType',
      render: value => {
        const data = employTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '计费类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueType',
      render: value => {
        const data = accrueTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '扣保底类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'calculationRules',
      render: value => {
        const data = calculationRulesList.find(item => item.value === value);
        return data ? data.label : value === -1 ? '' : value;
      },
    },
    {
      title: '税率',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'taxRate',
      render: text => text || '',
    },
    {
      title: '班次类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'shiftType',
      render: value => {
        const data = shiftTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '日单价',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'dayPrice',
      render: text => text || '--',
    },
    {
      title: '小时单价',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'hourPrice',
      render: text => text || '--',
    },
    {
      title: '装车单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueLoadPrice',
    },
    {
      title: '省内装车单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'provinceLoadPrice',
    },
    {
      title: '省际装车单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'interprovincialLoadPrice',
    },
    {
      title: '装车日保底(T)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accrueLoadDayPrice',
      render: text => text || '--',
    },
    {
      title: '卸车单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueUnloadPrice',
      render: text => text || '--',
    },
    {
      title: '卸车日保底(T)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accrueUnloadDayPrice',
      render: text => text || '--',
    },
    {
      title: '装卸车月保底(T)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accrueUnloadMonPrice',
      render: text => text || '--',
    },
    {
      title: '是否按里程计提',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'forkliftByDistance',
      render: value => (value ? '是' : '否'),
    },
    {
      title: '叉车计重单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueType',
      render: (value, row) =>
        !(value === 5 && row.orgid === 'SX') ? row.forkliftPrice || '--' : '--',
    },
    {
      title: '叉车计重日保底(T)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accrueType',
      render: (value, row) => {
        if ([1, 2, 3, 5].includes(value) && row.orgid === 'SF')
          return row.forkliftDayPrice || '--';
        if ([2, 3].includes(value) && row.orgid === 'SX')
          return row.forkliftDayPrice || '--';
        return '--';
      },
    },
    // 顺心自有叉车选择计费类型为“计费重量”（accrueType：1）时，字段调整为：叉车单价、叉车月保底
    // 顺心自有叉车选择计费类型为“叉车计重”（accrueType：12）时
    {
      title: '叉车计重月保底(T)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accrueType',
      render: (value, row) =>
        [1, 12].includes(value) && row.orgid === 'SX'
          ? row.forkliftDayPrice || '--'
          : '--',
    },
    // 顺心计费类型为“计板”（accrueType：5）时，新增字段为：叉车计板单价（元/板）、叉车计板月保底（板）
    {
      title: '叉车计板单价（元/板）',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accrueType',
      render: (value, row) =>
        value === 5 && row.orgid === 'SX' ? row.forkliftPrice || '--' : '--',
    },
    {
      title: '叉车计板月保底（板）',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accrueType',
      render: (value, row) =>
        value === 5 && row.orgid === 'SX' ? row.forkliftDayPrice || '--' : '--',
    },
    {
      title: '叉车板均单价（元/T）',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'forkliftPalletPrice',
      render: text => text || '--',
    },
    {
      title: '叉车板均月保底（T）',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'forkliftPalletMonthWeight',
      render: text => text || '--',
    },
    {
      title: '叉车卡均单价（T）',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'forkliftCardPrice',
      render: text => text || '--',
    },
    {
      title: '叉车卡均月保底（T)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'forkliftCardMonthWeight',
      render: text => text || '--',
    },
    {
      title: '叉车类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'forkliftType',
      render: value => {
        const data = forkliftTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '叉车计里程单价(元/KM)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'forkliftKmPrice',
      render: text => text || '--',
    },
    {
      title: '叉车计里程日保底(KM)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'forkliftKmDayPrice',
      render: text => text || '--',
    },
    {
      title: '叉车按里程计提是否设上限',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'forkliftByLimit',
      render: value => (value ? '是' : '否'),
    },
    {
      title: '叉车里程月计提上限(元)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'forkliftMonthLimitPrice',
      render: text => text || '--',
    },
    {
      title: '固定绩效',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'forkliftFixedPrice',
      render: text => (text == undefined || text == null ? '--' : text),
    },
    {
      title: '分拣单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'sortPrice',
      render: text => text || '--',
    },
    {
      title: '分拣日保底(T)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'sortDayPrice',
      render: text => text || '--',
    },
    {
      title: '状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'status',
      render: value => {
        const data = statusList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '生效时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'startTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '失效时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'endTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },

    {
      title: '创建人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'creator',
    },
    {
      title: '创建时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'createTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'modifier',
    },
    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '审核人',
      dataIndex: 'approvalUserNo',
      width: 180,
    },
    {
      title: '审核时间',
      dataIndex: 'approvalTime',
      width: 180,
    },
    {
      title: '审核状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'approvalStatus',
      render: value => {
        const data = operationStatusList.find(item => item.value === value);
        return data ? data.label : '';
      },
    },
    {
      title: '备注',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'approvalRemark',
    },
    {
      title: '操作',
      align: 'center',
      width: 200,
      fixed: 'right',
      dataIndex: 'a',
      render: (v, record) => (
        <Fragment>
          <Button size="small" type="link" onClick={e => update(e, record)}>
            编辑
          </Button>
          {record.approvalStatus === 0 && record.orgid === 'SX' && (
            <AuthButton
              size="small"
              moduleCode={moduleCode}
              code="accrualPriceConfig-adopt"
              type="link"
              onClick={e => adoptUpdate(e, record, 1)}
            >
              审核通过
            </AuthButton>
          )}
          {record.approvalStatus === 0 && record.orgid === 'SX' && (
            <AuthButton
              moduleCode={moduleCode}
              code="accrualPriceConfig-reject"
              size="small"
              type="link"
              onClick={e => adoptUpdate(e, record, 2)}
            >
              驳回
            </AuthButton>
          )}
        </Fragment>
      ),
    },
  ];
  // const [columns, setColumns] = useState(columns);

  const adoptUpdate = async (e, v, approvalStatus) => {
    e.stopPropagation();
    operationModal([v.id], approvalStatus);
  };

  const operationModal = (idList, approvalStatus) => {
    Modal.confirm({
      title: '提示',
      content: (
        <div>
          <p>{operationList[approvalStatus]}</p>
          {approvalStatus === 2 && (
            <div style={{ display: 'flex' }}>
              <p>请反馈原因:</p>
              <TextArea
                showCount
                maxLength={200}
                onChange={e => {
                  approvalWord = e.target.value;
                }}
              />
            </div>
          )}
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const params = { idList, approvalStatus, approvalWord };
        const res = await approvalList(params);
        if (res.success) {
          success('操作成功');
          approvalWord = '';
          refresh();
        } else {
          error(res.errorMessage);
        }
      },
      onCancel() {
        approvalWord = '';
      },
    });
  };

  const getQueryParams = () => {
    // const { currentPage: current = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    condition = params;
    // refresh({ current, pageSize });
    return {
      ...params,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = { condition, current, size: pageSize };
    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum, total } = obj;
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: pageNum,
              pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const add = () => {
    setAddModalVisible(true);
    setOutvisible(false);
  };

  const update = async (e, v) => {
    e.stopPropagation();
    const reult = await searchDetail({ id: v.id });
    setEditingTarget(reult.obj);
    setSfvisible(true);
    setOutvisible(reult.obj.employType);
    setAddModalVisible(true);
  };

  // 删除
  const remove = record => {
    const ids = [];
    if (record instanceof Array) {
      for (const item of record) {
        ids.push(item.id);
      }
    } else {
      ids.push(record.id);
    }
    Modal.confirm({
      title: '提示',
      content: '确定删除当前数据，删除后无法恢复',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const res = await deleteAccruePrice(ids, record.modifier);
        if (res.success) {
          success('删除成功');
          refresh();
        } else {
          error(res.errorMessage);
        }
      },
      onCancel() {},
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="编码" name="zoneCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  // disabled={logRoleCode.roleCode === '88888888888'}
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.zoneCode !== curValues.zoneCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <FormItem name="supplierCode" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      isName={false}
                      allowClear
                      gysParams={getFieldValue('zoneCode') || userInfo.deptCode}
                    />
                  </FormItem>
                </Col>
              )}
            </FormItem>
            <Col {...colStyle}>
              <FormItem label="用工类型" name="employType">
                <Select
                  allowClear
                  options={employTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="生效状态" name="status">
                <Select
                  allowClear
                  options={statusList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="网点归属" name="orgid">
                <Select
                  allowClear
                  options={[
                    { label: 'SF', value: 'SF' },
                    { label: 'SX', value: 'SX' },
                  ]}
                  placeholder="请选择网点归属"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="审核状态" name="approvalStatus">
                <Select
                  allowClear
                  options={[
                    { label: '待审核', value: 0 },
                    { label: '审核通过', value: 1 },
                    { label: '已驳回', value: 2 },
                  ]}
                  placeholder="请选择审核状态"
                ></Select>
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <Button
            icon={<PlusOutlined />}
            style={{ marginRight: 15 }}
            type="primary"
            onClick={() => add()}
          >
            新增
          </Button>
          {/* <Button
            icon={<DeleteOutlined />}
            disabled={!selectedRows.length}
            onClick={() => remove(selectedRows)}
            type="primary"
            style={{ marginRight: 15 }}
          >
            删除
          </Button> */}
          <AsyncExport
            text="导出"
            style={{ marginRight: 15 }}
            icon={<ExportOutlined />}
            // disabled={!selectedRows.length}
            disabled={datas.list && datas.list.length < 1}
            // moduleCode={moduleCode}
            // code="priceConfig-export"
            options={{
              filename: '计提单价配置.xlsx',
              requstParams: [
                `/tdmsAccrueService/accruePriceInfoService/asyncExportAccruePrice`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
          <ImportButton
            danger={false}
            style={{ marginRight: 15 }}
            type="primary"
            pagename="计提单价配置"
            // moduleCode={moduleCode}
            // code="priceConfig-import"
            title="导入"
            action="/tdmsAccrueService/accruePriceInfoService/import"
            modalUrl="/tdmsAccrueService/accruePriceInfoService/import/download/tmp"
            modalName="计提单价配置模板.xlsx"
            handleSyncImport={refresh}
          />
        </div>
      </div>
    </Form>
  );

  useEffect(() => {
    form.setFieldsValue({ zoneCode: userInfo?.deptCode });
    if (logRoleCode && userInfo) {
      // setRoleCode(logRoleCode.roleCode);
      setIntZoneCode(userInfo.deptCode);
    }
    getQueryParams();
    refresh({ currentPage: 1 });
  }, [userInfo, logRoleCode]);

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey={rowKey}
        selectedRows={selectedRows}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          initialValues={editingTarget}
          roleCode={logRoleCode.roleCode}
          intZoneCode={intZoneCode}
          visible={addModalVisible}
          sfvisible={sfVisible}
          outvisible={outVisible}
          userInfo={userInfo}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh({ currentPage: editingTarget ? undefined : 1 });
          }}
          width={1000}
        />
      )}
    </div>
  );
};
@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
@authDecorator
class Container extends PureComponent {
  render() {
    const {
      moduleCode,
      userInfo,
      logRoleCode,
      areaListSF,
      areaListSX,
    } = this.props;
    return (
      <Page
        moduleCode={moduleCode}
        userInfo={userInfo}
        logRoleCode={logRoleCode}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
      />
    );
  }
}

export default withRouter(Container);
