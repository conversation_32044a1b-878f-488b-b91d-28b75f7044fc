export const accrueTypeList = [
  { label: '计费重量', value: 1 },
  { label: '实际重量', value: 2 },
  { label: '操作重量', value: 3 },
  { label: '计件', value: 4 },
  { label: '计板', value: 5 },
  { label: '按天', value: 6 },
  { label: '按时', value: 7 },
  { label: '全场均摊', value: 8 },
  { label: '板均', value: 9 },
  { label: '固定绩效', value: 10 },
  { label: '卡均', value: 11 },
  { label: '叉车计重', value: 12 },
];

export const statusList = [
  {
    label: '待生效',
    value: 0,
  },
  {
    label: '已生效',
    value: 1,
  },
  {
    label: '已过期',
    value: 2,
  },
];

export const employTypeList = [
  {
    label: '自有',
    value: 1,
  },
  {
    label: '外包',
    value: 2,
  },
];

export const forkliftTypeList = [
  {
    label: '站叉',
    value: 0,
  },
  {
    label: '座叉',
    value: 1,
  },
  {
    label: '双库操作员',
    value: 2,
  },
];

export const getForkliftTypeList = accrueType => {
  const arr = [...forkliftTypeList];
  if (accrueType !== 10)
    arr.pop({
      label: '双库操作员',
      value: 2,
    });
  return arr;
};

export const shiftTypeList = [
  {
    label: '白班',
    value: 0,
  },
  {
    label: '晚班',
    value: 1,
  },
  {
    label: '白晚班',
    value: 2,
  },
];

export const calculationRulesList = [
  {
    label: '岗位主工序扣保底',
    value: 0,
  },
  {
    label: '四个岗位交叉扣保底',
    value: 1,
  },
];

export const operationStatusList = [
  {
    label: '待审核',
    value: 0,
  },
  {
    label: '审核通过',
    value: 1,
  },
  {
    label: '已驳回',
    value: 2,
  },
];

export const operationList = {
  1: '是否确认审核通过已选供应商数据',
  2: '是否确认驳回已选供应商数据',
};
