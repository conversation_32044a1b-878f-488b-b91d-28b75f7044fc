import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Input, Modal, Select, DatePicker } from 'antd';
import {
  PlusOutlined,
  ExportOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  AuthButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import { success, error, limitCrossMonths } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import ExportButton from 'src/components/ExportButton';
import ImportButton from 'src/components/ImportButton';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import { deleteSchedulingConfig, search, searchDetail } from './servers/api';
import Add from './Components/Add';
import { workTypeList } from './Components/status';

const { RangePicker } = DatePicker;
const { Item: FormItem } = Form;

let inputParam = {};
const Page = ({ logRoleCode, userInfo, areaListSF, areaListSX }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  // const [roleCode, setRoleCode] = useState(null); // 角色编码
  const [selectedRows, setSelectedRows] = useState([]);
  const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [datesLimit, setDatesLimit] = useState([]);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  // 权限查询
  // const getCurrentRoleCode = () => {
  //   const roleId = sessionStorage.getItem('roleId');
  //   if (roleId && systemRole && systemRole.length) {
  //     const role = systemRole.find(item => item.roleId === roleId);
  //     if (role) {
  //       setRoleCode(role.roleCode);
  //     }
  //   }
  // };

  const columns = [
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'zoneCode',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: values => {
        const areaList = areaListSF.concat(areaListSX);
        const date = areaList.find(item => item.value === values);
        return date ? date.label : values;
      },
    },
    {
      title: '工号',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'userNo',
    },
    {
      title: '姓名',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'userName',
    },
    {
      title: '岗位',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'position',
    },
    {
      title: '开始日期',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'startTime',
      render: v => moment(v).format('YYYY-MM-DD'),
    },
    {
      title: '结束日期',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'endTime',
      render: v => moment(v).format('YYYY-MM-DD'),
    },
    {
      title: '班次类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'shiftType',
      render: value => {
        const data = workTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '创建人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'creator',
      render: text => text || '--',
    },
    {
      title: '创建时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'createTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'modifier',
      render: text => text || '--',
    },
    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          <AuthButton
            modulecode={moduleCode}
            code="fieldPersonnelConfig-edit"
            size="small"
            type="link"
            onClick={e => update(e, record)}
          >
            编辑
          </AuthButton>
        </Fragment>
      ),
    },
  ];

  const getQueryParams = () => {
    // const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    if (params.time && params.time.length > 0) {
      params.startTime = moment(params.time[0]).format('YYYY-MM-DD');
      params.endTime = moment(params.time[1]).format('YYYY-MM-DD');
    }
    inputParam = cloneDeep(params);
    delete inputParam.time;
    // refresh({ pageNum, pageSize });
    return {
      ...inputParam,
    };
  };
  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      ...inputParam,
      pageNum: current,
      pageSize,
    };
    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum, total } = obj;
          setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: pageNum,
              pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 新增
  const add = () => {
    setAddModalVisible(true);
  };

  // 删除
  const remove = rowList => {
    Modal.confirm({
      title: '提示',
      content: '确定删除当前数据，删除后无法恢复',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const res = await deleteSchedulingConfig({
          idList: rowList.map(ele => ele.id),
        });
        if (res.success) {
          success('删除成功');
          refresh();
        } else {
          error(res.errorMessage);
        }
      },
      onCancel() {},
    });
  };

  // 修改
  const update = async (e, v) => {
    e.stopPropagation();
    const reult = await searchDetail({
      zoneCode: v.zoneCode,
      userNo: v.userNo,
      position: v.position,
      startTime: moment(v.startTime)
        .startOf('month')
        .format('YYYY-MM-DD'),
      endTime: moment(v.startTime)
        .endOf('month')
        .format('YYYY-MM-DD'),
    });
    setEditingTarget(reult.obj);
    setAddModalVisible(true);
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        time: [moment().startOf('month'), moment().startOf('day')],
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  // disabled={logRoleCode.roleCode === '88888888888'}
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="工号" name="userNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="岗位" name="position">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="班次类型" name="shiftType">
                <Select
                  allowClear
                  options={workTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="日期"
                name="time"
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <RangePicker
                  allowClear
                  disabledDate={current =>
                    limitCrossMonths(current, datesLimit)
                  }
                  onCalendarChange={val => setDatesLimit(val)}
                  style={{
                    width: '100%',
                  }}
                />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AuthButton
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="fieldPersonnelConfig-add"
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => add()}
          >
            新增
          </AuthButton>
          <AuthButton
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            disabled={!selectedRows.length}
            code="fieldPersonnelConfig-delete"
            icon={<DeleteOutlined />}
            type="primary"
            onClick={() => remove(selectedRows)}
          >
            删除
          </AuthButton>
          <ExportButton
            style={{ marginRight: 15 }}
            text="导出所选"
            disabled={!selectedRows.length}
            moduleCode={moduleCode}
            code="fieldPersonnelConfig-exportSelect"
            icon={<ExportOutlined />}
            options={{
              total: totalNum,
              filename: '场站人员排班配置导出.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueSchedulingConfigRest/exportSchedulingConfig`,
                {
                  method: 'POST',
                  body: {
                    idList: selectedRows.map(({ id }) => id),
                  },
                },
              ],
            }}
          />
          <AsyncExport
            text="导出全部"
            style={{ marginRight: 15 }}
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            modulecode={moduleCode}
            code="fieldPersonnelConfig-exportAll"
            options={{
              filename: '场站人员排班配置.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueSchedulingConfigRest/schedulingConfigExportSync`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
          <ImportButton
            danger={false}
            type="primary"
            style={{ marginRight: 15 }}
            pagename="场站人员排班配置入"
            moduleCode={moduleCode}
            code="fieldPersonnelConfig-import"
            title="导入"
            action="/tdmsAccrueService/accrueSchedulingConfigRest/upload"
            modalUrl="/tdmsAccrueService/accrueSchedulingConfigRest/downImport"
            modalName="场站人员排班配置导入模板.xlsx"
            handleSyncImport={refresh}
          />
        </div>
      </div>
    </Form>
  );

  useEffect(() => {
    form.setFieldsValue({
      time: [moment().startOf('month'), moment().startOf('day')],
      zoneCode:
        logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
    });
    getQueryParams();
    refresh({ currentPage: 1 });
  }, [userInfo, logRoleCode]);

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        selectedRows={selectedRows}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          initialValues={editingTarget}
          roleCode={logRoleCode.roleCode}
          visible={addModalVisible}
          zoneCode={userInfo.deptCode}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh({ currentPage: editingTarget ? undefined : 1 });
          }}
          width={900}
        />
      )}
    </div>
  );
};
@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const { userInfo, logRoleCode, areaListSF, areaListSX } = this.props;
    return (
      <Page
        userInfo={userInfo}
        logRoleCode={logRoleCode}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
      />
    );
  }
}

export default withRouter(Container);
