import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Select, DatePicker } from 'antd';
import { PlusOutlined, ExportOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  rangePickerColStyle,
  rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  AuthButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import AsyncExport from 'src/components/AsyncExport';
import { search } from './servers/api';
import { timeTypeList, typeList } from './status';
import Add from './Components/Add';
// import './index.scss';
const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;

let inputParam = {};
const Page = ({
  userInfo,
  // systemRole,
  areaListSF,
  areaListSX,
  logRoleCode,
}) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  const [selectedRows, setSelectedRows] = useState([]);
  // const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '归属组织',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'sourceType',
    },
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'zoneCode',
    },
    {
      title: '网点名称',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'zoneName',
    },
    {
      title: '分拨区代码',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'allocationAreaCode',
    },
    {
      title: '分拨区名称',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'allocationArea',
    },
    {
      title: '线路编码',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'lineCode',
    },
    {
      title: '折算类型',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'calculationRulesName',
    },
    {
      title: '生效状态',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'effectiveStatus',
    },
    {
      title: '生效时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'effectiveTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '失效时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'expirationTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '创建人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'creator',
    },
    {
      title: '创建时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'creatorTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'modifier',
      render: text => text || '',
    },

    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          <AuthButton
            modulecode={moduleCode}
            code="loadTransportLineConfig-edit"
            size="small"
            type="link"
            onClick={e => update(e, record, 'edit')}
          >
            编辑
          </AuthButton>
        </Fragment>
      ),
    },
  ];

  const getQueryParams = () => {
    // const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    if (inputParam.time && inputParam.time.length > 0) {
      inputParam.effectiveTime = moment(inputParam.time[0]).valueOf('x');
      inputParam.expirationTime = moment(inputParam.time[1]).valueOf('x');
      delete inputParam.time;
    }
    // refresh({
    //   pageNum: datas.pagination.current,
    //   pageSize: datas.pagination.pageSize,
    // });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      ...inputParam,
      pageNum: current,
      pageSize,
    };
    search(param)
      .then(res => {
        const { success, obj } = res;
        if (success) {
          const { list, total } = obj;
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.pageNum,
              pageSize: obj.pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 新增
  const add = () => {
    setAddModalVisible(true);
  };

  // 修改
  const update = async (e, row, type) => {
    e.stopPropagation();
    row.type = type;
    row.effectTime = moment(row.effectTime);
    row.expireTime = moment(row.expireTime);
    setEditingTarget(row);
    setAddModalVisible(true);
  };

  useEffect(() => {
    if (userInfo && userInfo.deptCode && logRoleCode && logRoleCode.roleCode) {
      form.setFieldsValue({
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888' ||
          logRoleCode.roleCode === 'tp00001'
            ? userInfo.areaCode
            : undefined,
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
      });
    }
    getQueryParams();
    refresh({ currentPage: 1 });
  }, [logRoleCode, userInfo]);

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={
        {
          // zoneCode: userInfo.deptCode,
        }
      }
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <DeptSearch
                  // disabled={logRoleCode.roleCode === 'tp00001'}
                  placeholder="请输入中转场名称或者代码"
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  disabled={
                    logRoleCode.roleCode === '88888888888' ||
                    logRoleCode.roleCode === 'tp00001'
                  }
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="生效状态" name="effectiveStatus">
                <Select
                  allowClear
                  options={timeTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...rangePickerColStyle}>
              <FormItem label="生效时间" name="time" {...rangePickerLayout}>
                <RangePicker
                  allowClear
                  showTime
                  style={{
                    width: '100%',
                  }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="折算类型" name="calculationRules">
                <Select
                  allowClear
                  options={typeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AuthButton
            modulecode={moduleCode}
            code="loadTransportLineConfig-add"
            icon={<PlusOutlined />}
            style={{ marginRight: 15 }}
            type="primary"
            onClick={() => add()}
          >
            新增
          </AuthButton>
          <AsyncExport
            text="导出"
            modulecode={moduleCode}
            code="loadTransportLineConfig-export"
            icon={<ExportOutlined />}
            style={{ marginRight: 15 }}
            disabled={datas.list && datas.list.length < 1}
            options={{
              filename: '折算线路.xlsx',
              requstParams: [
                `/tdmsAccrueService/shipmentOneConfigRest/exportSync`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        selectedRows={selectedRows}
        showSelection={false}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          areaListSF={areaListSF}
          initialValues={editingTarget}
          roleCode={logRoleCode.roleCode}
          userInfo={userInfo}
          visible={addModalVisible}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh({ currentPage: editingTarget ? undefined : 1 });
          }}
          width={850}
        />
      )}
    </div>
  );
};
@connect(state => ({
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
  logRoleCode: state.global.logRoleCode,
}))
class Container extends PureComponent {
  render() {
    const { userInfo, areaListSF, areaListSX, logRoleCode } = this.props;
    return (
      <Page
        userInfo={userInfo}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
        logRoleCode={logRoleCode}
      />
    );
  }
}

export default withRouter(Container);
