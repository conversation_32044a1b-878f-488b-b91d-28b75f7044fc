import React, { useState, useEffect } from 'react';
import {
  Form,
  Modal,
  Col,
  Row,
  Button,
  DatePicker,
  TimePicker,
  Radio,
  Input,
} from 'antd';
import moment from 'moment';
import { success, error } from 'src/utils/utils';
import DeptSearch from 'src/components/DeptSearch';
import { add, update } from '../../servers/api';
import { typeList } from '../../status';
// import { operateTypeList } from '../status';

const { Item: FormItem } = Form;
const Add = props => {
  const {
    onOk,
    visible,
    initialValues,
    areaListSF,
    userInfo,
    roleCode,
    ...rest
  } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [deptNameValue, setDeptNameValue] = useState(''); // 网点名称
  const [areaName, setAreaName] = useState(''); // 分拨区名称
  const [areaCode, setAreaCode] = useState(''); // 分拨区编码
  const [type, setType] = useState(''); // 公司归属
  const [disableCalculationRules, setDisableCalculationRules] = useState(false);

  // 表单默认值
  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
    // initialValues,
  };

  // 提交时请求
  const submit = async () => {
    const param = await form.validateFields();
    setLoading(true);
    param.startTime = moment(param.startTime).valueOf('x');
    param.effectiveTime = moment(param.effectiveTime).valueOf('x');
    param.expirationTime = moment(param.expirationTime).valueOf('x');
    param.zoneName = deptNameValue || userInfo.deptName;
    param.allocationArea = areaName;
    param.allocationAreaCode = areaCode || userInfo.areaCode;
    param.sourceType = type || userInfo.orgCode;
    try {
      const res = initialValues
        ? await update({ ...param, id: initialValues.id })
        : await add(param);
      if (res.success) {
        setLoading(false);
        success(`${initialValues ? '修改' : '新增'}成功！`);
        onOk();
      } else {
        setLoading(false);
        error(res.errorMessage);
      }
    } catch {
      setLoading(false);
    }
  };
  const handleCancel = () => {
    form.resetFields();
  };
  const getItem = (v, option, datas) => {
    if (datas.length > 0) {
      const objList = datas.filter(item => v === item.value);
      if (objList.length > 0) {
        setDeptNameValue(objList[0].deptName);
        setAreaName(objList[0].areaName);
        setAreaCode(objList[0].areaCode);
        setType(objList[0].orgCode);
      }
    }
  };

  const disabledDate = current => current && current < moment().startOf('day');

  const range = (start, end) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    // console.log(result, 'result');
    return result;
  };

  const disabledDateTime = data => {
    if (moment(data).day() === moment().day()) {
      const hours = moment().hour(); // 0~23
      const minutes = moment().minute(); // 0~59
      // const seconds = moment().second(); // 0~59
      // 当日只能选择当前时间之后的时间点
      return {
        disabledHours: () => range(0, hours),
        disabledMinutes: () => range(0, minutes + 5),
        // disabledSeconds: () => range(0, 59),
      };
    }
  };

  useEffect(() => {
    if (initialValues) {
      form.setFieldsValue({
        zoneCode: initialValues.zoneCode,
        calculationRules: initialValues.calculationRules,
        preStationCode: initialValues.preStationCode,
        nextStationCode: initialValues.nextStationCode,
        lineCode: initialValues.lineCode,
        startTime: initialValues.startTime
          ? moment(initialValues.startTime)
          : undefined,
        effectiveTime: initialValues.effectiveTime
          ? moment(initialValues.effectiveTime)
          : undefined,
        expirationTime: initialValues.expirationTime
          ? moment(initialValues.expirationTime)
          : undefined,
      });
      setDeptNameValue(initialValues.zoneName);
      setAreaName(initialValues.allocationArea);
      setAreaCode(initialValues.allocationAreaCode);
      setType(initialValues.sourceType);
      setDisableCalculationRules(true);
    } else if (roleCode === 'tp00001') {
      form.setFieldsValue({ zoneCode: userInfo.deptCode });
    }
  }, [visible, initialValues]);

  useEffect(() => {
    if (userInfo && userInfo.areaCode && areaListSF && areaListSF.length > 0) {
      const a = areaListSF.filter(item => item.value === userInfo.areaCode);
      if (a.length > 0) {
        setAreaName(a[0].labelValue);
      }
    }
  }, [userInfo, areaListSF]);

  return (
    <Modal
      title={initialValues ? '编辑' : '新增'}
      maskClosable={false}
      onCancel={handleCancel}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={[
        <Button key="back" onClick={rest.onCancel}>
          取消
        </Button>,
        <Button key="submit" loading={loading} type="primary" onClick={submit}>
          确定
        </Button>,
      ]}
    >
      <Form {...formProps}>
        <Row>
          <Col md={18}>
            <FormItem
              label="折算类型"
              name="calculationRules"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Radio.Group
                options={typeList}
                disabled={disableCalculationRules}
                onChange={() => {
                  // 赋值后需要禁用
                  setDisableCalculationRules(true);
                }}
              ></Radio.Group>
            </FormItem>
          </Col>
        </Row>
        <Row>
          {/* 这期改了文案及字段名称，建议后端说根据语义定字段，后端说不要这样搞 */}
          {/* 这期还没上，后端又说要将上一站字段改为 preStationCode */}
          <FormItem dependencies={['calculationRules']} noStyle>
            {() =>
              form.getFieldValue('calculationRules') === 1 && (
                <Col md={18}>
                  <FormItem
                    label="上一站网点"
                    name="preStationCode"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                  >
                    <DeptSearch placeholder="请输入上一站网点" allowClear />
                  </FormItem>
                </Col>
              )
            }
          </FormItem>
          <Col md={18}>
            <FormItem
              label="网点"
              name="zoneCode"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DeptSearch
                disabled={
                  initialValues &&
                  (initialValues.type === 'check' ||
                    initialValues.type === 'edit')
                }
                onChange={getItem}
                placeholder="请输入中转场名称或者代码"
                allowClear
              />
            </FormItem>
          </Col>
          <FormItem dependencies={['calculationRules']} noStyle>
            {() =>
              [0, 2].includes(form.getFieldValue('calculationRules')) && (
                <Col md={18}>
                  <FormItem
                    label="下一站网点"
                    name="nextStationCode"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                  >
                    <DeptSearch placeholder="请输入下一站网点" allowClear />
                  </FormItem>
                </Col>
              )
            }
          </FormItem>
          <Col md={18}>
            <FormItem
              label="线路编码"
              name="lineCode"
              rules={[
                {
                  required: true,
                  message: '请输入',
                },
                {
                  pattern: /^[A-Z0-9]+$/,
                  message: '线路编码仅支持数字+大写英文',
                },
              ]}
            >
              <Input
                placeholder="请输入线路编码"
                maxLength={50}
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem
              label="发车时间"
              name="startTime"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <TimePicker
                format="HH:mm:ss"
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem
              label="生效时间"
              name="effectiveTime"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DatePicker
                disabledDate={disabledDate}
                disabledTime={disabledDateTime}
                showTime
                showNow={false}
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem
              label="失效时间"
              name="expirationTime"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DatePicker showTime allowClear style={{ width: '100%' }} />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default Add;
