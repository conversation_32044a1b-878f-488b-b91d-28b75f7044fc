import React, { useEffect } from 'react';
import { InputNumber } from 'antd';

export default props => {
  const { value, diffValue, diffDisabled, status, ...restProps } = props;
  useEffect(() => {}, [value]);

  return (
    <InputNumber
      min={0}
      precision={1}
      formatter={v => String(v).split('.')[0]}
      // disabled={init||props.disabled ? true : false}
      value={value}
      {...restProps}
    />
  );
};
