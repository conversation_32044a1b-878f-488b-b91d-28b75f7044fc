import React, { useState, useEffect } from 'react';
import { Form, Modal, Col, Row, Button, DatePicker } from 'antd';
import moment from 'moment';
import { success, error } from 'src/utils/utils';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import { getDept } from 'src/services/api';
import ModifyNumber from '../ModifyNumber';
import { addAttendanceDays, updateAttendanceDays } from '../../servers/api';

const { Item: FormItem } = Form;
const Add = props => {
  const { onOk, visible, zoneCode, initialValue, roleCode, ...rest } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [deptNameValue, setDeptNameValue] = useState('');

  // 提交时请求
  const submit = async () => {
    const param = form.getFieldsValue();
    param.month = param.month
      ? moment(param.month).format('YYYY-MM')
      : undefined;
    param.creator = sessionStorage.userid;
    param.companyName = deptNameValue;
    setLoading(true);
    try {
      const res = initialValue
        ? await updateAttendanceDays({ ...param, id: initialValue.id })
        : await addAttendanceDays(param);
      if (res.success) {
        setLoading(false);
        success(`${initialValue ? '修改' : '新增'}成功！`);
        onOk();
      } else {
        error(res.errorMessage);
        setLoading(false);
      }
    } catch {
      setLoading(false);
    }
  };
  const handleCancel = () => {
    form.resetFields();
  };

  const getItem = (v, option, datas) => {
    if (datas.length > 0) {
      const objList = datas.filter(item => v === item.value);
      if (objList.length > 0) {
        setDeptNameValue(objList[0].deptName);
      }
    }
  };

  const searchCode = value => {
    if (value !== '') {
      getDept(value).then(res => {
        if (res.obj.list != null) {
          res.obj.list.forEach(v => {
            if (v.deptCode === value) {
              setDeptNameValue(v.deptName);
            }
          });
        }
      });
    }
  };

  useEffect(() => {
    if (initialValue) {
      setDeptNameValue(initialValue.companyName);
      if (!initialValue.companyName) {
        searchCode(initialValue.companyCode);
      }
    }
  }, [initialValue]);

  return (
    <Modal
      title={`${initialValue ? '编辑' : '新增'}`}
      maskClosable={false}
      onCancel={handleCancel}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={[
        <Button key="back" onClick={rest.onCancel}>
          取消
        </Button>,
        <Button key="submit" loading={loading} type="primary" onClick={submit}>
          确定
        </Button>,
      ]}
    >
      <Form
        form={form}
        labelCol={{ md: 8, xs: 8 }}
        wrapperCol={{ md: 16 }}
        initialValues={{
          month: initialValue ? moment(initialValue.month) : undefined,
          companyCode: initialValue
            ? initialValue.companyCode
            : roleCode === 'tp00001'
            ? zoneCode
            : undefined,
          attendanceDays: initialValue ? initialValue.attendanceDays : '',
        }}
      >
        <Row>
          <Col md={20} sm={24}>
            <FormItem label="网点名称" name="companyCode">
              <DeptSearch
                placeholder="请输入网点名称"
                addSXAllZoneCode
                allowClear
                onChange={getItem}
              />
            </FormItem>
          </Col>
          <Col md={20} sm={24}>
            <FormItem
              label="月份"
              name="month"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DatePicker
                // disabled={initialValue}
                picker="month"
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
          <Col md={20} sm={24}>
            <FormItem
              label="规定出勤天数"
              name="attendanceDays"
              rules={[
                {
                  required: true,
                  message: '请输入',
                },
              ]}
            >
              <ModifyNumber placeholder="请输入" allowClear />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default Add;
