import React, { useState, useEffect, PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { Form, Button, Row, Col, DatePicker, Select } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { cloneDeep } from 'lodash';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  AuthButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import ImportButton from 'src/components/ImportButton';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import AsyncExport from 'src/components/AsyncExport';
import { timeLimit } from 'src/utils/utils';
import { queryAttendanceDays } from './servers/api';
import Add from './Components/Add';

import './index.scss';
const { Item: FormItem } = Form;

let inputParam = {};
const Page = ({ userInfo, systemRole, areaListSF, areaListSX }) => {
  const moduleCode = useModuleCode();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null);
  const [roleCode, setRoleCode] = useState(null); // 角色编码
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  const columns = [
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'companyCode',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: values => {
        const areaList = areaListSF.concat(areaListSX);
        const date = areaList.find(item => item.value === values);
        return date ? date.label : values;
      },
    },
    {
      title: '月份',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'month',
    },

    {
      title: '规定出勤天数',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'attendanceDays',
    },
    {
      title: '修改人',
      ellipsis: true,
      align: 'center',
      width: 100,
      dataIndex: 'modifier',
    },
    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'modifyTime',
      render: value =>
        value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '--',
    },
    {
      title: '创建人',
      ellipsis: true,
      align: 'center',
      width: 100,
      dataIndex: 'creator',
    },
    {
      title: '创建时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'createTime',
      render: value =>
        value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : '--',
    },
    {
      title: '操作',
      fixed: 'right',
      align: 'center',
      width: 100,
      dataIndex: 'operate',
      render: (v, record) => (
        // 当月12号24:00前  可以   修改上个月数据，上个月以前数据不能修改
        // 当月12号24:00后  不可以  修改上个月和上个月以前的数据
        <Fragment>
          {timeLimit(12) ? (
            moment(record.month).format('x') >
            moment()
              .endOf('month')
              .subtract(1, 'months')
              .format('x') ? (
              <Button type="link" onClick={e => update(record, e)}>
                修改
              </Button>
            ) : (
              '--'
            )
          ) : moment(record.month).format('x') >
            moment()
              .endOf('month')
              .subtract(2, 'months')
              .format('x') ? (
            <Button type="link" onClick={e => update(record, e)}>
              修改
            </Button>
          ) : (
            '--'
          )}
        </Fragment>
      ),
    },
  ];
  useEffect(() => {
    getCurrentRoleCode();
  }, [userInfo]);

  // 查询参数的获取
  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    inputParam.month = moment(inputParam.month).format('YYYY-MM');
    refresh({ pageNum, pageSize });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = { ...inputParam, pageNum, pageSize };
    queryAttendanceDays(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum: current, total } = obj;
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current,
              pageSize,
            },
            list,
          });
        } else {
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setLoading(false);
      });
  };
  const update = (value, e) => {
    e.stopPropagation();
    setEditingTarget(value);
    setAddModalVisible(true);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const add = () => {
    setAddModalVisible(true);
  };

  // 权限查询
  const getCurrentRoleCode = () => {
    const roleId = sessionStorage.getItem('roleId');
    if (roleId && systemRole && systemRole.length) {
      const role = systemRole.find(item => item.roleId === roleId);
      if (role) {
        setRoleCode(role.roleCode);
        form.setFieldsValue({
          companyCode: userInfo.deptCode,
          month: moment(),
        });
        getQueryParams();
        refresh();
      }
    }
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={{
        companyCode: userInfo.deptCode,
        month: moment(),
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="网点" name="companyCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={roleCode === 'tp00001'}
                  addSXAllZoneCode
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  // disabled={logRoleCode.roleCode === '88888888888'}
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="查询月份"
                name="month"
                rules={[
                  {
                    required: true,
                    message: '请选择月份',
                  },
                ]}
              >
                <DatePicker
                  showToday
                  picker="month"
                  allowClear
                  style={{ width: '100%' }}
                />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AuthButton
            style={{ marginRight: 15 }}
            modulecode={moduleCode} // 角色列表
            code="attendanceDaysData-add" // 模块编码
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => add()}
          >
            新增
          </AuthButton>
          <ImportButton
            danger={false}
            modulecode={moduleCode}
            code="attendanceDaysData-import"
            type="primary"
            pagename="出勤天数导入"
            title="导入"
            action="/tdmsAccrueService/attendanceDaysConfig/importData"
            modalUrl="/tdmsAccrueService/attendanceDaysConfig/download/tmp"
            modalName="出勤天数导入模板.xlsx"
            handleSyncImport={refresh}
          />
          <div
            style={{ display: 'inline-block', width: '15px', height: '1px' }}
          ></div>
          <AsyncExport
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="attendanceDaysData-exportAll"
            type="primary"
            disabled={datas.list && datas.list.length < 1}
            text="导出全部"
            options={{
              requstParams: [
                '/tdmsAccrueService/attendanceDaysConfig/asyncExportAttendanceDaysConfig',
                {
                  method: 'POST',
                  body: inputParam,
                },
              ],
            }}
          />
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        showSelection={false}
        loading={loading}
        data={datas}
        columns={columns}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          initialValue={editingTarget}
          visible={addModalVisible}
          roleCode={roleCode}
          zoneCode={userInfo.deptCode}
          deptName={userInfo.deptName}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh();
          }}
          width={550}
        />
      )}
    </div>
  );
};

class Container extends PureComponent {
  render() {
    const { systemRole, userInfo, areaListSF, areaListSX } = this.props;
    return (
      <Page
        userInfo={userInfo}
        systemRole={systemRole}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
      />
    );
  }
}

export default connect(state => ({
  userInfo: state.global.userInfo,
  systemRole: state.global.roles,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))(withRouter(Container));
