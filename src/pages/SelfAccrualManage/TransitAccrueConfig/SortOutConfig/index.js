import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Select, DatePicker, Input, Tooltip } from 'antd';
import { PlusOutlined, ExportOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  AuthButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import AsyncExport from 'src/components/AsyncExport';
import StandardTable from 'src/components/StandardTable';

import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import { search } from './servers/api';
import AddModal from './Components/AddModal';
import { sortTypeList } from './Components/status';
import './index.scss';
const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;
let inputParam = {};
const Page = ({ userInfo, logRoleCode, areaListSF, areaListSX }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  const [selectedRows, setSelectedRows] = useState([]);
  // const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '场地',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'zoneCode',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'allocationAreaCode',
    },
    {
      title: '承包制组长工号',
      align: 'center',
      ellipsis: true,
      // width: 120,
      dataIndex: 'userNo',
    },
    {
      title: '承包制组长姓名',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'userName',
    },
    {
      title: '承包班次号',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'shiftName',
    },
    {
      title: '班次开始时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'startTime',
    },
    {
      title: '班次结束时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'endTime',
    },
    {
      title: '承包月台',
      align: 'center',
      width: 170,
      dataIndex: 'platformNo',
      ellipsis: {
        showTitle: false,
      },
      render: text => {
        const showText = text.replace(/,/gi, '，');
        return (
          <Tooltip placement="topLeft" title={showText}>
            {showText}
          </Tooltip>
        );
      },
    },
    {
      title: '分拣类型',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'sortType',
      render: value => {
        const data = sortTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '流向/网点',
      align: 'center',
      width: 170,
      dataIndex: 'flowDirectionCz',
      ellipsis: {
        showTitle: false,
      },
      render: (text, row) => {
        const showText = text.replace(/,/gi, '，');
        return row.sortType === 2 ? (
          <Tooltip placement="topLeft" title={showText}>
            {showText}
          </Tooltip>
        ) : (
          text
        );
      },
    },
    {
      title: '生效时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'startWorkTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '失效时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'endWorkTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '操作',
      align: 'center',
      // width: 100,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          <AuthButton
            modulecode={moduleCode}
            code="sortInclusiveConfig-edit"
            size="small"
            type="link"
            onClick={e => update(e, record)}
          >
            编辑
          </AuthButton>
        </Fragment>
      ),
    },
  ];

  const getQueryParams = () => {
    // const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);

    if (inputParam.time && inputParam.time.length > 0) {
      inputParam.startWorkTime = moment(inputParam.time[0]).valueOf();
      inputParam.endWorkTime = moment(inputParam.time[1]).valueOf();
    }
    delete inputParam.time;
    // refresh({ pageNum, pageSize });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      ...inputParam,
      pageNum: current,
      pageSize,
    };
    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum, total } = obj;
          // setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: pageNum,
              pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 新增
  const add = () => {
    setAddModalVisible(true);
  };

  // 修改
  const update = async (e, row, type) => {
    e.stopPropagation();
    row.type = type;
    row.effectTime = moment(row.effectTime);
    row.expireTime = moment(row.expireTime);
    setEditingTarget(row);
    setAddModalVisible(true);
  };

  // useEffect(() => {
  //   // 当前登录的角色判断
  //   if (
  //     logRoleCode &&
  //     userInfo &&
  //     userInfo.deptCode &&
  //     (logRoleCode.roleCode === 'tp00001' ||
  //       logRoleCode.roleCode === '88888888888')
  //   ) {
  //     form.setFieldsValue({
  //       zoneCode:
  //         logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
  //       allocationAreaCode:
  //         logRoleCode.roleCode === '88888888888'
  //           ? userInfo.areaCode
  //           : undefined,
  //       time: [moment().startOf('month'), moment().endOf('month')],
  //     });
  //     getQueryParams();
  //   } else {
  //     form.setFieldsValue({
  //       time: [moment().startOf('month'), moment().endOf('month')],
  //     });
  //   }
  // }, [logRoleCode, userInfo]);

  useEffect(() => {
    // 当前登录的角色判断
    if (logRoleCode && userInfo && userInfo.deptCode) {
      form.setFieldsValue({
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        time: [moment().startOf('month'), moment().endOf('month')],
      });
      getQueryParams();
      refresh({ currentPage: 1 });
    }
  }, [logRoleCode, userInfo]);

  const resetForm = () => {
    form.resetFields();
    // if (
    //   logRoleCode.roleCode === '88888888888' ||
    //   logRoleCode.roleCode === 'tp00001'
    // ) {
    //   getQueryParams();
    // }
    getQueryParams();
    refresh({ currentPage: 1 });
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        time: [moment().startOf('month'), moment().endOf('month')],
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="网点编码"
                name="zoneCode"
                // rules={[
                //   ({ getFieldValue }) => ({
                //     validator(_, value) {
                //       if (getFieldValue('allocationAreaCode') || value) {
                //         return Promise.resolve();
                //       }
                //       return Promise.reject(
                //         new Error('分拨区和网点不能同时为空'),
                //       );
                //     },
                //   }),
                // ]}
              >
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="分拨区"
                name="allocationAreaCode"
                // rules={[
                //   ({ getFieldValue }) => ({
                //     validator(_, value) {
                //       if (getFieldValue('zoneCode') || value) {
                //         return Promise.resolve();
                //       }
                //       return Promise.reject(
                //         new Error('分拨区和网点不能同时为空'),
                //       );
                //     },
                //   }),
                // ]}
              >
                <Select
                  allowClear
                  disabled={logRoleCode.roleCode === '88888888888'}
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请选择分拨区"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="班次号" name="shiftName">
                <Input placeholder="请输入班次号" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="月台号" name="platformNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                // {...rangePickerLayout}
                label="生效时间"
                name="time"
                rules={[
                  {
                    required: true,
                    message: '请填写',
                  },
                ]}
              >
                <RangePicker
                  // showTime
                  ranges={{
                    今天: [moment().startOf('day'), moment().endOf('day')],
                    本周: [
                      moment()
                        .weekday(0)
                        .startOf('day'),
                      moment()
                        .weekday(6)
                        .endOf('day'),
                    ],
                    本月: [moment().startOf('month'), moment().endOf('month')],
                  }}
                  // showTime
                  allowClear
                  style={{ width: '100%' }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组长姓名" name="userName">
                <Input placeholder="请输入组长姓名" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组长工号" name="userNo">
                <Input placeholder="请输入组长工号" allowClear />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AuthButton
            modulecode={moduleCode}
            code="sortInclusiveConfig-add"
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => add()}
          >
            新增
          </AuthButton>
          <AsyncExport
            type="primary"
            style={{ marginLeft: 15 }}
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            modulecode={moduleCode}
            code="sortInclusiveConfig-exportAll"
            text="导出全部"
            options={{
              filename: '分拣包干配置.xlsx',
              requstParams: [
                '/restApi/fcamsForkliftServices/sortingBagApi/aSyncExport',
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        selectedRows={selectedRows}
        showSelection={false}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <AddModal
          initialValues={editingTarget}
          roleCode={logRoleCode.roleCode}
          userInfo={userInfo}
          areaListSF={areaListSF}
          visible={addModalVisible}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            getQueryParams();
            refresh({ currentPage: editingTarget ? undefined : 1 });
          }}
          width={850}
        />
      )}
    </div>
  );
};
@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const { logRoleCode, userInfo, areaListSF, areaListSX } = this.props;
    return (
      <Page
        logRoleCode={logRoleCode}
        userInfo={userInfo}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
      />
    );
  }
}

export default withRouter(Container);
