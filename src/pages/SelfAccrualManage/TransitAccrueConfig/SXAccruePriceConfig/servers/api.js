import request from '@/utils/request';

// 计提单价配置查询
export function search(params) {
  return request(
    `/tdmsAccrueService/accruePriceInfoSxService/queryAccruePriceInfoByPage`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 添加计提单价配置
export function add(params) {
  return request(
    `/tdmsAccrueService/accruePriceInfoSxService/addAccruePriceInfo`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 更新计提单价配置
export function update(params) {
  return request(
    `/tdmsAccrueService/accruePriceInfoSxService/updateAccruePriceInfo`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

/**
 * @description: 根据网点查询供应商列表
 * @param {type} orgCode
 * @return:
 */
export function supplierSearch(params) {
  return request(
    `/tdmsAccrueService/supplierRestService/querySupplierByZoneCode`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

export function approvalList(params) {
  return request(
    `/tdmsAccrueService/accruePriceInfoSxService/approvalPassPrice`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}
