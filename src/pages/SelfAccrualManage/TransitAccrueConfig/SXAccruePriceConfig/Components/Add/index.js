import React, { useState, useEffect, useRef } from 'react';
import { Form, Modal, Col, Row, Input, Select, DatePicker, Button } from 'antd';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { success, error } from 'utils/utils';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import { getDept } from 'src/services/api';
import {
  getForkliftTypeList,
  employTypeList,
  CARD_PALLET_FETCH_OBJ,
  commissionTypeList,
} from '../status';
import { add, update, supplierSearch } from '../../servers/api';
import style from './index.module.less';

const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;

/** 税率 */
const TAX_RATE_OPTIONS = [
  { value: '1%', label: '1%' },
  { value: '3%', label: '3%' },
  { value: '5%', label: '5%' },
  { value: '6%', label: '6%' },
];

const uerId = sessionStorage.username; // 当前登录人工号

const Add = props => {
  const {
    onOk,
    visible,
    initialValues,
    intZoneCode,
    roleCode,
    sfvisible,
    outvisible,
    userInfo,
    ...rest
  } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [arrList, setArrList] = useState([]);
  const timeId = useRef(null);

  const requiredRule = [{ required: true, message: '请输入' }];
  const priceRule = [
    { required: true, message: '请输入' },
    {
      pattern: /(^(([1-9]([0-9]+)?)|(0{1}))(\.[0-9]{1,2})?$)/,
      message: '请输入数字，最多保留两位小数',
    },
  ];
  const price = [
    {
      pattern: /(^(([1-9]([0-9]+)?)|(0{1}))(\.[0-9]{1,2})?$)/,
      message: '请输入数字，最多保留两位小数',
    },
  ];
  const priceInt = [
    { required: true, message: '请输入' },
    {
      pattern: /^([1-9]\d{0,3})?$|^(10000)$|^0$/,
      message: '请输入0-10000之间的整数',
    },
  ];
  const priceInt100 = [
    { required: true, message: '请输入' },
    {
      pattern: /^([1-9]\d{0,1})?$|^(100)$|^0$/,
      message: '请输入0-100之间的整数',
    },
  ];
  const [addAfter, setAddAfter] = useState('元/T');

  useEffect(() => {
    // 中转场场地负责人
    if (initialValues === null && roleCode === 'tp00001') {
      searchCode(intZoneCode);
      form.setFieldsValue({ zoneCode: intZoneCode });
    }
  }, []);

  // 表单初始值的处理
  const tem = initialValues ? cloneDeep(initialValues) : null;
  if (initialValues !== null) {
    tem.time = [moment(tem.startTime), moment(tem.endTime)];
  }

  // 表单基础 props
  const formProps = {
    form,
    labelCol: { span: 8 },
    initialValues: tem,
  };

  /**
   * 当用工类型发生变更后重置字段
   */
  const handleResetOnEmployTypeChange = () => {
    form.setFieldsValue({
      supplierName: undefined,
      supplierCode: undefined,
      accrueType: undefined,
    });
  };

  /**
   * 当计费类型发生变更后重置字段
   */
  const handleResetOnAccrueTypeChange = () => {
    form.setFieldsValue({
      provinceLoadPrice: undefined,
      interprovincialLoadPrice: undefined,
      unloadPrice: undefined,
      beltUnloadPrice: undefined,
      beltForkliftPrice: undefined,
      beltSortPrice: undefined,
      loadUnloadMonMin: undefined,
      time: undefined,
      forkliftPrice: undefined,
      forkliftMonMin: undefined,
      forkliftFixedPrice: undefined,
      forkliftType: undefined,
      forkliftWeightPercent: undefined,
      cardPalletFetch: undefined,
      cardWeightPercent: undefined,
      taxRate: undefined,
      loadPrice: undefined,
      commissionType: undefined,
    });
  };

  /**
   * 当供应商更改时执行
   * 这里的逻辑没看懂
   * @param {string} value 供应商编码
   */
  const handleOnSupplierChange = value => {
    form.setFieldsValue({ supplierCode: value });
  };

  /**
   * 网点切换时更新对应字段
   * @param {string} v 网点代码
   * @param {*} _ 当前 label 对象
   * @param {*} datas 数组
   */
  const handleOnZoneCodeChnage = (v, _, datas) => {
    if (v && datas.length > 0) {
      const objList = datas.filter(item => v === item.value);
      if (objList.length > 0) {
        form.setFieldsValue({
          zoneName: objList[0].deptName,
          zoneCode: objList[0].deptCode,
        });
      }
      searchCode(v);
    }

    // 避免网点代码切换后会导致税率未更新
    form.setFieldsValue({ taxRate: undefined });
  };

  // 根据网点编码查询对应网名名称
  const searchCode = value => {
    if (value === '') {
      form.setFieldsValue({ zoneName: '' });
      return;
    }

    const valueTransit = value.toUpperCase();
    clearTimeout(timeId.current);

    timeId.current = setTimeout(async () => {
      const {
        obj: { list },
      } = await getDept(valueTransit);

      if (list == null) {
        error('网点查询失败');
        form.setFieldsValue({ zoneName: '', zoneCode: '' });
        return;
      }

      const arrNew = list.filter(item => item.deptCode === valueTransit);

      if (arrNew.length === 0) {
        error('请输入正确的网点代码');
        form.setFieldsValue({ zoneName: '', zoneCode: '' });
      }

      form.setFieldsValue({
        zoneName: arrNew[0].deptName,
        zoneCode: valueTransit,
      });

      // 获取供应商列表
      const { obj } = await supplierSearch({
        zoneCode: valueTransit,
        status: 1,
        approvalStatus: 1,
      });

      const arrListss = obj.map(item => ({
        value: item.supplierNo,
        label: item.supplierName,
      }));

      setArrList(arrListss);
    }, 1000);
  };

  /**
   * 提交时请求
   */
  const submit = async () => {
    const param = await form.validateFields();
    param.startTime = moment(param.time[0])
      .startOf('month')
      .valueOf();
    param.endTime = moment(param.time[1])
      .endOf('month')
      .valueOf();

    // 移除对应字段
    param.time = undefined;

    arrList.forEach(v => {
      if (v.value === param.supplierCode) {
        param.supplierName = v.label;
      }
    });

    if (tem !== null) {
      param.modifier = uerId;
      param.id = tem.id;
    } else {
      param.creator = uerId;
    }

    try {
      setLoading(true);
      const res = tem ? await update(param) : await add(param);
      if (res.success) {
        setLoading(false);
        success(`${tem ? '修改' : '新增'}成功！`);
        onOk();
      } else {
        error(res.errorMessage);
        setLoading(false);
      }
    } catch {
      setLoading(false);
    }
  };

  /**
   * 重置表单
   */
  const handleCancel = () => {
    form.resetFields();
  };

  return (
    <Modal
      title={`${initialValues ? '编辑' : '新增'}`}
      maskClosable={false}
      onCancel={handleCancel}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={[
        <Button key="back" onClick={rest.onCancel}>
          取消
        </Button>,
        <Button key="submit" loading={loading} type="primary" onClick={submit}>
          确定
        </Button>,
      ]}
    >
      <Form {...formProps}>
        {/* 网点代码 */}
        <Col md={11}>
          <FormItem
            label="网点代码"
            name="zoneCode"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '请输入网点代码',
              },
            ]}
          >
            <DeptSearch
              allowClear
              disabled={tem}
              placeholder="请输入网点代码"
              customSearch={{ orgCode: 'SX' }}
              onChange={handleOnZoneCodeChnage}
            />
          </FormItem>
        </Col>

        {/* 网点名称 */}
        <FormItem label="网点名称" name="zoneName" hidden>
          <Input disabled placeholder="请输入" />
        </FormItem>

        {/* 用工类型 */}
        <Row gutter={{ md: 4 }}>
          <Col md={11}>
            <FormItem
              label="用工类型"
              name="employType"
              rules={[
                {
                  required: true,
                  message: '请选择用工类型',
                },
              ]}
            >
              <Select
                disabled={tem}
                options={employTypeList}
                placeholder="请选择用工类型"
                onChange={handleResetOnEmployTypeChange}
                allowClear
              ></Select>
            </FormItem>
          </Col>
        </Row>

        {/* 外包-供应商名称 */}
        <Row gutter={{ md: 4 }}>
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.zoneCode !== curValues.zoneCode
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [2].includes(getFieldValue('employType')) && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="供应商名称"
                      name="supplierName"
                      rules={[
                        {
                          required: true,
                          message: '请选择',
                        },
                      ]}
                    >
                      <Select
                        onChange={handleOnSupplierChange}
                        disabled={tem}
                        placeholder="请选择供应商"
                        options={arrList}
                        allowClear
                        showSearch
                        filterOption={(input, option) =>
                          (option?.label ?? '')
                            .toLowerCase()
                            .includes(input.toLowerCase())
                        }
                      ></Select>
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem label="供应商编码" name="supplierCode">
                      <Input disabled placeholder="请输入" />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>
        </Row>

        {/* 计费类型 */}
        <Row gutter={{ md: 4 }}>
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType
            }
            noStyle
          >
            {({ getFieldValue }) => (
              <Col md={11}>
                <FormItem
                  label="计费类型"
                  name="accrueType"
                  rules={[
                    {
                      required: true,
                      message: '请选择',
                    },
                  ]}
                >
                  {/* 1计费重量、2系数、5计板、9板均、10固定绩效、11卡均、12叉车计重 */}
                  <Select
                    disabled={tem}
                    placeholder="请选择计费类型"
                    options={[
                      { value: 1, label: '计费重量' },
                      { value: 5, label: '计板' },
                      ...(getFieldValue('employType') === 1
                        ? [
                            { value: 9, label: '板均' },
                            { value: 10, label: '固定绩效' },
                            { value: 11, label: '卡均' },
                            { value: 12, label: '叉车计重' },
                            { value: 2, label: '系数' },
                            { value: 13, label: '大小件' },
                            { value: 14, label: '计卡口' },
                          ]
                        : []),
                    ]}
                    onChange={handleResetOnAccrueTypeChange}
                    allowClear
                  ></Select>
                </FormItem>
              </Col>
            )}
          </Form.Item>
        </Row>

        <Row gutter={{ md: 4 }}>
          {/* 自有-计费重量 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 1 &&
              getFieldValue('accrueType') === 1 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="省内装车单价"
                      name="provinceLoadPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="省际装车单价"
                      name="interprovincialLoadPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="支线装车单价"
                      name="branchLoadPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="卸车单价"
                      name="unloadPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="皮带机卸车单价"
                      name="beltUnloadPrice"
                      rules={[...price]}
                    >
                      <Input
                        placeholder="请输入数字，最多保留两位小数"
                        addonAfter="元/T"
                      />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="装卸车月保底"
                      name="loadUnloadMonMin"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="T" />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>

          {/* 自有-计板 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 1 &&
              getFieldValue('accrueType') === 5 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="叉车单价"
                      name="forkliftPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/板" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车月保底"
                      name="forkliftMonMin"
                      rules={[...requiredRule, ...price]}
                    >
                      <Input placeholder="请输入" addonAfter="板" />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>

          {/* 自有-板均 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 1 &&
              getFieldValue('accrueType') === 9 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="叉车单价"
                      name="forkliftPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车月保底"
                      name="forkliftMonMin"
                      rules={[...requiredRule, ...price]}
                    >
                      <Input placeholder="请输入" addonAfter="T" />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>

          {/* 自有-固定绩效 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 1 &&
              getFieldValue('accrueType') === 10 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="绩效"
                      name="forkliftFixedPrice"
                      rules={[
                        { required: true, message: '请选择' },
                        ...priceInt,
                      ]}
                    >
                      <Input placeholder="请输入" addonAfter="元/月" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车类型"
                      name="forkliftType"
                      rules={[{ required: true, message: '请选择' }]}
                    >
                      <Select
                        options={getForkliftTypeList(
                          getFieldValue('accrueType'),
                        )}
                        placeholder="请选择"
                        allowClear
                      ></Select>
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>
          {/* 自有-固定绩效-机叉 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType ||
              prevValues.forkliftType !== curValues.forkliftType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 1 &&
              getFieldValue('accrueType') === 10 &&
              getFieldValue('forkliftType') === 4 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="提成类型"
                      name="commissionType"
                      rules={[{ required: true, message: '请选择' }]}
                    >
                      <Select
                        options={commissionTypeList}
                        placeholder="请选择"
                        allowClear
                        onChange={value => {
                          setAddAfter(value === 4 ? '元/板' : '元/T');
                        }}
                      ></Select>
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车提成单价"
                      name="forkliftPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter={addAfter} />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>

          {/* 自有-卡均 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 1 &&
              getFieldValue('accrueType') === 11 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="叉车单价"
                      name="forkliftPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车月保底"
                      name="forkliftMonMin"
                      rules={[...requiredRule, ...price]}
                    >
                      <Input placeholder="请输入" addonAfter="T" />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>

          {/* 自有-叉车计重 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 1 &&
              getFieldValue('accrueType') === 12 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="叉车单价"
                      name="forkliftPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车月保底"
                      name="forkliftMonMin"
                      rules={[...price]}
                    >
                      <Input placeholder="请输入" addonAfter="T" />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>

          {/* 自有-系数 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 1 &&
              getFieldValue('accrueType') === 2 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="叉车单价"
                      name="forkliftPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车月保底"
                      name="forkliftMonMin"
                      rules={[...requiredRule, ...price]}
                    >
                      <Input placeholder="请输入" addonAfter="T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车计重货量占比"
                      name="forkliftWeightPercent"
                      rules={[...priceInt100]}
                    >
                      <Input
                        placeholder="请输入"
                        addonAfter="%"
                        onChange={({ target: { value: val } }) => {
                          const remain = 100 - val;
                          form.setFieldsValue({
                            cardWeightPercent: remain,
                          });
                        }}
                      />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label={
                        <span className="label-required no-indent">
                          卡均板均货量占比
                        </span>
                      }
                    >
                      <div className={style.cardPalletFetchSelect}>
                        <Form.Item
                          name="cardPalletFetch"
                          noStyle
                          rules={[
                            {
                              required: true,
                              message: '请选择卡均/板均货量占比选项',
                            },
                          ]}
                        >
                          <Select
                            options={Object.values(CARD_PALLET_FETCH_OBJ)}
                            placeholder="请选择卡均/板均货量占比选项"
                          />
                        </Form.Item>

                        <Form.Item
                          name="cardWeightPercent"
                          noStyle
                          rules={[
                            {
                              required: true,
                              message: '请输入卡均板均货量占比',
                            },
                          ]}
                        >
                          <Input
                            placeholder="请输入卡均板均货量占比"
                            addonAfter="%"
                            disabled
                          />
                        </Form.Item>
                      </div>
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>
          {/* 自有-大小件 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 1 &&
              getFieldValue('accrueType') === 13 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="叉车小件单价"
                      name="forkliftSmallPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车大件单价"
                      name="forkliftBigPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车月保底"
                      name="forkliftMonMin"
                      rules={[...requiredRule, ...price]}
                    >
                      <Input placeholder="请输入" addonAfter="T" />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>
          {/* 外包-计费重量 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 2 &&
              getFieldValue('accrueType') === 1 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="税率"
                      name="taxRate"
                      rules={[{ required: true, message: '请选择税率' }]}
                    >
                      <Select
                        options={TAX_RATE_OPTIONS}
                        placeholder="请选择税率"
                        allowClear
                      ></Select>
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="装车单价"
                      name="loadPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="卸车单价"
                      name="unloadPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车单价"
                      name="forkliftPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/T" />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>

          {/* 外包-计板 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 2 &&
              getFieldValue('accrueType') === 5 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="税率"
                      name="taxRate"
                      rules={[{ required: true, message: '请选择税率' }]}
                    >
                      <Select
                        options={TAX_RATE_OPTIONS}
                        placeholder="请选择税率"
                        allowClear
                      ></Select>
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车单价"
                      name="forkliftPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/板" />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>

          {/* 自有-计卡口 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 1 &&
              getFieldValue('accrueType') === 14 && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="叉车单价"
                      name="forkliftPrice"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="元/个" />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="叉车月保底"
                      name="forkliftMonMin"
                      rules={[...priceRule]}
                    >
                      <Input placeholder="请输入" addonAfter="个" />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>

          {/* 自有-皮带机叉车单价和皮带机分拣单价 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              getFieldValue('employType') === 1 &&
              [5, 9, 11, 12, 13, 14].includes(getFieldValue('accrueType')) && (
                <>
                  <Col md={11}>
                    <FormItem
                      label="皮带机叉车单价"
                      name="beltForkliftPrice"
                      rules={[...price]}
                    >
                      <Input
                        placeholder="请输入数字，最多保留两位小数"
                        addonAfter="元/T"
                      />
                    </FormItem>
                  </Col>
                  <Col md={11}>
                    <FormItem
                      label="皮带机分拣单价"
                      name="beltSortPrice"
                      rules={[...price]}
                    >
                      <Input
                        placeholder="请输入数字，最多保留两位小数"
                        addonAfter="元/T"
                      />
                    </FormItem>
                  </Col>
                </>
              )
            }
          </Form.Item>

          {/* 生效时间 - 统一放在最后，保持原有业务逻辑 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType
            }
            noStyle
          >
            {({ getFieldValue }) => {
              const employType = getFieldValue('employType');
              const accrueType = getFieldValue('accrueType');

              // 保持原有的业务逻辑：只有特定的 employType 和 accrueType 组合才显示生效时间
              const shouldShowTime =
                (employType === 1 &&
                  [1, 5, 9, 10, 11, 12, 13, 14, 2].includes(accrueType)) ||
                (employType === 2 && [1, 5].includes(accrueType));

              return shouldShowTime ? (
                <Col md={11}>
                  <FormItem
                    label="生效时间"
                    name="time"
                    rules={[{ required: true, message: '请选择失效时间' }]}
                  >
                    <RangePicker picker="month" style={{ width: '100%' }} />
                  </FormItem>
                </Col>
              ) : null;
            }}
          </Form.Item>
        </Row>
      </Form>
    </Modal>
  );
};

export default Add;
