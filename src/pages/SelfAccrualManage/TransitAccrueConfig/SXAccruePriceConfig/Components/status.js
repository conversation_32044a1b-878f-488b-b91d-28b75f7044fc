export const accrueTypeList = [
  { label: '计费重量', value: 1 },
  { label: '系数', value: 2 },
  { label: '计板', value: 5 },
  { label: '板均', value: 9 },
  { label: '固定绩效', value: 10 },
  { label: '卡均', value: 11 },
  { label: '叉车计重', value: 12 },
  { value: 13, label: '大小件' },
  { value: 14, label: '计卡口' },
];

export const statusList = [
  {
    label: '待生效',
    value: 0,
  },
  {
    label: '已生效',
    value: 1,
  },
  {
    label: '已过期',
    value: 2,
  },
];

export const employTypeList = [
  {
    label: '自有',
    value: 1,
  },
  {
    label: '外包',
    value: 2,
  },
];

export const forkliftTypeList = [
  {
    label: '电叉',
    value: 3,
  },
  {
    label: '机叉',
    value: 4,
  },
  {
    label: '双库操作员',
    value: 2,
  },
];
// 提成类型 commissionType：1-卡均、2-板均、3-叉车计重、4-计板
export const commissionTypeList = [
  {
    label: '卡均',
    value: 1,
  },
  {
    label: '板均',
    value: 2,
  },
  {
    label: '叉车计重',
    value: 3,
  },
  {
    label: '计板',
    value: 4,
  },
];

export const getForkliftTypeList = accrueType => {
  const arr = [...forkliftTypeList];
  if (accrueType !== 10)
    arr.pop({
      label: '双库操作员',
      value: 2,
    });
  return arr;
};

export const operationStatusList = [
  {
    label: '待审核',
    value: 0,
  },
  {
    label: '审核通过',
    value: 1,
  },
  {
    label: '已驳回',
    value: 2,
  },
];

export const operationList = {
  1: '是否确认审核通过已选供应商数据',
  2: '是否确认驳回已选供应商数据',
};

/** 卡均板均货量占比对象 */
export const CARD_PALLET_FETCH_OBJ = {
  0: { label: '卡均', value: 0 },
  1: { label: '板均', value: 1 },
};
