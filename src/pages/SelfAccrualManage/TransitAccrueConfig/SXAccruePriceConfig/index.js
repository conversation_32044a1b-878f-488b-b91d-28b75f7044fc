import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Input, Modal, Select } from 'antd';
import { PlusOutlined, ExportOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { formItemLayout, colStyle, rowStyle, SearchFold } from 'ky-giant';
import { success, error } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import AuthButton from 'src/components/AuthButton';
import authDecorator from 'src/components/AuthDecorator';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import SuppliersSearch from 'src/components/SuppliersSearch';
import { search, approvalList } from './servers/api';
import Add from './Components/Add';
import {
  accrueTypeList,
  statusList,
  employTypeList,
  forkliftTypeList,
  commissionTypeList,
  operationList,
  operationStatusList,
  CARD_PALLET_FETCH_OBJ,
} from './Components/status';

const { Item: FormItem } = Form;
const { TextArea } = Input;
const rowKey = 'id';

let condition = {};
const Page = ({
  userInfo,
  logRoleCode,
  moduleCode,
  areaListSF,
  areaListSX,
}) => {
  const [form] = Form.useForm();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  const [sfVisible, setSfvisible] = useState(false);
  const [outVisible, setOutvisible] = useState(false);
  const [intZoneCode, setIntZoneCode] = useState(null); // 网点代码
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  let approvalWord = '';

  const columns = [
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'zoneCode',
      render: text => text ?? '--',
    },
    {
      title: '网点名称',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'zoneName',
      render: text => text ?? '--',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: (values, row) =>
        `${row.allocationAreaCode ?? ''} ${values ?? ''}`.trim() ?? '--',
    },
    {
      title: '供应商名称',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'supplierName',
      render: text => text ?? '--',
    },
    {
      title: '供应商编码',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierCode',
      render: text => text ?? '--',
    },
    {
      title: '用工类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'employType',
      render: value => {
        const data = employTypeList.find(item => item.value === value);
        return data?.label ?? '--';
      },
    },
    {
      title: '计费类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueType',
      render: value => {
        const data = accrueTypeList.find(item => item.value === value);
        return data?.label ?? '--';
      },
    },
    {
      title: '税率',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'taxRate',
      render: text => text ?? '--',
    },
    {
      title: '装车单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'loadPrice',
      render: text => text ?? '--',
    },
    {
      title: '省内装车单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'provinceLoadPrice',
      render: text => text ?? '--',
    },
    {
      title: '省际装车单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'interprovincialLoadPrice',
      render: text => text ?? '--',
    },
    {
      title: '支线装车单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'branchLoadPrice',
      render: text => text ?? '--',
    },
    {
      title: '卸车单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'unloadPrice',
      render: text => text ?? '--',
    },
    {
      title: '皮带机卸车单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 130,
      dataIndex: 'beltUnloadPrice',
      render: text => text ?? '--',
    },
    {
      title: '装卸车月保底(T)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'loadUnloadMonMin',
      render: text => text ?? '--',
    },
    {
      title: '叉车单价',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'forkliftPrice',
      render: text =>
        // 如果是顺心自有且是固定 绩效，这个叉车单价就是叉车提成单价
        text ?? '--',
    },
    {
      title: '叉车小件单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'forkliftSmallPrice',
      render: text =>
        // 如果是顺心自有且是固定 绩效，这个叉车单价就是叉车提成单价
        text ?? '--',
    },
    {
      title: '叉车大件单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'forkliftBigPrice',
      render: text =>
        // 如果是顺心自有且是固定 绩效，这个叉车单价就是叉车提成单价
        text ?? '--',
    },
    {
      title: '皮带机叉车单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 130,
      dataIndex: 'beltForkliftPrice',
      render: text => text ?? '--',
    },
    {
      title: '皮带机分拣单价(元/T)',
      align: 'center',
      ellipsis: true,
      width: 130,
      dataIndex: 'beltSortPrice',
      render: text => text ?? '--',
    },
    {
      title: '叉车月保底',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'forkliftMonMin',
      render: text => text ?? '--',
    },
    {
      title: '叉车类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'forkliftType',
      render: value => {
        const data = forkliftTypeList.find(item => item.value === value);
        return data?.label ?? '--';
      },
    },
    {
      title: '提成类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'commissionType',
      render: value => {
        const data = commissionTypeList.find(item => item.value === value);
        return data?.label ?? '--';
      },
    },
    {
      title: '固定绩效',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'forkliftFixedPrice',
      render: text => text ?? '--',
    },
    // {
    //   title: '叉车提成单价',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 100,
    //   dataIndex: 'forkliftPrice',
    //   render: text => text ?? '--',
    // },
    {
      title: '叉车计重货量占比(%)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'forkliftWeightPercent',
      render: text => text ?? '--',
    },
    {
      title: '卡均/板均货量占比选项',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'cardPalletFetch',
      render: v => CARD_PALLET_FETCH_OBJ[v]?.label ?? '--',
    },
    {
      title: '卡均/板均货量占比(%)',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'cardWeightPercent',
      render(v) {
        return v ?? '--';
      },
    },
    {
      title: '状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'status',
      render: value => {
        const data = statusList.find(item => item.value === value);
        return data?.label ?? '--';
      },
    },
    {
      title: '生效时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'startTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '失效时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'endTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },

    {
      title: '创建人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'creator',
      render: text => text ?? '--',
    },
    {
      title: '创建时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'createTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'modifier',
      render: text => text ?? '--',
    },
    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '审核人',
      dataIndex: 'approvalUserNo',
      width: 180,
      render: text => text ?? '--',
    },
    {
      title: '审核时间',
      dataIndex: 'approvalTime',
      width: 180,
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '审核状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'approvalStatus',
      render: value => {
        const data = operationStatusList.find(item => item.value === value);
        return data?.label ?? '--';
      },
    },
    {
      title: '备注',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'approvalRemark',
      render: text => text ?? '--',
    },
    {
      title: '操作',
      align: 'center',
      width: 200,
      fixed: 'right',
      dataIndex: 'a',
      render: (v, record) => (
        <Fragment>
          <AuthButton
            moduleCode={moduleCode}
            code="sxAccrualPriceConfig-edit"
            size="small"
            type="link"
            onClick={e => update(e, record)}
          >
            编辑
          </AuthButton>
          {record.approvalStatus === 0 && (
            <AuthButton
              size="small"
              moduleCode={moduleCode}
              code="sxAccrualPriceConfig-adopt"
              type="link"
              onClick={e => adoptUpdate(e, record, 1)}
            >
              审核通过
            </AuthButton>
          )}
          {record.approvalStatus === 0 && (
            <AuthButton
              moduleCode={moduleCode}
              code="sxAccrualPriceConfig-reject"
              size="small"
              type="link"
              onClick={e => adoptUpdate(e, record, 2)}
            >
              驳回
            </AuthButton>
          )}
        </Fragment>
      ),
    },
  ];

  const adoptUpdate = async (e, v, approvalStatus) => {
    e.stopPropagation();
    operationModal([v.id], approvalStatus);
  };

  const operationModal = (idList, approvalStatus) => {
    Modal.confirm({
      title: '提示',
      content: (
        <div>
          <p>{operationList[approvalStatus]}</p>
          {approvalStatus === 2 && (
            <div style={{ display: 'flex' }}>
              <p>请反馈原因:</p>
              <TextArea
                showCount
                maxLength={200}
                onChange={e => {
                  approvalWord = e.target.value;
                }}
              />
            </div>
          )}
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const params = { idList, approvalStatus, approvalWord };
        const res = await approvalList(params);
        if (res.success) {
          success('操作成功');
          approvalWord = '';
          refresh();
        } else {
          error(res.errorMessage);
        }
      },
      onCancel() {
        approvalWord = '';
      },
    });
  };

  const getQueryParams = () => {
    const params = form.getFieldValue();
    condition = params;

    return {
      ...params,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = { condition, current, size: pageSize };
    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum, total } = obj;
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: pageNum,
              pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const add = () => {
    setAddModalVisible(true);
    setOutvisible(false);
  };

  const update = async (e, v) => {
    e.stopPropagation();
    setEditingTarget(v);
    setSfvisible(true);
    setOutvisible(v.employType);
    setAddModalVisible(true);
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.zoneCode !== curValues.zoneCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <FormItem name="supplierCode" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      isName={false}
                      allowClear
                      gysParams={getFieldValue('zoneCode') || userInfo.deptCode}
                    />
                  </FormItem>
                </Col>
              )}
            </FormItem>
            <Col {...colStyle}>
              <FormItem label="用工类型" name="employType">
                <Select
                  allowClear
                  options={employTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="生效状态" name="status">
                <Select
                  allowClear
                  options={statusList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="审核状态" name="approvalStatus">
                <Select
                  allowClear
                  options={[
                    { label: '待审核', value: 0 },
                    { label: '审核通过', value: 1 },
                    { label: '已驳回', value: 2 },
                  ]}
                  placeholder="请选择审核状态"
                ></Select>
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AuthButton
            moduleCode={moduleCode}
            code="sxAccrualPriceConfig-add"
            icon={<PlusOutlined />}
            style={{ marginRight: 15 }}
            type="primary"
            onClick={() => add()}
          >
            新增
          </AuthButton>
          {moduleCode.includes('sxAccrualPriceConfig-export') && (
            <AsyncExport
              text="导出"
              style={{ marginRight: 15 }}
              icon={<ExportOutlined />}
              disabled={datas.list && datas.list.length < 1}
              options={{
                filename: '计提单价配置.xlsx',
                requstParams: [
                  `/tdmsAccrueService/accruePriceInfoSxService/asyncExportAccruePrice`,
                  {
                    method: 'POST',
                    body: getQueryParams,
                  },
                ],
              }}
            />
          )}
        </div>
      </div>
    </Form>
  );

  useEffect(() => {
    form.setFieldsValue({ zoneCode: userInfo?.deptCode });
    if (logRoleCode && userInfo) {
      setIntZoneCode(userInfo.deptCode);
    }
    getQueryParams();
    refresh({ currentPage: 1 });
  }, [userInfo, logRoleCode]);

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey={rowKey}
        selectedRows={selectedRows}
        loading={loading}
        data={datas}
        columns={columns}
        rowSelection={false}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          initialValues={editingTarget}
          roleCode={logRoleCode.roleCode}
          intZoneCode={intZoneCode}
          visible={addModalVisible}
          sfvisible={sfVisible}
          outvisible={outVisible}
          userInfo={userInfo}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh({ currentPage: editingTarget ? undefined : 1 });
          }}
          width={1000}
        />
      )}
    </div>
  );
};
@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
@authDecorator
class Container extends PureComponent {
  render() {
    const {
      moduleCode,
      userInfo,
      logRoleCode,
      areaListSF,
      areaListSX,
    } = this.props;
    return (
      <Page
        moduleCode={moduleCode}
        userInfo={userInfo}
        logRoleCode={logRoleCode}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
      />
    );
  }
}

export default withRouter(Container);
