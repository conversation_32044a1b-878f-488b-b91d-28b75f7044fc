import React, { PureComponent, useEffect, useState } from 'react';
import { connect } from 'dva';
import { Col, DatePicker, Form, Input, Row, Select, Tooltip } from 'antd';
import {
  colStyle,
  formItemLayout,
  rangePickerColStyle,
  rangePickerLayout,
  rowStyle,
  SearchFold,
  useModuleCode,
} from 'ky-giant';

import { ExportOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';

import moment from 'moment';
import { cloneDeep } from 'lodash';
import ExportButton from 'src/components/ExportButton';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch';
import AsyncExport from 'src/components/AsyncExport';
import { read } from './servers/api';
import { operateTypeList, statusList } from './Components/status';

import './index.scss';

const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;

let inputParam = {};
const Page = ({ userInfo, logRoleCode, areaListSX, areaListSF }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [selectedRows, setSelectedRows] = useState([]);
  const [dates, setDates] = useState([moment().subtract(2, 'days'), moment()]);
  // const [datesLimit, setDatesLimit] = useState([]);
  const [value, setValue] = useState();
  const [hackValue, setHackValue] = useState();
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  // const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);

  // const valueRef = useRef(value);
  // valueRef.current = value;
  const columns = [
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'zoneCode',
    },
    // {
    //   title: '分拨区',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 150,
    //   dataIndex: 'allocationArea',
    //   render: values => {
    //     const areaList = areaListSF.concat(areaListSX);
    //     const date = areaList.find(item => item.value === values);
    //     return date ? date.label : values;
    //   },
    // },
    {
      title: '运单号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'waybillNo',
    },
    {
      title: '母单号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'mainWaybillNo',
    },
    {
      title: '产品名称',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'productCode',
    },
    {
      title: '计费重量',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'meterageWeight',
    },
    {
      title: '车标号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'carNo',
    },
    {
      title: '是否绑定托盘',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'palletBinding',
      render: text => {
        switch (text) {
          case 0:
            return '否';
          case 1:
            return '是';
          default:
            return '';
        }
      },
    },
    {
      title: '托盘号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'palletNo',
    },
    {
      title: '是否内部件',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'innerWaybill',
      render: text => {
        switch (text) {
          case 0:
            return '否';
          case 1:
            return '是';
          default:
            return '';
        }
      },
    },
    {
      title: '状态',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'status',
      render: v => {
        const date = statusList.find(item => item.value === v);
        return date ? date.label : v;
      },
    },
    {
      title: '扫描人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'creator',
    },
    {
      title: '扫描时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'scanTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '作业环节',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'operateType',
      render: v => {
        const date = operateTypeList.find(item => item.value === v);
        return date ? date.label : v;
      },
    },
    {
      title: '任务号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'taskId',
    },
    {
      title: '子任务',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'flowId',
    },
    {
      title: '月台号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'platformNumber',
    },
    {
      title: '卡口号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'inPortCode',
    },
  ];

  // 查询参数的获取
  const getQueryParams = () => {
    const params = form.getFieldValue();
    if (params.time && params.time.length === 2) {
      params.scanTimeStart = moment(params.time[0]).format('x');
      params.scanTimeEnd = moment(params.time[1]).format('x');
    }
    inputParam = cloneDeep(params);
    delete inputParam.time;
    refresh({ current: 1, pageSize: 10 });
    return {
      ...inputParam,
    };
  };
  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      pageNum = pagination.current,
      pageSize = pagination.pageSize,
    } = pagination;
    const param = { ...inputParam, pageNum, pageSize };
    read(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { rows, total } = obj;
          // setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.currentPage,
              pageSize: obj.limit,
            },
            list: rows,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 权限查询
  // const getCurrentRoleCode = () => {
  //   const roleId = sessionStorage.getItem('roleId');
  //   if (roleId && systemRole && systemRole.length) {
  //     const role = systemRole.find(item => item.roleId === roleId);
  //     if (role) {
  //       setRoleCode(role.roleCode);
  //     }
  //   }
  // };

  const onOpenChange = open => {
    if (open) {
      // form.setFieldsValue({ time: [] });
      // setHackValue([]);
      // setDates([null, null]);
    } else {
      // setHackValue(undefined);
      // setTimeout(() => {
      //   if (!valueRef.current) {
      //     form.setFieldsValue({
      //       time: [moment().subtract(2, 'days'), moment()],
      //     });
      //   } else {
      //     form.setFieldsValue({
      //       time: valueRef.current,
      //     });
      //   }
      // }, 0);
      // setDates(null);
    }
  };

  const disabledDate = current => {
    if (!dates) {
      return false;
    }

    const cur = current.valueOf();
    const left = dates[0]?.valueOf() ?? 0;
    const right = dates[1]?.valueOf() ?? Infinity;
    const day = 24 * 60 * 60 * 1000;

    if (dates[0] && dates[1]) {
      if (left < cur && cur < right) {
        return false;
      }
      if (cur <= left) {
        return dates[1].valueOf() - current.valueOf() > 2 * day;
      }
      if (cur >= right) {
        return -dates[0].valueOf() + current.valueOf() > 2 * day;
      }
      return false;
    }
    if (dates[0]) {
      return Math.abs(current.valueOf() - dates[0].valueOf()) > 2 * day;
    }
    if (dates[1]) {
      return Math.abs(current.valueOf() - dates[1].valueOf()) > 2 * day;
    }
    return false;
  };

  useEffect(() => {
    if (userInfo.deptCode && userInfo.areaCode) {
      form.setFieldsValue({
        time: [moment().subtract(2, 'days'), moment()],
        // zoneCode:
        //   logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        zoneCode: userInfo.deptCode,
      });
      getQueryParams();
    }
  }, [userInfo]);

  const handleStandardTableChange = pagination => {
    refresh({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={{
        time: [moment().subtract(2, 'days'), moment()],
        zoneCode: userInfo.deptCode,
        operateType: 1,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="网点"
                name="zoneCode"
                rules={[
                  {
                    required: true,
                    message: '请输入',
                  },
                ]}
                // rules={[
                //   ({ getFieldValue }) => ({
                //     validator(_, values) {
                //       if (getFieldValue('allocationArea') || values) {
                //         return Promise.resolve();
                //       }
                //       return Promise.reject(
                //         new Error('分拨区和网点不能同时为空'),
                //       );
                //     },
                //   }),
                // ]}
              >
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  // disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            {/* <Col {...colStyle}>
              <FormItem
                label="分拨区"
                name="allocationArea"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, values) {
                      if (getFieldValue('zoneCode') || values) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区和网点不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  // disabled={logRoleCode.roleCode === '88888888888'}
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col> */}
            <Col {...colStyle}>
              <FormItem label="运单号" name="waybillNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="作业环节"
                name="operateType"
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <Select
                  allowClear
                  options={operateTypeList}
                  placeholder="请选择"
                  onChange={val => {
                    if ([14, 15].includes(val)) {
                      form.setFieldsValue({ carNo: undefined });
                    }
                  }}
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务号" name="taskId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>

            <Col {...colStyle}>
              <FormItem label="状态" name="status">
                <Select
                  allowClear
                  options={statusList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...rangePickerColStyle}>
              <FormItem
                label={
                  <p style={{ marginBottom: 0 }}>
                    扫描时间
                    <Tooltip title="限制查询三天数据">
                      <QuestionCircleOutlined />
                    </Tooltip>
                  </p>
                }
                name="time"
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
                {...rangePickerLayout}
              >
                <RangePicker
                  showTime
                  allowClear
                  style={{ width: '100%' }}
                  value={dates || value}
                  disabledDate={disabledDate}
                  onCalendarChange={val => setDates(val)}
                  onChange={val => {
                    setDates(val);
                    setValue(val);
                  }}
                  onOpenChange={onOpenChange}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem dependencies={['operateType']} noStyle>
                {({ getFieldValue }) => (
                  <FormItem label="车标号" name="carNo">
                    <Input
                      placeholder="请输入"
                      allowClear
                      disabled={[14, 15].includes(getFieldValue('operateType'))}
                    />
                  </FormItem>
                )}
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="扫描人" name="creator">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div style={{ color: '#df2e3f', paddingBottom: '16px' }}>
          原“装卸车重复扫描”，可协助查询装卸车中运单的归属任务任务号、车标和扫描时间
        </div>
        <div>
          <ExportButton
            type="primary"
            text="导出所选"
            style={{ marginRight: 15 }}
            moduleCode={moduleCode}
            code="liftTruckRepetitionScan-exportSelect"
            icon={<ExportOutlined />}
            disabled={!selectedRows.length}
            options={{
              // total: totalNum,
              filename: '装卸车重复扫描列表导出.xlsx',
              requstParams: [
                `/tdmsAccrueService/itemQueryService/exportRepeatItemByIds`,
                {
                  method: 'POST',
                  body: {
                    ids: selectedRows.map(({ id }) => id),
                    operateType: inputParam.operateType,
                  },
                },
              ],
            }}
          />
          <AsyncExport
            type="primary"
            style={{ marginRight: 15 }}
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            modulecode={moduleCode}
            code="liftTruckRepetitionScan-exportAll"
            text="导出全部"
            options={{
              requstParams: [
                '/tdmsAccrueService/itemQueryService/exportRepeatItem',
                {
                  method: 'POST',
                  body: inputParam,
                },
              ],
            }}
          />
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        // showSelection={false}
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        loading={loading}
        data={datas}
        columns={columns}
        onChange={handleStandardTableChange}
      />
    </div>
  );
};

@connect(state => ({
  userInfo: state.global.userInfo,
  logRoleCode: state.global.logRoleCode,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const { userInfo, logRoleCode, areaListSX, areaListSF } = this.props;
    return (
      <Page
        userInfo={userInfo}
        logRoleCode={logRoleCode}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
      />
    );
  }
}

export default withRouter(Container);
