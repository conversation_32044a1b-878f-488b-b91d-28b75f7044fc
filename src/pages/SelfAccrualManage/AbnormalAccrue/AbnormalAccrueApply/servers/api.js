import request from 'src/utils/request';

// 查询
export function search(params) {
  return request(`/tdmsAccrueService/accrueExceptionManageRest/query`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 新增
export function add(params) {
  return request(`/tdmsAccrueService/accrueExceptionManageRest/add`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 根据ID查询详情（编辑）
export function searchDetail(params) {
  return request(`/tdmsAccrueService/accrueExceptionManageRest/detail`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 更新
export function update(params) {
  return request(`/tdmsAccrueService/accrueExceptionManageRest/update`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

/**
 * @description: 根据网点查询供应商列表
 * @param {type} orgCode
 * @return:
 */
export function supplierSearch(params) {
  return request(
    `/tdmsAccrueService/supplierRestService/querySupplierByZoneCode`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 根据归属网点和组员工号查询供应商列表
export async function searchSupplierName(params) {
  return request(`/tdmsAccrueService/user/queryStaffAttendInInfoV2`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
// 叉车里程异常-根据归属网点和组员工号查询供应商列表-使用旧接口
export async function searchVendorSupplierName(params) {
  return request(`/tdmsAccrueService/user/queryOperateEmpListByUpmGrs`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 根据归属网点和组员工号查询供应商列表
export async function queryFemSupplierByZoneCode(params) {
  return request(
    `/tdmsAccrueService/supplierRestService/queryFemSupplierByZoneCode`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 货量&班组-通过任务号查询子任务号详情
export async function searchTaskDetail(params) {
  return request(`/tdmsAccrueService/accrueRestService/queryAccrueTask`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 叉车里程异常-查询内容
export async function queryForkLiftKm(params) {
  return request(
    `/restApi/fcamsForkliftServices/forkLiftAccrueManage/queryForkLiftKm`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 工时异常-通过任务号查询子任务号详情
export async function workTimeAbnormal(params) {
  return request(
    `/restApi/fcamsForkliftServices/sorterForklift/queryDetailNew`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 审批
export async function approveHandel(params) {
  return request(`/tdmsAccrueService/accrueExceptionRest/approval`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 批量审批
export async function batchApply(params) {
  return request(`/tdmsAccrueService/accrueExceptionRest/approvalList`, {
    method: 'POST',
    body: params,
  });
}

// 获取原始货量
export async function getRiginTotalNum(params) {
  return request(
    `/tdmsAccrueService/accrueExceptionManageRest/queryOriginTotalNum`,
    {
      method: 'POST',
      body: params,
    },
  );
}

// 获取月台号列表
export async function queryPlatformList(params) {
  return request(`/sdmBasicService/platformInfo/queryPlatformWeb`, {
    method: 'POST',
    body: params,
  });
}

// 获取格口列表
export async function queryPlatformNo(params) {
  return request(
    `/restApi/fcamsForkliftServices/forkliftAppApi/queryPlatformNo`,
    {
      method: 'POST',
      body: params,
    },
  );
}

// 获取卡口号列表 入参{"zoneCode":"755VF"}  出参：[{"nextStation":"下一网点编码","portCode":"卡口号"}]
export async function queryInPortCodes(params) {
  return request(
    `/tdmsAccrueService/accrueExceptionManageRest/getPortMappingByZoneCode`,
    {
      method: 'POST',
      body: params,
    },
  );
}
