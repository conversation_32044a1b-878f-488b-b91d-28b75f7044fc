import React, { useState, useEffect, useRef } from 'react';

import {
  Form,
  Row,
  Col,
  Button,
  Input,
  Card,
  Select,
  message,
  DatePicker,
  Checkbox,
} from 'antd';
// import './index.scss';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { error } from 'src/utils/utils';
import BatchOperation from '../BatchOperation';

import {
  searchSupplierName,
  queryFemSupplierByZoneCode,
} from '../../servers/api';
const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: { span: 2 },
  wrapperCol: { span: 22 },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    span: 22,
    offset: 2,
  },
};
let lists = [];

const DynamicForm = props => {
  const {
    form,
    propsName,
    disabled,
    // listArr,
    zoneCode,
    startTime,
    ...restProps
  } = props;
  const [supplierList, setSupplierList] = useState([]); // 所有组员的供应商信息
  const [timeId, setTimeId] = useState([]);
  const [daySave, setDaySave] = useState(0); // 天
  const [hourSave, setHourSave] = useState(0); // 小时
  const [minSave, setMinSave] = useState(0); // 分钟
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const [checkedList, setCheckedList] = useState([]);
  const [editValues, setEditValues] = useState([]); // 存放修改后的值
  const [operationVisible, setOperationVisible] = useState(false);
  const [allList, setAllList] = useState([]);

  // 这里改为动态获取，否则 add 后，listArr 传入的还是旧数据
  const listArr = useRef(form.getFieldValue(propsName));

  const range = (start, end) => {
    const result = [];

    for (let i = start; i <= end; i++) {
      result.push(i);
    }
    return result;
  };

  const disabledRangeTime = (dates, partial) => {
    if (dates !== null) {
      // console.log(partial, 'partial');
      if (partial === 'start') {
        setDaySave(
          moment(dates)
            .subtract(0, 'days')
            .format('D'),
        );
        setHourSave(moment(dates).hour());
        setMinSave(moment(dates).minute());
      }
      // const hour = moment(dates).hour(); // 0~23
      // const minute = moment(dates).minute(); // 0~59
      // const second = moment().second(); // 0~59
      // console.log(hour, 'hour');
      // console.log(minute, 'minute');

      // 当日只能选择当前时间之后的时间点
      // console.log(
      //   moment(dates[1])
      //     .subtract(0, 'days')
      //     .format('D'),
      //   '当前选中天',
      // );
      // console.log(
      //   moment(dates)
      //     .subtract(0, 'days')
      //     .format('D'),
      //   '当前天',
      // );
      // console.log(
      //   moment(dates)
      //     .subtract(0, 'days')
      //     .format('D') === daySave,
      //   '前后天数是否对等',
      // );

      if (
        dates &&
        moment(dates)
          .subtract(0, 'days')
          .format('D') === daySave &&
        partial === 'end'
      ) {
        return {
          disabledHours: () => range(0, hourSave > 1 ? hourSave - 1 : hourSave),
          disabledMinutes: () => range(0, minSave + 1),
          // disabledSeconds: () => range(seconds + 1, 59),
        };
      }
    }
  };

  useEffect(() => {
    if (zoneCode) {
      try {
        queryFemSupplierByZoneCode({
          zoneCode,
        }).then(res => {
          if (res.success) {
            if (Array.isArray(res.obj)) {
              const arrLists = res.obj.map(item => ({
                value: `${item.companyName}-${item.companyCode}`,
                label: `${item.companyName}-${item.companyCode}`,
              }));
              setAllList(arrLists);
            } else {
              setAllList([]);
            }
          }
        });
      } catch (err) {
        console.log(err);
      }
    }
  }, [zoneCode]);

  // 计算工时
  const getTimeDates = (dates, index) => {
    const accrueTeams = form.getFieldValue('teamList');
    if (dates !== null && dates[0] && dates[1]) {
      const a = moment(dates[0]).format('x');
      const b = moment(dates[1]).format('x');
      const c = b - a;
      const r = c / 1000 / 60 / 60;
      accrueTeams[index].avgWorks = r.toFixed(1);
      form.setFieldsValue({ accrueTeams });
    } else {
      accrueTeams[index].avgWorks = undefined;
      form.setFieldsValue({
        accrueTeams: undefined,
      });
    }
  };

  // 根据组员工号查询 姓名 归属供应商list
  const getDetailInfor = (value, index) => {
    const accrueTeams = form.getFieldValue('teamList');
    if (value !== '') {
      // 重复组员的判断
      const copyAccrueTeams = cloneDeep(accrueTeams);
      // console.log(copyAccrueTeams, 'copyAccrueTeams');
      if (
        copyAccrueTeams.length > 0 &&
        copyAccrueTeams[index].userName === undefined
      ) {
        copyAccrueTeams.splice(index, 1);
        const a = copyAccrueTeams.some(item => value === item.operateUserNo);
        if (a) {
          accrueTeams[index].operateUserNo = '';
          message.warning('小组内以有此人，请勿重复添加');
          return;
        }
      }

      // 根据工号调接口查询供应商list
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          searchSupplierName({
            operateUserNo: value,
            deptCode: zoneCode,
            includeDelFlag: true,
            workDate: moment(startTime).format('YYYY-MM-DD HH:mm:ss'),
            taskId: form.getFieldValue('taskId'),
            flowId: form.getFieldValue('flowId'),
            operateType: form.getFieldValue('operateType'),
          }).then(res => {
            if (res.success) {
              if (res.obj.length > 0) {
                // 是否自有员工 1 自有
                lists[index] = [];
                res.obj.forEach(item => {
                  if (item.innerFlag === 0) {
                    lists[index].push({
                      ...item,
                      value: `${item.supplierName}-${item.supplierNo}`,
                      label: `${item.supplierName}-${item.supplierNo}`,
                    });
                    accrueTeams[index].operateUserName = item.empName || '';
                    accrueTeams[index].supplierUserNum = '1';
                    accrueTeams[index].userType = 2;
                    accrueTeams[index].supplierNo = '';
                    accrueTeams[index].supplierName = null;
                    accrueTeams[index].originTeamId = '';
                  } else {
                    accrueTeams[index].operateUserName = item.empName || '';
                    accrueTeams[index].supplierUserNum = '1';
                    accrueTeams[index].userType = 1;
                    accrueTeams[index].supplierNo = '';
                    accrueTeams[index].supplierName = null;
                    accrueTeams[index].originTeamId = '';
                  }
                })
                setSupplierList(lists);
                form.setFieldsValue({ accrueTeams });
              } else {
                error('请检查工号是否输入正确');
              }
            } else {
              error('工号查询失败');
            }
          });
        }, 1000),
      );
    }
  };

  // 工时异常-遍历工号查询对应的供应商list
  const handleSupplier = async list => {
    lists = []; // 记录员工的数量 长度
    if (list.length > 0) {
      // !注意：
      // !这里的promise长度与list长度不一致，因为有些组员工号为空，所以不需要查询供应商
      // !如果后面有其他需求，这里需要修改
      const promise = list
        .filter(item => item?.operateUserNo)
        .map(item =>
          searchSupplierName({
            operateUserNo: item.operateUserNo,
            deptCode: zoneCode,
            includeDelFlag: true,
            workDate: moment(startTime).format('YYYY-MM-DD HH:mm:ss'),
            taskId: form.getFieldValue('taskId'),
            flowId: form.getFieldValue('flowId'),
            operateType: form.getFieldValue('operateType'),
          }),
        );

      const resList = await Promise.all(promise);
      const newList = [];
      // eslint-disable-next-line array-callback-return
      resList.map((item, index) => {
        // eslint-disable-next-line array-callback-return
        newList[index] = [];
        item.obj.map(itemValue => {
          if (itemValue.innerFlag === 0) {
            newList[index].push({
              ...itemValue,
              value: `${itemValue.supplierName}-${itemValue.supplierNo}`,
              label: `${itemValue.supplierName}-${itemValue.supplierNo}`,
            });
          } else {
            // 修复 新增时点击供应商下拉没有数据的bug
            newList.push([]);
          }
        });
      });
      lists = newList;

      setSupplierList(newList);
    }
  };

  // 批量上下岗时间后触发
  useEffect(() => {
    if (editValues.length > 0) {
      form.setFieldsValue({
        teamList: editValues,
      });
    }
  }, [editValues]);

  // 元素选择
  const onChangeValue = list => {
    // console.log(list, 'list');
    setCheckedList(list);
    setIndeterminate(!!list.length && list.length < listArr.current.length);
    setCheckAll(list.length === listArr.current.length);
  };

  // 全选按钮
  const onCheckAllChange = e => {
    listArr.current = form.getFieldValue(propsName);
    setCheckedList(
      e.target.checked ? listArr.current.map((_, index) => index) : [],
    );
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };

  const batchOperation = () => {
    setOperationVisible(true);
  };

  const handleCancel = () => {
    setOperationVisible(false);
  };

  useEffect(() => {
    handleSupplier(listArr?.current);
  }, [listArr.current]);

  useEffect(() => {
    listArr.current = form.getFieldValue(propsName);
    console.log(form.getFieldValue(propsName));
  }, [form.getFieldValue(propsName)]);

  return (
    <Form.List name={propsName} {...restProps}>
      {(fields, { add, remove }) => (
        <Card>
          <div
            style={{
              marginLeft: 110,
              marginBottom: 20,
            }}
          >
            <Checkbox
              disabled={disabled}
              indeterminate={indeterminate}
              onChange={onCheckAllChange}
              checked={checkAll}
            >
              全选
            </Checkbox>
            <Button
              type="primary"
              size="small"
              disabled={disabled ? true : checkedList.length < 1}
              onClick={batchOperation}
            >
              批量操作
            </Button>
          </div>
          <Checkbox.Group
            disabled={disabled}
            value={checkedList}
            style={{ width: '100%' }}
            onChange={onChangeValue}
          >
            {fields.map((field, index) => (
              <FormItem
                key={field.name}
                className="style-Bottom"
                label={`${index === 0 ? '组员明细' : ''}`}
                {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
              >
                <Row gutter={8}>
                  <Checkbox
                    value={field.name}
                    style={{ paddingTop: 5, marginLeft: 5, marginRight: 5 }}
                  />
                  <Col md={3} sm={24}>
                    <Form.Item
                      {...field}
                      name={[field.name, 'operateUserNo']}
                      fieldKey={[field.fieldKey, 'operateUserNo']}
                      key={`${field.fieldKey}operateUserNo`}
                    >
                      <Input
                        disabled={disabled}
                        placeholder="请输入工号"
                        allowClear
                        onChange={e => {
                          getDetailInfor(e.target.value, index);
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col md={2} sm={24}>
                    <Form.Item
                      {...field}
                      name={[field.name, 'operateUserName']}
                      fieldKey={[field.fieldKey, 'operateUserName']}
                      key={`${field.fieldKey}operateUserName`}
                    >
                      <Input disabled placeholder="姓名" />
                    </Form.Item>
                  </Col>
                  <Col md={5} sm={24}>
                    <Form.Item noStyle shouldUpdate>
                      {() => (
                        <Form.Item
                          {...field}
                          name={[field.name, 'supplierName']}
                          fieldKey={[field.fieldKey, 'supplierName']}
                          key={`${field.fieldKey}supplierName`}
                        >
                          <Select
                            disabled={
                              listArr.current?.[index]?.userType === 1
                                ? true
                                : disabled
                            }
                            placeholder={
                              supplierList[index] &&
                              supplierList[index].length > 0 &&
                              supplierList[index].value
                                ? '请选择供应商'
                                : '归属供应商:无'
                            }
                            options={supplierList[index] || []}
                            allowClear
                          ></Select>
                        </Form.Item>
                      )}
                    </Form.Item>
                  </Col>
                  <Col md={8} sm={24}>
                    <Form.Item
                      {...field}
                      name={[field.name, 'time']}
                      fieldKey={[field.fieldKey, 'time']}
                      key={`${field.fieldKey}time`}
                    >
                      <RangePicker
                        showTime
                        allowClear
                        disabled={disabled}
                        style={{ width: '100%' }}
                        // onCalendarChange={onCalendarChange}
                        disabledTime={disabledRangeTime}
                        placeholder={['上岗开始时间', '上岗结束时间']}
                        onChange={e => getTimeDates(e, index)}
                      />
                    </Form.Item>
                  </Col>
                  <Col md={3} sm={24}>
                    <Form.Item
                      {...field}
                      name={[field.name, 'avgWorks']}
                      fieldKey={[field.fieldKey, 'avgWorks']}
                      key={`${field.fieldKey}avgWorks`}
                    >
                      <Input
                        disabled
                        // placeholder="请输入工时"
                        allowClear
                        addonAfter="H"
                      />
                    </Form.Item>
                  </Col>
                  <Col md={1} sm={24}>
                    <FormItem {...field}>
                      {!disabled && (
                        <Button
                          type="link"
                          onClick={() => {
                            add();
                          }}
                          style={{
                            color: '#0e7aff',
                          }}
                        >
                          新增
                        </Button>
                      )}
                    </FormItem>
                  </Col>
                  <Col md={1} sm={24}>
                    <FormItem {...field}>
                      {fields.length > 1 && !disabled && (
                        <Button
                          type="link"
                          onClick={() => {
                            remove(field.name);
                            lists.splice(index, 1);
                          }}
                        >
                          删除
                        </Button>
                      )}
                    </FormItem>
                  </Col>
                </Row>
              </FormItem>
            ))}
          </Checkbox.Group>
          {operationVisible && (
            <BatchOperation
              visible={operationVisible}
              handleCancel={handleCancel}
              listArr={listArr.current} // 所有的item
              checkedList={checkedList} // 选择修改的item索引
              setEditValues={setEditValues}
            />
          )}
        </Card>
      )}
    </Form.List>
  );
};

export default DynamicForm;
