import React, { useState, useEffect } from 'react';
import {
  Form,
  Modal,
  Col,
  Row,
  Input,
  Button,
  Radio,
  Select,
  // Upload,
  // message,
  DatePicker,
  notification,
  message,
} from 'antd';
import { cloneDeep } from 'lodash';
import { success, error, timeFixed } from '@/utils/utils';
import moment from 'moment';
import DeptSearch from '@/components/DeptSearch'; // 网点代码查询
// import { UploadOutlined } from '@ant-design/icons';

import {
  add,
  update,
  searchTaskDetail,
  workTimeAbnormal,
  approveHandel,
} from '../../servers/api';
import AccrueGroupDynamicForm from '../AccrueGroupDynamicForm'; // 班组和货量异常的动态表单
import WorkTimeDynamicFrom from '../WorkTimeDynamicFrom'; // 工时异常的动态表单
import { abnormalTypeList, approveSuggest, operateTypeList } from '../status';
const { Item: FormItem } = Form;
const { TextArea } = Input;
// const { Dragger } = Upload;

const ModalHandle = props => {
  const {
    onOk,
    operationType, // 编辑edit--查看check--审批approve--新增add
    visible,
    initialValues,
    zoneCode,
    roleCode,
    ...rest
  } = props;
  // const {typeValue}=initialValues;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [flowidList, setFlowidList] = useState([]);
  const [allObjList, setFAllObjList] = useState([]); // 储存返回结果
  const [isPass, setIsPass] = useState(false); // 驳回原因是否必填
  const [isHide, setIsHide] = useState(false);
  const [titleText, setTitleText] = useState(''); // 弹框类型
  const [timeId, setTimeId] = useState([]);
  const [listArr, setListArr] = useState([]);
  const [zoneCodeNo, setZoneCodeNo] = useState('');

  useEffect(() => {
    setTitleText(getTitle()); // 获取标题
    if (operationType === 'add') {
      form.setFieldsValue({
        status: 0,
      });
    } else {
      if (
        initialValues &&
        initialValues.status === 2 &&
        initialValues.teamList.length > 0
      ) {
        initialValues.list = initialValues.teamList.map(item => {
          const time = [];
          time[0] = moment(item.startWorkTime);
          time[1] = moment(item.endWorkTime);
          item.avgWorks = item.avgWorks ? Number(item.avgWorks) : '';
          delete item.startWorkTime;
          delete item.endWorkTime;
          item.time = time;
          return item;
        });
      }
      form.setFieldsValue({
        status: initialValues.status, // 异常类型
        zoneCode: initialValues.zoneCode, // 网点
        taskId: initialValues.taskId, // 任务号
        flowId: initialValues.flowId, // 子任务号
        operateType: initialValues.operateType, // 作业环节
        originTotalNum: initialValues.originTotalNum, // 货量
        totalNum: initialValues.totalNum, // 申请后货量
        startTime: moment(initialValues.startTime), // 任务开始时间
        finishTime: moment(initialValues.finishTime), // 任务结束时间
        exceptionTxt: initialValues.exceptionTxt, // 异常描述
        teamList: initialValues.teamList,
      });
      if (operationType === 'approve') {
        setIsHide(true);
        form.setFieldsValue({ type: 0 });
      }
      if (operationType === 'check') {
        setIsHide(initialValues.type !== 2);
        form.setFieldsValue({
          type: initialValues.type,
          approvalWord: initialValues.approvalWord,
        });
      }
    }
  }, [visible]);

  useEffect(() => {
    if (roleCode === 'tp00001') {
      form.setFieldsValue({
        zoneCode,
      });
      setZoneCodeNo(zoneCode);
    }
  }, [roleCode, zoneCode]);

  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
    initialValues: {},
  };

  // 获取供应商ID
  const getId = (value, name) => {
    if (value !== null) {
      if (name.indexOf('-') !== -1) {
        return name.substring(name.indexOf('-') + 1, name.length);
      }
      return value;
    }
    return value;
  };

  // 获取供应商name
  const getName = value => {
    if (value !== null) {
      if (value.indexOf('-') !== -1) {
        return value.substring(0, value.indexOf('-'));
      }
      return value;
    }
    return value;
  };

  // 根据任务号查询 子任务号 list 作业环节 货量  班组list
  const getDetailInfor = value => {
    setFlowidList([]);
    form.setFieldsValue({
      flowId: undefined,
      operateType: undefined,
      originTotalNum: '',
      startTime: undefined,
      finishTime: undefined,
      teamList: [
        {
          operateUserNo: '',
          operateUserName: '',
          supplierName: undefined,
        },
      ],
    });
    if (value !== '') {
      const judge = form.getFieldValue('status');
      // const zoneCodeValue = form.getFieldValue('zoneCode');
      if (!zoneCodeNo) {
        message.warning('网点代码不能为空');
        return false;
      }
      // setZoneCodeNo(zoneCodeValue);
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          if (judge === 2) {
            workTimeAbnormal({
              taskId: value,
            }).then(res => {
              if (res.obj && res.success) {
                const { operateType, finishTime, startTime, list } = res.obj;
                if (list && list != null) {
                  const workTimeErrList = list.map(item => ({
                    operateUserNo: item.operateUserNo,
                    operateUserName: item.operateUserName,
                    time: [
                      item.startWorkTime
                        ? moment(item.startWorkTime)
                        : undefined,
                      item.endWorkTime ? moment(item.endWorkTime) : undefined,
                    ],
                    avgWorks: item.avgWorks,
                    userType: item.userType,
                    supplierName: item.supplierName,
                    supplierNo: item.supplierNo,
                    originTeamId: item.id,
                  }));
                  form.setFieldsValue({
                    teamList: workTimeErrList,
                    operateType,
                    finishTime: finishTime ? moment(finishTime) : undefined,
                    startTime: finishTime ? moment(startTime) : undefined,
                  });
                } else {
                  message.warning('该任务号查询为空');
                }
              } else {
                setFlowidList([]);
                form.setFieldsValue({
                  flowId: undefined,
                  operateType: undefined,
                  originTotalNum: '',
                  teamList: [],
                });
                notification.warning({
                  message: '提示',
                  description: '不存在此任务号',
                });
              }
            });
          } else {
            searchTaskDetail({
              taskId: value,
              zoneCode: zoneCodeNo,
            }).then(res => {
              if (res.obj && res.obj.length > 0) {
                setFAllObjList(res.obj);
                // 删除数组中的重复对象
                const newArr = [];
                const arrId = [];
                for (const item of res.obj) {
                  if (arrId.indexOf(item.flowId) === -1) {
                    arrId.push(item.flowId);
                    newArr.push(item);
                  }
                }

                const newArrList = newArr.map(item => ({
                  label: item.flowId,
                  value: item.flowId,
                }));
                setFlowidList(newArrList);
              } else {
                setFlowidList([]);
                form.setFieldsValue({
                  flowId: undefined,
                  operateType: undefined,
                  originTotalNum: '',
                  teamList: [],
                });
                notification.warning({
                  message: '提示',
                  description: '不存在此任务号',
                });
              }
            });
          }
        }, 500),
      );
    } else {
      form.setFieldsValue({
        position: '',
        userName: '',
      });
    }
  };

  // 提交时请求
  const submit = async () => {
    const initialValue = await form.validateFields();
    const param = cloneDeep(initialValue);
    if (param.taskSourceDate && !timeFixed(param.taskSourceDate)) {
      message.warning(
        '当月（任务归属日）班组、工时、货量修改，超过次月6日2359，则不允许发起新增',
      );
      return false;
    }
    // 任务开始结束时间处理
    if (param.startTime && param.finishTime) {
      param.startTime = moment(param.startTime).valueOf();
      param.finishTime = moment(param.finishTime).valueOf();
    }
    // 工时异常动态表单数据处理
    if (param.status === 2) {
      const a = param.teamList.some(item => item.time);
      if (!a) {
        message.warning('上岗和离岗时间不能为空');
        return false;
      }
      param.teamList = param.teamList.map(item => {
        item.startWorkTime = moment(item.time[0]).valueOf();
        item.endWorkTime = moment(item.time[1]).valueOf();
        item.avgWorks = item.avgWorks ? Number(item.avgWorks) : '';
        delete item.time;
        return item;
      });
    }

    // 班组异常供应商id处理
    if (param.status === 1) {
      param.teamList = param.teamList.map(item => {
        if (
          item.userType === 2 &&
          item.operateUserNo &&
          item.operateUserName &&
          item.supplierName
        ) {
          item.supplierNo = item.supplierName
            ? getId(item.supplierNo, item.supplierName)
            : item.supplierNo;
          item.supplierName = getName(item.supplierName);
        }
        return item;
      });
    }
    if (param.status === 0 && param.totalNum >= param.originTotalNum) {
      message.warning('货量修改只能小于当前任务，知悉');
      return false;
    }

    if (initialValues && initialValues.id) {
      Object.assign(param, {
        id: initialValues.id,
      });
    }

    let res;
    if (operationType === 'add') {
      res = await add(param);
    } else if (operationType === 'edit') {
      res = await update(param);
    } else if (operationType === 'approve') {
      res = await approveHandel({
        approvalWord: param.approvalWord,
        staffUuid: initialValues.manageUuid,
        type: param.type,
      });
    }

    setLoading(true);
    if (res && res.success) {
      setLoading(false);
      success(`${titleText}成功！`);
      onOk();
    } else if (res) {
      error(res.errorMessage || '');
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
  };

  const getIndex = value => {
    const newObjList = allObjList.filter(item => item.flowId === value);

    // 班组总人数
    const teamList = newObjList.map(item => ({
      operateUserNo: item.operateUserNo,
      operateUserName: item.userName,
      supplierName: item.supplierName,
      supplierNo: item.supplierNo,
      userType: item.userType,
      originTeamId: item.id,
    }));
    setListArr(teamList);

    form.setFieldsValue({
      operateType: newObjList[0].operateType,
      originTotalNum: newObjList[0].totalWeight,
      startTime: moment(newObjList[0].startTime),
      finishTime: moment(newObjList[0].finishTime),
      taskSourceDate: moment(newObjList[0].taskSourceDate),
      teamList,
    });
  };

  // const propsss = {
  //   name: 'file',
  //   multiple: true,
  //   action: '/tdmsAccrueService/accrueExceptionManageRest/uploadFile',
  //   onChange(info) {
  //     const { status } = info.file;
  //     if (status !== 'uploading') {
  //       // console.log(info.file, info.fileList);
  //     }
  //     if (status === 'done') {
  //       message.success(`${info.file.name} file uploaded successfully.`);
  //     } else if (status === 'error') {
  //       message.error(`${info.file.name} file upload failed.`);
  //     }
  //   },
  // };

  const totalNumRules = [
    { required: true, message: '请输入' },
    {
      pattern: /(^(([1-9]([0-9]+)?)|(0{1}))(\.[0-9]{1,4})?$)/,
      message: '请输入数字，最多保留四位小数',
    },
  ];

  const getInforCode = value => {
    if (value) setZoneCodeNo(value);
  };

  const onChangeValue = e => {
    if (e.target.value === 2) {
      form.setFieldsValue({
        flowId: undefined,
        taskId: undefined,
        operateType: undefined,
        originTotalNum: '',
        startTime: undefined,
        finishTime: undefined,
        exceptionTxt: '',
        teamList: [
          {
            operateUserNo: '',
            operateUserName: '',
            time: [undefined, undefined],
            avgWorks: '',
          },
        ],
      });
    } else {
      form.setFieldsValue({
        flowId: undefined,
        taskId: undefined,
        operateType: undefined,
        startTime: undefined,
        finishTime: undefined,
        originTotalNum: '',
        exceptionTxt: '',
        teamList: [
          {
            operateUserNo: '',
            operateUserName: '',
            supplierName: undefined,
          },
        ],
      });
    }
  };

  const getTitle = () => {
    switch (operationType) {
      case 'edit':
        return '编辑';
      case 'approve':
        return '审批';
      case 'check':
        return '查看';
      case 'add':
        return '新增';
      default:
        throw error;
    }
  };

  const handleSelect = value => {
    if (value === 1) {
      setIsPass(true);
    } else {
      setIsPass(false);
    }
  };

  const footerBtn = [
    <Button key="back" onClick={rest.onCancel}>
      取消
    </Button>,
    <Button key="submit" loading={loading} type="primary" onClick={submit}>
      提交
    </Button>,
  ];

  return (
    <Modal
      title={titleText}
      onCancel={handleCancel}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={operationType === 'check' ? null : footerBtn}
    >
      <Form {...formProps}>
        <Row>
          <Col md={20} sm={24}>
            <FormItem
              label="异常类型"
              name="status"
              labelCol={{ xl: 4 }}
              wrapperCol={{ xl: 20 }}
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Radio.Group
                onChange={onChangeValue}
                options={abnormalTypeList}
                disabled={
                  operationType === 'check' || operationType === 'approve'
                }
              ></Radio.Group>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col md={10} sm={24}>
            <FormItem
              label="网点"
              name="zoneCode"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DeptSearch
                onChange={e => {
                  getInforCode(e);
                }}
                disabled={
                  roleCode === 'tp00001' ||
                  operationType === 'check' ||
                  operationType === 'approve'
                }
                placeholder="请输入中转场名称或者代码"
                allowClear
              />
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col md={10} sm={24}>
            <FormItem
              label="任务号"
              name="taskId"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Input
                placeholder="请输入"
                allowClear
                disabled={
                  operationType === 'check' || operationType === 'approve'
                }
                onBlur={e => {
                  getDetailInfor(e.target.value);
                }}
              />
            </FormItem>
          </Col>
          {/* 工时异常没有 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.status !== curValues.status
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [0, 1].includes(getFieldValue('status')) && (
                <Col md={10} sm={24}>
                  <FormItem
                    label="子任务号"
                    name="flowId"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                  >
                    <Select
                      onChange={getIndex}
                      allowClear
                      options={flowidList}
                      placeholder="请选择"
                      disabled={
                        operationType === 'check' || operationType === 'approve'
                      }
                    ></Select>
                  </FormItem>
                </Col>
              )
            }
          </Form.Item>
          <Col md={10} sm={24}>
            <FormItem label="作业环节" name="operateType">
              <Select
                disabled
                allowClear
                options={operateTypeList}
                placeholder="根据子任务号带出"
              ></Select>
            </FormItem>
          </Col>
          {/* 工时异常没有 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.status !== curValues.status
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [0, 1].includes(getFieldValue('status')) && (
                <Col md={10} sm={24}>
                  <FormItem label="货量" name="originTotalNum">
                    <Input
                      placeholder="根据子任务号带出"
                      disabled
                      allowClear
                      addonAfter="吨"
                    />
                  </FormItem>
                </Col>
              )
            }
          </Form.Item>
          {/* 货量异常 多的 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.status !== curValues.status
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [0].includes(getFieldValue('status')) && (
                <Col md={10} sm={24}>
                  <FormItem
                    label="申请后货量"
                    name="totalNum"
                    rules={[...totalNumRules]}
                  >
                    <Input
                      disabled={
                        operationType === 'check' || operationType === 'approve'
                      }
                      placeholder="请输入"
                      allowClear
                      addonAfter="吨"
                    />
                  </FormItem>
                </Col>
              )
            }
          </Form.Item>
          <Col md={10} sm={24}>
            <FormItem label="任务开始时间" name="startTime">
              <DatePicker
                disabled
                showTime
                allowClear
                placeholder="根据子任务号带出"
                style={{
                  width: '100%',
                }}
              />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="任务结束时间" name="finishTime">
              <DatePicker
                disabled
                showTime
                allowClear
                placeholder="根据子任务号带出"
                style={{
                  width: '100%',
                }}
              />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="任务归属时间" name="taskSourceDate">
              <DatePicker
                disabled
                showTime
                allowClear
                placeholder="根据子任务号带出"
                style={{
                  width: '100%',
                }}
              />
            </FormItem>
          </Col>
          {/* 工时异常 */}
          {/* <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.status !== curValues.status
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [0, 1, 2].includes(getFieldValue('status')) && (
                <Col md={10} sm={24}>
                  <FormItem label="任务开始时间" name="startTime">
                    <DatePicker
                      disabled
                      showTime
                      allowClear
                      placeholder="根据子任务号带出"
                      style={{
                        width: '100%',
                      }}
                    />
                  </FormItem>
                </Col>
              )
            }
          </Form.Item> */}
          {/* <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.status !== curValues.status
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [0, 1, 2].includes(getFieldValue('status')) && (
                <Col md={10} sm={24}>
                  <FormItem label="任务结束时间" name="finishTime">
                    <DatePicker
                      disabled
                      showTime
                      allowClear
                      placeholder="根据子任务号带出"
                      style={{
                        width: '100%',
                      }}
                    />
                  </FormItem>
                </Col>
              )
            }
          </Form.Item> */}
        </Row>
        {/* 班组异常--组员明细 */}
        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.status !== curValues.status
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [1].includes(getFieldValue('status')) && (
              <AccrueGroupDynamicForm
                label="组员"
                propsName="teamList"
                form={form}
                zoneCode={zoneCodeNo}
                listArr={listArr}
                disabled={
                  operationType === 'check' || operationType === 'approve'
                }
              />
            )
          }
        </Form.Item>
        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.status !== curValues.status
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [2].includes(getFieldValue('status')) && (
              <WorkTimeDynamicFrom
                form={form}
                disabled={
                  operationType === 'check' || operationType === 'approve'
                }
                propsName="teamList"
              ></WorkTimeDynamicFrom>
            )
          }
        </Form.Item>
        <Row>
          <Col md={20} sm={24} style={{ marginTop: 25 }}>
            <FormItem
              label="异常描述"
              name="exceptionTxt"
              labelCol={{ xl: 4 }}
              wrapperCol={{ xl: 20 }}
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <TextArea
                showCount
                allowClear
                // placeholder="班组录入错误，申请修改"
                maxLength={500}
                disabled={
                  operationType === 'check' || operationType === 'approve'
                }
              />
            </FormItem>
          </Col>
          {/* <Col md={10} sm={24}>
            <FormItem
              label="上传附件"
              name="fileList"
              labelCol={{ xl: 8 }}
              wrapperCol={{ xl: 16 }}
            >
              <Dragger {...propsss}>
                <p className="ant-upload-drag-icon">
                  <UploadOutlined />
                </p>
                <p className="ant-upload-text">点击上传</p>
              </Dragger>
            </FormItem>
          </Col> */}
          {/* {operationType === 'check' && (
            <Col md={20} sm={24}>
              <FormItem
                label="退回记录"
                name="abnormalMiao"
                labelCol={{ xl: 4 }}
                wrapperCol={{ xl: 20 }}
              >
                <TextArea
                  disabled={
                    operationType === 'check' || operationType === 'approve'
                  }
                  showCount
                  allowClear
                  placeholder="退回记录"
                  maxLength={500}
                />
              </FormItem>
            </Col>
          )} */}
          {(operationType === 'approve' || operationType === 'check') &&
            isHide && (
              <Col md={20} sm={24}>
                <FormItem
                  label="审核状态"
                  name="type"
                  labelCol={{ xl: 4 }}
                  wrapperCol={{ xl: 20 }}
                  rules={[
                    {
                      required: true,
                      message: '请选择',
                    },
                  ]}
                >
                  <Radio.Group
                    disabled={operationType === 'check'}
                    onChange={event => {
                      handleSelect(event.target.value);
                    }}
                    options={approveSuggest}
                  ></Radio.Group>
                </FormItem>
              </Col>
            )}
          {(operationType === 'approve' || operationType === 'check') &&
            isHide && (
              <Col md={20} sm={24}>
                <FormItem
                  label="审核意见"
                  name="approvalWord"
                  labelCol={{ xl: 4 }}
                  wrapperCol={{ xl: 20 }}
                  rules={[
                    {
                      required: isPass,
                      message: '请选择',
                    },
                  ]}
                >
                  <TextArea
                    disabled={operationType === 'check'}
                    showCount
                    allowClear
                    maxLength={500}
                  />
                </FormItem>
              </Col>
            )}
        </Row>
      </Form>
    </Modal>
  );
};

export default ModalHandle;
