import React, { useState, useEffect, useMemo } from 'react';
import {
  Form,
  Modal,
  Col,
  Row,
  Input,
  Button,
  Radio,
  Select,
  DatePicker,
  message,
  notification,
} from 'antd';
import { cloneDeep } from 'lodash';
// import { success, error, timeFixed } from '@/utils/utils';
import moment from 'moment';
import { success, error } from 'src/utils/utils';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询

import {
  add,
  update,
  searchTaskDetail,
  workTimeAbnormal,
  approveHandel,
  queryPlatformList,
  getRiginTotalNum,
  queryForkLiftKm,
  queryPlatformNo,
  queryInPortCodes,
} from '../../servers/api';
import AccrueGroupDynamicForm from '../AccrueGroupDynamicForm'; // 班组和货量异常的动态表单
import WorkTimeDynamicFrom from '../WorkTimeDynamicFrom'; // 工时异常的动态表单
import ExceptionDetailsForm from '../ExceptionDetails';

import {
  abnormalTypeList,
  juegeList,
  approveSuggest,
  operateTypeList,
} from '../status';
const { Item: FormItem } = Form;
const { TextArea } = Input;
const ModalHandle = props => {
  const {
    onOk,
    operationType, // 编辑edit--查看check--审批approve--新增add
    visible,
    initialValues,
    zoneCode,
    roleCode,
    ...rest
  } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [flowidList, setFlowidList] = useState([]); // 子任务号列表
  const [allObjList, setFAllObjList] = useState([]); // 储存返回结果
  const [isPass, setIsPass] = useState(false); // 驳回原因是否必填
  const [cancleBtnDisable, setCancleBtnDisable] = useState(false); // 取消货量折算系数是否置灰
  const [isZyTask, setIsZyTask] = useState(false); // 子任务是否为装运一体任务，控制 取消货量折算系数 展示
  const [isHide, setIsHide] = useState(false);
  const [titleText, setTitleText] = useState(''); // 弹框类型
  const [timeId, setTimeId] = useState([]);
  const [listArr, setListArr] = useState([]);
  const [zoneCodeNo, setZoneCodeNo] = useState('');
  const [selectDatas, setSelectDatas] = useState(''); // 选中的子任务号数据
  const [abnormalType, setAbnormalType] = useState(''); // 异常类型的值
  const [coefficient, setCoefficient] = useState(''); // 折算系数
  const [cargoVol, setCargoVol] = useState('');
  const [platformNoList, setPlatformNoList] = useState([]); // 月台号list
  const [inPortCodesList, setInPortCodesList] = useState([]); // 卡口号list
  const [isDMXLXFJ, setIsDMXLXFJ] = useState(false); // 是否为地面线流向分拣
  const [taskType, setTaskType] = useState(0); // 地面线流向分拣 辅助参数
  const [options, setOptions] = useState(operateTypeList); // 动态显示作业环节的list

  const [zoneCodeValue, setZoneCodeValue] = useState(''); // 储存接口返回的网点值用做提交校验
  useEffect(() => {
    setTitleText(getTitle()); // 获取标题
    if (operationType === 'add') {
      form.setFieldsValue({
        status: 0,
        isVolumeConvert: 0, // 取消货量折算
      });
    } else {
      // 编辑---审批---查看
      setZoneCodeValue(initialValues.zoneCode);
      setCoefficient(initialValues.sharingCoefficient);
      if (initialValues.status === 0) {
        setIsZyTask(
          initialValues.zy === 1 ||
            initialValues.zy === 2 ||
            initialValues.zy === 4,
        );
      }
      if (initialValues.status !== 1 && initialValues.status !== 2) {
        getPlatformList(initialValues);
      }
      if (initialValues.status === 2) {
        // getPlatformNo(initialValues);
        if (initialValues.operateType === 5) {
          getPlatformNo(initialValues);
        } else {
          getPlatformList(initialValues);
        }
      }
      if (initialValues && initialValues.status === 3) {
        form.setFieldsValue({
          operationType: initialValues.operationType,
          exceptionDate: moment(initialValues.exceptionDate),
        });
      }
      // 班组异常
      if (
        initialValues &&
        initialValues.status === 1 &&
        initialValues.teamList.length > 0
      ) {
        setListArr(initialValues.teamList);
      }
      // 工时异常
      if (
        initialValues &&
        initialValues.status === 2 &&
        initialValues.teamList.length > 0
      ) {
        if (initialValues.taskType === 1 && initialValues.operateType === 3) {
          setIsDMXLXFJ(true);
          getInPortCodes(initialValues.zoneCode);
          setOptions([
            {
              label: '地面线流向分拣',
              value: 3,
            },
          ]);
        }
        form.setFieldsValue({
          platformNos:
            initialValues.platformNos === null
              ? undefined
              : initialValues.platformNos,
          inPortCodes:
            initialValues.inPortCodes === null
              ? undefined
              : initialValues.inPortCodes,
        });
        initialValues.list = workTimeHandel(initialValues);
        setListArr(initialValues.teamList);
      }
      // 货量异常--班组异常
      form.setFieldsValue({
        status: initialValues.status, // 异常类型
        isVolumeConvert: initialValues.isVolumeConvert, // 取消货量折算
        platformNo: initialValues.platformNo,
        zoneCode: initialValues.zoneCode, // 网点
        taskId: initialValues.taskId, // 任务号
        flowId: initialValues.flowId, // 子任务号
        operateType: initialValues.operateType, // 作业环节
        originTotalNum: initialValues.originTotalNum, // 货量
        originTotalSxNum: initialValues.originTotalSxNum, // 顺心货量（子任务号为 顺丰时出现）
        totalNum: initialValues.totalNum, // 申请后货量
        totalSxNum: initialValues.totalSxNum, // 申请后顺心货量（子任务号为 顺丰时出现）
        sourceType: initialValues.sourceType,
        startTime: initialValues.startTime
          ? moment(initialValues.startTime)
          : undefined, // 任务开始时间
        finishTime: initialValues.finishTime
          ? moment(initialValues.finishTime)
          : undefined, // 任务结束时间
        // taskSourceDate: initialValues.taskSourceDat
        //   ? moment(initialValues.taskSourceDate)
        //   : undefined, // 任务归属时间
        exceptionTxt: initialValues.exceptionTxt, // 异常描述
        teamList: initialValues.teamList,
      });
      // 审批
      if (operationType === 'approve') {
        setIsHide(true);
        form.setFieldsValue({
          type: 0,
        });
      }
      // 查看
      if (operationType === 'check') {
        setIsHide(initialValues.type !== 2);
        form.setFieldsValue({
          type: initialValues.type,
          approvalWord: initialValues.approvalWord,
          platformNos: initialValues.platformNos || undefined,
        });
      }
    }
  }, [visible]);

  // 工时异常的动态表单数据处理
  const workTimeHandel = list =>
    list.teamList.map(item => {
      const time = [];
      time[0] = moment(item.startWorkTime);
      time[1] = moment(item.endWorkTime);
      item.avgWorks = item.avgWorks ? Number(item.avgWorks) : '';
      item.supplierName = item.supplierName || undefined;
      delete item.startWorkTime;
      delete item.endWorkTime;
      item.time = time;
      return item;
    });

  // 场地负责人置灰网点代码
  useEffect(() => {
    if (roleCode === 'tp00001') {
      form.setFieldsValue({
        zoneCode,
      });
      setZoneCodeNo(zoneCode);
    }
  }, [roleCode, zoneCode]);

  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
    initialValues: {},
  };

  // 获取月台号list
  const getPlatformList = obj => {
    queryPlatformList({
      deptCode: obj.zoneCode,
      module: 2,
    }).then(res => {
      const list = res.obj?.map((item, index) => ({
        label: item.platformNo,
        value: item.platformNo,
        key: index,
      }));
      setPlatformNoList(list);
    });
  };

  // 获取卡口号list
  const getInPortCodes = deptCode => {
    queryInPortCodes({
      zoneCode: deptCode || form.getFieldValue('zoneCode'),
    }).then(res => {
      const list = res.obj?.map((item, index) => ({
        label: item.portCode,
        value: item.portCode,
        key: index,
      }));
      setInPortCodesList(list);
    });
  };

  // 获取格口list
  const getPlatformNo = obj => {
    queryPlatformNo({
      zoneCode: obj.zoneCode,
      module: 2,
    }).then(res => {
      const list = res.obj.map((item, index) => ({
        label: item,
        value: item,
        key: index,
      }));
      setPlatformNoList(list);
    });
  };

  // 获取供应商ID
  const getId = (value, name) => {
    if (value !== null) {
      if (name.indexOf('-') !== -1) {
        return name.substring(name.indexOf('-') + 1, name.length);
      }
      return value;
    }
    return value;
  };

  // 获取供应商name
  const getName = value => {
    if (value !== null) {
      if (value.indexOf('-') !== -1) {
        return value.substring(0, value.indexOf('-'));
      }
      return value || undefined;
    }
    return value || undefined;
  };

  // 请求子任务号列表并过滤
  const dataHandel = async value => {
    const params = { taskId: value, zoneCode: form.getFieldValue('zoneCode') };
    try {
      const objValue =
        abnormalType === 2
          ? await workTimeAbnormal(params)
          : await searchTaskDetail(params);

      if (objValue.obj && objValue.obj.length > 0) {
        setFAllObjList(objValue.obj);
        // 删除数组中的重复对象
        const newArr = [];
        const arrId = [];
        let isReturnFlow = false; // 非班组异常且回流任务 提示不可选
        for (const item of objValue.obj) {
          if (arrId.indexOf(item.flowId) === -1) {
            arrId.push(item.flowId);
            if (item.operateType === 10 && abnormalType !== 1)
              isReturnFlow = true;
            newArr.push(item);
          }
        }
        if (isReturnFlow) {
          message.warning('该异常类型不能新增回流分拣异常申请');
        }
        // (当前审核状态：0正常，1审核中") Integer approvalStatus;
        const newArrList = newArr.map(item => ({
          label: item.flowId,
          value: item.flowId,
          disabled:
            item.approvalStatus === 1 ||
            (abnormalType !== 1 && operationType === 10), // 审核中不可选,非班组异常且回流任务不可选
        }));
        setFlowidList(newArrList);
      }
    } catch (err) {
      // eslint-disable-next-line no-console
      console.log(err);
    }
  };

  // 根据任务号查询 子任务号 list 作业环节 货量  班组list
  const getDetailInfor = value => {
    setFlowidList([]);
    form.setFieldsValue({
      flowId: undefined,
      operateType: undefined,
      originTotalNum: '',
      totalNum: '',
      sourceType: '',
      startTime: undefined,
      finishTime: undefined,
      // taskSourceDate: undefined,
      exceptionTxt: '',
      teamList: [
        {
          operateUserNo: '',
          operateUserName: '',
          supplierName: undefined,
        },
      ],
    });
    if (value !== '') {
      if (!form.getFieldValue('zoneCode')) {
        message.warning('网点代码不能为空');
        return false;
      }
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          dataHandel(value);
        }, 500),
      );
    } else {
      form.setFieldsValue({
        position: '',
        userName: '',
      });
    }
  };

  // 提交时请求
  const submit = async () => {
    const initialValue = await form.validateFields();
    // 因为可以由其他场地进行支援因此去掉这个逻辑
    // if (initialValue.zoneCode !== zoneCodeValue) {
    //   message.warning('请填写与任务关联的网点进行提交！');
    //   return false;
    // }
    const param = cloneDeep(initialValue);
    // 任务开始结束时间处理
    if (param.startTime && param.finishTime) {
      param.startTime = moment(param.startTime).valueOf();
      param.finishTime = moment(param.finishTime).valueOf();
    }

    // 后端校验
    // if (param.status === 0) {
    //   if (param.sourceType === 'SF') {
    //     if (param.originTotalSxNum < param.totalSxNum) {
    //       message.warning('申请后顺心货量只能小于或等于当前顺心货量，请知悉');
    //       return false;
    //     }

    //     if (param.originTotalNum < param.totalNum) {
    //       message.warning('申请后顺丰货量只能小于或等于当前顺丰货量，请知悉');
    //       return false;
    //     }
    //   } else if (param.sourceType === 'SX') {
    //     if (param.totalNum > param.originTotalNum) {
    //       message.warning('货量修改只能小于或等于当前任务总操作货量，请知悉');
    //       return false;
    //     }
    //   }
    // }
    if (param.status === 0) {
      delete param.sourceType;
    }

    // 班组异常供应商id处理
    if (param.status === 1) {
      param.teamList = param.teamList.map(item => {
        if (
          item.userType === 2 &&
          item.operateUserNo &&
          item.operateUserName &&
          item.supplierName
        ) {
          item.supplierNo = item.supplierName
            ? getId(item.supplierNo, item.supplierName)
            : item.supplierNo;
          item.supplierName = getName(item.supplierName);
        }
        return item;
      });
    }
    // 工时异常动态表单数据处理
    if (param.status === 2) {
      param.taskType = taskType;
      const a = param.teamList.every(item => item.time);
      if (!a) {
        message.warning('上岗和离岗时间不能为空');
        return false;
      }
      // 动态表单参数处理
      param.teamList = param.teamList.map(item => {
        item.startWorkTime = moment(item.time[0]).valueOf();
        item.endWorkTime = moment(item.time[1]).valueOf();
        item.avgWorks =
          item.avgWorks !== null || item.avgWorks !== undefined
            ? Number(item.avgWorks)
            : ''; // 兼容为0的情况
        delete item.time;
        if (
          item.userType === 2 &&
          item.operateUserNo &&
          item.operateUserName &&
          item.supplierName
        ) {
          item.supplierNo = item.supplierName
            ? getId(item.supplierNo, item.supplierName)
            : item.supplierNo;
          item.supplierName = getName(item.supplierName);
        }
        return item;
      });
      // 供应商编码缺失的工号
      const useNoList = param.teamList.filter(
        item => !item.supplierNo && item.userType === 2,
      );
      if (useNoList.length !== 0) {
        const useIdList = useNoList
          .map(({ operateUserNo }) => operateUserNo)
          .toString();
        notification.warning({
          message: '操作失败',
          description: (
            <div>
              <span
                style={{
                  color: 'red',
                  fontSize: '16px',
                  fontWeight: 500,
                }}
              >{`${useIdList}`}</span>
              请重新选择供应商！
            </div>
          ),
        });
        return false;
      }
    }

    if (param.status === 3) {
      param.exceptionDate = moment(param.exceptionDate).format('YYYY-MM-DD');
    }

    if (initialValues && initialValues.id) {
      Object.assign(param, {
        id: initialValues.id,
      });
    }
    try {
      setLoading(true);
      let res;
      if (operationType === 'add') {
        res = await add(param);
      } else if (operationType === 'edit') {
        res = await update(param);
      } else if (operationType === 'approve') {
        res = await approveHandel({
          approvalWord: param.approvalWord,
          staffUuid: initialValues.manageUuid,
          type: param.type,
        });
      }
      if (res && res.success) {
        setLoading(false);
        onOk();
        success(`${titleText}成功！`);
      } else if (res) {
        error(res.errorMessage || '');
        setLoading(false);
      }
    } catch {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
  };

  const setZone = v => {
    setZoneCodeValue(v);
  };
  const getForklift = async () => {
    const { operationType, zoneCode, exceptionDate } = form.getFieldValue();
    const time = moment(exceptionDate).format('YYYY-MM-DD');
    const res = await queryForkLiftKm({
      operationType,
      zoneCode,
      exceptionDate: time,
    });
    if (res && res.success) {
      const newTeamList = [];
      if (res.obj && res.obj.length > 0) {
        setZoneCodeValue(res.obj[0].zoneCode);
        res.obj.map(v => {
          newTeamList.push({
            operateUserNo: v.userCode,
            operateUserName: v.userName,
            orginAvgWorks: v.dayOperationVol,
            avgWorks: '',
            userType: v.userType,
            zoneCode: v.zoneCode,
          });
        });
        form.setFieldsValue({
          teamList: newTeamList,
        });
      }
    } else if (res) {
      error(res.errorMessage || '');
    }
  };

  const getIndex = value => {
    const abnormalTypeValue = form.getFieldValue('status'); // 异常类型 0 1 2
    // 取出当前选中的子任务号下对应的网点 做提交时网点填写一致的校验
    const newObjList = allObjList.filter(item => item.flowId === value);
    // console.log(newObjList, 'newObjList');
    if (newObjList.length > 0) {
      setSelectDatas(newObjList[0]);
      setZoneCodeValue(newObjList[0].zoneCode);
      setCancleBtnDisable(newObjList[0].isVolumeConvert === 1);
      setIsZyTask(
        newObjList[0].zy === 1 ||
          newObjList[0].zy === 2 ||
          newObjList[0].zy === 4,
      );
    }
    // 工时异常2
    if (abnormalTypeValue === 2) {
      if (newObjList.length > 0) {
        // 地面流向分拣特殊处理
        setTaskType(newObjList[0].taskType);
        if (newObjList[0].taskType === 1 && newObjList[0].operateType === 3) {
          setIsDMXLXFJ(true);
          getInPortCodes();
          setOptions([
            {
              label: '地面线流向分拣',
              value: 3,
            },
          ]);
        }
        if (newObjList[0].operateType === 5) {
          getPlatformNo(newObjList[0]);
        } else {
          getPlatformList(newObjList[0]);
        }
      }
      let platformNosList = [];
      const workTimeErrList = newObjList.map(item => {
        if (item.platformNos) {
          platformNosList = [...platformNosList, ...item.platformNos];
        }
        return {
          operateUserNo: item.operateUserNo,
          operateUserName: item.operateUserName,
          time: [
            item.startWorkTime ? moment(item.startWorkTime) : undefined,
            item.endWorkTime ? moment(item.endWorkTime) : undefined,
          ],
          avgWorks: item.avgWorks,
          userType: item.userType,
          supplierName: item.supplierName,
          supplierNo: item.supplierNo,
          originTeamId: item.id,
        };
      });

      setListArr(workTimeErrList);
      form.setFieldsValue({
        teamList: workTimeErrList,
        platformNos: [...new Set(platformNosList)],
        inPortCodes: newObjList[0].inPortCodes,
        operateType: newObjList[0].operateType,
        startTime: newObjList[0].startTime
          ? moment(newObjList[0].startTime)
          : undefined,
        finishTime: newObjList[0].finishTime
          ? moment(newObjList[0].finishTime)
          : undefined,
        taskType: newObjList[0].taskType,
      });
    } else {
      // 班组总人数
      const teamList = newObjList.map(item => ({
        operateUserNo: item.operateUserNo,
        operateUserName: item.userName,
        supplierName: item.supplierName,
        supplierNo: item.supplierNo,
        userType: item.userType,
        originTeamId: item.id,
      }));
      // 根据网点和作业类型拉取月台号
      if (newObjList.length > 0) {
        getPlatformList(newObjList[0]);
      }
      setListArr(teamList);
      setCargoVol(newObjList[0].totalWeight);
      setCoefficient(newObjList[0].sharingCoefficient);
      form.setFieldsValue({
        operateType: newObjList[0].operateType,
        isVolumeConvert: newObjList[0].isVolumeConvert,
        originTotalNum:
          newObjList[0].sourceType === 'SF'
            ? newObjList[0].totalSfWeight
            : newObjList[0].totalWeight, // 货量
        originTotalSxNum: newObjList[0].totalSxWeight, // 顺心货量（子任务号为 顺丰时出现）
        totalNum: newObjList[0].totalNum, // 申请后货量
        totalSxNum: newObjList[0].totalSxNum, // 申请后顺心货量（子任务号为 顺丰时出现）
        sourceType: newObjList[0].sourceType,
        platformNo: newObjList[0].platformNo,
        startTime: moment(newObjList[0].startTime),
        finishTime: moment(newObjList[0].finishTime),
        // taskSourceDate: moment(newObjList[0].taskSourceDate),
        teamList,
      });
      // 切换子任务号&&新增--清空申请后货量值
      if (operationType === 'add') {
        form.setFieldsValue({
          totalNum: '', // 申请后货量
        });
      }
    }
  };

  const totalNumRules = [
    { required: true, message: '请输入' },
    {
      pattern: /(^(([1-9]([0-9]+)?)|(0{1}))(\.[0-9]{1,4})?$)/,
      message: '请输入数字，最多保留四位小数',
    },
  ];

  const getInforCode = value => {
    if (value) setZoneCodeNo(value);
  };

  const getRound = (num, len) => Math.round(num * 10 ** len) / 10 ** len;

  // 取消货量折算的切换--给货量和申请后货量赋值
  const getConvertType = async value => {
    // 编辑时
    if (operationType === 'edit') {
      if (value === 1) {
        // 否——》是
        form.setFieldsValue({
          // totalNum: initialValues.totalNum, // 申请后货量
          totalNum: getRound(
            form.getFieldValue('originTotalNum') / coefficient,
            4,
          ), // 申请后货量

          originTotalNum: getRound(
            form.getFieldValue('originTotalNum') / coefficient,
            4,
            // 货量
          ),
        });
      } else {
        // 是=》否
        form.setFieldsValue({
          totalNum: '', // 申请后货量
          originTotalNum: getRound(
            form.getFieldValue('originTotalNum') * coefficient,
            4,
          ), // 货量
        });
      }
      // 新增
    } else if (operationType === 'add') {
      if (value === 1) {
        // setCancelConvert(true);
        // 否——》是
        getRiginTotalNum({
          zoneCode: selectDatas.zoneCode,
          taskId: selectDatas.taskId,
          flowId: selectDatas.flowId,
          sharingCoefficient: selectDatas.sharingCoefficient,
        }).then(res => {
          // console.log(res, 'res');
          form.setFieldsValue({
            totalNum: res.obj || getRound(cargoVol / coefficient, 4), // 申请后货量
            originTotalNum: res.obj || getRound(cargoVol / coefficient, 4),
          });
        });
      } else {
        // 是=》否
        form.setFieldsValue({
          totalNum: '', // 申请后货量
          originTotalNum: cargoVol, // 货量
        });
      }
    }
    // }
  };

  const onChangeValue = e => {
    setAbnormalType(e.target.value);
    form.setFieldsValue({
      flowId: undefined, // 任务号
      taskId: undefined, // 子任务号
      operateType: undefined, // 作业类型
      startTime: undefined, // 任务开始时间
      finishTime: undefined, // 任务结束时间
      // taskSourceDate: undefined, // 任务归属时间
      exceptionTxt: '', // 异常描述
    });
    if (e.target.value === 2) {
      form.setFieldsValue({
        teamList: [
          {
            operateUserNo: '',
            operateUserName: '',
            time: [undefined, undefined],
            avgWorks: '',
          },
        ],
        platformNos: undefined,
      });
    } else if (e.target.value === 1) {
      form.setFieldsValue({
        originTotalNum: '', // 货量
        teamList: [
          {
            operateUserNo: '',
            operateUserName: '',
            supplierName: undefined,
          },
        ],
      });
    } else if (e.target.value === 3) {
      form.setFieldsValue({
        teamList: [
          {
            operateUserNo: '',
            operateUserName: '',
            orginAvgWorks: '',
            avgWorks: '',
          },
        ],
      });
    } else {
      form.setFieldsValue({
        originTotalNum: '', // 货量
        totalNum: '', // 申请后货量
      });
    }
  };

  const getTitle = () => {
    switch (operationType) {
      case 'edit':
        return '编辑';
      case 'approve':
        return '审批';
      case 'check':
        return '查看';
      case 'add':
        return '新增';
      default:
        throw error;
    }
  };

  const handleSelect = value => {
    if (value === 1) {
      setIsPass(true);
    } else {
      setIsPass(false);
    }
  };

  const showPlatformNo = useMemo(() => {
    const { status, operateType } = form.getFieldsValue();
    if (status === 2) {
      // 工时异常，作业环节NC初分14）和NC细分（15）不展示月台号 其他环节要展示
      if (operateType === 14 || operateType === 15) {
        return false;
      }
      return true;
    }
    return false;
  }, [form.getFieldsValue().status, form.getFieldsValue().operateType]);

  const footerBtn = [
    <Button key="back" onClick={rest.onCancel}>
      取消
    </Button>,
    <Button key="submit" loading={loading} type="primary" onClick={submit}>
      提交
    </Button>,
  ];

  const resetList = () => {
    if (form.getFieldValue('status') === 3) {
      form.setFieldsValue({
        teamList: [
          {
            operateUserNo: '',
            operateUserName: '',
            orginAvgWorks: '',
            avgWorks: '',
            userType: '',
            zoneCode: '',
          },
        ],
      });
    }
  };
  return (
    <Modal
      title={titleText}
      onCancel={handleCancel}
      maskClosable={false}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={operationType === 'check' ? null : footerBtn}
    >
      <Form {...formProps}>
        <Row>
          <Col md={20} sm={24}>
            <FormItem
              label="异常类型"
              name="status"
              labelCol={{ xl: 4 }}
              wrapperCol={{ xl: 20 }}
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Radio.Group
                onChange={onChangeValue}
                options={abnormalTypeList}
                disabled={
                  operationType === 'check' ||
                  operationType === 'approve' ||
                  operationType === 'edit'
                }
              ></Radio.Group>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.status !== curValues.status
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [3].includes(getFieldValue('status')) && (
                <Col md={10} sm={24}>
                  <FormItem
                    label="类型"
                    name="operationType"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                  >
                    <Select
                      disabled={operationType !== 'add'}
                      onChange={resetList}
                      options={[
                        {
                          label: '叉车打卡里程',
                          value: 4,
                        },
                        {
                          label: '叉车扫码里程',
                          value: 5,
                        },
                      ]}
                      placeholder="请选择类型"
                      allowClear
                    ></Select>
                  </FormItem>
                </Col>
              )
            }
          </Form.Item>
        </Row>
        <Row>
          <Col md={10} sm={24}>
            <FormItem
              label="网点"
              name="zoneCode"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DeptSearch
                onChange={e => {
                  getInforCode(e);
                  resetList();
                }}
                disabled={operationType !== 'add'}
                placeholder="请输入中转场名称或者代码"
                allowClear
              />
            </FormItem>
          </Col>
          {/* 用来区分顺心还是顺丰的字段 sourceType */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.status !== curValues.status
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [0, 1].includes(getFieldValue('status')) && (
                <Col md={10} sm={24}>
                  <FormItem label="子任务类型" name="sourceType">
                    <Input disabled allowClear />
                  </FormItem>
                </Col>
              )
            }
          </Form.Item>
          {/* 货量异常 多的 */}
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.status !== curValues.status
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [0].includes(getFieldValue('status')) &&
              (getFieldValue('taskId')
                ? getFieldValue('taskId').substring(0, 2) === 'ZY' ||
                  getFieldValue('taskId').substring(0, 4) === 'JTTC' ||
                  isZyTask
                : false) &&
              getFieldValue('flowId') && (
                <Col md={10} sm={24}>
                  <FormItem
                    label="取消货量折算"
                    name="isVolumeConvert"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                  >
                    <Radio.Group
                      onChange={e => getConvertType(e.target.value)}
                      options={juegeList}
                      disabled={
                        operationType === 'check' ||
                        operationType === 'approve' ||
                        cancleBtnDisable
                      }
                    ></Radio.Group>
                  </FormItem>
                </Col>
              )
            }
          </Form.Item>
        </Row>
        <Row>
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.status !== curValues.status
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [3].includes(getFieldValue('status')) && (
                <Col md={10} sm={24}>
                  <FormItem
                    label="异常日期"
                    name="exceptionDate"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                  >
                    <DatePicker
                      placeholder="请选择日期"
                      disabled={operationType !== 'add'}
                      onChange={resetList}
                      style={{
                        width: '100%',
                      }}
                    />
                  </FormItem>
                </Col>
              )
            }
          </Form.Item>
        </Row>
        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.status !== curValues.status
          }
          noStyle
        >
          {({ getFieldValue }) =>
            ![3].includes(getFieldValue('status')) && (
              <Row>
                <Col md={10} sm={24}>
                  <FormItem
                    label="任务号"
                    name="taskId"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                  >
                    <Input
                      placeholder="请输入"
                      allowClear
                      disabled={operationType !== 'add'}
                      onBlur={e => {
                        getDetailInfor(e.target.value);
                      }}
                    />
                  </FormItem>
                </Col>
                <Col md={10} sm={24}>
                  <FormItem
                    label="子任务号"
                    name="flowId"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                  >
                    <Select
                      onChange={getIndex}
                      allowClear
                      options={flowidList}
                      placeholder="请选择"
                      disabled={operationType !== 'add'}
                    ></Select>
                  </FormItem>
                </Col>
                <Col md={10} sm={24}>
                  <FormItem label="作业环节" name="operateType">
                    <Select
                      disabled
                      allowClear
                      options={options}
                      placeholder="根据子任务号带出"
                    ></Select>
                  </FormItem>
                </Col>
                <Form.Item
                  shouldUpdate={(prevValues, curValues) =>
                    prevValues.status !== curValues.status
                  }
                  noStyle
                >
                  {({ getFieldValue }) =>
                    [0].includes(getFieldValue('status')) &&
                    getFieldValue('flowId') && (
                      <Col md={10} sm={24}>
                        <FormItem
                          label="月台号"
                          name="platformNo"
                          rules={[
                            {
                              required: true,
                              message: '请选择',
                            },
                          ]}
                        >
                          <Select
                            disabled={
                              operationType === 'check' ||
                              operationType === 'approve'
                            }
                            allowClear
                            options={platformNoList}
                            placeholder="请选择月台号"
                          ></Select>
                        </FormItem>
                      </Col>
                    )
                  }
                </Form.Item>
                {/* 工时异常没有 */}
                <Form.Item
                  shouldUpdate={(prevValues, curValues) =>
                    prevValues.status !== curValues.status
                  }
                  noStyle
                >
                  {({ getFieldValue }) =>
                    [0, 1].includes(getFieldValue('status')) && (
                      <Col md={10} sm={24}>
                        <FormItem
                          label={
                            getFieldValue('sourceType') === 'SF'
                              ? '顺丰货量'
                              : '货量'
                          }
                          name="originTotalNum"
                        >
                          <Input
                            placeholder="根据子任务号带出"
                            disabled
                            allowClear
                            addonAfter="吨"
                          />
                        </FormItem>
                      </Col>
                    )
                  }
                </Form.Item>
                {/* 货量异常 多的 */}
                <Form.Item
                  shouldUpdate={(prevValues, curValues) =>
                    prevValues.status !== curValues.status
                  }
                  noStyle
                >
                  {({ getFieldValue }) =>
                    [0].includes(getFieldValue('status')) && (
                      <Col md={10} sm={24}>
                        <FormItem
                          label={
                            getFieldValue('sourceType') === 'SF'
                              ? '申请后顺丰货量'
                              : '货量'
                          }
                          name="totalNum"
                          rules={[...totalNumRules]}
                        >
                          <Input
                            disabled={
                              operationType === 'check' ||
                              operationType === 'approve'
                            }
                            placeholder="请输入"
                            allowClear
                            addonAfter="吨"
                          />
                        </FormItem>
                      </Col>
                    )
                  }
                </Form.Item>
                {/* 当子任务号是顺丰时 展示 顺心货量 */}
                <Form.Item
                  shouldUpdate={(prevValues, curValues) =>
                    prevValues.status !== curValues.status ||
                    prevValues.sourceType !== curValues.sourceType
                  }
                  noStyle
                >
                  {({ getFieldValue }) =>
                    [0, 1].includes(getFieldValue('status')) &&
                    getFieldValue('sourceType') === 'SF' && (
                      <Col md={10} sm={24}>
                        <FormItem label="顺心货量" name="originTotalSxNum">
                          <Input
                            placeholder="根据子任务号带出"
                            disabled
                            allowClear
                            addonAfter="吨"
                          />
                        </FormItem>
                      </Col>
                    )
                  }
                </Form.Item>
                {/* 当子任务号是顺丰时 展示 顺心申请后货量 */}
                <Form.Item
                  shouldUpdate={(prevValues, curValues) =>
                    prevValues.status !== curValues.status ||
                    prevValues.sourceType !== curValues.sourceType
                  }
                  noStyle
                >
                  {({ getFieldValue }) =>
                    [0].includes(getFieldValue('status')) &&
                    getFieldValue('sourceType') === 'SF' && (
                      <Col md={10} sm={24}>
                        <FormItem
                          label="申请后顺心货量"
                          name="totalSxNum"
                          rules={[...totalNumRules]}
                        >
                          <Input
                            disabled={
                              operationType === 'check' ||
                              operationType === 'approve'
                            }
                            placeholder="请输入"
                            allowClear
                            addonAfter="吨"
                          />
                        </FormItem>
                      </Col>
                    )
                  }
                </Form.Item>
                <Col md={10} sm={24}>
                  <FormItem label="任务开始时间" name="startTime">
                    <DatePicker
                      disabled
                      showTime
                      allowClear
                      placeholder="根据子任务号带出"
                      style={{
                        width: '100%',
                      }}
                    />
                  </FormItem>
                </Col>
                <Col md={10} sm={24}>
                  <FormItem label="任务结束时间" name="finishTime">
                    <DatePicker
                      disabled
                      showTime
                      allowClear
                      placeholder="根据子任务号带出"
                      style={{
                        width: '100%',
                      }}
                    />
                  </FormItem>
                </Col>
                {/* <Col md={10} sm={24}>
            <FormItem label="任务归属时间" name="taskSourceDate">
              <DatePicker
                disabled
                showTime
                allowClear
                placeholder="根据子任务号带出"
                style={{
                  width: '100%',
                }}
              />
            </FormItem>
          </Col> */}
              </Row>
            )
          }
        </Form.Item>
        {/* 班组异常--组员明细 */}
        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.status !== curValues.status ||
            prevValues.startTime !== curValues.startTime
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [1].includes(getFieldValue('status')) && (
              <AccrueGroupDynamicForm
                label="组员"
                propsName="teamList"
                form={form}
                zoneCode={
                  operationType === 'edit'
                    ? initialValues?.zoneCode
                    : zoneCodeNo
                }
                startTime={getFieldValue('startTime')}
                listArr={listArr}
                disabled={
                  operationType === 'check' || operationType === 'approve'
                }
              />
            )
          }
        </Form.Item>
        {/* 工时异常--组员明细 */}
        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.status !== curValues.status ||
            prevValues.startTime !== curValues.startTime
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [2].includes(getFieldValue('status')) && (
              <WorkTimeDynamicFrom
                form={form}
                zoneCode={
                  operationType === 'edit'
                    ? initialValues?.zoneCode
                    : zoneCodeNo
                }
                // listArr={listArr}
                startTime={getFieldValue('startTime')}
                disabled={
                  operationType === 'check' || operationType === 'approve'
                }
                propsName="teamList"
              ></WorkTimeDynamicFrom>
            )
          }
        </Form.Item>
        {/* 叉车里程异常--组员明细 */}
        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.status !== curValues.status ||
            prevValues.startTime !== curValues.startTime
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [3].includes(getFieldValue('status')) && (
              <ExceptionDetailsForm
                label="异常明细"
                propsName="teamList"
                form={form}
                zoneCode={zoneCodeNo}
                listArr={listArr}
                type={operationType}
                totalNumRules={totalNumRules}
                setZone={setZone}
                startTime={getFieldValue('exceptionDate')}
                disabled={
                  operationType === 'check' || operationType === 'approve'
                }
              ></ExceptionDetailsForm>
            )
          }
        </Form.Item>
        <Row>
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.status !== curValues.status
            }
            noStyle
          >
            {() =>
              showPlatformNo && (
                <Col md={20}>
                  <FormItem
                    label="月台号"
                    name="platformNos"
                    labelCol={{ xl: 4 }}
                    wrapperCol={{ xl: 20 }}
                  >
                    <Select
                      allowClear
                      disabled={
                        operationType === 'check' || operationType === 'approve'
                      }
                      mode="multiple"
                      options={platformNoList}
                      placeholder="请选择月台号"
                    ></Select>
                  </FormItem>
                </Col>
              )
            }
          </Form.Item>
          <Col md={20}>
            <Form.Item
              shouldUpdate={(prevValues, curValues) =>
                prevValues.status !== curValues.status
              }
              noStyle
            >
              {/* 地面流向分拣才显示卡口号 */}
              {() =>
                isDMXLXFJ && (
                  <FormItem
                    label="卡口号"
                    name="inPortCodes"
                    labelCol={{ xl: 4 }}
                    wrapperCol={{ xl: 20 }}
                  >
                    <Select
                      allowClear
                      disabled={
                        operationType === 'check' || operationType === 'approve'
                      }
                      mode="multiple"
                      options={inPortCodesList}
                      placeholder="请选择卡口号"
                    ></Select>
                  </FormItem>
                )
              }
            </Form.Item>
          </Col>

          <Col md={20}>
            <FormItem
              style={{ marginBottom: -6 }}
              label="异常描述"
              name="exceptionTxt"
              labelCol={{ xl: 4 }}
              wrapperCol={{ xl: 20 }}
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <TextArea
                showCount
                allowClear
                // placeholder="班组录入错误，申请修改"
                maxLength={500}
                disabled={
                  operationType === 'check' || operationType === 'approve'
                }
              />
            </FormItem>
          </Col>
          {(operationType === 'approve' || operationType === 'check') &&
            isHide && (
              <Col md={20} sm={24}>
                <FormItem
                  label="审核状态"
                  name="type"
                  style={{ marginBottom: 20 }}
                  labelCol={{ xl: 4 }}
                  wrapperCol={{ xl: 20 }}
                  rules={[
                    {
                      required: true,
                      message: '请选择',
                    },
                  ]}
                >
                  <Radio.Group
                    disabled={operationType === 'check'}
                    onChange={event => {
                      handleSelect(event.target.value);
                    }}
                    options={approveSuggest}
                  ></Radio.Group>
                </FormItem>
              </Col>
            )}
          {(operationType === 'approve' || operationType === 'check') &&
            isHide && (
              <Col md={20} sm={24}>
                <FormItem
                  label="审核意见"
                  name="approvalWord"
                  labelCol={{ xl: 4 }}
                  wrapperCol={{ xl: 20 }}
                  rules={[
                    {
                      required: isPass,
                      message: '请选择',
                    },
                  ]}
                >
                  <TextArea
                    disabled={operationType === 'check'}
                    showCount
                    allowClear
                    maxLength={500}
                  />
                </FormItem>
              </Col>
            )}
        </Row>
      </Form>
    </Modal>
  );
};

export default ModalHandle;
