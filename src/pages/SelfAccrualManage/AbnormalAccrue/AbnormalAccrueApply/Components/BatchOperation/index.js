import React, { useState } from 'react';
import { Form, Modal, Col, Row, Button, DatePicker } from 'antd';
import { cloneDeep } from 'lodash';
import moment from 'moment';

const { Item: FormItem } = Form;
const HandelModal = props => {
  const { handleCancel, visible, listArr, checkedList, setEditValues } = props;
  const [form] = Form.useForm();
  const [newAvgWorks, setNewAvgWorks] = useState(0);
  const [daySave, setDaySave] = useState(0); // 天
  const [hourSave, setHourSave] = useState(0); // 小时
  const [minSave, setMinSave] = useState(0); // 分钟
  const formProps = {
    form,
    labelCol: { md: 4 },
    wrapperCol: { md: 20 },
    initialValues: {},
  };

  // 提交时请求
  const submit = async () => {
    const initialValue = await form.validateFields();
    const param = cloneDeep(initialValue);
    const newArr = listArr.map(({ time, avgWorks, ...rest } = {}, index) => ({
      time: checkedList.includes(index) ? param.workDate : time,
      avgWorks: checkedList.includes(index) ? newAvgWorks : avgWorks,
      ...rest,
    }));
    setEditValues(newArr);
    handleCancelModal();
  };

  const handleCancelModal = () => {
    form.resetFields();
    handleCancel();
  };

  // 计算工时
  const getTimeDates = dates => {
    if (dates !== null && dates[0] && dates[1]) {
      const a = moment(dates[0]).format('x');
      const b = moment(dates[1]).format('x');
      const c = b - a;
      const r = c / 1000 / 60 / 60;
      setNewAvgWorks(r.toFixed(1));
    }
  };

  const range = (start, end) => {
    const result = [];
    for (let i = start; i <= end; i++) {
      result.push(i);
    }
    return result;
  };

  const disabledRangeTime = (dates, partial) => {
    if (dates !== null) {
      if (partial === 'start') {
        setDaySave(
          moment(dates)
            .subtract(0, 'days')
            .format('D'),
        );
        setHourSave(moment(dates).hour());
        setMinSave(moment(dates).minute());
      }

      if (
        dates &&
        moment(dates)
          .subtract(0, 'days')
          .format('D') === daySave &&
        partial === 'end'
      ) {
        return {
          disabledHours: () => range(0, hourSave > 1 ? hourSave - 1 : hourSave),
          disabledMinutes: () => range(0, minSave + 1),
          // disabledSeconds: () => range(seconds + 1, 59),
        };
      }
    }
  };

  return (
    <div>
      <Modal
        title="批量修改上下岗时间"
        onCancel={handleCancelModal}
        onOk={submit}
        visible={visible}
        width={700}
        footer={[
          <Button key="back" onClick={handleCancelModal}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={submit}>
            确定
          </Button>,
        ]}
      >
        <Form {...formProps}>
          <Row>
            <Col md={24}>
              <FormItem
                label="上下岗时间"
                name="workDate"
                rules={[
                  {
                    required: true,
                    message: '请填写',
                  },
                ]}
              >
                <DatePicker.RangePicker
                  showTime
                  allowClear
                  style={{ width: '100%' }}
                  placeholder={['上岗开始时间', '上岗结束时间']}
                  onChange={getTimeDates}
                  disabledTime={disabledRangeTime}
                />
              </FormItem>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default HandelModal;
