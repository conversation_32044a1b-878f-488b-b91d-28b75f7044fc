import React, { useState } from 'react';
import { Modal, Button, Col, Row, Form, Input, message, Radio } from 'antd';

import '../../index.scss';
import { batchApply } from '../../servers/api';
import { approveSuggest } from '../status';

const { Item: FormItem } = Form;
const { TextArea } = Input;

const tailLayout = {
  wrapperCol: { offset: 8, span: 16 },
};
const handleModal = props => {
  const { visible, beChose, handleSearch } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isPass, setIsPass] = useState(false); // 驳回原因是否必填

  const handleCancel = () => {
    form.resetFields();
    props.handleCancel();
  };

  const submit = () => {
    form.validateFields().then(data => {
      setLoading(true);
      const params = beChose.map(item => ({
        approvalWord: data.approvalWord,
        type: data.type,
        staffUuid: item.manageUuid,
      }));
      batchApply(params)
        .then(res => {
          setLoading(false);
          if (res.success) {
            message.success(data.type === 1 ? '退回成功！' : '审批通过');
            handleCancel();
            handleSearch();
          } else {
            setLoading(false);
            message.error(res.errorMessage);
          }
        })
        .catch(err => {
          setLoading(false);
          message.error(err);
        });
    });
  };

  const handleSelect = value => {
    if (value === 1) {
      setIsPass(true);
    } else {
      setIsPass(false);
    }
  };

  return (
    <div>
      <Modal
        title="批量审批"
        width={800}
        visible={visible}
        onCancel={handleCancel}
        maskClosable={false}
        footer={null}
        destroyOnClose
      >
        <Form
          name="basic"
          form={form}
          initialValues={beChose}
          labelCol={{ md: 2, xs: 8 }}
          wrapperCol={{ md: 22 }}
        >
          <Row>
            <Col md={24} sm={24}>
              <FormItem
                label="审核状态"
                name="type"
                labelCol={{ xl: 4 }}
                wrapperCol={{ xl: 20 }}
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <Radio.Group
                  onChange={event => {
                    handleSelect(event.target.value);
                  }}
                  options={approveSuggest}
                ></Radio.Group>
              </FormItem>
            </Col>
            <Col md={24} sm={24}>
              <FormItem
                label="审批内容"
                name="approvalWord"
                labelCol={{ xl: 4 }}
                wrapperCol={{ xl: 20 }}
                rules={[
                  {
                    required: isPass,
                    message: '请选择',
                  },
                ]}
              >
                <TextArea
                  allowClear
                  placeholder="只能输入数字和英文逗号"
                  showCount
                  maxLength={500}
                />
              </FormItem>
            </Col>
          </Row>

          <Form.Item {...tailLayout}>
            <Button onClick={handleCancel}>取消</Button>
            <Button
              type="primary"
              onClick={submit}
              loading={loading}
              style={{ marginLeft: '20px' }}
            >
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default handleModal;
