import React, { useState, useEffect } from 'react';
import { Form, Input, Row, Button, Col, Select, message, Card } from 'antd';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import './index.scss';
import { error } from 'src/utils/utils';
import {
  searchSupplierName,
  queryFemSupplierByZoneCode,
} from '../../servers/api';

const formItemLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    span: 21,
    offset: 3,
  },
};
let lists = [];

const DynamicForm = props => {
  const {
    label,
    zoneCode,
    form,
    propsName,
    disabled,
    listArr,
    startTime,
  } = props;
  const [timeId, setTimeId] = useState([]);
  const [supplierList, setSupplierList] = useState([]); // 所有组员的供应商信息
  const [allList, setAllList] = useState([]);

  useEffect(() => {
    if (zoneCode) {
      try {
        queryFemSupplierByZoneCode({
          zoneCode,
        }).then(res => {
          if (res.success) {
            if (Array.isArray(res.obj)) {
              const arrLists = res.obj.map(item => ({
                value: `${item.companyName}-${item.companyCode}`,
                label: `${item.companyName}-${item.companyCode}`,
              }));
              setAllList(arrLists);
            } else {
              setAllList([]);
            }
          }
        });
      } catch (err) {
        console.log(err);
      }
    }
  }, [zoneCode]);

  // 根据组员工号查询 姓名 归属供应商list
  const getDetailInfor = (value, index) => {
    if (value !== '') {
      const accrueTeams = form.getFieldValue('teamList');
      // 重复组员的判断
      const copyAccrueTeams = cloneDeep(accrueTeams);
      if (copyAccrueTeams[index].userName === undefined) {
        copyAccrueTeams.splice(index, 1);
        const a = copyAccrueTeams.some(item => value === item.operateUserNo);
        if (a) {
          accrueTeams[index].operateUserNo = '';
          message.warning('小组内以有此人，请勿重复添加');
          return;
        }
      }

      // 根据工号调接口查询供应商list
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          searchSupplierName({
            operateUserNo: value,
            deptCode: zoneCode,
            includeDelFlag: true,
            workDate: moment(startTime).format('YYYY-MM-DD HH:mm:ss'),
            taskId: form.getFieldValue('taskId'),
            flowId: form.getFieldValue('flowId'),
            operateType: form.getFieldValue('operateType'),
          }).then(res => {
            if (res.success) {
              if (res.obj.length > 0) {
                // 是否自有员工 1 自有
                lists[index] = [];
                res.obj.forEach(item => {
                  if (item.innerFlag === 0) {
                    lists[index].push({
                      ...item,
                      value: `${item.supplierName}-${item.supplierNo}`,
                      label: `${item.supplierName}-${item.supplierNo}`,
                    });
                    accrueTeams[index].operateUserName = item.empName;
                    accrueTeams[index].supplierUserNum = '1';
                    accrueTeams[index].userType = 2;
                    accrueTeams[index].supplierNo = item.supplierNo;
                    accrueTeams[index].supplierName = item.supplierName;
                    accrueTeams[index].originTeamId = '';
                  } else {
                    accrueTeams[index].operateUserName = item.empName;
                    accrueTeams[index].supplierUserNum = '1';
                    accrueTeams[index].userType = 1;
                    accrueTeams[index].supplierNo = '';
                    accrueTeams[index].supplierName = null;
                    accrueTeams[index].originTeamId = '';
                  }
                });
                setSupplierList(lists);
                // if (res.obj[0].innerFlag === 0) {
                //   if (lists.length === index) {
                //     lists.push(allList);
                //   } else {
                //     lists[index] = allList;
                //   }
                //   setSupplierList(lists);
                //   accrueTeams[index].operateUserName = res.obj[0].empName;
                //   accrueTeams[index].supplierUserNum = '1';
                //   accrueTeams[index].userType = 2;
                //   accrueTeams[index].supplierNo = '';
                //   accrueTeams[index].supplierName = '';
                //   accrueTeams[index].originTeamId = '';
                // } else if (res.obj[0].innerFlag === 1) {
                //   accrueTeams[index].operateUserName = res.obj[0].empName;
                //   accrueTeams[index].supplierUserNum = '1';
                //   accrueTeams[index].userType = 1;
                //   accrueTeams[index].supplierNo = '';
                //   accrueTeams[index].supplierName = null;
                //   accrueTeams[index].originTeamId = '';

                //   if (lists.length === index) {
                //     lists.push([]);
                //   } else {
                //     lists[index] = [];
                //   }

                //   setSupplierList(lists);
                // }
                form.setFieldsValue({ accrueTeams });
              } else {
                error('请检查工号是否输入正确');
              }
            } else {
              error('工号查询失败');
            }
          });
        }, 1000),
      );
    }
  };

  // 点击修改和查看时  获取对应工号的供应商名称list
  useEffect(() => {
    lists = []; // 记录员工的数量 长度
    // debugger;
    listArr.forEach((item, index) => {
      if (item.userType === 2) {
        searchSupplierName({
          operateUserNo: item.operateUserNo,
          deptCode: zoneCode,
          includeDelFlag: true,
          workDate: moment(startTime).format('YYYY-MM-DD HH:mm:ss'),
          taskId: form.getFieldValue('taskId'),
          flowId: form.getFieldValue('flowId'),
          operateType: form.getFieldValue('operateType'),
        }).then(res => {
          lists[index] = [];
          res.obj.forEach(itemValue => {
            lists[index].push({
              ...itemValue,
              value: `${itemValue.supplierName}-${itemValue.supplierNo}`,
              label: `${itemValue.supplierName}-${itemValue.supplierNo}`,
            });
          });
          // lists[index] = allList;
          setSupplierList([...lists]);
        });
      } else {
        // 自有员工 占位
        lists[index] = [{}];
      }
    });
  }, [listArr, allList]);

  return (
    <Form.List name={propsName}>
      {(fields, { add, remove }) => (
        <Card>
          {fields.map((field, index) => (
            <Form.Item
              key={field.key}
              className="style-Bottom"
              label={`${index === 0 ? label : ''}`}
              {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
            >
              <Row gutter={8}>
                <Col md={5} sm={24}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'operateUserNo']}
                    fieldKey={[field.fieldKey, 'operateUserNo']}
                    key={`${field.fieldKey}operateUserNo`}
                  >
                    <Input
                      disabled={disabled}
                      placeholder="请输入工号"
                      onChange={e => {
                        getDetailInfor(e.target.value, index);
                      }}
                      allowClear
                    />
                  </Form.Item>
                </Col>
                <Col md={5} sm={24}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'operateUserName']}
                    fieldKey={[field.fieldKey, 'operateUserName']}
                    key={`${field.fieldKey}operateUserName`}
                  >
                    <Input disabled placeholder="请输入姓名" />
                  </Form.Item>
                </Col>
                <Col md={10} sm={24}>
                  <Form.Item noStyle shouldUpdate>
                    {() => (
                      <Form.Item
                        {...field}
                        name={[field.name, 'supplierName']}
                        fieldKey={[field.fieldKey, 'supplierName']}
                        key={`${field.fieldKey}supplierName`}
                      >
                        <Select
                          disabled={disabled}
                          placeholder="归属供应商:无"
                          options={supplierList[index] || []}
                          allowClear
                        ></Select>
                      </Form.Item>
                    )}
                  </Form.Item>
                </Col>
                {!disabled && (
                  <Col md={1} sm={24}>
                    <Form.Item {...field}>
                      <Button
                        type="link"
                        onClick={() => {
                          add();
                        }}
                        style={{ color: '#0e7aff' }}
                      >
                        新增
                      </Button>
                    </Form.Item>
                  </Col>
                )}
                {!disabled && (
                  <Col md={1} sm={24}>
                    <Form.Item {...field}>
                      {fields.length > 1 && (
                        <Button
                          type="link"
                          onClick={() => {
                            remove(field.name);
                            // supplierList.splice(index, 1);
                            lists.splice(index, 1);
                          }}
                        >
                          删除
                        </Button>
                      )}
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Form.Item>
          ))}
        </Card>
      )}
    </Form.List>
  );
};

export default DynamicForm;
