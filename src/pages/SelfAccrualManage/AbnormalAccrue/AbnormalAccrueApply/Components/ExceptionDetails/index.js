import React, { useState, useEffect } from 'react';
import { Form, Input, Row, Button, Col, Select, message, Card } from 'antd';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import './index.scss';
import { error } from 'src/utils/utils';
import { searchVendorSupplierName, queryForkLiftKm } from '../../servers/api';

const formItemLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    span: 21,
    offset: 3,
  },
};
let lists = [];

const ExceptionDetailsForm = props => {
  const {
    label,
    zoneCode,
    form,
    propsName,
    disabled,
    listArr,
    setZone,
    totalNumRules,
    type,
    startTime,
  } = props;
  const [timeId, setTimeId] = useState([]);
  const [supplierList, setSupplierList] = useState([]); // 所有组员的供应商信息

  // 根据组员工号查询 姓名 归属供应商list
  const getDetailInfor = (value, index) => {
    if (value !== '') {
      const accrueTeams = form.getFieldValue('teamList');
      // 重复组员的判断
      const copyAccrueTeams = cloneDeep(accrueTeams);
      if (copyAccrueTeams[index].userName === undefined) {
        copyAccrueTeams.splice(index, 1);
        const a = copyAccrueTeams.some(item => value === item.operateUserNo);
        if (a) {
          accrueTeams[index].operateUserNo = '';
          message.warning('小组内以有此人，请勿重复添加');
          return;
        }
      }

      // 根据工号调接口查询供应商list
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          searchVendorSupplierName({
            operateUserNo: value,
            deptCode: zoneCode,
            includeDelFlag: true,
            workDate: moment(startTime)
              .startOf('date')
              .format('YYYY-MM-DD HH:mm:ss'),
            workDateEnd: moment(startTime)
              .endOf('date')
              .format('YYYY-MM-DD HH:mm:ss'),
          }).then(res => {
            if (res.success) {
              if (res.obj.length > 0) {
                // 是否自有员工 1 自有
                if (res.obj[0].innerFlag === 0) {
                  const arrLists = res.obj.map(item => ({
                    value: `${item.supplierName}-${item.supplierId}`,
                    label: item.supplierName,
                  }));
                  if (lists.length === index) {
                    lists.push(arrLists);
                  } else {
                    lists[index] = arrLists;
                  }
                  setSupplierList(lists);
                  accrueTeams[index].operateUserName = res.obj[0].empName;
                  accrueTeams[index].supplierUserNum = '1';
                  accrueTeams[index].userType = 2;
                  accrueTeams[index].supplierNo = '';
                  accrueTeams[index].supplierName = '';
                  accrueTeams[index].originTeamId = '';
                } else if (res.obj[0].innerFlag === 1) {
                  accrueTeams[index].operateUserName = res.obj[0].empName;
                  accrueTeams[index].supplierUserNum = '1';
                  accrueTeams[index].userType = 1;
                  accrueTeams[index].supplierNo = '';
                  accrueTeams[index].supplierName = null;
                  accrueTeams[index].originTeamId = '';

                  if (lists.length === index) {
                    lists.push([]);
                  } else {
                    lists[index] = [];
                  }

                  setSupplierList(lists);
                }
                form.setFieldsValue({ accrueTeams });
              } else {
                error('请检查工号是否输入正确');
              }
            } else {
              error('工号查询失败');
            }
          });
        }, 1000),
      );
    }
  };

  const defaultUser = async (value, index) => {
    const accrueTeams = form.getFieldValue('teamList');
    accrueTeams[index] = {
      operateUserNo: '',
      operateUserName: '',
      orginAvgWorks: '',
      avgWorks: '',
      userType: '',
      zoneCode: '',
    };
    try {
      const res = await searchVendorSupplierName({
        operateUserNo: value,
        deptCode: zoneCode,
        includeDelFlag: true,
        workDate: moment(startTime)
          .startOf('date')
          .format('YYYY-MM-DD HH:mm:ss'),
        workDateEnd: moment(startTime)
          .endOf('date')
          .format('YYYY-MM-DD HH:mm:ss'),
      });
      if (res && res.success) {
        if (res.obj && res.obj.length > 0) {
          setZone(res.obj[0].netCode);
          res.obj.map(v => {
            accrueTeams[index] = {
              operateUserNo: v.empNum,
              operateUserName: v.empName,
              orginAvgWorks: 0,
              avgWorks: '',
              userType: v.innerFlag === 1 ? 1 : 2,
              zoneCode: v.netCode,
            };
          });
        } else {
          message.error('没有查到相关数据');
        }
      }
      form.setFieldsValue({
        teamList: accrueTeams,
      });
    } catch (e) {
      console.log(e);
    }
  };

  const getForklift = async (value, index) => {
    const { operationType, zoneCode, exceptionDate } = form.getFieldValue();
    if (value !== '') {
      const accrueTeams = form.getFieldValue('teamList');
      // 重复组员的判断
      const copyAccrueTeams = cloneDeep(accrueTeams);
      if (copyAccrueTeams[index].userName === undefined) {
        copyAccrueTeams.splice(index, 1);
        const a = copyAccrueTeams.some(item => value === item?.operateUserNo);
        if (a) {
          accrueTeams[index].operateUserNo = '';
          message.warning('小组内以有此人，请勿重复添加');
          return;
        }
      }
      const time = moment(exceptionDate).format('YYYY-MM-DD');
      if (!operationType && !zoneCode && !exceptionDate) return;
      try {
        const res = await queryForkLiftKm({
          operationType,
          zoneCode,
          exceptionDate: time,
          userCode: value,
        });
        if (res && res.success) {
          if (res.obj && res.obj.length > 0) {
            setZone(res.obj[0].zoneCode);
            res.obj.map(v => {
              accrueTeams[index] = {
                operateUserNo: v.userCode,
                operateUserName: v.userName,
                orginAvgWorks: v.dayOperationVol,
                avgWorks: '',
                userType: v.userType,
                zoneCode: v.zoneCode,
              };
            });
            form.setFieldsValue({
              teamList: accrueTeams,
            });
          } else {
            defaultUser(value, index);
          }
        }
      } catch (e) {
        console.log(e);
      }
    }
  };

  const isDisabled = () => {
    const { operationType, zoneCode, exceptionDate } = form.getFieldValue();
    if (operationType && zoneCode && exceptionDate) {
      return disabled || type == 'edit';
    }
    return true;
  };

  // 点击修改和查看时  获取对应工号的供应商名称list
  useEffect(() => {
    lists = []; // 记录员工的数量 长度
    // debugger;
    listArr.forEach((item, index) => {
      if (item.userType === 2) {
        searchVendorSupplierName({
          operateUserNo: item.operateUserNo,
          deptCode: zoneCode,
          includeDelFlag: true,
          workDate: moment(startTime)
            .startOf('date')
            .format('YYYY-MM-DD HH:mm:ss'),
          workDateEnd: moment(startTime)
            .endOf('date')
            .format('YYYY-MM-DD HH:mm:ss'),
        }).then(res => {
          const arrLists = res.obj.map(v => ({
            value: `${v.supplierName}-${v.supplierId}`,
            label: v.supplierName,
          }));
          lists[index] = arrLists;
          if (listArr.length === lists.length) {
            // 当员工数和用供应商list长度一致赋值
            setSupplierList(lists);
          }
        });
      } else {
        // 自有员工 占位
        lists[index] = [{}];
      }
    });
  }, [listArr]);

  return (
    <Form.List name={propsName}>
      {(fields, { add, remove }) => (
        <Card>
          {fields.map((field, index) => (
            <Form.Item
              key={field.key}
              className="style-Bottom"
              label={`${index === 0 ? label : ''}`}
              {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
            >
              <Row gutter={8}>
                <Col md={5} sm={24}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'operateUserNo']}
                    fieldKey={[field.fieldKey, 'operateUserNo']}
                    key={`${field.fieldKey}operateUserNo`}
                    rules={[
                      {
                        required: true,
                        message: '请输入',
                      },
                    ]}
                  >
                    <Input
                      disabled={isDisabled()}
                      placeholder="请输入工号"
                      onBlur={e => {
                        getForklift(e.target.value, index);
                      }}
                      allowClear
                    />
                  </Form.Item>
                </Col>
                <Col md={5} sm={24}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'operateUserName']}
                    fieldKey={[field.fieldKey, 'operateUserName']}
                    key={`${field.fieldKey}operateUserName`}
                  >
                    <Input disabled placeholder="请输入姓名" />
                  </Form.Item>
                </Col>
                <Col md={5} sm={24}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'orginAvgWorks']}
                    fieldKey={[field.fieldKey, 'orginAvgWorks']}
                    key={`${field.fieldKey}orginAvgWorks`}
                  >
                    <Input
                      placeholder={`系统采集叉车(${
                        form.getFieldValue('operationType') === 4
                          ? '打卡'
                          : '扫码'
                      })里程`}
                      disabled
                      addonAfter="KM"
                    />
                  </Form.Item>
                </Col>
                <Col md={5} sm={24}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'avgWorks']}
                    fieldKey={[field.fieldKey, 'avgWorks']}
                    key={`${field.fieldKey}avgWorks`}
                    rules={totalNumRules}
                  >
                    <Input
                      placeholder={`调整后叉车(${
                        form.getFieldValue('operationType') === 4
                          ? '打卡'
                          : '扫码'
                      })里程`}
                      disabled={disabled}
                      addonAfter="KM"
                      type="number"
                    />
                  </Form.Item>
                </Col>
                {/* {!disabled && (
                  <Col md={1} sm={24}>
                    <Form.Item {...field}>
                      <Button
                        type="link"
                        onClick={() => {
                          add();
                        }}
                        style={{ color: '#0e7aff' }}
                      >
                        新增
                      </Button>
                    </Form.Item>
                  </Col>
                )} */}
                {/* {!disabled && (
                  <Col md={1} sm={24}>
                    <Form.Item {...field}>
                      {fields.length > 1 && (
                        <Button
                          type="link"
                          onClick={() => {
                            remove(field.name);
                            supplierList.splice(index, 1);
                          }}
                        >
                          删除
                        </Button>
                      )}
                    </Form.Item>
                  </Col>
                )} */}
              </Row>
            </Form.Item>
          ))}
        </Card>
      )}
    </Form.List>
  );
};

export default ExceptionDetailsForm;
