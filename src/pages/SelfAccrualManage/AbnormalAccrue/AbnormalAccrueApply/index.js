import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Form,
  Input,
  // Button,
  Select,
  DatePicker,
  message,
} from 'antd';
// import { success, error } from '@/utils/utils';
import {
  PlusOutlined,
  ExportOutlined,
  FolderViewOutlined,
} from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  formItemLayout,
  colStyle,
  rowStyle,
  AuthButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import StandardTable from 'components/StandardTable';
import AsyncExport from 'components/AsyncExport';
import ImportButton from 'components/ImportButton';
import DeptSearch from 'components/DeptSearch'; // 网点代码查询
import { search, searchDetail } from './servers/api';
import HandleModal from './Components/HandleModal';
import BatchApprove from './Components/BatchApprove';

import {
  operateTypeList,
  statusList,
  abnormalTypeList,
} from './Components/status';

import './index.scss';
const { RangePicker } = DatePicker;
const { Item: FormItem } = Form;
let inputParam = {};
const Page = ({ userInfo, areaListSF, areaListSX, logRoleCode }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [handleModalVisible, setHandleModalVisible] = useState(false); // 新增-编辑-查看-审核-弹框
  const [operationType, setOperationType] = useState(''); // 操作状态--查看&&审核&&编辑
  const [editingTarget, setEditingTarget] = useState(null);
  const [selectedRows, setSelectedRows] = useState([]);
  // const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [batchModal, setBatchModal] = useState(false); // 批量审批框显示隐藏
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '网点代码',
      align: 'center',
      width: 100,
      dataIndex: 'zoneCode',
    },
    {
      title: '分拨区代码',
      align: 'center',
      width: 100,
      dataIndex: 'allocationAreaCode',
      // render: (value, row) => `${value} ${row.allocationArea}`,
    },
    {
      title: '分拨区名称',
      align: 'center',
      width: 100,
      dataIndex: 'allocationArea',
      // render: (value, row) => `${value} ${row.allocationArea}`,
    },
    {
      title: '任务号',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'taskId',
    },
    {
      title: '月台号',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'platformNo',
    },
    {
      title: '卡口号',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'inPortCode',
    },
    {
      title: '子任务号',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'flowId',
    },
    {
      title: '异常日期',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'exceptionDate',
    },
    {
      title: '异常人工号',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'exceptionUserNo',
    },
    {
      title: '异常人姓名',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'exceptionUserName',
    },
    {
      title: '车标号',
      align: 'center',
      ellipsis: true,
      width: 130,
      dataIndex: 'logoNo',
    },
    {
      title: '作业环节',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'operateType',
      render: (v, row) => {
        // 1、operateType = 3 并且 taskType = 0时为地面线分拣
        // 2、operateType = 3 并且 taskType = 1时为地面线流向分拣
        const { taskType } = row;
        if (v === 3 && taskType === 0) return '地面线分拣';
        if (v === 3 && taskType === 1) return '地面线流向分拣';
        const data = operateTypeList.find(item => item.value === v);
        return data ? data.label : v;
      },
    },
    {
      title: '审核状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'approvalStatus',
      render: value => {
        const data = statusList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '异常类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'status',
      render: value => {
        const data = abnormalTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '变更内容',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'changeContent',
    },
    {
      title: '申请人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'creator',
    },
    {
      title: '申请时间',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'createTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD') : ''),
    },
    {
      title: '任务结束时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'finishTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'modifier',
      render: text => text || '',
    },
    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '操作',
      align: 'center',
      width: 160,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, row) => (
        <Fragment>
          <AuthButton
            modulecode={moduleCode}
            code="abnormalAccrualApply-check"
            size="small"
            type="link"
            onClick={e => update(e, row, 'check')}
          >
            查看
          </AuthButton>
          {row.approvalStatus !== 2 && (
            <AuthButton
              modulecode={moduleCode}
              code="abnormalAccrualApply-edit"
              size="small"
              type="link"
              onClick={e => update(e, row, 'edit')}
            >
              编辑
            </AuthButton>
          )}
          {row.approvalStatus !== 2 && (
            <AuthButton
              modulecode={moduleCode}
              code="abnormalAccrualApply-approve"
              size="small"
              type="link"
              onClick={e => update(e, row, 'approve')}
            >
              审核
            </AuthButton>
          )}
        </Fragment>
      ),
    },
  ];

  const getQueryParams = () => {
    const params = form.getFieldValue();
    if (params.time && params.time.length > 0) {
      params.startTime = moment(params.time[0]).format('YYYY-MM-DD');
      params.endTime = moment(params.time[1]).format('YYYY-MM-DD');
    }
    if (params.time2) {
      params.exceptionStartTime = moment(params.time2[0]).format('YYYY-MM-DD');
      params.exceptionEndTime = moment(params.time2[1]).format('YYYY-MM-DD');
    }
    inputParam = cloneDeep(params);
    delete inputParam.time;
    delete inputParam.time2;
    // refresh({
    //   currentPage: 1,
    //   pageSize: 10,
    // });
    return {
      ...inputParam,
    };
  };
  const refresh = (pagination = {}) => {
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    setLoading(true);
    const param = {
      ...inputParam,
      pageNum: current,
      pageSize,
    };
    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, total } = obj;
          // setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.pageNum,
              pageSize: obj.pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 新增&&编辑&&审核&&查看
  const update = async (e, row, type) => {
    e.stopPropagation();
    setOperationType(type); // 编辑 查看 审核标识属性
    if (type !== 'add') {
      try {
        const reult = await searchDetail({
          id: row.id,
        });
        setEditingTarget(reult.obj);
        setHandleModalVisible(true);
      } catch (err) {
        // eslint-disable-next-line no-console
        console.log(err);
      }
    } else {
      setHandleModalVisible(true);
    }
  };

  // 批量审批
  const batchApply = () => {
    const errStatus = selectedRows.some(item => item.approvalStatus === 2);
    if (errStatus) {
      message.warning('选择的数据中不能包含审核通过的数据,请核对后再审批');
      return false;
    }
    setBatchModal(true);
  };

  const resetForm = () => {
    form.resetFields();
    refresh({ currentPage: 1 });
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        time: [
          moment()
            .subtract(1, 'months')
            .startOf('month'),
          moment().endOf('month'),
        ],
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  // disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  // disabled={logRoleCode.roleCode === '88888888888'}
                  placeholder="请输入分拨区名称或代码"
                  allowClear
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务号" name="taskId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="状态" name="approvalStatus">
                <Select
                  allowClear
                  options={statusList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="异常类型" name="status">
                <Select
                  allowClear
                  options={abnormalTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="申请人" name="creator">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>

            <Col {...colStyle}>
              <FormItem
                label="申请时间"
                name="time"
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <RangePicker
                  allowClear
                  style={{
                    width: '100%',
                  }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="异常日期" name="time2">
                <RangePicker
                  allowClear
                  style={{
                    width: '100%',
                  }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="异常人" name="exceptionUserNo">
                <Input placeholder="请输入工号" allowClear />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div style={{ color: '#df2e3f', paddingBottom: '16px' }}>
          当月异常数据请在货量固化时间前提交系统并审批完毕，否则影响结算！顺丰：次月6日23:59:59；顺心：次月4日23:59:59
        </div>
        <div>
          <AuthButton
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="abnormalAccrualApply-add"
            icon={<PlusOutlined />}
            type="primary"
            onClick={e => update(e, {}, 'add')}
          >
            新增
          </AuthButton>
          <AuthButton
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            disabled={!selectedRows.length}
            code="abnormalAccrualApply-batch"
            icon={<FolderViewOutlined />}
            type="primary"
            onClick={batchApply}
          >
            批量审批
          </AuthButton>
          <AsyncExport
            text="导出所选"
            style={{ marginRight: 15 }}
            icon={<ExportOutlined />}
            disabled={!selectedRows.length}
            modulecode={moduleCode}
            code="abnormalAccrualApply-exportSlect"
            options={{
              filename: '异常管理.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueExceptionManageRest/exportSelect`,
                {
                  method: 'POST',
                  body: {
                    idList: selectedRows.map(({ id }) => id),
                  },
                },
              ],
            }}
          />
          <AsyncExport
            text="导出全部"
            style={{ marginRight: 15 }}
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            modulecode={moduleCode}
            code="abnormalAccrualApply-exportAll"
            options={{
              filename: '异常管理.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueExceptionManageRest/exportSync`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
          <ImportButton
            danger={false}
            type="primary"
            style={{ marginRight: 15 }}
            // pagename="异常管理"
            moduleCode={moduleCode}
            code="abnormalAccrualApply-cargoImport"
            title="货量异常导入"
            action="/tdmsAccrueService/accrueExceptionManageRest/upload/volumeModel"
            // modalUrl="/tdmsAccrueService/accrueExceptionManageRest/downTemplate/volumeModel"
            // modalName="异常管理.xlsx"
            handleSyncImport={refresh}
            modals={[
              {
                url:
                  '/tdmsAccrueService/accrueExceptionManageRest/downTemplate/volumeModel',
                text: '货量异常模板下载.xlsx',
              },
            ]}
            errorObj={{
              name: '货量异常导入错误日志.xlsx',
              url: '/tdmsAccrueService/accrueExceptionManageRest/upload/report',
            }}
          />
          <ImportButton
            danger={false}
            type="primary"
            style={{ marginRight: 15 }}
            // pagename="异常管理"
            moduleCode={moduleCode}
            code="abnormalAccrualApply-teamImport"
            title="班组异常导入"
            action="/tdmsAccrueService/accrueExceptionManageRest/upload/teamModel"
            // modalUrl="/tdmsAccrueService/accrueExceptionManageRest/downTemplate/teamModel"
            // modalName="异常管理.xlsx"
            handleSyncImport={refresh}
            modals={[
              {
                url:
                  '/tdmsAccrueService/accrueExceptionManageRest/downTemplate/teamModel',
                text: '班组异常模板下载.xlsx',
              },
            ]}
            errorObj={{
              name: '班组异常导入错误日志.xlsx',
              url: '/tdmsAccrueService/accrueExceptionManageRest/upload/report',
            }}
          />
          <ImportButton
            danger={false}
            type="primary"
            style={{ marginRight: 15 }}
            // pagename="异常管理"
            moduleCode={moduleCode}
            code="abnormalAccrualApply-workTimeImport"
            title="工时异常导入"
            action="/tdmsAccrueService/accrueExceptionManageRest/upload/hoursModel"
            // modalUrl="/tdmsAccrueService/accrueExceptionManageRest/downTemplate/hoursModel"
            // modalName="异常管理.xlsx"
            modals={[
              {
                url:
                  '/tdmsAccrueService/accrueExceptionManageRest/downTemplate/hoursModel',
                text: '工时异常模板下载.xlsx',
              },
            ]}
            handleSyncImport={refresh}
            errorObj={{
              name: '工时异常导入错误日志.xlsx',
              url: '/tdmsAccrueService/accrueExceptionManageRest/upload/report',
            }}
          />
          <ImportButton
            danger={false}
            type="primary"
            style={{ marginRight: 15 }}
            // pagename="异常管理"
            moduleCode={moduleCode}
            code="abnormalAccrualApply-workTimeImport"
            title="叉车里程异常导入"
            action="/tdmsAccrueService/accrueExceptionManageRest/upload/forkliftModel"
            // modalUrl="/tdmsAccrueService/accrueExceptionManageRest/downTemplate/hoursModel"
            // modalName="异常管理.xlsx"
            modals={[
              {
                url:
                  '/tdmsAccrueService/accrueExceptionManageRest/downTemplate/forkliftModel',
                text: '叉车里程异常导入.xlsx',
              },
            ]}
            handleSyncImport={refresh}
            errorObj={{
              name: '工时异常导入错误日志.xlsx',
              url: '/tdmsAccrueService/accrueExceptionManageRest/upload/report',
            }}
          />
        </div>
      </div>
    </Form>
  );

  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        time: [
          moment()
            .subtract(1, 'months')
            .startOf('month'),
          moment().endOf('month'),
        ],
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
      });
      getQueryParams();
      refresh({ currentPage: 1 });
    }
  }, [userInfo, logRoleCode]);

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        disableColumns
        selectedRows={selectedRows}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {handleModalVisible && (
        <HandleModal
          initialValues={editingTarget}
          operationType={operationType}
          roleCode={logRoleCode.roleCode}
          visible={handleModalVisible}
          zoneCode={userInfo.deptCode}
          onCancel={() => {
            setHandleModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setHandleModalVisible(false);
            setEditingTarget(null);
            refresh({
              currentPage: operationType === 'add' ? 1 : undefined,
              pageSize: 10,
            });
          }}
          width={1400}
        />
      )}
      {batchModal && (
        <BatchApprove
          visible={batchModal}
          beChose={selectedRows}
          handleSearch={refresh}
          handleCancel={() => {
            setBatchModal(false);
          }}
        />
      )}
    </div>
  );
};
@connect(state => ({
  systemRole: state.global.roles,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
  logRoleCode: state.global.logRoleCode,
}))
class Container extends PureComponent {
  render() {
    const {
      systemRole,
      userInfo,
      areaListSF,
      areaListSX,
      logRoleCode,
    } = this.props;
    return (
      <Page
        systemRole={systemRole}
        userInfo={userInfo}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
        logRoleCode={logRoleCode}
      />
    );
  }
}

export default withRouter(Container);
