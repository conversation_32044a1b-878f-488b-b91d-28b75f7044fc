import React, { useState, useEffect } from 'react';
import { Modal } from 'antd';
import { connect } from 'dva';
import moment from 'moment';
import StandardTable from 'src/components/StandardTable';
import request from 'src/utils/request';

const columns = [
  {
    title: '操作事项',
    align: 'center',
    ellipsis: true,
    width: 150,
    dataIndex: 'approvalStatusName',
  },
  {
    title: '操作描述',
    align: 'center',
    ellipsis: true,
    width: 150,
    dataIndex: 'approvalWord',
  },
  {
    title: '操作人',
    align: 'center',
    ellipsis: true,
    width: 120,
    dataIndex: 'approvalUserNo',
    render: (v, row) => `${v} ${row.approvalUserName}`,
  },
  {
    title: '操作时间',
    align: 'center',
    ellipsis: true,
    width: 120,
    dataIndex: 'approvalTime',
    render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
  },
];
function DetaiInfor(props) {
  const { visible, beChose, handleCancel } = props;
  const { staffUuid } = beChose;
  const [loading, setLoading] = useState(false);
  const cancle = () => {
    handleCancel('approveRecord');
  };
  //   const [tableData, setTableData] = useState([]);
  const [tableData, setTableData] = useState({
    list: [],
    pagination: false,
  });
  const refresh = () => {
    setLoading(true);
    // TODO:
    request(`/tdmsAccrueService/approvalRest/seeApprovalDetails`, {
      method: 'POST',
      body: { staffUuid },
    }).then(res => {
      const { success: resSuccess, obj } = res;
      if (resSuccess) {
        setLoading(false);
        setTableData({
          pagination: false,
          list: obj,
        });
      } else {
        setLoading(false);
        setTableData([]);
      }
    });
  };

  useEffect(() => {
    refresh();
  }, [visible]);
  return (
    <Modal
      centered
      title={null}
      maskClosable={false}
      visible={visible}
      onCancel={cancle}
      width={1200}
      footer={null}
    >
      <StandardTable
        size="small"
        rowKey="approvalTime"
        showSelection={false}
        bordered={false}
        multiple={false}
        loading={loading}
        data={tableData}
        columns={columns}
      />
    </Modal>
  );
}
export default connect(() => ({}))(props => (
  <DetaiInfor {...props}></DetaiInfor>
));
