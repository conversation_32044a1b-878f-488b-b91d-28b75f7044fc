import React, { useState, useEffect, PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Form,
  Select,
  DatePicker,
  Button,
  Drawer,
  Tooltip,
} from 'antd';
import { ExportOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  formItemLayout,
  colStyle,
  rowStyle,
  //   AuthButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import { formatRatio } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import AsyncExport from 'src/components/AsyncExport';
import request from 'src/utils/request';
import ApproveRecord from '../ApproveRecord';
import SubmitApproval from '../SubmitApproval';
import Approval from '../Approval';
// import { approvalStatusList, APPROVAL_STATUS_LIST_OBJ } from '../status';
import PreviewDetail from '../StationPreviewDetail';

import './index.scss';
const { Item: FormItem } = Form;
// const { Option } = Select;
const DEFAULT_PAGINATION = { total: 0, current: 1, pageSize: 10 };

let inputParam = {};
const Page = props => {
  const { userInfo, logRoleCode, areaListSX, areaListSF, swicthTabs } = props;
  const moduleCode = useModuleCode();
  const [form] = Form.useForm();
  const [recordVisible, setRecordVisible] = useState(false); // 审核记录
  const [submitApproveVisible, setSubmitApproveVisible] = useState(false); // 提交审核
  const [approveVisible, setApproveVisible] = useState(false); // 审核
  const [rowTable, setRowTable] = useState(false); // 某一行的数据
  const [visibleClose, setVisibleClose] = useState(false);

  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: DEFAULT_PAGINATION,
    list: [],
  });

  const columns = [
    {
      title: '月份',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'accrueMonth',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: (v, row) => `${row.allocationAreaCode} ${v}`,
    },
    {
      title: '网点',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'zoneCode',
      render: (value, row) => `${row.zoneCode} ${row.zoneName}`,
    },
    {
      title: '上月效率奖金（实发）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'accrueAmountLastMonth',
    },
    {
      title: '上月效率奖金（系统）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'accrueAmountLastMonthOrgin',
    },
    {
      title: () => (
        <div>
          <p style={{ marginBottom: 0 }}>
            当月效率奖金（实发）
            <Tooltip
              overlayClassName="max-w-680"
              title="汇总统计所属月份该网点所有员工的“最终发放计提”之和"
            >
              {' '}
              <QuestionCircleOutlined />
            </Tooltip>
          </p>
        </div>
      ),
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'accrueAmount',
    },
    {
      title: () => (
        <div>
          <p style={{ marginBottom: 0 }}>
            当月效率奖金（系统）
            <Tooltip
              overlayClassName="max-w-680"
              title="汇总统计所属月份该网点所有员工的“累计计提（系统）”之和"
            >
              {' '}
              <QuestionCircleOutlined />
            </Tooltip>
          </p>
        </div>
      ),
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'accrueAmountOrgin',
    },
    {
      title: () => (
        <div>
          <p style={{ marginBottom: 0 }}>
            装卸计提奖金
            <Tooltip
              overlayClassName="max-w-680"
              title="汇总统计所属月份该网点操作员、操作组长岗位员工的计提奖金之和"
            >
              {' '}
              <QuestionCircleOutlined />
            </Tooltip>
          </p>
        </div>
      ),
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'loadUnloadAmount',
    },
    {
      title: () => (
        <div>
          <p style={{ marginBottom: 0 }}>
            叉车计提奖金
            <Tooltip
              overlayClassName="max-w-680"
              title="汇总统计所属月份该网点电叉司机、电叉组长岗位员工的计提奖金之和"
            >
              {' '}
              <QuestionCircleOutlined />
            </Tooltip>
          </p>
        </div>
      ),
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'forkliftAmount',
    },
    {
      title: () => (
        <div>
          <p style={{ marginBottom: 0 }}>
            双库操作员计提奖金
            <Tooltip
              overlayClassName="max-w-680"
              title="汇总统计所属月份该网点双库操作员岗位员工的计提奖金之和"
            >
              {' '}
              <QuestionCircleOutlined />
            </Tooltip>
          </p>
        </div>
      ),
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'loadFixAmount',
    },
    {
      title: () => (
        <div>
          <p style={{ marginBottom: 0 }}>
            机叉计提奖金
            <Tooltip
              overlayClassName="max-w-680"
              title="汇总统计所属月份该网点机叉司机岗位员工的计提奖金之和"
            >
              {' '}
              <QuestionCircleOutlined />
            </Tooltip>
          </p>
        </div>
      ),
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'forkliftMachineAmount',
    },

    {
      title: '装卸货量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'loadUnloadWeight',
      render: value => formatRatio(value, 'decimals', 2),
    },
    {
      title: '叉车货量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'forkliftWeight',
      render: value => formatRatio(value, 'decimals', 2),
    },
    {
      title: '机叉货量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'forkliftMachineWeight',
      render: value => formatRatio(value, 'decimals', 2),
    },
    {
      title: '叉车工作量补发补扣',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'forkliftFixWeight',
      render: value => {
        if (typeof value === 'number') {
          if (value === 0) return 0;
          return (
            <span style={{ color: value > 0 ? 'red' : 'green' }}>
              {formatRatio(value, 'decimals', 2)}
            </span>
          );
        }
        return '--';
      },
    },
    {
      title: '短期方案激励',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'shortPlanIncentives',
      render: value => formatRatio(value, 'decimals', 2),
    },
    {
      title: '工作变更补发补扣',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'changeJobIncentives',
      render: value => formatRatio(value, 'decimals', 2),
    },
    {
      title: '其他补发补扣',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'approvalBuckle',
      render: value => formatRatio(value, 'decimals', 2),
    },
    // v14.0 去掉审核
    // {
    //   title: '审核状态',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 150,
    //   dataIndex: 'approvalStatus',
    //   render: value => APPROVAL_STATUS_LIST_OBJ[value]?.label ?? value,
    // },
    {
      title: '操作',
      align: 'center',
      ellipsis: true,
      width: 200,
      fixed: 'right',
      dataIndex: 'operate',
      render: (v, record) => (
        // const baseMoment = moment(record.accrueMonth).add(1, 'month');
        // const submitStartTime = baseMoment.add(4, 'day').valueOf(); // 次月4号23:59:59固化数据后才显示按钮
        // const submitDeadline = baseMoment
        //   // 初次提交截止时间为 6号18点
        //   // 驳回提交截止时间为 8号18点
        //   // 月份开始时间为 1号，在 add 的时候需要注意这点
        //   .add(record.approvalStatus === 0 ? 5 : 7, 'day')
        //   .add(18, 'hour')
        //   .valueOf();

        <Fragment>
          <Button
            size="small"
            type="link"
            onClick={e => checkDetail(e, record)}
          >
            查看详情
          </Button>
          {/* <Button
              type="link"
              onClick={e => clickBtn(e, record, 'approveRecord')}
            >
              审核记录
            </Button> */}

          {/* 待提交/驳回后修改 */}
          {/* {(record.approvalStatus === 0 || record.approvalStatus === 8) &&
              Date.now() >= submitStartTime &&
              Date.now() < submitDeadline && (
                <AuthButton
                  modulecode={moduleCode}
                  code="sxAccrualPayable-submitApprove"
                  type="link"
                  onClick={e => clickBtn(e, record, 'submitApprove')}
                >
                  提交审核
                </AuthButton>
              )} */}

          {/* 待分拨区审核/待总部审核 */}
          {/* {((record.approvalStatus === 2 &&
              logRoleCode.roleCode === '88888888888') ||
              (record.approvalStatus === 6 &&
                logRoleCode.roleCode === 'baspAdmin')) && (
              <AuthButton
                modulecode={moduleCode}
                code="sxAccrualPayable-approve"
                type="link"
                onClick={e => clickBtn(e, record, 'approve')}
              >
                审核
              </AuthButton>
            )} */}
        </Fragment>
      ),
    },
  ];

  // 查询参数的获取
  const getQueryParams = (pagination = {}) => {
    const {
      currentPage: pageNum = DEFAULT_PAGINATION.current,
      pageSize = DEFAULT_PAGINATION.pageSize,
    } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    inputParam.accrueMonth = moment(inputParam.accrueMonth).format('YYYY-MM');
    refresh({ pageNum, pageSize, inputParam });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: pageNum = DEFAULT_PAGINATION.current,
      pageSize = DEFAULT_PAGINATION.pageSize,
    } = pagination;
    const param = { ...inputParam, pageNum, pageSize };
    request(`/tdmsAccrueService/accrueMonthSxStaffDetailRest/query`, {
      method: 'POST',
      body: param,
    })
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum: current, total } = obj;
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current,
              pageSize,
            },
            list,
          });
        } else {
          setLoading(false);
          setDatas({
            pagination: DEFAULT_PAGINATION,
            list: [],
          });
        }
      })
      .catch(() => {
        setLoading(false);
      });
  };

  //   const clickBtn = (e, record, value) => {
  //     e.stopPropagation();
  //     setRowTable(record);
  //     closeModal(value, true);
  //   };

  useEffect(() => {
    form.setFieldsValue({
      zoneCode: userInfo.deptCode,
      accrueMonth: moment(new Date()).subtract(1, 'months'),
      allocationAreaCode: userInfo.areaCode,
    });
    getQueryParams();
  }, [userInfo]);

  useEffect(() => {
    // 当前登录的角色判断
    if (
      logRoleCode &&
      userInfo &&
      userInfo.deptCode &&
      (logRoleCode.roleCode === 'tp00001' ||
        logRoleCode.roleCode === '88888888888')
    ) {
      form.setFieldsValue({
        zoneCode: userInfo.deptCode,
        accrueMonth: moment(new Date()).subtract(1, 'months'),
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
      });
      getQueryParams();
    } else {
      form.setFieldsValue({
        accrueMonth: moment(new Date()).subtract(1, 'months'),
      });
    }
  }, [logRoleCode, userInfo]);

  const handleStandardTableChange = pagination => {
    setSelectedRows([]);

    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const closeModal = (value, isTrue) => {
    if (value === 'submitApprove') {
      // 提交审核;
      setSubmitApproveVisible(isTrue);
    } else if (value === 'approveRecord') {
      // 审核记录
      setRecordVisible(isTrue);
    } else if (value === 'approve') {
      // 审核;
      setApproveVisible(isTrue);
    }
  };

  // 选择数据
  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const onDBRowClick = value => {
    swicthTabs('2', value);
  };
  //   查看详情
  const checkDetail = (e, row) => {
    e.stopPropagation();
    setRowTable(row);
    setVisibleClose(true);
  };

  const onClose = () => {
    setVisibleClose(false);
  };

  //   const onRowDBShow = row => {
  //     setRowTable(row);
  //     setVisibleClose(true);
  //   };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={{
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ||
          logRoleCode.roleCode === '88888888888'
            ? userInfo.deptCode
            : undefined,
        accrueMonth: moment(new Date()).subtract(1, 'months'),
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="查询月份"
                name="accrueMonth"
                rules={[
                  {
                    required: true,
                    message: '请选择月份',
                  },
                ]}
              >
                <DatePicker
                  showToday
                  picker="month"
                  allowClear
                  style={{ width: '100%' }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            {/* <Col {...colStyle}>
              <FormItem label="审核状态" name="approvalStatus">
                <Select allowClear placeholder="请选择审核状态">
                  {approvalStatusList.map(ele => (
                    <Option key={ele.value}>{ele.label}</Option>
                  ))}
                </Select>
              </FormItem>
            </Col> */}
          </Row>
        </SearchFold>
      </div>

      <div className="btn-con">
        <p className="SxAccrueManage__SxAccrueManage__StationPayableDetail-text--danger">
          上月场站应付数据请在本月6号18:00前提交
        </p>

        <div>
          <AsyncExport
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="sxAccrualPayable-exportSelect"
            text="导出所选"
            disabled={selectedRows.length < 1}
            icon={<ExportOutlined />}
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueMonthSxStaffDetailRest/queryExportSync',
                {
                  method: 'POST',
                  body: {
                    idList: selectedRows.map(ele => ele.id),
                  },
                },
              ],
            }}
          />
          <AsyncExport
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="sxAccrualPayable-exportAll"
            type="primary"
            disabled={datas.list && datas.list.length < 1}
            text="导出全部"
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueMonthSxStaffDetailRest/queryExportSync',
                {
                  method: 'POST',
                  body: inputParam,
                },
              ],
            }}
          />
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>

      <StandardTable
        size="small"
        rowKey="id"
        loading={loading}
        data={datas}
        columns={columns}
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
        onDBClick={onDBRowClick}
      />
      {/* 查看详情 */}
      <Drawer
        width={900}
        placement="right"
        closable={false}
        onClose={onClose}
        visible={visibleClose}
      >
        <PreviewDetail rowValue={rowTable} />
      </Drawer>

      {/* 审核记录 */}
      {recordVisible && (
        <ApproveRecord
          beChose={rowTable}
          visible={recordVisible}
          handleCancel={closeModal}
        ></ApproveRecord>
      )}

      {/* 提交审核 */}
      {submitApproveVisible && (
        <SubmitApproval
          beChose={rowTable}
          visible={submitApproveVisible}
          handleSearch={refresh}
          handleCancel={closeModal}
        ></SubmitApproval>
      )}

      {/* 审核 */}
      {approveVisible && (
        <Approval
          beChose={rowTable}
          visible={approveVisible}
          handleSearch={refresh}
          handleCancel={closeModal}
        ></Approval>
      )}
    </div>
  );
};

@connect(state => ({
  userInfo: state.global.userInfo,
  logRoleCode: state.global.logRoleCode,

  systemRole: state.global.roles,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const {
      userInfo,
      areaListSF,
      areaListSX,
      swicthTabs,
      logRoleCode,
    } = this.props;
    return (
      <Page
        userInfo={userInfo}
        logRoleCode={logRoleCode}
        areaListSX={areaListSX}
        areaListSF={areaListSF}
        swicthTabs={swicthTabs}
      />
    );
  }
}

export default withRouter(Container);
