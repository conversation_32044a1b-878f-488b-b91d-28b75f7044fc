// 01385149 测试工号; 顺心计提应付--人员应付明细
import React, { useState, useEffect, PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Form,
  Select,
  DatePicker,
  Input,
  Drawer,
  Button,
  message,
  Modal,
  Tooltip,
} from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import { formatRatio } from 'src/utils/utils';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import AsyncExport from 'src/components/AsyncExport';
import ImportButton from 'src/components/ImportButton';
import AuthButton from 'src/components/AuthButton';
import request from 'src/utils/request';
import { operate } from './api';
import {
  positionList,
  logoutList,
  ACCRUE_TYPE_DICT,
  statusList,
  //   FORKLIFT_AACCRUE_TYPE_MAP,
} from '../status';
import PreviewDetail from '../PreviewDetail';
import BatchApprove from '../BatchApprove';

import './index.scss';
// const { TextArea } = Input;
const { Item: FormItem } = Form;
let inputParam = {};
const Page = ({ userInfo, areaListSX, areaListSF, rowValue, logRoleCode }) => {
  const moduleCode = useModuleCode();
  const [form] = Form.useForm();
  // const [roleCode, setRoleCode] = useState(null); // 角色编码
  const [rowTable, setRowTable] = useState({}); // 某一行的数据
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  // const [previewClose, setPreviewClose] = useState(false);
  const [visibleClose, setVisibleClose] = useState(false);
  const [batchModal, setBatchModal] = useState(false); // 审核框显示隐藏
  const [batchApproveIdList, setBatchApproveIdList] = useState([]); // 审核id

  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '基础信息',
      children: [
        {
          title: '流水号',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'staffUuid',
        },
        {
          title: '月份',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'accrueMonth',
        },
        {
          title: '分拨区代码',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'allocationAreaCode',
        },
        {
          title: '分拨区',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'allocationArea',
        },
        {
          title: '网点代码',
          align: 'center',
          ellipsis: true,
          width: 150,
          dataIndex: 'zoneCode',
        },
        {
          title: '网点名称',
          align: 'center',
          ellipsis: true,
          width: 150,
          dataIndex: 'zoneName',
        },
        {
          title: '工号',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'userNo',
        },
        {
          title: '姓名',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'userName',
        },
        {
          title: '发薪岗位',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'position',
        },
        {
          title: '入职日期',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'registerDt',
          render(v) {
            return v ? moment(v).format('YYYY-MM-DD') : '';
          },
        },
        {
          title: '是否离职',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'logoutFlag',
          render(v) {
            return v !== null ? logoutList.map(item => item.label)[v] : '-';
          },
        },
        {
          title: '离职日期',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'logoutDt',
          render(v) {
            return v ? moment(v).format('YYYY-MM-DD') : '-';
          },
        },
      ],
    },
    // 一个岗位计费类型只会有一种
    {
      title: '计费类型',
      children: [
        {
          title: '计费类型',
          align: 'center',
          ellipsis: true,
          width: 120,
          dataIndex: 'accrueType',
          render(v) {
            return ACCRUE_TYPE_DICT[v];
          },
        },
      ],
    },
    {
      title: '计提应付',
      children: [
        {
          title: () => (
            <div>
              <p style={{ marginBottom: 0 }}>
                最终发放计提
                <Tooltip
                  overlayClassName="max-w-680"
                  title="最终发放计提=累计计提(系统）+老带新激励+返岗激励+兼职机叉激励+支援大件激励+工作变更补发补扣+其他补发补扣"
                >
                  {' '}
                  <QuestionCircleOutlined />
                </Tooltip>
              </p>
            </div>
          ),
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'approvalAccrueAmount',
        },
        {
          title: () => (
            <div>
              <p style={{ marginBottom: 0 }}>
                累计计提(系统)
                <Tooltip
                  overlayClassName="max-w-680"
                  title="累计计提(系统)=计提奖金加上“叉车工作量补发补扣”和“系数”的计算"
                >
                  {' '}
                  <QuestionCircleOutlined />
                </Tooltip>
              </p>
            </div>
          ),
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'accrueAmount',
        },
      ],
    },
    {
      title: '计提奖金',
      children: [
        {
          title: () => (
            <div>
              <p style={{ marginBottom: 0 }}>
                计提奖金
                <Tooltip
                  overlayClassName="max-w-680"
                  title={
                    <>
                      岗位=操作员、操作组长时，计提奖金=（省际装车总量-省际装车总量/装卸总量合计*装卸应扣保底货量）*省际装车单价+（省内装车总量-省内装车总量/装卸总量合计*装卸应扣保底货量）*省内装车单价+（卸车总量-卸车总量/装卸总量合计*装卸应扣保底货量）*卸车单价
                      <br />
                      <br />
                      岗位=电叉司机、电叉组长时，优先级1：卡均，计提奖金=（叉车卡均货量-叉车应扣保底货量）*叉车单价
                      <br />
                      <br />
                      优先级2：板均，计提奖金=（叉车板均货量-叉车应扣保底货量）*叉车单价
                      <br />
                      <br />
                      优先级3：叉车计重，计提奖金=（叉车操作货量-叉车应扣保底货量）*叉车单价
                      <br />
                      <br />
                      优先级4：计板，计提奖金=（叉车托盘数-叉车应扣保底货量）*叉车单价
                      <br />
                      <br />
                      岗位=双库操作员时，计提奖金=双库操作员绩效
                      <br />
                      <br />
                      岗位=机叉司机时，优先级1：固定绩效，计提奖金=机叉绩效+叉车卡均货量/板均货量/操作货量/托盘数*叉车提成单价
                      <br />
                      <br />
                      优先级2：卡均，计提奖金=（叉车卡均货量-叉车应扣保底货量）*叉车单价
                      <br />
                      <br />
                      优先级3：板均，计提奖金=（叉车板均货量-叉车应扣保底货量）*叉车单价
                      <br />
                      <br />
                      优先级4：叉车计重，计提奖金=（叉车操作货量-叉车应扣保底货量）*叉车单价
                      <br />
                      <br />
                      优先级5：计板，计提奖金=（叉车托盘数-叉车应扣保底货量）*叉车单价
                    </>
                  }
                >
                  {' '}
                  <QuestionCircleOutlined />
                </Tooltip>
              </p>
            </div>
          ),
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'loadUnloadAmount',
        },
        // {
        //   title: '叉车计提奖金',
        //   align: 'center',
        //   ellipsis: true,
        //   width: 100,
        //   dataIndex: 'forkliftAmount',
        // },
      ],
    },
    {
      title: '计提货量',
      children: [
        {
          title: '卸车货量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'unloadWeight',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '省际装车货量',

          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'loadWeight',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '省内装车货量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'provinceLoadWeight',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '支线装车货量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'branchLoadWeight',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '叉车托盘数',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftWorks',
          render: value => formatRatio(value, 'decimals', 0),
        },
        {
          title: '叉车卡口数',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftKaKou',
          render: value => formatRatio(value, 'decimals', 0),
        },
        {
          title: '叉车操作货量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftWeight',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '叉车板均货量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftPalletWeight',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '叉车卡均货量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftCardWeight',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '叉车小件货量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftSmallWeight',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '叉车大件货量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftBigWeight',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '皮带机分拣货量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'beltSortWeight',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '皮带机叉车货量',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'beltForkliftWeight',
          render: value => formatRatio(value, 'decimals', 2),
        },
      ],
    },
    {
      title: ' 计提单价',
      children: [
        {
          title: '卸车单价',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'unloadPrice',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '省际装车单价',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'loadPrice',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '省内装车单价',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'provinceLoadPrice',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '支线装车单价',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'branchLoadPrice',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '叉车单价',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftPrice',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '叉车小件单价',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftSmallPrice',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '叉车大件单价',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftBigPrice',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '双库操作员绩效',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftFixPrice',
          //   render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '机叉绩效',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftMachinePrice',
          //   render: value => formatRatio(value, 'decimals', 2),
        },
      ],
    },
    {
      title: '保底',
      children: [
        {
          title: '装卸月保底',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'loadUnloadAmountMin',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '叉车月保底',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'forkliftAmountMin',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: () => (
            <div>
              <p style={{ marginBottom: 0 }}>
                装卸应扣保底货量
                <Tooltip
                  overlayClassName="max-w-680"
                  title='装卸应扣保底货量=MIN[(顺心装卸车月保底/配置出勤天数)*出勤天数,月保底]（出勤天数优先取“分拨区导入出勤天数”，其次取"系统出勤天数"）'
                >
                  {' '}
                  <QuestionCircleOutlined />
                </Tooltip>
              </p>
            </div>
          ),
          align: 'center',
          ellipsis: true,
          width: 130,
          dataIndex: 'loadUnloadAmountCut',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: () => (
            <div>
              <p style={{ marginBottom: 0 }}>
                叉车应扣保底货量
                <Tooltip
                  overlayClassName="max-w-680"
                  title='叉车应扣保底货量=MIN[(顺心叉车月保底/配置出勤天数)*出勤天数,月保底]（出勤天数优先取“分拨区导入出勤天数”，其次取"系统出勤天数"）'
                >
                  {' '}
                  <QuestionCircleOutlined />
                </Tooltip>
              </p>
            </div>
          ),
          align: 'center',
          ellipsis: true,
          width: 130,
          dataIndex: 'forkliftAmountCut',
          render: value => formatRatio(value, 'decimals', 2),
        },
      ],
    },
    {
      title: '工作量补发补扣',
      children: [
        {
          title: '叉车工作量补发补扣',
          align: 'center',
          ellipsis: true,
          width: 140,
          dataIndex: 'approvalForkliftWorks',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '工作量补发补扣原因',
          align: 'center',
          ellipsis: true,
          width: 150,
          dataIndex: 'approvalForkliftWorksMsg',
          render: text => text || '--',
          onCell: () => ({
            style: {
              maxWidth: 150,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              cursor: 'pointer',
            },
          }),
        },
      ],
    },
    {
      title: '短期方案激励',
      children: [
        {
          title: '老带新激励',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'oldWithNewIncentives',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '返岗激励',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'returnWorkIncentives',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '兼职机叉激励',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'partTimeForkIncentives',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '支援大件激励',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'supportingSfIncentives',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '短期方案激励原因',
          align: 'center',
          ellipsis: true,
          width: 150,
          dataIndex: 'shortPlanIncentivesMsg',
          render: text => text || '--',
          onCell: () => ({
            style: {
              maxWidth: 150,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              cursor: 'pointer',
            },
          }),
        },
      ],
    },
    {
      title: '工种变更补发补扣',
      children: [
        {
          title: '工种变更补发补扣',
          align: 'center',
          ellipsis: true,
          width: 130,
          dataIndex: 'changeJobIncentives',
          render: value => formatRatio(value, 'decimals', 2),
        },
      ],
    },
    {
      title: '其他补发补扣',
      children: [
        {
          title: '其他补发补扣',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'approvalBuckle',
          render: value => formatRatio(value, 'decimals', 2),
        },
        {
          title: '其他补发补扣原因',
          align: 'center',
          ellipsis: true,
          width: 160,
          dataIndex: 'approvalBuckleTxt',
          render: text => text || '--',
          onCell: () => ({
            style: {
              maxWidth: 160,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              cursor: 'pointer',
            },
          }),
        },
      ],
    },
    {
      title: '系数',
      children: [
        {
          title: '双库操作员系数',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'doubleOperator',
        },
        {
          title: '机叉系数',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'machineFork',
        },
        {
          title: '系数说明',
          align: 'center',
          ellipsis: true,
          width: 200,
          dataIndex: 'coefficientMsg',
          onCell: () => ({
            style: {
              maxWidth: 200,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              cursor: 'pointer',
            },
          }),
        },
      ],
    },
    {
      title: '出勤天数',
      children: [
        {
          title: '配置出勤天数',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'systemAttendNum',
        },
        {
          title: '系统出勤天数',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'attendNum',
        },
        {
          title: '分拨区导入出勤天数',
          align: 'center',
          ellipsis: true,
          width: 80,
          dataIndex: 'approvalAttendNum',
        },
      ],
    },
    {
      title: '操作记录',
      children: [
        {
          title: '补发补扣导入人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'approvalBuckleCreator',
        },
        {
          title: '补发补扣导入时间',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'approvalBuckleTime',
          render(v) {
            return v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '';
          },
        },
        {
          title: '出勤天数导入人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'approvalAttendNumCreator',
        },
        {
          title: '出勤天数导入时间',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'approvalAttendNumTime',
          render(v) {
            return v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '';
          },
        },
        {
          title: '出勤天数修改说明',
          align: 'center',
          width: 150,
          dataIndex: 'approvalAttendNumMsg',
          render: text => text || '--',
          onCell: () => ({
            style: {
              maxWidth: 150,
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              cursor: 'pointer',
            },
          }),
        },
        {
          title: '提交人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'submitter',
        },
        {
          title: '提交时间',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'submitTime',
          render(v) {
            return v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '';
          },
        },
        {
          title: '地区审核人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'areaUserNo',
        },
        {
          title: '地区审核时间',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'areaApprovalTime',
          render(v) {
            return v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '';
          },
        },
        {
          title: '总部审核人',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'leaderUserNo',
        },
        {
          title: '总部审核时间',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'leaderApprovalTime',
          render(v) {
            return v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '';
          },
        },
        {
          title: '驳回理由',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'approvalWord',
        },
      ],
    },
    {
      title: '状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'approvalStatus',
      render(v) {
        return v !== null ? statusList.map(item => item.label)[v] : '-';
      },
    },
    {
      title: '操作',
      align: 'center',
      width: 200,
      fixed: 'right',
      dataIndex: 'operation',
      render: (_, record) => (
        <Fragment>
          {/* 待提交,待修改显示提交 */}
          {[1, 4].includes(record.approvalStatus) &&
            moment(record.accrueMonth).isSameOrAfter(
              getLastMonthFirstDay(),
              'month',
            ) && (
              <AuthButton
                moduleCode={moduleCode}
                code="sxAccrualPayable-personSubmit"
                size="small"
                type="link"
                onClick={() => {
                  handleSubmit([record.id]);
                }}
              >
                提交
              </AuthButton>
            )}
          {/* 地区审核 */}
          {record.approvalStatus === 2 &&
            moment(record.accrueMonth).isSameOrAfter(
              getLastMonthFirstDay(),
              'month',
            ) && (
              <AuthButton
                moduleCode={moduleCode}
                code="sxAccrualPayable-personApprove"
                size="small"
                type="link"
                onClick={() => {
                  operationModal([record.id]);
                }}
              >
                审核
              </AuthButton>
            )}
          {/* 总部审核 */}
          {record.approvalStatus === 3 &&
            moment(record.accrueMonth).isSameOrAfter(
              getLastMonthFirstDay(),
              'month',
            ) && (
              <AuthButton
                moduleCode={moduleCode}
                code="sxAccrualPayable-HQpersonApprove"
                size="small"
                type="link"
                onClick={() => {
                  operationModal([record.id]);
                }}
              >
                审核
              </AuthButton>
            )}
          <Button
            size="small"
            type="link"
            onClick={e => checkDetail(e, record)}
          >
            查看详情
          </Button>
        </Fragment>
      ),
    },
  ];

  /** 默认查询月份 */
  const DEFAULT_ACCRUE_MONTH = moment(new Date()).subtract(1, 'month');

  // 辅助函数：获取上个月的第一天
  const getLastMonthFirstDay = () => {
    const currentDate = moment();
    return currentDate
      .clone()
      .subtract(1, 'month')
      .startOf('month');
  };
  const checkDetail = (e, row) => {
    e.stopPropagation();
    setRowTable(row);
    setVisibleClose(true);
  };

  const onClose = () => {
    setVisibleClose(false);
  };

  const onRowDBShow = row => {
    setRowTable(row);
    setVisibleClose(true);
  };

  // 查询参数的获取
  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    inputParam.accrueMonth = moment(inputParam.accrueMonth).format('YYYY-MM');
    inputParam.position = inputParam.position && inputParam.position.join(',');
    inputParam.userNoList = inputParam.userNoList
      ?.split(/[,，]/)
      .map(item => item.trim())
      .filter(item => item);

    if (inputParam.userNoList?.length === 0) {
      inputParam.userNoList = undefined;
    }

    refresh({ pageNum, pageSize, inputParam });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    setSelectedRows([]);
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = { ...inputParam, pageNum, pageSize };
    request(
      `/tdmsAccrueService/accrueMonthSxStaffDetailRest/queryStaffDetail`,
      {
        method: 'POST',
        body: param,
      },
    )
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum: current, total } = obj;
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current,
              pageSize,
            },
            list: list || [],
          });
        } else {
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    if (userInfo && userInfo.deptCode) {
      if (rowValue) {
        form.setFieldsValue({
          zoneCode: rowValue.zoneCode,
          accrueMonth: moment(rowValue.accrueMonth),
        });
      } else {
        form.setFieldsValue({
          zoneCode: userInfo.deptCode,
          accrueMonth: DEFAULT_ACCRUE_MONTH,
        });
      }
      getQueryParams();
    }
  }, [userInfo, rowValue]);

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 选择数据
  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  // 操作--审核 type:待地区审核，待总部审核
  const batchOperation = (selectedData, approvalStatus, type) => {
    const errStatus = selectedData.some(
      item => item.approvalStatus !== approvalStatus,
    );
    if (errStatus) {
      message.warning(`存在状态不为【${type}】的数据，请核对后再提交`);
      return false;
    }

    operationModal(selectedData.map(item => item.id));
  };

  const operationModal = idList => {
    setBatchApproveIdList(idList);
    setBatchModal(true);
  };

  const handleSubmit = idList => {
    Modal.confirm({
      title: '提交',
      content: '确认提交选中数据？',
      onOk() {
        const params = {
          idList,
          approvalStatus: 1,
        };
        operate(params).then(res => {
          if (res.success) {
            message.success('提交成功');
            refresh();
          }
        });
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  const handleBatchSubmit = selectedData => {
    const errStatus = selectedData.some(item => item.approvalStatus !== 1);
    if (errStatus) {
      message.warning('存在状态不为【待提交】的数据,请核对后再提交');
      return false;
    }
    handleSubmit(selectedData.map(item => item.id));
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={{
        zoneCode: userInfo.deptCode,
        accrueMonth: DEFAULT_ACCRUE_MONTH,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="查询月份"
                name="accrueMonth"
                rules={[
                  {
                    required: true,
                    message: '请选择月份',
                  },
                ]}
              >
                <DatePicker
                  showToday
                  picker="month"
                  allowClear
                  style={{ width: '100%' }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  // style={{backgroundColor}}
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="工号" name="userNoList">
                <Input allowClear placeholder="请输入工号，多个用逗号隔开" />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="姓名" name="userName">
                <Input allowClear placeholder="请输入" />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="发薪岗位" name="position">
                <Select
                  mode="multiple"
                  allowClear
                  options={positionList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="是否离职" name="logoutFlag">
                <Select
                  allowClear
                  options={logoutList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="流水号" name="staffUuid">
                <Input allowClear placeholder="请输入" />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="状态" name="approvalStatus">
                <Select
                  allowClear
                  options={statusList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <ImportButton
            style={{ marginRight: 15 }}
            danger={false}
            type="primary"
            pagename="导入补发补扣"
            moduleCode={moduleCode}
            code="sxAccrualPayable-detailImport"
            title="导入补发补扣"
            action="/tdmsAccrueService/accrueMonthSxStaffDetailRest/importForkliftWeight"
            modalUrl="/tdmsAccrueService/accrueMonthSxStaffDetailRest/downLoadForkliftWeightTemplate"
            modalName="导入补发补扣模板.xlsx"
            hideDownloadErrorLog
            handleSyncImport={refresh}
          />
          <ImportButton
            danger={false}
            style={{ marginRight: 15 }}
            type="primary"
            pagename="导入出勤天数"
            moduleCode={moduleCode}
            code="sxAccrualPayable-supplement"
            title="导入出勤天数"
            action="/tdmsAccrueService/accrueMonthSxStaffDetailRest/importAttendNum"
            modalUrl="/tdmsAccrueService/accrueMonthSxStaffDetailRest/downLoadAttendNumTemplate"
            modalName="导入出勤天数模板.xlsx"
            hideDownloadErrorLog
            handleSyncImport={refresh}
          />
          <AsyncExport
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="sxAccrualPayable-detailExport"
            type="primary"
            disabled={datas.list && datas.list.length < 1}
            text="导出"
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueMonthSxStaffDetailRest/exportSync',
                {
                  method: 'POST',
                  body: inputParam,
                },
              ],
            }}
          />
          <AsyncExport
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="sxAccrualPayable-detailDayExport"
            type="primary"
            disabled={datas.list && datas.list.length < 1}
            text="导出人员每日明细"
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueMonthSxStaffDetailRest/exportSyncStaff',
                {
                  method: 'POST',
                  body: inputParam,
                },
              ],
            }}
          />
          <AuthButton
            moduleCode={moduleCode}
            code="sxAccrualPayable-personSubmit"
            disabled={!selectedRows.length}
            onClick={() => handleBatchSubmit(selectedRows)}
            type="primary"
            style={{ marginRight: 15 }}
          >
            批量提交
          </AuthButton>
          <AuthButton
            moduleCode={moduleCode}
            code="sxAccrualPayable-personApprove"
            disabled={!selectedRows.length}
            onClick={() => batchOperation(selectedRows, 2, '待地区审核')}
            type="primary"
            style={{ marginRight: 15 }}
          >
            地区批量审核
          </AuthButton>
          <AuthButton
            moduleCode={moduleCode}
            code="sxAccrualPayable-HQpersonApprove"
            disabled={!selectedRows.length}
            onClick={() => batchOperation(selectedRows, 3, '待总部审核')}
            type="primary"
            style={{ marginRight: 15 }}
          >
            总部批量审核
          </AuthButton>
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        loading={loading}
        data={datas}
        columns={columns}
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
        onRow={record => ({
          onClick: () => {},
          onDoubleClick: () => {
            onRowDBShow(record);
          },
        })}
      />
      <Drawer
        width={900}
        placement="right"
        closable={false}
        onClose={onClose}
        visible={visibleClose}
      >
        <PreviewDetail rowValue={rowTable} />
      </Drawer>
      {batchModal && (
        <BatchApprove
          visible={batchModal}
          idList={batchApproveIdList}
          handleSearch={refresh}
          handleCancel={() => {
            setBatchModal(false);
          }}
        />
      )}
    </div>
  );
};

@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  systemRole: state.global.roles,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const {
      dispatch,
      history,
      userInfo,
      areaListSF,
      areaListSX,
      systemRole,
      rowValue,
      logRoleCode,
    } = this.props;
    return (
      <Page
        dispatch={dispatch}
        logRoleCode={logRoleCode}
        history={history}
        systemRole={systemRole}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
        userInfo={userInfo}
        rowValue={rowValue}
      />
    );
  }
}

export default withRouter(Container);
