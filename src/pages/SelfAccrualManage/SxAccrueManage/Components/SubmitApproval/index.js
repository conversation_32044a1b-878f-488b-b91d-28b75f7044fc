import React, { useState } from 'react';
import { Modal, Button, Form, Input, message } from 'antd';
import { success, error } from 'src/utils/utils';
import request from 'src/utils/request';

const { Item: FormItem } = Form;
const { TextArea } = Input;

const tailLayout = {
  wrapperCol: { offset: 8, span: 16 },
};
function Remark(props) {
  const { visible, beChose, handleSearch, handleCancel } = props;
  const { staffUuid } = beChose;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const cancel = () => {
    form.resetFields();
    handleCancel('submitApprove');
  };

  const submit = () => {
    form.validateFields().then(data => {
      setLoading(true);
      if (staffUuid) {
        Object.assign(data, {
          staffUuid,
        });
      }
      request(`/tdmsAccrueService/approvalRest/submitApproval`, {
        method: 'POST',
        body: data,
      })
        .then(res => {
          setLoading(false);
          if (res.success) {
            success('修改成功！');
            cancel();
            handleSearch();
          } else {
            error(res.errorMessage);
          }
        })
        .catch(err => {
          setLoading(false);
          message.error(err);
        });
    });
  };

  return (
    <div>
      <Modal
        maskClosable={false}
        title="提交审核"
        width={500}
        visible={visible}
        onCancel={cancel}
        footer={null}
        destroyOnClose
      >
        <Form form={form}>
          <FormItem label="说明描述" name="approvalWord">
            <TextArea maxLength={50} showCount />
          </FormItem>
          <Form.Item {...tailLayout}>
            <Button onClick={cancel}>取消</Button>
            <Button
              type="primary"
              onClick={submit}
              loading={loading}
              style={{ marginLeft: '20px' }}
            >
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
export default Remark;
