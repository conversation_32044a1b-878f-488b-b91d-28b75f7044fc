import React, { useEffect, useState } from 'react';
import { Descriptions, Divider } from 'antd';
import moment from 'moment';
// import request from 'src/utils/request';
import {
  baseInfoList,
  accrueMomenyStation,
  accrueCorgoStation,
  ACCRUE_TYPE_DICT,
  approvalBuckleStation,
} from '../status';
import './index.scss';

const PreviewDetails = props => {
  const { rowValue } = props;
  const [showDetail, setShowDetail] = useState(true);

  useEffect(() => {
    // if (rowValue) {
    //   request(`/tdmsAccrueService/accrueMothStaffRest/isAvailable`, {
    //     method: 'POST',
    //     body: {
    //       zoneCode: rowValue.zoneCode,
    //       accrueMonth: rowValue.accrueMonth,
    //     },
    //   }).then(res => {
    //     setShowDetail(!res.obj);
    //   });
    // }
  }, [rowValue]);

  return (
    <div className="station-preview">
      <Descriptions column={4} title="基本信息">
        {baseInfoList.map(({ label, value }) => (
          <Descriptions.Item span={2} label={label} key={label}>
            {value.includes('unknown')
              ? value
              : value === 'accrueType'
              ? ACCRUE_TYPE_DICT[rowValue[value]]
              : value === 'approvalBuckleTime' ||
                value === 'approvalAttendNumTime'
              ? rowValue[value]
                ? moment(rowValue[value]).format('YYYY-MM-DD HH:mm:ss')
                : ''
              : rowValue[value]}
          </Descriptions.Item>
        ))}
      </Descriptions>
      <Divider />
      {showDetail && (
        <div>
          <div style={{ display: 'flex' }}>
            <Descriptions title="计提奖金" column={4} style={{ flex: 1 }}>
              {accrueMomenyStation.map(({ title, value }) => (
                <Descriptions.Item span={2} label={title} key={value}>
                  {value.includes('unknown') ? value : rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </div>
          <Divider />
          <Descriptions title="计提货量" column={4} style={{ flex: 1 }}>
            {accrueCorgoStation.map(({ title, value }) => (
              <Descriptions.Item
                span={2}
                label={title}
                key={value}
                className={
                  value === 'approvalForkliftWorksMsg'
                    ? 'SelfAccrualManage__SxAccrueManage__PreviewDetail__flex-wrap'
                    : undefined
                }
              >
                {value.includes('unknown') ? value : rowValue[value]}
              </Descriptions.Item>
            ))}
          </Descriptions>
          <Divider />
          <div style={{ display: 'flex' }}>
            <Descriptions title="补发补扣" column={4} style={{ flex: 1 }}>
              {approvalBuckleStation.map(({ title, value }) => (
                <Descriptions.Item span={2} label={title} key={value}>
                  {value.includes('unknown') ? value : rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </div>
        </div>
      )}
    </div>
  );
};
export default PreviewDetails;
