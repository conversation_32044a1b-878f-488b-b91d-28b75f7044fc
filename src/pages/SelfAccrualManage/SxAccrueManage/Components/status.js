/** 提交状态 */
export const APPROVAL_STATUS_LIST_OBJ = {
  0: { label: '待提交', value: 0 },
  2: { label: '待分拨区审核', value: 2 },
  6: { label: '待总部审核', value: 6 },
  7: { label: '总部审核通过', value: 7 },
  8: { label: '待修改', value: 8 },
};

export const approvalStatusList = Object.values(APPROVAL_STATUS_LIST_OBJ);

export const isModifyList = [
  { label: '否', value: 0 },
  { label: '是', value: 1 },
];

export const sourceTypeList = [
  { label: '顺丰', value: 'SF' },
  { label: '顺心', value: 'SX' },
];

/** 装卸车计费类型 */
export const ACCRUE_TYPE_MAP = {
  1: '计费重量',
  2: '系数',
  3: '操作货量',
  4: '计件',
  5: '计板',
  6: '按天',
  7: '按时',
  8: '全场均摊',
};

/** 顺心计提应付--人员应付明细  计费类型 1计费重量、2实际重量、3操作货量、4计件、5计板、6按天、7按时、8全场均摊、9板均、10固定绩效、11卡均、12叉车计重 */
export const ACCRUE_TYPE_DICT = {
  1: '计费重量',
  2: '系数',
  3: '操作货量',
  4: '计件',
  5: '计板',
  6: '按天',
  7: '按时',
  8: '全场均摊',
  9: '板均',
  10: '固定绩效',
  11: '卡均',
  12: '叉车计重',
  13: '大小件',
};

/** 叉车计费类型 */
export const FORKLIFT_AACCRUE_TYPE_MAP = {
  1: '计费重量',
  5: '计板',
  9: '板均',
};

// 计提奖金-区分顺心 顺丰
export const accrueMomenyList = [
  { title: '计提奖金', value: 'loadUnloadAmount' },
  //   { title: '顺心叉车', value: 'forkliftAmount' },
];

// 计提货量-区分顺心 顺丰
export const accrueCorgoList = [
  { title: '顺心卸车', value: 'unloadWeight' },
  { title: '顺心省际装车', value: 'loadWeight' },
  { title: '顺心省内装车', value: 'provinceLoadWeight' },
  { title: '顺心支线装车', value: 'branchLoadWeight' },
  { title: '顺心叉车托盘数', value: 'forkliftWorks' },
  { title: '顺心叉车卡口数', value: 'forkliftKaKou' },
  { title: '顺心叉车板均货量', value: 'forkliftPalletWeight' },
  { title: '顺心叉车操作货量', value: 'forkliftWeight' },
  { title: '叉车卡均货量', value: 'forkliftCardWeight' },
  { title: '叉车小件货量', value: 'forkliftSmallWeight' },
  { title: '叉车大件货量', value: 'forkliftBigWeight' },
  { title: '皮带机分拣货量', value: 'beltSortWeight' },
  { title: '皮带机叉车货量', value: 'beltForkliftWeight' },
  //   { title: '修正后顺心叉车工作量', value: 'approvalForkliftWorks' },
  //   { title: '顺心叉车工作量修正原因', value: 'approvalForkliftWorksMsg' },
];

// 计提单价-区分顺心 顺丰
export const accruePriceList = [
  { title: '顺心卸车', value: 'unloadPrice' },
  { title: '顺心省际装车', value: 'loadPrice' },
  { title: '顺心省内装车', value: 'provinceLoadPrice' },
  { title: '顺心支线装车', value: 'branchLoadPrice' },
  { title: '顺心叉车', value: 'forkliftPrice' },
  { title: '双库操作员绩效', value: 'forkliftFixPrice' },
  { title: '机叉绩效', value: 'forkliftMachinePrice' },
  { title: '叉车小件', value: 'forkliftSmallPrice' },
  { title: '叉车大件', value: 'forkliftBigPrice' },
];

// 保底-区分顺心 顺丰
export const accrueMinList = [
  { title: '顺心装卸月保底', value: 'loadUnloadAmountMin' },
  { title: '顺心叉车月保底', value: 'forkliftAmountMin' },
  { title: '顺心装卸应扣保底货量', value: 'loadUnloadAmountCut' },
  { title: '顺心叉车应扣保底货量', value: 'forkliftAmountCut' },
];

// 列表基本信息

export const baseInforListA = [
  { label: '月份', value: 'accrueMonth' },
  { label: '分拨区', value: 'allocationArea' },
  { label: '网点', value: 'zoneName' },
  { label: '工号', value: 'userNo' },
  { label: '姓名', value: 'userName' },
  { label: '发薪岗位', value: 'position' },
  { label: '计费类型', value: 'accrueType' },
  //   { label: '叉车计费类型', value: 'forkliftAccrueType' },
  { label: '最终发放计提', value: 'approvalAccrueAmount' },
  { label: '累计计提(系统）', value: 'accrueAmount' },
  //   { label: '补发补扣', value: 'approvalBuckle' },
  { label: '配置出勤天数', value: 'systemAttendNum' },
  { label: '系统出勤天数', value: 'attendNum' },
  { label: '分拨区导入出勤天数', value: 'approvalAttendNum' },
  { label: '补发补扣导入人', value: 'approvalBuckleCreator' },
  { label: '补发补扣导入时间', value: 'approvalBuckleTime' },
  //   { label: '补发补扣说明', value: 'approvalBuckleTxt' },
  { label: '出勤天数导入人', value: 'approvalAttendNumCreator' },
  { label: '出勤天数导入时间', value: 'approvalAttendNumTime' },
  { label: '出勤天数修改说明', value: 'approvalAttendNumMsg' },
];

// 工作量补发补扣 叉车工作量补发补扣,工作量补发补扣原因
export const approvalBucklelist = [
  { title: '叉车工作量补发补扣', value: 'approvalForkliftWorks' },
  { title: '工作量补发补扣原因', value: 'approvalForkliftWorksMsg' },
];

// 短期方案激励 字段：老带新激励,返岗激励,兼职机叉激励,支援大件激励,短期方案激励原因
export const incentiveslist = [
  { title: '老带新激励', value: 'oldWithNewIncentives' },
  { title: '返岗激励', value: 'returnWorkIncentives' },
  { title: '兼职机叉激励', value: 'partTimeForkIncentives' },
  { title: '支援大件激励', value: 'supportingSfIncentives' },
  { title: '短期方案激励原因', value: 'shortPlanIncentivesMsg' },
];

// 工种变更补发补扣”字段：工种变更补发补扣
export const changeJobIncentivelist = [
  { title: '工种变更补发补扣', value: 'changeJobIncentives' },
];

// 其他补发补扣
export const otherApprovalBucklelist = [
  { title: '其他补发补扣', value: 'approvalBuckle' },
  { title: '其他补发补扣原因', value: 'approvalBuckleTxt' },
];

// 系数
export const rateList = [
  { title: '双库操作员系数', value: 'doubleOperator' },
  { title: '机叉系数', value: 'machineFork' },
  { title: '系数说明', value: 'coefficientMsg' },
];

// 发薪岗位
export const positionList = [
  { label: '操作员', value: '操作员' },
  { label: '操作组长', value: '操作组长' },
  { label: '电叉司机', value: '电叉司机' },
  { label: '电叉组长', value: '电叉组长' },
  { label: '机叉司机', value: '机叉司机' },
  { label: '双库操作员', value: '双库操作员' },
];

// logoutFlag  '0未离职，1非当月离职,  2当月离职'
// 是否离职
export const logoutList = [
  { label: '在职', value: 0 },
  { label: '非当月离职', value: 1 },
  { label: '当月离职', value: 2 },
];

// 状态：0待固化、1待提交、2待地区审核、3待总部审核、4待修改、5已确定
export const status = {
  待固化: 0,
  待提交: 1,
  待地区审核: 2,
  待总部审核: 3,
  待修改: 4,
  已确定: 5,
};

export const statusList = [
  { label: '待固化', value: 0 },
  { label: '待提交', value: 1 },
  { label: '待地区审核', value: 2 },
  { label: '待总部审核', value: 3 },
  { label: '待修改', value: 4 },
  { label: '已确定', value: 5 },
];

// 场站应付--列表基本信息
export const baseInfoList = [
  { label: '月份', value: 'accrueMonth' },
  { label: '分拨区', value: 'allocationArea' },
  { label: '网点', value: 'zoneName' },
];

// 场站应付---计提奖金
export const accrueMomenyStation = [
  { title: '上月效率奖金（实发）', value: 'accrueAmountLastMonth' },
  { title: '上月效率奖金（系统）', value: 'accrueAmountLastMonthOrgin' },
  {
    title: '当月效率奖金（实发）',
    value: 'accrueAmount',
  },
  {
    title: '当月效率奖金（系统）',
    value: 'accrueAmountOrgin',
  },
  {
    title: '装卸计提奖金',
    value: 'loadUnloadAmount',
  },
  {
    title: '叉车计提奖金',
    value: 'forkliftAmount',
  },
  {
    title: '双库操作员计提奖金',
    value: 'loadFixAmount',
  },
  {
    title: '机叉计提奖金',
    value: 'forkliftMachineAmount',
  },
];

// 场站应付---计提货量
export const accrueCorgoStation = [
  {
    title: '装卸货量',
    value: 'loadUnloadWeight',
  },
  {
    title: '叉车货量',
    value: 'forkliftWeight',
  },
  {
    title: '机叉货量',
    value: 'forkliftMachineWeight',
  },
];

// 场站应付--- 补发补扣
export const approvalBuckleStation = [
  {
    title: '叉车工作量补发补扣',
    value: 'forkliftFixWeight',
  },
  {
    title: '短期方案激励',
    value: 'shortPlanIncentives',
  },
  {
    title: '工作变更补发补扣',
    value: 'changeJobIncentives',
  },
  {
    title: '其他补发补扣',
    value: 'approvalBuckle',
  },
];
