import React, { useState } from 'react';
import { Modal, Button, Col, Row, Form, Input, message, Radio } from 'antd';
import { operate } from '../PersonPayableDetail/api';
const { Item: FormItem } = Form;
const { TextArea } = Input;

const tailLayout = {
  wrapperCol: { offset: 8, span: 16 },
};
const handleModal = props => {
  const { visible, idList, handleSearch } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isPass, setIsPass] = useState(false); // 驳回原因是否必填

  const handleCancel = () => {
    form.resetFields();
    props.handleCancel();
  };

  const submit = () => {
    form.validateFields().then(data => {
      setLoading(true);
      const params = {
        idList,
        approvalStatus: data.approvalStatus,
        approvalWord: data.approvalWord,
      };
      operate(params)
        .then(res => {
          setLoading(false);
          if (res.success) {
            message.success('提交成功');
            handleCancel();
            handleSearch();
          } else {
            setLoading(false);
            message.error(res.errorMessage);
          }
        })
        .catch(err => {
          setLoading(false);
          message.error(err);
        });
    });
  };

  const handleSelect = value => {
    if (value === 3) {
      setIsPass(true);
    } else {
      setIsPass(false);
    }
  };

  return (
    <div>
      <Modal
        title="审批"
        width={800}
        visible={visible}
        onCancel={handleCancel}
        maskClosable={false}
        footer={null}
        destroyOnClose
      >
        <Form
          name="basic"
          form={form}
          labelCol={{ md: 2, xs: 8 }}
          wrapperCol={{ md: 22 }}
        >
          <Row>
            <Col md={24} sm={24}>
              <FormItem
                label="审核意见"
                name="approvalStatus"
                labelCol={{ xl: 4 }}
                wrapperCol={{ xl: 20 }}
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <Radio.Group
                  onChange={event => {
                    handleSelect(event.target.value);
                  }}
                >
                  <Radio value={2}>通过</Radio>
                  <Radio value={3}>驳回</Radio>
                </Radio.Group>
              </FormItem>
            </Col>
            {isPass && (
              <Col md={24} sm={24}>
                <FormItem
                  label="驳回理由"
                  name="approvalWord"
                  labelCol={{ xl: 4 }}
                  wrapperCol={{ xl: 20 }}
                  rules={[
                    {
                      required: true,
                      message: '请选择',
                    },
                  ]}
                >
                  <TextArea
                    allowClear
                    placeholder="请输入"
                    showCount
                    maxLength={50}
                  />
                </FormItem>
              </Col>
            )}
          </Row>

          <Form.Item {...tailLayout}>
            <Button onClick={handleCancel}>取消</Button>
            <Button
              type="primary"
              onClick={submit}
              loading={loading}
              style={{ marginLeft: '20px' }}
            >
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default handleModal;
