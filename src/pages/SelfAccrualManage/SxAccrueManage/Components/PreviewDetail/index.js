import React, { useEffect, useState } from 'react';
import { Descriptions, Divider } from 'antd';
import moment from 'moment';
import request from 'src/utils/request';

import {
  baseInforListA,
  accrueMomenyList,
  accrueCorgoList,
  accruePriceList,
  accrueMinList,
  ACCRUE_TYPE_DICT,
  approvalBucklelist,
  otherApprovalBucklelist,
  changeJobIncentivelist,
  incentiveslist,
  rateList,
} from '../status';
import './index.scss';

const Remark = props => {
  const { rowValue } = props;
  const [showDetail, setShowDetail] = useState(false);

  useEffect(() => {
    if (rowValue) {
      request(`/tdmsAccrueService/accrueMothStaffRest/isAvailable`, {
        method: 'POST',
        body: {
          zoneCode: rowValue.zoneCode,
          accrueMonth: rowValue.accrueMonth,
        },
      }).then(res => {
        setShowDetail(!res.obj);
      });
    }
  }, [rowValue]);

  return (
    <div>
      <Descriptions column={6} title="基本信息">
        {baseInforListA.map(({ label, value }) => (
          <Descriptions.Item
            // span={
            //   index === 15 && rowValue[value] && rowValue[value].length > 3
            //     ? 4
            //     : 1
            // }
            span={2}
            label={label}
            key={label}
          >
            {value.includes('unknown')
              ? value
              : value === 'accrueType'
              ? ACCRUE_TYPE_DICT[rowValue[value]]
              : value === 'approvalBuckleTime' ||
                value === 'approvalAttendNumTime'
              ? rowValue[value]
                ? moment(rowValue[value]).format('YYYY-MM-DD HH:mm:ss')
                : ''
              : rowValue[value]}
          </Descriptions.Item>
        ))}
      </Descriptions>
      <Divider />
      {showDetail && (
        <div>
          <div style={{ display: 'flex' }}>
            <Descriptions title="计提奖金" column={2} style={{ flex: 1 }}>
              {accrueMomenyList.map(({ title, value }) => (
                <Descriptions.Item span={1} label={title} key={value}>
                  {value.includes('unknown') ? value : rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
            <Descriptions title="计提货量" column={2} style={{ flex: 1 }}>
              {accrueCorgoList.map(({ title, value }) => (
                <Descriptions.Item
                  span={1}
                  label={title}
                  key={value}
                  className={
                    value === 'approvalForkliftWorksMsg'
                      ? 'SelfAccrualManage__SxAccrueManage__PreviewDetail__flex-wrap'
                      : undefined
                  }
                >
                  {value.includes('unknown') ? value : rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </div>
          <Divider />
          <div style={{ display: 'flex' }}>
            <Descriptions title="计提单价" column={2} style={{ flex: 1 }}>
              {accruePriceList.map(({ title, value }) => (
                <Descriptions.Item span={1} label={title} key={value}>
                  {value.includes('unknown') ? value : rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
            <Descriptions title="保底" column={2} style={{ flex: 1 }}>
              {accrueMinList.map(({ title, value }) => (
                <Descriptions.Item span={1} label={title} key={value}>
                  {value.includes('unknown') ? value : rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </div>
          <div style={{ display: 'flex' }}>
            <Descriptions title="工作量补发补扣" column={2} style={{ flex: 1 }}>
              {approvalBucklelist.map(({ title, value }) => (
                <Descriptions.Item span={2} label={title} key={value}>
                  {value.includes('unknown') ? value : rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
            <Descriptions title="短期方案激励" column={2} style={{ flex: 1 }}>
              {incentiveslist.map(({ title, value }) => (
                <Descriptions.Item
                  span={title === '短期方案激励原因' ? 2 : 1}
                  label={title}
                  key={value}
                >
                  {value.includes('unknown') ? value : rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </div>
          <div style={{ display: 'flex' }}>
            <Descriptions
              title="工种变更补发补扣"
              column={2}
              style={{ flex: 1 }}
            >
              {changeJobIncentivelist.map(({ title, value }) => (
                <Descriptions.Item span={2} label={title} key={value}>
                  {value.includes('unknown') ? value : rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
            <Descriptions title="其他补发补扣" column={2} style={{ flex: 1 }}>
              {otherApprovalBucklelist.map(({ title, value }) => (
                <Descriptions.Item span={2} label={title} key={value}>
                  {value.includes('unknown') ? value : rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </div>
          <div style={{ display: 'flex' }}>
            <Descriptions title="系数" column={2} style={{ flex: 1 }}>
              {rateList.map(({ title, value }) => (
                <Descriptions.Item span={1} label={title} key={value}>
                  {value.includes('unknown') ? value : rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>

            {/* 占位 */}
            <div style={{ flex: 1 }} />
          </div>
        </div>
      )}
    </div>
  );
};
export default Remark;
