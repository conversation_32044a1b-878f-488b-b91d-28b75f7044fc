// 01385149 测试工号; 顺心计提应付
import React, { PureComponent, useState } from 'react';
import { Tabs } from 'antd';
import { withRouter } from 'react-router-dom';
import StationPayableDetail from './Components/StationPayableDetail'; // 场站人员明细
import PersonPayableDetail from './Components/PersonPayableDetail'; // 人员应付明细

import './index.scss';

const { TabPane } = Tabs;

const Page = () => {
  const [tabKey, setTabKey] = useState('1');
  const [rowValue, setRowValue] = useState('');

  const swicthTabs = (value, row) => {
    setRowValue(row);
    setTabKey(value);
  };

  return (
    <Tabs activeKey={tabKey} size="large" onChange={swicthTabs}>
      <TabPane tab="场站应付明细" key="1">
        <StationPayableDetail swicthTabs={swicthTabs} />
      </TabPane>
      <TabPane tab="人员应付明细" key="2">
        <PersonPayableDetail rowValue={rowValue} />
      </TabPane>
    </Tabs>
  );
};

class Container extends PureComponent {
  render() {
    return <Page />;
  }
}

export default withRouter(Container);
