import React, { useState, useEffect } from 'react';
import {
  Modal,
  Button,
  Form,
  Col,
  Row,
  message,
  Input,
  DatePicker,
  Select,
} from 'antd';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import { connect } from 'dva';
import { success, error } from 'src/utils/utils';
import { searchTaskList, editTeamUser } from '../../services/api';
import DynamicForm from '../DynamicForm';
import { operateTypeList } from '../status';
// import './index.scss';
let judge = false;
const Edit = props => {
  const { visible, initialValue, refresh, handleCancel } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const [listArr, setListArr] = useState([]);
  const tem = cloneDeep(initialValue);
  tem.finishTime = moment(tem.finishTime);
  const {
    isEdit, // 区别查看(true)&&修改(false)
    allocationArea, // 分拨区
    creator, // 任务创建人工号
    startTime, // 开始时间
    endTime, // 结束时间
    flowId, // 子任务号
    logoNo, // 车标号
    operateType, // 作业环节
    platformNo, // 月台号
    relayBillId, // 交接单号
    taskId, //  任务号
    teamLeaderNo, // 班组组长 && 组长工号
    zoneCode, // 网点代码
  } = tem || {};
  useEffect(() => {
    // 查询班组组员详情
    // searchTaskList({
    //   allocationArea,
    //   creator,
    //   startTime,
    //   endTime,
    //   flowId,
    //   logoNo,
    //   operateType,
    //   platformNo,
    //   taskId,
    //   relayBillId,
    //   teamLeaderNo,
    //   zoneCode,
    // }).then(res => {
    //   const list = res.obj.list.map(item => ({
    //     operateUserName: item.userName,
    //     operateUserNo: item.operateUserNo,
    //     supplierUserNum: item.supplierUserNum,
    //     supplierNo: item.supplierNo,
    //     userType: item.userType, // 1 自有 2 外包
    //     supplierName: item.supplierName,
    //   }));
    //   setListArr(list);
    //   form.setFieldsValue({ accrueTeams: list });
    // });
  }, []);

  const handleOk = () => {};
  const handleCancelClick = () => {
    form.resetFields();
    handleCancel();
  };
  const onFinish = () => {};

  const onFinishFailed = () => {};
  // 获取供应商name
  const getName = value => {
    if (value !== null) {
      if (value.indexOf('-') !== -1) {
        const name = value.substring(0, value.indexOf('-'));
        return name;
      }
      return value;
    }
    return value;
  };
  // 获取供应商ID
  const getId = (value, name) => {
    if (value !== null) {
      if (name.indexOf('-') !== -1) {
        const suId = name.substring(name.indexOf('-') + 1, name.length);
        return suId;
      }
      return value;
    }
    return value;
  };
  // 提交
  const submit = () => {
    if (!isEdit) {
      handleCancelClick();
      return;
    }

    form.validateFields().then(data => {
      setLoading(true);
      const dates = cloneDeep(data.accrueTeams);
      dates.forEach(item => {
        if (item) {
          if (
            item.userType === 1 &&
            item.operateUserNo &&
            item.operateUserName
          ) {
            item.flowId = data.flowId;
            item.taskId = data.taskId;
            item.groupLeaderFlag = 0;
            item.modifyTime = new Date().getTime();
            item.modifier = sessionStorage.userid;
            item.supplierName = '';
            item.supplierNo = '';
            judge = false;
          } else if (
            item.userType === 2 &&
            item.operateUserNo &&
            item.operateUserName &&
            item.supplierName
          ) {
            item.flowId = data.flowId;
            item.taskId = data.taskId;
            item.groupLeaderFlag = 0;
            item.modifyTime = new Date().getTime();
            item.modifier = sessionStorage.userid;
            item.supplierNo = item.supplierName
              ? getId(item.supplierNo, item.supplierName)
              : item.supplierNo;
            item.supplierName = getName(item.supplierName);
            judge = false;
          } else {
            message.warning('请检查工号是否输入正确');
            judge = true;
          }
        } else {
          message.warning('请输入组员工号');
          judge = true;
        }
      });
      if (judge) {
        setLoading(false);
        return;
      }
      const params = { accrueTeams: [] };
      params.accrueTeams = dates;
      editTeamUser(params)
        .then(res => {
          if (res.success) {
            setLoading(false);
            handleCancelClick();
            success('修改成功');
            refresh();
          } else {
            setLoading(false);
            error(res.errorMessage);
          }
        })
        .catch(err => {
          setLoading(false);
          message.error(err);
        });
    });
  };
  const formProps = {
    form,
    labelCol: { md: 9 },
    wrapperCol: { md: 15 },
    initialValues: {
      ...tem,
    },
  };
  return (
    <div>
      <Modal
        // title={`${initialValue ? '修改' : '查看'}`}
        title="查看"
        width={900}
        visible={visible}
        onOk={handleOk}
        onCancel={handleCancelClick}
        footer={
          isEdit
            ? [
                <Button key="back" onClick={handleCancelClick}>
                  取消
                </Button>,
                <Button
                  key="submit"
                  loading={loading}
                  type="primary"
                  onClick={submit}
                >
                  确定
                </Button>,
              ]
            : [
                <Button key="back" onClick={handleCancelClick}>
                  取消
                </Button>,
              ]
        }
        destroyOnClose
      >
        <Form
          {...formProps}
          name="basic"
          onFinish={onFinish}
          onFinishFailed={onFinishFailed}
          form={form}
          className="employ-target"
        >
          <Row gutter={{ md: 4, lg: 5, xl: 20 }}>
            <Col md={10} sm={24}>
              <Form.Item label="任务号" name="taskId">
                <Input disabled placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col md={10} sm={24}>
              <Form.Item label="子任务号" name="flowId">
                <Input disabled placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col md={10} sm={24}>
              <Form.Item label="任务完成时间" name="finishTime">
                <DatePicker disabled style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col md={10} sm={24}>
              <Form.Item label="交接单号" name="relayBillId">
                <Input disabled placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col md={10} sm={24}>
              <Form.Item label="车标号" name="logoNo">
                <Input disabled placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col md={10} sm={24}>
              <Form.Item label="月台号" name="platformNo">
                <Input disabled placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col md={10} sm={24}>
              <Form.Item label="任务总操作货量" name="totalWeight">
                <Input addonAfter="T" disabled placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col md={10} sm={24}>
              <Form.Item label="个人计提货量" name="avgWeightMultiNum">
                <Input addonAfter="T" disabled placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col md={10} sm={24}>
              <Form.Item label="任务创建人工号" name="creator">
                <Input disabled placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col md={10} sm={24}>
              <Form.Item label="操作总货量" name="totalWeight">
                <Input addonAfter="T" disabled placeholder="请输入" />
              </Form.Item>
            </Col>
            <Col md={10} sm={24}>
              <Form.Item label="作业环节" name="operateType">
                <Select
                  allowClear
                  disabled
                  options={operateTypeList}
                  placeholder="请选择"
                ></Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={{ md: 4, lg: 5, xl: 20 }}>
            <Col md={10} sm={24}>
              <Form.Item label="班组组长" name="teamLeaderNo">
                <Input disabled placeholder="请输入" />
              </Form.Item>
            </Col>
          </Row>
          <Row style={{ marginLeft: '-4px' }} gutter={{ md: 4, lg: 5, xl: 20 }}>
            {visible ? (
              <DynamicForm
                label="组员"
                name="test"
                zoneCode={zoneCode}
                form={form}
                listArr={listArr}
                isEdit={isEdit}
              />
            ) : null}
          </Row>
          <Row gutter={{ md: 4, lg: 5, xl: 20 }}>
            <Col md={10} sm={24}>
              <Form.Item label="班组总人数" name="teamNum">
                <Input disabled placeholder="请输入" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};
export default connect(() => ({}))(props => <Edit {...props}></Edit>);
