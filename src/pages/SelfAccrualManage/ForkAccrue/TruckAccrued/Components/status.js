export const operateTypeList = [
  { label: '装车', value: 1 },
  { label: '卸车', value: 2 },
  { label: '叉车', value: 4 },
  { label: '装卸车', value: 6 },
  { label: '省内装车', value: 8 },
  { label: '省际装车', value: 9 },
  { label: '支线装车', value: 16 },
  { label: '回流分拣', value: 10 },
  { label: '铁路装车', value: 17 },
  { label: '铁路卸车', value: 18 },
  { label: '皮带机卸车', value: 30 },
  { label: '非皮带机卸车', value: 31 },
];
// taskStatus "任务状态",0(待执行) 1(执行中) 2(已完成)
export const taskStatusList = [
  { label: '待执行', value: 0 },
  { label: '执行中', value: 1 },
  { label: '已完成', value: 2 },
];

export const userTypeList = [
  { label: '自有', value: 1 },
  { label: '外包', value: 2 },
];

export const vehicleTypeList = [
  { label: '平板运输车', value: '002007' },
  // { label: '虚拟车', value: '991001' },
  { label: '厢式运输车', value: '002001' },
  { label: '高栏运输车', value: '002024' },
];
// 月台类型platformType：
export const platformTypeList = [
  { label: 'NC件操作', value: 1 },
  { label: '伸缩皮带机', value: 2 },
  { label: '无动力滚筒', value: 3 },
  { label: '其他', value: 4 },
];
// 是否货量折算 zy: 同场装车折算-1、装运一体折算-2、卸车折算-4、L0外包货量折-5
export const zyList = [
  { label: '同场装车折算', value: 1 },
  { label: '装运一体折算', value: 2 },
  { label: '卸车折算', value: 4 },
  { label: 'L0外包装车折算', value: 5 },
  { label: 'L0外包卸车折算', value: 6 },
  { label: '标快零担卸车折算', value: 7 },
  { label: '标快零担装车折算', value: 8 },
  { label: '陆运港到港卸车折算', value: 9 },
  { label: '陆运港到港装车折算', value: 10 },
];

// export const lineTypeList = [
//   { label: '省内用车', value: 0 },
//   { label: '省际用车', value: 5 },
// ];

export const weightNumList = [
  { title: '1号', dataIndex: 'weight_01' },
  { title: '2号', dataIndex: 'weight_02' },
  // { title: '3号', dataIndex: 'weight_03' },
  // { title: '4号', dataIndex: 'weight_04' },
  // { title: '5号', dataIndex: 'weight_05' },
  // { title: '6号', dataIndex: 'weight_06' },
  // { title: '7号', dataIndex: 'weight_07' },
  // { title: '8号', dataIndex: 'weight_08' },
  // { title: '9号', dataIndex: 'weight_9' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
  // { label: '1号', value: 'weight_01' },
];

export const sourceTypeList = [
  { label: 'SF', value: 'SF' },
  { label: 'SX', value: 'SX' },
];

export const skilledLevelList = [
  { label: 'L0', value: 0 },
  { label: 'L1', value: 1 },
  { label: 'L2', value: 2 },
  { label: 'L3', value: 3 },
  { label: 'L4', value: 4 },
  { label: 'L5', value: 5 },
];

export const supplierServiceCodeList = [
  {
    label: 'O线运作',
    value: 429,
  },
  {
    label: '灵活派遣',
    value: 442,
  },
  {
    label: '平台运作',
    value: 458,
  },
  {
    label: '平台运作PMP',
    value: 461,
  },
];
