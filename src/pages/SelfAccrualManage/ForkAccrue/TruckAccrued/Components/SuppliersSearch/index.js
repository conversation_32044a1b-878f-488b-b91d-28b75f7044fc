import React, { useState, useEffect, Fragment } from 'react';
import { Select, Tooltip } from 'antd';
import { getSupplierName } from 'src/services/api';
const { Option } = Select;

function supplierNameSearch(props) {
  const { onChange, value, ...rest } = props;
  const [dataSource, setDataSource] = useState([]);
  const [timeId, setTimeId] = useState([]);

  const handleSearchChange = query => {
    if (query.length >= 1) {
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          getSupplierName(query).then(res => {
            const data = res.obj.supplierNames.map(item => ({
              label: item,
              value: item,
              text: item,
            }));
            setDataSource(data || []);
          });
        }, 1000),
      );
    }
  };

  const handleChange = (v, option) => {
    if (typeof v === 'object') {
      v.label = v.label ? v.label.props.children : '';
    }
    const beChose = dataSource.find(item => item.v === v) || {};
    onChange(
      v,
      Object.assign({}, option, {
        text: beChose.text,
      }),
    );
  };

  useEffect(() => {
    // eslint-disable-next-line no-unused-expressions
    value &&
      handleSearchChange(typeof value === 'object' ? value.value : value);
  }, [value]);
  return (
    <Fragment>
      <Select
        showSearch
        // style={{ width: '100%' }}
        defaultActiveFirstOption={false}
        filterOption={false}
        onSearch={handleSearchChange}
        onChange={handleChange}
        // className="map-person-select"
        value={value}
        allowClear
        {...rest}
      >
        {dataSource.map(item => (
          <Option value={item.value} key={item.value}>
            <Tooltip placement="rightTop" title={item.label}>
              {item.label}
            </Tooltip>
          </Option>
        ))}
      </Select>
    </Fragment>
  );
}
export default supplierNameSearch;
