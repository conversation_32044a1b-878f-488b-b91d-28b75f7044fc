// 场内任务-装卸
import React, { useState, useEffect, Fragment, PureComponent } from 'react';
import { connect } from 'dva';
import { Col, Form, Input, Button, Select, DatePicker, Row, Modal } from 'antd';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  rangePickerColStyle,
  rangePickerLayout,
  formItemLayout,
  colStyle,
  AuthButton,
  SearchFold,
  rowStyle,
  useModuleCode,
} from 'ky-giant';
import { timeLimit } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable'; // table组件
import AsyncExport from 'src/components/AsyncExport'; // 异步导出
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import SuppliersSearch from 'src/components/SuppliersSearch';
import {
  searchTaskList,
  taskAllAccrueWeightSum,
  finishTaskApi,
} from '../services/api';
import {
  operateTypeList,
  userTypeList,
  sourceTypeList,
  vehicleTypeList,
  skilledLevelList,
  platformTypeList,
  zyList,
  taskStatusList,
} from '../Components/status';
import SubTaskInfor from '../Components/SubTaskInfor';
import Edit from '../Components/Edit';
import { success, error } from '@/utils/utils';

import './style.less';

const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;
let inputParam = {};

const Page = ({ userInfo, areaListSF, areaListSX, logRoleCode }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [selectedRows, setSelectedRows] = useState([]);
  const [editVisible, setEditVisible] = useState(false); // 修改框显示
  const [editValue, setEditValue] = useState({}); // 初始值
  const [arriveTrue, setArriveTrue] = useState(false); // 触发网点的动态校验
  // const [unloadShow, setUnloadShow] = useState(false); // 查询条件 卸车任务类型显示隐藏
  const [timeId, setTimeId] = useState([]);
  const [personalAccrueWeightSum, setPersonalAccrueWeightSum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [taskInforVisible, setTaskInforVisible] = useState(false);
  const [userOrg, setUserOrg] = useState('SF');
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '所属公司',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'sourceType',
    },
    {
      title: '工号',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'operateUserNo',
    },
    {
      title: '姓名',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'userName',
    },
    {
      title: '员工类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'userType',
      render: value => {
        const data = userTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '岗位',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'empDutyName',
    },
    {
      title: '外包熟练工等级',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'skilledLevel',
      render: value => {
        const data = skilledLevelList.find(item => item.value === value);
        return data ? data.label : '';
      },
    },
    {
      title: '网点',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'zoneCode',
      render: (value, row) => `${row.zoneCode} ${row.zoneName}`,
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: value => {
        const areaList = areaListSF.concat(areaListSX);
        const date = areaList.find(item => item.value === value);
        return date ? date.label : value;
      },
    },
    {
      title: '作业环节',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'operateType',
      render: value => {
        const data = operateTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '卸车任务类型',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'isSorterUnload',
      render: (text, row) => (
        <Fragment>
          {row.operateType === 2 && text === 0 && <span>普通件卸车</span>}
          {row.operateType === 2 && text === 1 && <span>分拣机卸车</span>}
        </Fragment>
      ),
    },
    {
      title: '月台类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'platformType',
      render: value => {
        const data = platformTypeList.find(item => item.value === value);
        return data ? data.label : '-';
      },
    },
    {
      title: '任务号',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'taskId',
    },
    {
      title: '子任务号',
      align: 'center',
      ellipsis: true,
      width: 220,
      dataIndex: 'flowId',
      render: (v, record) => (
        <Fragment>
          <Button
            size="small"
            type="link"
            onClick={e => subTaskInfor(e, record)}
          >
            {v}
          </Button>
        </Fragment>
      ),
    },
    {
      title: '托盘个数',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'palletNum',
    },
    {
      title: '交接单号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'relayBillId',
    },
    {
      title: '车标号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'logoNo',
    },
    {
      title: '线下车标号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'offlineCarNo',
    },
    {
      title: '线路标识',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'lineCode',
    },
    {
      title: '时效线路编码',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'lineCodeNew',
    },
    {
      title: '班次号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'requireId',
    },
    {
      title: '车辆类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'vehicleType',
      render: value => {
        const data = vehicleTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '是否货量折算',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'zy',
    },
    // {
    //   title: '用车类型',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 100,
    //   dataIndex: 'lineTypes',
    //   // render: value => {
    //   //   const data = lineTypeList.find(item => item.value === value);
    //   //   return data ? data.label : value;
    //   // },
    // },

    {
      title: '月台号',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'platformNo',
    },
    {
      title: '班组组长',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'teamLeaderNo',
    },
    {
      title: '子任务总操作货量（T）',
      align: 'center',
      ellipsis: true,
      width: 250,
      dataIndex: 'totalWeight',
    },
    {
      title: '顺丰不同产品名称总操作货量(T)',
      align: 'center',
      ellipsis: true,
      width: 250,
      children: [
        {
          title: '标快零担',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'totalLessTruckWeight',
        },
        {
          title: '陆运港到港',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'totalPortArrivalWeight',
        },
      ],
    },
    {
      title: '子任务操作人数',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'teamNum',
    },
    {
      title: '个人操作货量（T）',
      align: 'center',
      ellipsis: true,
      width: 250,
      dataIndex: 'avgWeightMultiNum',
    },
    {
      title: '顺丰不同产品名称个人操作货量(T)',
      align: 'center',
      ellipsis: true,
      width: 250,
      children: [
        {
          title: '标快零担',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'avgLessTruckWeight',
        },
        {
          title: '陆运港到港',
          align: 'center',
          ellipsis: true,
          width: 100,
          dataIndex: 'avgPortArrivalWeight',
        },
      ],
    },
    {
      title: '件数',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'taskTotalNum',
    },
    {
      title: '车标操作人数',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'taskUserNum',
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierName',
    },
    {
      title: '供应商编号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierNo',
    },
    {
      title: '供应商结算方式',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accountModule',
    },
    {
      title: '外包服务类型',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'empTypeName',
    },
    {
      title: '任务创建时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'startTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '任务完成时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'finishTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '子任务创建时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'flowStartTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '子任务完成时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'flowEndTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '上岗时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'startWorkTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '离岗时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'endWorkTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '任务归属日期',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'taskSourceDate',
    },
    {
      title: '任务创建人工号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'creator',
    },
    {
      title: '车牌号',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'chinaPlateSerial',
    },
    {
      title: '发出网点',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'sourceZoneCode',
    },
    {
      title: '到达网点',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'destZoneCode',
    },
    {
      title: '结算网点',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accountZoneCode',
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'modifier',
    },
    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '任务状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'taskStatus',
      fixed: 'right',
      render: value => {
        const data = taskStatusList.find(item => item.value === value);
        return data ? data.label : '-';
      },
    },
    {
      title: '操作',
      align: 'center',
      width: 200,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          {record.taskStatus === 1 && (
            <Button
              size="small"
              type="link"
              onClick={() => finishTask([record.id])}
            >
              结束任务
            </Button>
          )}
          <Button size="small" type="link" onClick={() => update(record)}>
            查看
          </Button>
          <Button
            size="small"
            type="link"
            onClick={e => subTaskInfor(e, record)}
          >
            扫描明细
          </Button>

          {/* 未超过 归属日期在上个月及本月的可修改 */}
          {/* 超过 总部&&分拨区  任意月份都可修改 */}
          {/* 超过 非--总部&&分拨区  本月份可修改 */}
          {timeLimit(10) ? (
            moment(record.taskSourceDate).format('x') >
            moment()
              .endOf('month')
              .subtract(1, 'months')
              .format('x') ? (
              <AuthButton
                modulecode={moduleCode}
                code="fieldTask-edit"
                size="small"
                type="link"
                onClick={() => update(record, true)}
              >
                修改
              </AuthButton>
            ) : (
              ''
            )
          ) : moment(record.taskSourceDate).format('x') >
            moment()
              .endOf('month')
              .subtract(2, 'months')
              .format('x') ? (
            <AuthButton
              modulecode={moduleCode}
              code="fieldTask-edit"
              size="small"
              type="link"
              onClick={() => update(record, true)}
            >
              修改
            </AuthButton>
          ) : (
            ''
          )}
        </Fragment>
      ),
    },
  ];

  useEffect(() => {
    // 当前登录的角色判断

    if (
      logRoleCode &&
      userInfo &&
      userInfo.deptCode &&
      (logRoleCode.roleCode === 'tp00001' ||
        logRoleCode.roleCode === '88888888888')
    ) {
      form.setFieldsValue({
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationArea:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        time: [
          moment()
            .startOf('days')
            .subtract(1, 'day')
            .add(8, 'hour'),
          moment()
            .startOf('days')
            .add(8, 'hour'),
        ],
      });
    } else {
      form.setFieldsValue({
        time: [
          moment()
            .startOf('days')
            .subtract(1, 'day')
            .add(8, 'hour'),
          moment()
            .startOf('days')
            .add(8, 'hour'),
        ],
      });
    }

    if (!userInfo?.empCode) {
      return;
    }

    if (/.*SX.*/.test(userInfo.empCode.toUpperCase())) {
      setUserOrg('SX');
      form.setFieldsValue({
        sourceType: 'SX',
      });
    } else {
      setUserOrg('SF');
      form.setFieldsValue({
        sourceType: 'SF',
      });
    }

    getQueryParams();
    refresh({ currentPage: 1 });
  }, [logRoleCode, userInfo]);

  // 查询参数的获取
  const getQueryParams = () => {
    // form.validateFields().then(values => {
    //   if (!values.allocationArea && !values.zoneCode) {
    //     notification.warning({
    //       message: '校验提示',
    //       description: '所属网点和分拨区不能同时为空',
    //     });
    //     return false;
    //   }
    // const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    // if (!params.allocationArea && !params.zoneCode) {
    //   notification.warning({
    //     message: '校验提示',
    //     description: '所属网点和分拨区不能同时为空',
    //   });
    //   return false;
    // }
    inputParam = cloneDeep(params);
    inputParam.startTime =
      inputParam.time.length > 0
        ? moment(inputParam.time[0]).valueOf()
        : moment().startOf('month');
    inputParam.endTime =
      inputParam.time.length > 0
        ? moment(inputParam.time[1]).valueOf()
        : moment().endOf('month');
    if (inputParam.arriveCode || inputParam.startCode) {
      inputParam.lineCode = inputParam.arriveCode
        ? `${inputParam.startCode.trim()}${inputParam.arriveCode.trim()}`
        : `${inputParam.startCode.trim()}`;
      inputParam.lineCode = inputParam.lineCode.replace(/\s*/g, '');
    }
    inputParam.sourceZoneCode = inputParam?.startCode;
    inputParam.destZoneCode = inputParam?.arriveCode;
    delete inputParam.time;
    delete inputParam.startCode;
    delete inputParam.arriveCode;
    // refresh({
    //   current: 1,
    //   pageSize: 10,
    // });
    return {
      ...inputParam,
    };
    // });
  };
  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      ...inputParam,
      pageNum: current,
      pageSize,
      queryByBatch: false,
    };
    searchTaskList(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess && obj) {
          const { list, total } = obj;
          // setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.pageNum,
              pageSize: obj.pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setLoading(false);
        setSelectedRows([]);
      });
    taskAllAccrueWeightSum(param).then(res => {
      if (res.obj !== null) {
        setPersonalAccrueWeightSum(res.obj.personalAccrueWeightSum);
      } else {
        setPersonalAccrueWeightSum(res.obj);
      }
    });
  };

  // 关闭弹出层
  const closeModal = () => {
    setEditVisible(false);
  };

  const taskHandleCancel = () => {
    setTaskInforVisible(false);
  };

  // 人员详情
  const subTaskInfor = (e, record) => {
    e.stopPropagation();
    setEditValue(record);
    setTaskInforVisible(true);
  };

  // 批量结束任务
  const finishTaskBatch = record => {
    let unableFinishFlag = false;
    const idList = record.map(row => {
      if (row.taskStatus !== 1) {
        unableFinishFlag = true;
      }
      return row.id;
    });
    if (unableFinishFlag) {
      error('只有“执行中”的任务才可以结束');
      return;
    }
    finishTask(idList);
  };

  // 结束任务
  const finishTask = idList => {
    Modal.confirm({
      title: '提示',
      content: '是否确认结束已选任务',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        finishTaskApi({ idList }).then(res => {
          if (res.success) {
            success('提交成功');
            refresh();
          } else {
            error('提交失败');
          }
        });
      },
      onCancel() {},
    });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };
  // const getOperateType = vaule => {
  //   if (vaule === 2) {
  //     setUnloadShow(true);
  //   } else {
  //     setUnloadShow(false);
  //   }
  // };

  // 出发时间和到达时间的动态校验
  const getArriveCode = value => {
    if (value.trim() !== '') {
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          setArriveTrue(true);
        }, 1000),
      );
    } else {
      setArriveTrue(false);
    }
  };

  const update = (value, bll) => {
    const tempValue = cloneDeep(value);
    tempValue.endTime = inputParam.endTime;
    tempValue.startTime = inputParam.startTime;
    tempValue.isEdit = bll;
    setEditValue(tempValue);
    setEditVisible(true);
  };

  // // 是否按班次查询的判断&&查询条件切换
  // const getWorkDetail = value => {
  //   setIsWork(value);
  // };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        time: [
          moment()
            .startOf('days')
            .subtract(1, 'day')
            .add(8, 'hour'),
          moment()
            .startOf('days')
            .add(8, 'hour'),
        ],
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationArea:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        // queryByBatch: true,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="所属公司"
                name="sourceType"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  options={sourceTypeList}
                  placeholder="请选择所属公司"
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="网点"
                name="zoneCode"
                // rules={[
                //   ({ getFieldValue }) => ({
                //     validator(_, value) {
                //       if (getFieldValue('allocationArea') || value) {
                //         return Promise.resolve();
                //       }
                //       return Promise.reject(
                //         new Error('分拨区和网点不能同时为空'),
                //       );
                //     },
                //   }),
                // ]}
              >
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  // disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="分拨区"
                name="allocationArea"
                // rules={[
                //   ({ getFieldValue }) => ({
                //     validator(_, value) {
                //       if (getFieldValue('zoneCode') || value) {
                //         return Promise.resolve();
                //       }
                //       return Promise.reject(
                //         new Error('分拨区和网点不能同时为空'),
                //       );
                //     },
                //   }),
                // ]}
              >
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  // disabled={logRoleCode.roleCode === '88888888888'}
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务状态" name="taskStatus">
                <Select
                  allowClear
                  options={taskStatusList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...rangePickerColStyle}>
              <FormItem
                {...rangePickerLayout}
                label="开始时间"
                name="time"
                rules={[
                  {
                    required: true,
                    message: '请填写',
                  },
                ]}
              >
                <RangePicker showTime allowClear style={{ width: '100%' }} />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务号" name="taskId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组员工号" name="operateUserNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>

            <Col {...colStyle}>
              <FormItem label="子任务号" name="flowId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="作业环节" name="operateType">
                <Select
                  // onChange={getOperateType}
                  allowClear
                  options={operateTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务类型" name="isSorterUnload">
                <Select
                  allowClear
                  options={[
                    {
                      label: '分拣机任务',
                      value: 1,
                      key: 1,
                    },
                    {
                      label: '离线任务',
                      value: 2,
                      key: 2,
                    },
                  ]}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="员工类型" name="userType">
                <Select
                  allowClear
                  options={userTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.zoneCode !== curValues.zoneCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <FormItem name="supplierNo" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      allowClear
                      gysParams={getFieldValue('zoneCode') || userInfo.deptCode}
                    />
                  </FormItem>
                </Col>
              )}
            </FormItem>
            <Col {...colStyle}>
              <FormItem label="交接单号" name="relayBillId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="车标号" name="logoNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="月台号" name="platformNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组长姓名" name="teamLeaderName">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组长工号" name="teamLeaderNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务创建人工号" name="creator">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="出发网点"
                name="startCode"
                rules={[
                  {
                    required: arriveTrue,
                    message: '请填写',
                  },
                ]}
              >
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="到达网点" name="arriveCode">
                <Input
                  placeholder="请输入"
                  allowClear
                  onChange={e => {
                    getArriveCode(e.target.value);
                  }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="结算网点" name="accountZoneCode">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="车辆类型" name="vehicleType">
                <Select
                  options={vehicleTypeList}
                  placeholder="请选择车辆类型"
                  allowClear
                />
              </FormItem>
            </Col>
            {/* <Col {...colStyle}>
              <FormItem label="用车类型" name="lineType">
                <Select
                  options={lineTypeList}
                  placeholder="请选择用车类型"
                  allowClear
                />
              </FormItem>
            </Col> */}
            <Col {...colStyle}>
              <FormItem label="班次号" name="requireId">
                <Input placeholder="请输入班次号" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="外包熟练工等级" name="skilledLevel">
                <Select
                  options={skilledLevelList}
                  placeholder="请选择"
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="供应商结算方式" name="accountModule">
                <Select
                  options={[
                    {
                      label: '计时',
                      value: '计时',
                    },
                    {
                      label: '计重',
                      value: '计重',
                    },
                  ]}
                  placeholder="请选择"
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="月台类型" name="platformType">
                <Select
                  allowClear
                  options={platformTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="是否货量折算" name="zy">
                <Select
                  allowClear
                  options={zyList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AsyncExport
            type="primary"
            disabled={datas.list && datas.list.length < 1}
            // moudeCode={moudeCode}
            // code="personExport"
            text="任务导出"
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueRestService/asyncExportAccrueTaskWeb',
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
          <Button
            disabled={!selectedRows.length}
            onClick={() => finishTaskBatch(selectedRows)}
            type="primary"
            style={{
              marginLeft: 20,
            }}
          >
            批量结束任务
          </Button>
          <span
            style={{
              marginLeft: 100,
              fontSize: 18,
            }}
          >
            合计总操作货量 : {personalAccrueWeightSum || '--'}
          </span>
        </div>
      </div>
    </Form>
  );

  return (
    <div>
      <div className="table-list">
        <div className="tableListForm">{renderForm()}</div>
        <StandardTable
          size="small"
          rowKey={(record, index) => `${index}${record.operateUserNo}`}
          selectedRows={selectedRows}
          loading={loading}
          data={datas}
          columns={columns}
          showSelection
          onSelectRow={handleSelectRows}
          onChange={handleStandardTableChange}
        />
      </div>

      {editVisible ? (
        <Edit
          refresh={refresh}
          initialValue={editValue}
          visible={editVisible}
          handleCancel={closeModal}
        ></Edit>
      ) : null}
      {/* 子任务明细 */}
      {taskInforVisible && (
        <SubTaskInfor
          beChose={editValue}
          visible={taskInforVisible}
          handleCancel={taskHandleCancel}
        ></SubTaskInfor>
      )}
    </div>
  );
};

@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const { areaListSX, areaListSF, userInfo, logRoleCode } = this.props;
    return (
      <Page
        logRoleCode={logRoleCode}
        userInfo={userInfo}
        areaListSX={areaListSX}
        areaListSF={areaListSF}
      />
    );
  }
}

export default withRouter(Container);
