export const operationTypeList = [
  { label: '托盘绑定', value: 1 },
  { label: '智能叉车', value: 2 },
  { label: '操作工时', value: 3 },
  { label: '叉车(刷卡)里程', value: 4 },
  { label: '叉车(扫码)里程', value: 5 },
];
export const units = [
  { label: '次', value: 1 },
  { label: '板', value: 2 },
  { label: '小时', value: 3 },
  { label: '次', value: 4 },
  { label: '小时', value: 5 },
];
export const unitsList = [
  { label: 'T', value: 1 },
  { label: 'T', value: 3 },
  { label: 'km', value: 4 },
  { label: 'km', value: 5 },
];

export const operateTypeList = [
  {
    label: '地面线分拣',
    value: 3,
  },
  {
    label: '叉车',
    value: 4,
  },
  {
    label: '格口分拣',
    value: 5,
  },
  {
    label: 'NC初分',
    value: 14,
  },
  {
    label: 'NC细分',
    value: 15,
  },
  {
    label: '地面流向分拣',
    value: 13,
  },
  {
    label: '叉车细分',
    value: 20,
  },
];

// 时间范围组件col占比(时分秒)
export const rangePickerColStyle = {
  xs: 24, // <576px
  md: 12, // >= 768px
  lg: 12, // >= 992px
  xl: 16, // >= 1200px
  xxl: 12, // >= 1600px
};

// 时间范围组件样式(时分秒)
export const rangePickerLayout = {
  labelCol: {
    xs: 6, // <576px
    md: 8, // >= 768px
    lg: 8, // >= 992px
    xl: 4, // >= 1200px
    xxl: 4, // >= 1200px
  },
  wrapperCol: {
    xs: 18, // <576px
    md: 16, // >= 768px
    lg: 16, // >= 992px
    xl: 20, // >= 1200px
    xxl: 20, // >= 1200px
  },
};

// form表单样式
export const formItemLayout = {
  labelCol: {
    xs: 6, // <576px
    md: 8, // >= 768px
    lg: 8, // >= 992px
    xl: 8, // >= 1200px
    xxl: 8, // >= 1200px
  },
  wrapperCol: {
    xs: 18, // <576px
    md: 16, // >= 768px
    lg: 16, // >= 992px
    xl: 16, // >= 1200px
    xxl: 16, // >= 1200px
  },
};

// 列
export const colStyle = {
  xs: 24, // <576px
  md: 12, // >= 768px
  lg: 12, // >= 992px
  xl: 8, // >= 1200px
  xxl: 6, // >= 1600px
};

// 行
export const rowStyle = {
  gutter: {
    xs: 24, // <576px
    md: 24, // >= 768px
    lg: 24, // >= 992px
    xl: 8, // >= 1200px
    xxl: 16, // >= 1600px
  },
};

export const sourceTypeList = [
  { label: 'SF', value: 'SF' },
  { label: 'SX', value: 'SX' },
];
