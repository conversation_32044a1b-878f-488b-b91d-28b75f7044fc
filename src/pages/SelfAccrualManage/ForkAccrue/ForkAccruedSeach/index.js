// 场内人员-叉车分拣
import React, { useState, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Input, Select, DatePicker, Modal } from 'antd';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
  useModuleCode,
} from 'ky-giant';

import { ExportOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';

import moment from 'moment';
import { cloneDeep } from 'lodash';
import { limitCrossMonths } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch';
import AsyncExport from 'src/components/AsyncExport';
import SuppliersSearch from 'src/components/SuppliersSearch';
// eslint-disable-next-line import/extensions
import { querySuppliers } from '@/services/supplierApi';
import {
  read,
  queryForkLiftByZoneCodeUserNoDay,
  querySxForkLiftLadderConfig,
} from './servers/api';
import {
  operationTypeList,
  operateTypeList,
  units,
  unitsList,
  sourceTypeList,
} from './Components/status';

import './index.scss';
const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;

let inputParam = {};
const Page = ({ userInfo, areaListSF, areaListSX, logRoleCode }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  // const [roleCode, setRoleCode] = useState(null); // 角色编码
  const [datesLimit, setDatesLimit] = useState([]);
  const [userOrg, setUserOrg] = useState('SF');
  const [dialogVisible, setDialogVisible] = useState(false); // 叉车(扫码)里程弹窗 显示隐藏
  const [forkLiftData, setForkLiftData] = useState([]); // 叉车(扫码)里程详情
  const [ladderConfig, setLadderConfig] = useState([]); // 阶梯配置
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  // const [totalNum, setTotalNum] = useState(0);

  const [loading, setLoading] = useState(false);
  const columns = [
    {
      title: '所属公司',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'sourceType',
    },
    {
      title: '日期',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'busDate',
      render: v => moment(v).format('YYYY-MM-DD'),
    },
    // {
    //   title: '网点代码',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 150,
    //   dataIndex: 'zoneCode',
    // },
    // {
    //   title: '网点名称',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 150,
    //   dataIndex: 'zoneName',
    // },
    {
      title: '网点',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'zoneCode',
      render: (value, row) => `${row.zoneCode} ${row.zoneName}`,
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: (value, row) => `${row.allocationAreaCode} ${value}`,
      // const areaList = areaListSF.concat(areaListSX);
      // const date = areaList.find(item => item.value === value);
      // return date ? date.label : value;
      //   },
    },
    {
      title: '工号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userCode',
    },
    {
      title: '姓名',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userName',
    },
    {
      title: '岗位',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userStations',
    },
    {
      title: '用工类型',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userType',
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'supplierName',
    },
    {
      title: '供应商编码',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierNo',
    },
    {
      title: '操作类型',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'operationType',
      render: value => {
        const date = operationTypeList.find(item => item.value === value);
        return date ? date.label : value;
      },
    },
    {
      title: '作业环节',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'operateType',
      render: value => {
        const date = operateTypeList.find(item => item.value === value);
        return date ? date.label : value;
      },
    },
    {
      title: '所属公司',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'sourceType',
    },
    {
      title: '叉车类型',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'statisticsType',
    },
    {
      title: '当日实际工作量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'dayWorkCnt',
      // render: (value, row) => `value${row.operateType===3 ? '小时' : '次'}`,
      render: (value, row) => {
        const date = units.find(item => item.value === row.operationType);
        const val = date && value !== 0 ? value + date.label : value;
        if (row.operationType === 5) {
          // 只有操作类型是叉车(扫码)里程时，这个【当日实际工作量】结果才能点击查看详情
          return (
            <span
              style={{ color: 'red' }}
              onClick={() => handleDayWorkCntClick(row)}
            >
              {val}
            </span>
          );
        }
        return val;
      },
    },
    {
      title: '当日操作货量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'dayOperationVol',
      render: (value, row) => {
        const date = unitsList.find(item => item.value === row.operationType);
        return date && value !== 0 ? value + date.label : value;
      },
    },
    {
      title: '当日小件操作货量（0-70kg）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'dayLessSevenWeight',
      render: (value, row) => {
        const date = unitsList.find(item => item.value === row.operationType);
        return date && value !== 0 ? value + date.label : value;
      },
    },
    {
      title: '当日大件操作货量（大于70kg）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'dayGreaterSevenWeight',
      render: (value, row) => {
        const date = unitsList.find(item => item.value === row.operationType);
        return date && value !== 0 ? value + date.label : value;
      },
    },
    {
      title: '当日板均货量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'dayCorrectVol',
      render: value =>
        value !== null || value !== undefined ? `${value}T` : '--',
    },
    {
      title: '当日卡均货量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'dayCardVol',
      render: value =>
        value !== null || value !== undefined ? `${value}T` : '--',
    },
    {
      title: '当日卡口数',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'tranGateNoDayTotals',
    },
    {
      title: '日保底工作量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'dayBottomWorkCnt',
    },
    {
      title: '月累计出勤天数',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'monthAttendanceCnt',
    },
    {
      title: '月累计实际工作量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'monthWorkCnt',
      render: (value, row) => {
        const date = units.find(item => item.value === row.operationType);
        return date && value ? value + date.label : value;
      },
    },
    {
      title: '月累计计提工作量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'monthAccrueWorkCnt',
    },
    {
      title: '当月操作货量',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'monthOperationVol',
      render: (value, row) => {
        const date = unitsList.find(item => item.value === row.operationType);
        return date && value !== 0 ? value + date.label : value;
      },
    },
    {
      title: '当月小件操作货量（0-70kg）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'monthLessSevenWeight',
      render: (value, row) => {
        const date = unitsList.find(item => item.value === row.operationType);
        return date && value !== 0 ? value + date.label : value;
      },
    },
    {
      title: '当月大件操作货量（大于70kg）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'monthGreaterSevenWeight',
      render: (value, row) => {
        const date = unitsList.find(item => item.value === row.operationType);
        return date && value !== 0 ? value + date.label : value;
      },
    },
    {
      title: '当月板均货量',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'monthCorrectVol',
      render: (value, row) => {
        const date = unitsList.find(item => item.value === row.operationType);
        return date && value !== 0 && value !== null
          ? value + date.label
          : value || '';
      },
    },
    {
      title: '当月卡均货量',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'monthCardVol',
    },
    {
      title: '当月卡口数',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'tranGateNoMonthsTotals',
    },
    {
      title: '计提单价',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'accruePrice',
    },
    // {
    //   title: '当月卡口数',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 170,
    //   dataIndex: 'sfvsfvd',
    //   render: v => v || '--',
    // },
    {
      title: '月累计计提(元)',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'monthAccruePrice',
    },
  ];
  const forkLiftColumns = [
    {
      title: '上岗日期',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'taskSourceDate',
      render: v => moment(v).format('YYYY-MM-DD'),
    },
    {
      title: '设备ID',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'eqptUniqueCode',
    },

    {
      title: '上岗时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'workStartTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '离岗时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'workEndTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '在岗时长',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'operationDurationStr',
    },
  ];

  // 查询参数的获取
  const getQueryParams = () => {
    const params = form.getFieldValue();
    if (params.time && params.time.length === 2) {
      params.startDate = moment(params.time[0]).format('YYYY-MM-DD');
      params.endDate = moment(params.time[1]).format('YYYY-MM-DD');
    }
    inputParam = cloneDeep(params);
    delete inputParam.time;
    // refresh({
    //   currentPage: 1,
    //   pageSize: 10,
    // });
    return {
      ...inputParam,
    };
  };
  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = { ...inputParam, pageNum: current, pageSize };
    read(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, total } = obj;
          // setTotalNum(total);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.pageNum,
              pageSize: obj.pageSize,
            },
            list,
          });
        } else {
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        // setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleDayWorkCntClick = async row => {
    const { zoneCode, userCode, busDate } = row;
    try {
      const args = {
        zoneCode,
        userCode,
        bustDate: busDate,
      };
      const { obj, success } = await queryForkLiftByZoneCodeUserNoDay(args);

      if (success) {
        setDialogVisible(true);
        setForkLiftData({ list: [] }); // 修复数据错乱问题
        setForkLiftData({ list: obj });
      }
    } catch (error) {
      console.error(error);
    }
  };

  // 权限查询
  // const getCurrentRoleCode = () => {
  //   const roleId = sessionStorage.getItem('roleId');
  //   if (roleId && systemRole && systemRole.length) {
  //     const role = systemRole.find(item => item.roleId === roleId);
  //     if (role) {
  //       setRoleCode(role.roleCode);
  //     }
  //   }
  // };

  // useEffect(() => {
  //   getCurrentRoleCode();
  //   form.setFieldsValue({
  //     time: [moment().startOf('month'), moment().startOf('day')],
  //     zoneCode: userInfo.deptCode,
  //   });
  //   getQueryParams();
  //   refresh({ currentPage: 1 });
  // }, [userInfo]);

  // useEffect(() => {
  //   if (userInfo.deptCode && userInfo.areaCode) {
  //     form.setFieldsValue({
  //       time: [moment().startOf('day'), moment().endOf('day')],
  //       zoneCode:
  //         logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
  //       allocationAreaCode:
  //         logRoleCode.roleCode === '88888888888'
  //           ? userInfo.areaCode
  //           : undefined,
  //     });
  //     getQueryParams();
  //     refresh({ currentPage: 1 });
  //   }
  // }, [userInfo]);

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  useEffect(() => {
    // 当前登录的角色判断
    if (
      logRoleCode &&
      userInfo &&
      userInfo.deptCode &&
      (logRoleCode.roleCode === 'tp00001' ||
        logRoleCode.roleCode === '88888888888')
    ) {
      form.setFieldsValue({
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        time: [moment().startOf('month'), moment().endOf('month')],
      });
      getQueryParams();
      refresh({ currentPage: 1 });
    } else {
      form.setFieldsValue({
        time: [moment().startOf('month'), moment().endOf('month')],
      });
    }
    if (userInfo && userInfo.empCode) {
      if (/.*SX.*/.test(userInfo.empCode.toUpperCase())) {
        setUserOrg('SX');
      } else {
        setUserOrg('SF');
      }
    }
  }, [logRoleCode, userInfo]);
  // 获取供应商列表
  const handleQuerySuppliers = async () => {
    const args = {
      orgCode: form.getFieldsValue().zoneCode || userInfo.deptCode,
      srcCode: userOrg,
    };
    const res = await querySuppliers(args);
    if (res.success) {
      // 不再使用supplierNameList状态
      // const list =
      //   res.obj.map(({ companyCode, companyName }) => ({
      //     label: companyName,
      //     value: companyCode,
      //   })) || [];
      // setSupplierNameList(list);
    }
  };
  const resetForm = () => {
    form.resetFields();
    if (
      logRoleCode.roleCode === 'tp00001' ||
      logRoleCode.roleCode === '88888888888'
    ) {
      getQueryParams();
      refresh({ currentPage: 1 });
    }
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        time: [moment().startOf('month'), moment().endOf('month')],
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="所属公司" name="sourceType">
                <Select
                  options={sourceTypeList}
                  placeholder="请选择所属公司"
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  onChange={handleQuerySuppliers}
                  // disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="日期"
                name="time"
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <RangePicker
                  allowClear
                  style={{ width: '100%' }}
                  disabledDate={current =>
                    limitCrossMonths(current, datesLimit)
                  }
                  onCalendarChange={val => setDatesLimit(val)}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="用工类型" name="userType">
                <Select
                  allowClear
                  options={[
                    { label: '自有', value: 1 },
                    { label: '外包', value: 2 },
                  ]}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="叉车类型" name="statisticsType">
                <Select
                  allowClear
                  options={[
                    { label: '座叉', value: 1 },
                    { label: '站叉', value: 2 },
                    { label: '有人叉车+无人叉车', value: 3 },
                    { label: '有人叉车', value: 4 },
                    { label: '无人叉车', value: 5 },
                  ]}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="操作类型" name="operationType">
                <Select
                  allowClear
                  options={operationTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="作业环节" name="operateType">
                <Select
                  allowClear
                  options={operateTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="工号" name="userCode">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.zoneCode !== curValues.zoneCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <FormItem name="supplierNo" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      allowClear
                      gysParams={getFieldValue('zoneCode') || userInfo.deptCode}
                    />
                  </FormItem>
                </Col>
              )}
            </FormItem>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AsyncExport
            type="primary"
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            modulecode={moduleCode}
            code="sortForkliftPersonnel-export"
            text="导出"
            options={{
              requstParams: [
                '/restApi/fcamsForkliftServices/forkLiftAccrueManage/exportForkLiftAccrueSync',
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
        </div>
      </div>
      {/* <div>
        <span className="submitButtons">
          <Button icon={<ReloadOutlined />} type="primary" htmlType="submi">
            查询
          </Button>
          <Button
            icon={<RollbackOutlined />}
            style={{ marginLeft: 8 }}
            onClick={() => {
              form.resetFields();
              refresh({ currentPage: 1 });
            }}
          >
            重置
          </Button>
        </span>
      </div> */}
    </Form>
  );

  // 获取阶梯配置
  const fetchLadderConfig = async () => {
    try {
      const res = await querySxForkLiftLadderConfig();
      if (res.success && res.obj) {
        // 按照ladder字段排序
        const sortedConfig = res.obj.sort((a, b) => a.ladder - b.ladder);
        setLadderConfig(sortedConfig);
      }
    } catch (error) {
      console.error('获取阶梯配置失败:', error);
    }
  };

  // 在组件挂载时获取阶梯配置
  useEffect(() => {
    fetchLadderConfig();
  }, []);

  // 生成阶梯列
  const generateLadderColumns = () => {
    // 如果没有配置，返回空数组
    if (!ladderConfig || ladderConfig.length === 0) {
      return [];
    }

    // 固定的字段名映射
    const dayFieldNames = [
      'dayFirstLadderWeight',
      'daySecondLadderWeight',
      'dayThirdLadderWeight',
      'dayFourthLadderWeight',
      'dayFifthLadderWeight',
      'daySixthLadderWeight',
      'daySeventhLadderWeight',
    ];

    const monthFieldNames = [
      'monthFirstLadderWeight',
      'monthSecondLadderWeight',
      'monthThirdLadderWeight',
      'monthFourthLadderWeight',
      'monthFifthLadderWeight',
      'monthSixthLadderWeight',
      'monthSeventhLadderWeight',
    ];

    // 生成当日阶梯列
    const dayLadderColumns = {
      title: '当日顺心大小票阶梯操作货量',
      align: 'center',
      children: ladderConfig
        .map((config, index) => {
          // 确保不超出字段名数组范围
          if (index >= dayFieldNames.length) return null;

          const { ladder, minWeight, maxWeight } = config;
          // 判断是否是最后一个阶梯（没有maxWeight的那个）
          const isLastLadder = maxWeight === null || maxWeight === undefined;
          // 格式化标题，根据是否是最后一个阶梯来使用不同的区间符号
          let title;
          if (isLastLadder) {
            title = `第${ladder}阶梯(${minWeight}-∞)`;
          } else {
            title = `第${ladder}阶梯(${minWeight}-${maxWeight}]`;
          }

          return {
            title,
            align: 'center',
            ellipsis: true,
            width: 120,
            dataIndex: dayFieldNames[index],
            render: (value, row) => {
              if (row.sourceType === 'SX') {
                return value !== null && value !== undefined
                  ? `${value}T`
                  : '--';
              }
              return '--';
            },
          };
        })
        .filter(Boolean), // 过滤掉null值
    };

    // 生成当月阶梯列
    const monthLadderColumns = {
      title: '当月顺心大小票阶梯操作货量',
      align: 'center',
      children: ladderConfig
        .map((config, index) => {
          // 确保不超出字段名数组范围
          if (index >= monthFieldNames.length) return null;

          const { ladder, minWeight, maxWeight } = config;
          // 判断是否是最后一个阶梯（没有maxWeight的那个）
          const isLastLadder = maxWeight === null || maxWeight === undefined;
          // 格式化标题，根据是否是最后一个阶梯来使用不同的区间符号
          let title;
          if (isLastLadder) {
            title = `第${ladder}阶梯(${minWeight}-∞)`;
          } else {
            title = `第${ladder}阶梯(${minWeight}-${maxWeight}]`;
          }

          return {
            title,
            align: 'center',
            ellipsis: true,
            width: 120,
            dataIndex: monthFieldNames[index],
            render: (value, row) => {
              if (row.sourceType === 'SX') {
                return value !== null && value !== undefined
                  ? `${value}T`
                  : '--';
              }
              return '--';
            },
          };
        })
        .filter(Boolean), // 过滤掉null值
    };

    return [dayLadderColumns, monthLadderColumns];
  };

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        showSelection={false}
        loading={loading}
        data={datas}
        columns={[...columns, ...generateLadderColumns()]}
        onChange={handleStandardTableChange}
      />
      {dialogVisible && (
        <Modal
          width={1000}
          maskClosable={false}
          visible={dialogVisible}
          footer={false}
          centered
          onOk={() => setDialogVisible(false)}
          onCancel={() => setDialogVisible(false)}
        >
          <StandardTable
            size="small"
            rowKey="id"
            showSelection={false}
            loading={loading}
            data={forkLiftData}
            columns={forkLiftColumns}
            pagination={false}
          />
        </Modal>
      )}
    </div>
  );
};

@connect(state => ({
  userInfo: state.global.userInfo,
  logRoleCode: state.global.logRoleCode,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const { userInfo, logRoleCode, areaListSX, areaListSF } = this.props;
    return (
      <Page
        userInfo={userInfo}
        logRoleCode={logRoleCode}
        areaListSX={areaListSX}
        areaListSF={areaListSF}
      />
    );
  }
}

export default withRouter(Container);
