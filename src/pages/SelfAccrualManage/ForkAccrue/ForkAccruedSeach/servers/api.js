import request from 'src/utils/request';

// 查询
export function read(params) {
  return request(
    `/restApi/fcamsForkliftServices/forkLiftAccrueManage/queryForkLiftAccrueByPage`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}
// 人员查询
export function queryCompanyInfo(params) {
  return request(`/opbdsUPMService/epEmployee/queryEmpInfo`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
// 叉车(扫码)里程记录】点击查看详情
// 入参：
// {
//     "zoneCode": "010WS",
//     "userCode": "41901491工号",
//     "bustDate": "2023-06-26每天日期"
// }
export function queryForkLiftByZoneCodeUserNoDay(params) {
  return request(
    `/restApi/fcamsForkliftServices/forkLiftAccrueManage/queryForkLiftByZoneCodeUserNoDay`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 查询顺心大小票阶梯配置
export function querySxForkLiftLadderConfig() {
  return request(
    `/restApi/fcamsForkliftServices/forkLiftAccrueManage/querySxForkLiftLadderConfig`,
    {
      method: 'POST',
    },
  );
}
