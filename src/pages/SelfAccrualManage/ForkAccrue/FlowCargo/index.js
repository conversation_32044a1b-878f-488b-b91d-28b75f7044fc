import React, { useState, useEffect, Fragment, PureComponent } from 'react';
import { connect } from 'dva';
import {
  Col,
  Form,
  Input,
  Button,
  Select,
  DatePicker,
  Row,
  Tooltip,
} from 'antd';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  rangePickerColStyle,
  rangePickerLayout,
  formItemLayout,
  colStyle,
  // AuthButton,
  SearchFold,
  rowStyle,
  useModuleCode,
} from 'ky-giant';
// import { timeLimit } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable'; // table组件
import AsyncExport from 'src/components/AsyncExport'; // 异步导出
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import { dealObjectValue } from 'src/utils/utils'; // 网点代码查询

import { read } from './servers/api';
// import { operateTypeList } from './Components/status';
import SubTaskInfor from './Components/SubTaskInfor';
import './index.scss';

const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;
let inputParam = {};

const Page = ({ userInfo, areaListSF, areaListSX, logRoleCode }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [selectedRows, setSelectedRows] = useState([]);
  const [editValue, setEditValue] = useState({}); // 初始值
  const [loading, setLoading] = useState(false);
  const [taskInforVisible, setTaskInforVisible] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '日期',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'incDay',
    },
    {
      title: '场地',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'zoneCode',
    },
    {
      title: '网点名称',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'zoneName',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'allocationAreaCode',
    },
    {
      title: '分拨区名称',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'allocationArea',
    },
    {
      title: '承包制组长工号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userNo',
    },
    {
      title: '承包制组长姓名',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userName',
    },
    {
      title: '承包班次号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'shiftName',
      render: (v, record) => (
        <Fragment>
          <Button
            size="small"
            type="link"
            onClick={e => subTaskInfor(e, record)}
          >
            {v}
          </Button>
        </Fragment>
      ),
    },
    {
      title: '班次开始时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'shiftStartTime',
      // render: v => (v.trim() ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '班次结束时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'shiftEndTime',
      // render: v => (v.trim() ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '承包月台',
      align: 'center',
      width: 170,
      dataIndex: 'platformNo',
      ellipsis: {
        showTitle: false,
      },
      render: text => {
        const showText = text.replace(/,/gi, '，');
        return (
          <Tooltip placement="topLeft" title={showText}>
            {showText}
          </Tooltip>
        );
      },
    },
    {
      title: '流向/网点',
      align: 'center',
      width: 170,
      dataIndex: 'cityCode',
      ellipsis: {
        showTitle: false,
      },
      render: text => {
        const showText = text.replace(/,/gi, '，');
        return text ? (
          <Tooltip placement="topLeft" title={showText}>
            {showText}
          </Tooltip>
        ) : (
          text
        );
      },
    },
    // {
    //   title: '网点',
    //   align: 'center',
    //   width: 170,
    //   dataIndex: 'nextStation',
    //   ellipsis: {
    //     showTitle: false,
    //   },
    //   render: text => {
    //     const showText = text.replace(/,/gi, '，').replace(/\s/g, '');
    //     return text ? (
    //       <Tooltip placement="topLeft" title={showText}>
    //         {showText}
    //       </Tooltip>
    //     ) : (
    //       text
    //     );
    //   },
    // },
    {
      title: '货量（T）',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'meterageWeightAfter',
    },
  ];

  useEffect(() => {
    // 当前登录的角色判断
    if (
      logRoleCode &&
      userInfo &&
      userInfo.deptCode &&
      (logRoleCode.roleCode === 'tp00001' ||
        logRoleCode.roleCode === '88888888888')
    ) {
      form.setFieldsValue({
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        time: [moment().startOf('month'), moment().endOf('month')],
      });
      getQueryParams();
      refresh({ currentPage: 1 });
    } else {
      form.setFieldsValue({
        time: [moment().startOf('month'), moment().endOf('month')],
      });
    }
  }, [logRoleCode, userInfo]);

  // 查询参数的获取
  const getQueryParams = () => {
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    inputParam.startWorkTime = moment(inputParam.time[0]).valueOf();
    inputParam.endWorkTime = moment(inputParam.time[1]).valueOf();
    delete inputParam.time;
    // refresh({
    //   pageNum: datas.pagination.current,
    //   pageSize: datas.pagination.pageSize,
    // });
    return {
      ...dealObjectValue(inputParam),
    };
  };
  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      ...inputParam,
      pageNum: current,
      pageSize,
    };
    read(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess && obj) {
          const { list, total } = obj;
          // setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.pageNum,
              pageSize: obj.pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setLoading(false);
        setSelectedRows([]);
      });
  };

  const taskHandleCancel = () => {
    setTaskInforVisible(false);
  };

  // 人员详情
  const subTaskInfor = (e, record) => {
    e.stopPropagation();
    setEditValue(record);
    setTaskInforVisible(true);
  };

  const handleSelectRows = list => {
    setSelectedRows(list);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const resetForm = () => {
    form.resetFields();
    if (
      logRoleCode.roleCode === '88888888888' ||
      logRoleCode.roleCode === 'tp00001'
    ) {
      getQueryParams();
      refresh({ currentPage: 1 });
    }
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      initialValues={{
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        time: [moment().startOf('month'), moment().endOf('month')],
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="网点编码"
                name="zoneCode"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (getFieldValue('allocationAreaCode') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区和网点不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="分拨区"
                name="allocationAreaCode"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (getFieldValue('zoneCode') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区和网点不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <Select
                  allowClear
                  // disabled={logRoleCode.roleCode === '88888888888'}
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>

            <Col {...colStyle}>
              <FormItem label="组长工号" name="userNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="班次号" name="shiftName">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...rangePickerColStyle}>
              <FormItem
                {...rangePickerLayout}
                label="开始时间"
                name="time"
                rules={[
                  {
                    required: true,
                    message: '请填写',
                  },
                ]}
              >
                <RangePicker
                  ranges={{
                    今天: [moment().startOf('day'), moment().endOf('day')],
                    本周: [
                      moment()
                        .weekday(0)
                        .startOf('day'),
                      moment()
                        .weekday(6)
                        .endOf('day'),
                    ],
                    本月: [moment().startOf('month'), moment().endOf('month')],
                  }}
                  showTime
                  allowClear
                  style={{ width: '100%' }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组长姓名" name="userName">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>

            <Col {...colStyle}>
              <FormItem label="月台号" name="platformNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AsyncExport
            type="primary"
            disabled={datas.list && datas.list.length < 1}
            modulecode={moduleCode}
            code="flowCargoSort-exportAll"
            text="导出全部"
            options={{
              filename: '分拣流向-包干分拣.xlsx',
              requstParams: [
                '/restApi/fcamsLiftServices/sortingBagApi/aSyncExport ',
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
        </div>
      </div>
    </Form>
  );

  return (
    <div>
      <div className="table-list">
        <div className="tableListForm">{renderForm()}</div>
        <StandardTable
          size="small"
          rowKey={(record, index) => `${index}${record.operateUserNo}`}
          selectedRows={selectedRows}
          showSelection={false}
          loading={loading}
          data={datas}
          columns={columns}
          onSelectRow={handleSelectRows}
          onChange={handleStandardTableChange}
        />
      </div>
      {/* 子任务明细 */}
      {taskInforVisible && (
        <SubTaskInfor
          beChose={editValue}
          visible={taskInforVisible}
          handleCancel={taskHandleCancel}
        ></SubTaskInfor>
      )}
    </div>
  );
};

@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const { areaListSX, areaListSF, userInfo, logRoleCode } = this.props;
    return (
      <Page
        logRoleCode={logRoleCode}
        userInfo={userInfo}
        areaListSX={areaListSX}
        areaListSF={areaListSF}
      />
    );
  }
}

export default withRouter(Container);
