import React, { useState, useEffect } from 'react';
import { Modal } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import moment from 'moment';
import { getTaskItem } from '../../servers/api';

const DetaiInfor = props => {
  const { visible, beChose, handleCancel } = props;
  const { shiftName } = beChose;
  // const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const cancle = () => {
    handleCancel();
  };
  const [tableData, setTableData] = useState({
    list: [],
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
  });

  const columns = [
    {
      title: '运单号',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'waybillNo',
    },
    {
      title: '开单计重（KG）',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'meterageWeight',
    },
    {
      title: '有效计重（KG）',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'meterageWeightAfter',
    },
    {
      title: '计重是否变更',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'waybillChangedCount',
      render: text => {
        switch (Number(text)) {
          case 0:
            return '否';
          case 1:
            return '是';
          default:
            return '';
        }
      },
    },
    {
      title: '月台号',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'platformNumber',
    },
    {
      title: '下一站',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'nextStation',
    },
    {
      title: '扫描时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'scanTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '是否匹配班次',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'shiftName',
      render: v => (v === shiftName ? '是' : '否'),
    },
  ];

  const getQueryParams = () =>
    // refresh({
    //   pageNum: tableData.pagination.current,
    //   pageSize: tableData.pagination.pageSize,
    // });
    ({
      pageNum: tableData.pagination.current,
      pageSize: tableData.pagination.pageSize,
      ...beChose,
    });
  const refresh = (pagination = {}) => {
    setLoading(true);
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = { pageNum, pageSize, ...beChose };
    getTaskItem(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, total } = obj;
          // setTotalNum(total);
          setLoading(false);
          setTableData({
            pagination: {
              total,
              current: obj.pageNum,
              pageSize: obj.pageSize,
            },
            list,
          });
        } else {
          setLoading(false);
          setTableData({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => setLoading(false));
  };
  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  useEffect(() => {
    refresh();
  }, [visible]);
  return (
    <Modal
      centered
      title={null}
      visible={visible}
      onCancel={cancle}
      width={1300}
      footer={null}
      maskClosable={false}
    >
      <div>
        <AsyncExport
          icon={<ExportOutlined />}
          // moduleCode={moduleCode}
          // code="accrueManage-exportAll"
          handleCancel={handleCancel}
          // type="primary"
          disabled={tableData.list && tableData.list.length < 1}
          text="导出全部"
          options={{
            requstParams: [
              '/restApi/fcamsLiftServices/sortingBagApi/aSyncExportDetail',
              {
                method: 'POST',
                body: getQueryParams,
              },
            ],
          }}
        />
      </div>
      <StandardTable
        size="small"
        rowKey="id"
        showSelection={false}
        bordered={false}
        multiple={false}
        loading={loading}
        data={tableData}
        columns={columns}
        onChange={handleStandardTableChange}
      />
    </Modal>
  );
};
export default connect(() => ({}))(props => (
  <DetaiInfor {...props}></DetaiInfor>
));
