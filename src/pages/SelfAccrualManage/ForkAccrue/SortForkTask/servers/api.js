import request from 'src/utils/request';

// 查询
export function search(params) {
  return request(`/restApi/fcamsForkliftServices/sorterForklift/query`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
// 查询主任务视图数据
export function searchMainTask(params) {
  return request(
    `/restApi/fcamsForkliftServices/sorterForklift/queryMainTask`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}
// 查询组员明细
export function searchDetail(params) {
  return request(`/restApi/fcamsForkliftServices/sorterForklift/queryDetail`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
// 查询总操作货量
export function querySum(params) {
  return request(`/restApi/fcamsForkliftServices/sorterForklift/querySum`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 批量结束任务、结束任务
// 入参：idList: [1, 2, 3]
export async function finishTaskApi(params) {
  return request(`/restApi/fcamsForkliftServices/sorterForklift/finishTask`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
// 获取扫描明细列表数据
export function requestScanTable(params) {
  return request(`/tdmsAccrueService/itemQueryService/queryNcItem`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
