import React from 'react';

import { Form, Row, Col, Input, Card, DatePicker } from 'antd';
import './index.scss';

const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;

const formItemLayout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    span: 21,
    offset: 3,
  },
};
const DynamicFormTest = props => {
  const {
    id, // 查看&&编辑
    form,
    propsName = 'list',
    ...restProps
  } = props;
  // const [datesLimit, setDatesLimit] = useState([]);

  // const range = (start, end) => {
  //   const result = [];

  //   for (let i = start; i <= end; i++) {
  //     result.push(i);
  //   }

  //   return result;
  // };

  // const handleData = time => {
  //   console.log(time, 'time');
  //   if (!time) {
  //     return false;
  //   }
  //   // 大于当前日期不能选 time > moment()
  //   // 小于当前日期不能选 time < moment().subtract(1, "days")
  //   // 只能选前7后7 time < moment().subtract(7, "days") || time > moment().add(7, 'd')
  //   return time < moment().subtract(7, 'days') || time > moment().add(7, 'd');
  //   // return (
  //   //   time < moment().subtract(1, 'days') ||
  //   //   time >
  //   //     moment()
  //   //       .endOf('month')
  //   //       .subtract(1, 'months')
  //   // );
  // };

  // const disabledDate = current => current && current >= moment().endOf('day'); // 选择时间要大于等于当前天。若今天不能被选择，去掉等号即可。
  // const disabledDate = current => {
  //   console.log(current, 'current');
  //   return current => {};
  // };
  // const disabledRangeTime = (dates, partial) => {
  //   const hours = moment().hours(); // 0~23
  //   const minutes = moment().minutes(); // 0~59
  //   const seconds = moment().seconds(); // 0~59
  //   // 当日只能选择当前时间之后的时间点
  //   if (
  //     dates &&
  //     moment(dates[1]).date() === moment().date() &&
  //     partial === 'end'
  //   ) {
  //     return {
  //       disabledHours: () => range(hours + 1, 23),

  //       disabledMinutes: () => range(minutes + 1, 59),

  //       disabledSeconds: () => range(seconds + 1, 59),
  //     };
  //   }
  // };

  //   失效时间的校验
  // const disabledInvalidDate = current =>
  //   current &&
  //   current <
  //     (form.getFieldValue('startTime')
  //       ? form.getFieldValue('startTime').startOf('day')
  //       : moment().subtract(0, 'day'));

  return (
    <Form.List name={propsName} {...restProps}>
      {fields => (
        <Card>
          {fields.map((field, index) => (
            <FormItem
              key={field.name}
              className="style-Bottom"
              label={`${index === 0 ? '组员明细' : ''}`}
              {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
            >
              <Row gutter={8}>
                <Col md={3} sm={24}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'operateUserNo']}
                    fieldKey={[field.fieldKey, 'operateUserNo']}
                  >
                    <Input disabled allowClear />
                  </Form.Item>
                </Col>
                <Col md={3} sm={24}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'operateUserName']}
                    fieldKey={[field.fieldKey, 'operateUserName']}
                  >
                    <Input disabled allowClear />
                  </Form.Item>
                </Col>
                <Col md={10} sm={24}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'time']}
                    fieldKey={[field.fieldKey, 'time']}
                  >
                    <RangePicker
                      showTime
                      placeholder={['上岗开始时间', '上岗结束时间']}
                      disabled
                      allowClear
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>
                <Col md={3} sm={24}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'avgWorks']}
                    fieldKey={[field.fieldKey, 'avgWorks']}
                  >
                    <Input disabled allowClear addonAfter="H" />
                  </Form.Item>
                </Col>
                {/* <Col md={1} sm={24}>
                  <FormItem {...field}>
                    {fields.length < 5 && (
                      <Button
                        type="link"
                        onClick={() => {
                          add();
                        }}
                      >
                        新增
                      </Button>
                    )}
                  </FormItem>
                </Col>
                <Col md={1} sm={24}>
                  <FormItem {...field}>
                    {fields.length > 1 && (
                      <Button
                        type="link"
                        onClick={() => {
                          remove(field.name);
                        }}
                      >
                        删除
                      </Button>
                    )}
                  </FormItem>
                </Col> */}
              </Row>
            </FormItem>
          ))}
        </Card>
      )}
    </Form.List>
  );
};

export default DynamicFormTest;
