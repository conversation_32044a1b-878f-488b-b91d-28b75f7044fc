import React, { useEffect, useState } from 'react';
import { Modal } from 'antd';
import moment from 'moment';
import StandardTable from 'src/components/StandardTable';
import { ExportOutlined } from '@ant-design/icons';
import AsyncExport from 'src/components/AsyncExport';
import { statusList } from '../status';
import { requestScanTable } from '../../servers/api';

const ScanTable = props => {
  const { visible, initialValues, handleCancel } = props;
  const [loading, setLoading] = useState(false);

  const [tableData, setTableData] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  useEffect(() => {
    if (initialValues) {
      refresh({
        ...initialValues,
        current: 1,
        pageSize: 10,
      });
    }
  }, [visible]);

  const columns = [
    {
      title: '运单号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'waybillNo',
    },
    {
      title: '母单号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'mainWaybillNo',
    },
    {
      title: '计费重量',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'validMeterageWeight',
    },
    {
      title: '状态',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'status',
      render: v => {
        const date = statusList.find(item => item.value === v);
        return date ? date.label : v;
      },
    },
    {
      title: '扫描人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'scanner',
    },
    {
      title: '扫描时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'scanTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];
  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    return {
      pageNum,
      pageSize,
      flowId: initialValues.flowId,
      operateType: initialValues.operateType,
      taskId: initialValues.taskId,
      zoneCode: initialValues.zoneCode,
    };
  };
  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      pageNum = pagination.current,
      pageSize = pagination.pageSize,
    } = pagination;
    const param = {
      // ...initialValues,
      flowId: initialValues.flowId,
      operateType: initialValues.operateType,
      taskId: initialValues.taskId,
      zoneCode: initialValues.zoneCode,
      pageNum,
      pageSize,
    };
    requestScanTable(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { rows, total } = obj;
          setLoading(false);
          setTableData({
            pagination: {
              total,
              current: obj.currentPage,
              pageSize: obj.limit,
            },
            list: rows,
          });
        } else {
          setLoading(false);
          setTableData({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setLoading(false);
      });
  };
  const handleStandardTableChange = pagination => {
    refresh({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const cancle = () => {
    handleCancel();
  };
  return (
    <Modal
      title="扫描明细"
      visible={visible}
      onCancel={cancle}
      width={1200}
      footer={null}
    >
      <div className="export-btn">
        <AsyncExport
          type="primary"
          style={{ marginRight: 15 }}
          icon={<ExportOutlined />}
          // disabled={tableData.list && tableData.list.length < 1}
          // modulecode={moduleCode}
          // code="liftTruckRepetitionScan-exportAll"
          handleCancel={handleCancel}
          text="导出全部"
          options={{
            filename: `${initialValues?.flowId}扫描明细导出.xlsx`,
            requstParams: [
              '/tdmsAccrueService/itemQueryService/asyncExportNcItem',
              {
                method: 'POST',
                // body: initialValues,
                body: getQueryParams,
              },
            ],
          }}
        />
      </div>
      <StandardTable
        size="small"
        rowKey="id"
        loading={loading}
        showSelection={false}
        data={tableData}
        columns={columns}
        onChange={handleStandardTableChange}
      />
    </Modal>
  );
};

export default ScanTable;
