// 场内任务-叉车分拣
import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Form,
  Input,
  Select,
  DatePicker,
  Radio,
  Button,
  Modal,
} from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  rangePickerColStyle,
  rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  AuthButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import DeptSearch from 'src/components/DeptSearch';
import SuppliersSearch from 'src/components/SuppliersSearch';
import {
  search,
  searchMainTask,
  searchDetail,
  querySum,
  finishTaskApi,
} from './servers/api';
import Add from './Components/Add';
import {
  workTypeList,
  operateTypeList,
  skilledLevelList,
  sourceTypeList,
  taskStatusList,
} from './Components/status';
import { success, error } from '@/utils/utils';
import ScanModalTable from './Components/ScanModalTable';

import './index.scss';
const { RangePicker } = DatePicker;
const { Item: FormItem } = Form;

let inputParam = {};
const Page = ({ userInfo, areaListSF, areaListSX, logRoleCode }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  const [selectedRows, setSelectedRows] = useState([]);
  // const [totalNum, setTotalNum] = useState(0);
  const [avgWeight, setAvgWeight] = useState(0);
  const [avgWorks, setAvgWorks] = useState(0);
  const [taskView, setTaskView] = useState('subView'); // subView : 子任务视图 | mainView： 主任务视图

  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  const [mainViewdatas, setMainViewdatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  const [scanModalVisible, setScanModalVisible] = useState(false);
  const [scanRowData, setScanRowData] = useState(null);

  const columns = [
    {
      title: '所属公司',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'sourceType',
    },
    {
      title: '工号',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'operateUserNo',
    },
    {
      title: '姓名',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'operateUserName',
    },
    {
      title: '岗位',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'empDutyName',
    },
    {
      title: '员工类型',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'userType',
      render: v => {
        const date = workTypeList.find(item => item.value === v);
        return date ? date.label : v;
      },
    },
    {
      title: '外包熟练工等级',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'skilledLevel',
      render: value => {
        const data = skilledLevelList.find(item => item.value === value);
        return data ? data.label : '';
      },
    },
    {
      title: '网点',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'zoneCode',
      render: (value, row) => `${row.zoneCode} ${row.zoneName}`,
    },
    {
      title: '分拨区',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'allocationAreaCode',
      render: values => {
        const areaList = areaListSF.concat(areaListSX);
        const date = areaList.find(item => item.value === values);
        return date ? date.label : values;
      },
    },
    {
      title: '作业环节',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'operateType',
      render: (v, row) => {
        // 1、operateType = 3 并且 taskType = 0时为地面线分拣
        // 2、operateType = 3 并且 taskType = 1时为地面线流向分拣
        const { taskType } = row;
        if (v === 3 && taskType === 0) return '地面线分拣';
        if (v === 3 && taskType === 1) return '地面流向分拣';
        const data = operateTypeList.find(item => item.value === v);
        return data ? data.label : v;
      },
    },
    {
      title: '任务号',
      align: 'center',
      ellipsis: true,
      width: 190,
      dataIndex: 'taskId',
    },
    {
      title: '月台号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'platformNo',
    },
    {
      title: '卡口号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'inPortCode',
    },
    {
      title: '子任务号',
      align: 'center',
      ellipsis: true,
      width: 230,
      dataIndex: 'flowId',
      render: (v, row) =>
        row.operateType === 14 || row.operateType === 15 ? (
          <Button
            size="small"
            type="link"
            onClick={e => handleScanDetail(e, row)}
          >
            {row.flowId}
          </Button>
        ) : (
          <span>{row.flowId}</span>
        ),
    },
    {
      title: '个人工时(H)',
      align: 'center',
      width: 150,
      ellipsis: true,
      dataIndex: 'avgWorks',
      // render: v => `${v}H`,
    },
    {
      title: '个人货量(T)',
      align: 'center',
      width: 150,
      ellipsis: true,
      dataIndex: 'avgWeight',
      // render: v => `${v}T`,
    },
    {
      title: '子任务总工时(H)',
      align: 'center',
      width: 150,
      ellipsis: true,
      dataIndex: 'totalsFlowWorks',
    },
    {
      title: '子任务人数',
      align: 'center',
      width: 150,
      ellipsis: true,
      dataIndex: 'teamNum',
    },
    {
      title: '任务总工时(H)',
      align: 'center',
      width: 150,
      ellipsis: true,
      dataIndex: 'totalsWorks',
      // render: v => `${v}H`,
    },
    // {
    //   title: '任务总货量(T)',
    //   align: 'center',
    //   width: 150,
    //   ellipsis: true,
    //   dataIndex: 'totalsWeight',
    //   // render: v => `${v}T`,
    // },
    {
      title: '任务归属日期',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'taskSourceDate',
      render: v => moment(v).format('YYYY-MM-DD'),
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'supplierName',
    },
    {
      title: '供应商编码',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierNo',
    },
    {
      title: '供应商结算方式',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accountModule',
    },
    {
      title: '外包服务类型',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'empTypeName',
    },
    {
      title: '结算网点',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accountZoneCode',
    },
    {
      title: '上岗时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'startWorkTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '离岗时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'endWorkTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '任务创建时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'startTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '任务完成时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'finishTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '创建人',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'creator',
    },
    {
      title: '创建时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'createTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'modifier',
    },
    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '任务状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'taskStatus',
      fixed: 'right',
      render: value => {
        const data = taskStatusList.find(item => item.value === value);
        return data ? data.label : '-';
      },
    },
    {
      title: '操作',
      align: 'center',
      width: 160,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          {record.taskStatus === 1 &&
            // NC初分和NC细分 执行中不允许结束任务
            ![14, 15, 17].includes(record.operateType) && (
              <Button
                size="small"
                type="link"
                onClick={() => finishTask([record.id])}
              >
                结束任务
              </Button>
            )}
          <AuthButton
            modulecode={moduleCode}
            code="sortForkliftTask-check"
            size="small"
            type="link"
            onClick={e => update(e, record)}
          >
            查看
          </AuthButton>
          {/* 只有NC初分和NC细分可以查看任务明细 */}
          {[14, 15].includes(record.operateType) && (
            <Button
              size="small"
              type="link"
              onClick={e => handleScanDetail(e, record)}
            >
              扫描明细
            </Button>
          )}
        </Fragment>
      ),
    },
  ];
  const mainViewColumns = [
    {
      title: '所属公司',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'sourceType',
    },
    {
      title: '工号',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'operateUserNo',
    },
    {
      title: '姓名',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'operateUserName',
    },
    {
      title: '员工类型',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'userType',
      render: v => {
        const date = workTypeList.find(item => item.value === v);
        return date ? date.label : v;
      },
    },
    {
      title: '外包熟练工等级',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'skilledLevel',
      render: value => {
        const data = skilledLevelList.find(item => item.value === value);
        return data ? data.label : '';
      },
    },
    {
      title: '网点',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'zoneCode',
    },
    {
      title: '作业环节',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'operateType',
      render: v => {
        const date = operateTypeList.find(item => item.value === v);
        return date ? date.label : v;
      },
    },
    {
      title: '主任务号',
      align: 'center',
      width: 200,
      ellipsis: true,
      dataIndex: 'taskId',
    },
    {
      title: '月台号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'platformNo',
    },
    {
      title: '主任务个人工时',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'totalsPersonWorks',
    },
    {
      title: '主任务个人货量',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'totalsPersonWeight',
    },
    {
      title: '主任务总工时',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'totalsWorks',
    },
    {
      title: '主任务总货量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'totalsWeight',
    },
    {
      title: '任务归属日期',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'taskSourceDate',
      render: v => moment(v).format('YYYY-MM-DD'),
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'supplierName',
    },
    {
      title: '供应商编码',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'supplierNo',
    },
    {
      title: '供应商结算方式',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accountModule',
    },
    {
      title: '结算网点',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accountZoneCode',
    },
    {
      title: '上岗时间--下岗时间',
      align: 'center',
      ellipsis: true,
      width: 350,
      dataIndex: 'startWorkTimeList',
      render: v => (
        <span>
          {v.map(time => (
            <div>{time}</div>
          ))}
        </span>
      ),
    },
    {
      title: '主任务创建时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'startTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '主任务完成时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'finishTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '创建人',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'creator',
    },
  ];
  const getQueryParams = () => {
    // const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    if (inputParam.time && inputParam.time.length > 0) {
      inputParam.startTime = moment(inputParam.time[0]).valueOf();
      inputParam.endTime = moment(inputParam.time[1]).valueOf();
    }
    if (inputParam.timeRange && inputParam.timeRange.length > 0) {
      inputParam.taskSourceDateStart = moment(inputParam.timeRange[0])
        .startOf()
        .valueOf();
      inputParam.taskSourceDateEnd = moment(inputParam.timeRange[1])
        .endOf()
        .valueOf();
    }
    delete inputParam.time;
    delete inputParam.timeRange;
    // refresh({
    //   currentPage: 1,
    //   pageSize: 10,
    // });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}, viewType = taskView) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    // 主任务视图，删除 子任务号 条件
    if (viewType === 'mainView') {
      delete inputParam.flowId;
    }
    const param = {
      ...inputParam,
      pageNum: current,
      pageSize,
    };
    if (viewType === 'subView') {
      search(param)
        .then(res => {
          const { success: resSuccess, obj } = res;
          if (resSuccess) {
            const { list, total } = obj;
            // setTotalNum(total);
            setSelectedRows([]);
            setLoading(false);
            setDatas({
              pagination: {
                total,
                current: obj.pageNum,
                pageSize: obj.pageSize,
              },
              list,
            });
          } else {
            setSelectedRows([]);
            setLoading(false);
            setDatas({
              pagination: {
                total: 0,
                current: 1,
                pageSize: 10,
              },
              list: [],
            });
          }
        })
        .catch(() => {
          setSelectedRows([]);
          setLoading(false);
        });
      querySum(param).then(res => {
        if (res.obj !== null) {
          setAvgWeight(res.obj.avgWeight);
          setAvgWorks(res.obj.avgWorks);
        } else {
          setAvgWeight(res.obj);
        }
      });
    } else {
      searchMainTask(param)
        .then(res => {
          const { success: resSuccess, obj } = res;
          if (resSuccess) {
            const {
              list,
              total,
              avgWeight: avgWeightNum,
              avgWorks: avgWorksNum,
            } = obj;
            setAvgWeight(avgWeightNum);
            setAvgWorks(avgWorksNum);
            setSelectedRows([]);
            setLoading(false);
            setMainViewdatas({
              pagination: {
                total,
                current: obj.pageNum,
                pageSize: obj.pageSize,
              },
              list,
            });
          } else {
            setSelectedRows([]);
            setLoading(false);
            setMainViewdatas({
              pagination: {
                total: 0,
                current: 1,
                pageSize: 10,
              },
              list: [],
            });
          }
        })
        .catch(() => {
          setSelectedRows([]);
          setLoading(false);
        });
    }
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 修改
  const update = async (e, v) => {
    e.stopPropagation();
    const reult = await searchDetail({
      ...v,
    });
    setEditingTarget(reult.obj);
    setAddModalVisible(true);
  };

  // 查看扫描明细
  const handleScanDetail = (e, row) => {
    e.stopPropagation();
    setScanRowData(row);
    setScanModalVisible(true);
  };
  // 查看扫描明细
  const handleScanCancel = () => {
    setScanModalVisible(false);
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 批量结束任务
  const finishTaskBatch = record => {
    // 任务状态是否存在进行中
    let unableFinishFlag = false;
    // 作业环节 是否存在NC初分/NC细分
    let unableOperateTypeFlag = false;
    const idList = record.map(row => {
      if (row.taskStatus !== 1) {
        unableFinishFlag = true;
      }
      if ([14, 15, 17].includes(row.operateType)) {
        unableOperateTypeFlag = true;
      }
      return row.id;
    });
    if (unableFinishFlag) {
      error('存在不可结束的任务');
      return;
    }
    if (unableOperateTypeFlag) {
      error('NC初分/NC细分任务不可结束');
      return;
    }

    finishTask(idList);
  };

  // 结束任务
  const finishTask = idList => {
    Modal.confirm({
      title: '提示',
      content: '是否确认结束已选任务',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const params = form.getFieldValue();

        finishTaskApi({ idList, operateType: params.operateType }).then(res => {
          if (res.success) {
            success('提交成功');
            refresh();
          } else {
            error('提交失败');
          }
        });
      },
      onCancel() {},
    });
  };
  // useEffect(() => {
  //   if (userInfo.deptCode && userInfo.areaCode) {
  //     form.setFieldsValue({
  //       time: [moment().startOf('day'), moment().endOf('day')],
  //       zoneCode:
  //         logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
  //       allocationAreaCode:
  //         logRoleCode.roleCode === '88888888888'
  //           ? userInfo.areaCode
  //           : undefined,
  //     });
  //     getQueryParams();
  //   }
  // }, [userInfo]);

  useEffect(() => {
    // 当前登录的角色判断
    if (
      logRoleCode &&
      userInfo &&
      userInfo.deptCode &&
      (logRoleCode.roleCode === 'tp00001' ||
        logRoleCode.roleCode === '88888888888')
    ) {
      form.setFieldsValue({
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        time: [moment().startOf('month'), moment().endOf('month')],
      });
      getQueryParams();
      refresh({ currentPage: 1 });
    } else {
      form.setFieldsValue({
        time: [moment().startOf('month'), moment().endOf('month')],
      });
    }
  }, [logRoleCode, userInfo]);

  // 变更视图
  const handleChangeTaskView = e => {
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    if (inputParam.time && inputParam.time.length > 0) {
      inputParam.startTime = moment(inputParam.time[0]).valueOf();
      inputParam.endTime = moment(inputParam.time[1]).valueOf();
    }
    if (inputParam.timeRange && inputParam.timeRange.length > 0) {
      inputParam.taskSourceDateStart = moment(inputParam.timeRange[0])
        .startOf()
        .valueOf();
      inputParam.taskSourceDateEnd = moment(inputParam.timeRange[1])
        .endOf()
        .valueOf();
    }
    delete inputParam.time;
    delete inputParam.timeRange;
    if (e.target.value === 'mainView') {
      form.getFieldValue().flowId = '';
    }
    setTaskView(
      `${e.target.value}`,
      refresh({ currentPage: 1, pageSize: 10 }, `${e.target.value}`),
    );
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        time: [moment().startOf('day'), moment().endOf('day')],
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="所属公司" name="sourceType">
                <Select
                  options={sourceTypeList}
                  placeholder="请选择所属公司"
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="网点"
                name="zoneCode"
                //   去掉必选
                // rules={[
                //   ({ getFieldValue }) => ({
                //     validator(_, v) {
                //       if (getFieldValue('allocationAreaCode') || v) {
                //         return Promise.resolve();
                //       }
                //       return Promise.reject(
                //         new Error('分拨区和网点不能同时为空'),
                //       );
                //     },
                //   }),
                // ]}
              >
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  // disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="分拨区"
                name="allocationAreaCode"
                //   去掉必选
                // rules={[
                //   ({ getFieldValue }) => ({
                //     validator(_, v) {
                //       if (getFieldValue('zoneCode') || v) {
                //         return Promise.resolve();
                //       }
                //       return Promise.reject(
                //         new Error('分拨区和网点不能同时为空'),
                //       );
                //     },
                //   }),
                // ]}
              >
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  // disabled={logRoleCode.roleCode === '88888888888'}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务状态" name="taskStatus">
                <Select
                  allowClear
                  options={taskStatusList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务号" name="taskId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="子任务号" name="flowId">
                <Input
                  placeholder="请输入"
                  allowClear
                  disabled={taskView === 'mainView'}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="作业环节"
                name="operateType"
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <Select
                  allowClear
                  options={operateTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...rangePickerColStyle}>
              <FormItem
                {...rangePickerLayout}
                label="上岗时间"
                name="time"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, dateValue) {
                      if (getFieldValue('timeRange') || dateValue) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('开始日期和归属日期不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <RangePicker showTime allowClear style={{ width: '100%' }} />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                // {...rangePickerLayout}
                label="归属日期"
                name="timeRange"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, dateValue) {
                      if (getFieldValue('time') || dateValue) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('开始日期和归属日期不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <RangePicker
                  allowClear
                  style={{
                    width: '100%',
                  }}
                />
              </FormItem>
            </Col>

            <Col {...colStyle}>
              <FormItem label="工号" name="operateUserNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <FormItem
              shouldUpdate={(prevValues, curValues) =>
                prevValues.zoneCode !== curValues.zoneCode
              }
              noStyle
            >
              {({ getFieldValue }) => (
                <Col {...colStyle}>
                  <FormItem name="supplierNo" label="供应商">
                    <SuppliersSearch
                      placeholder="请输入供应商"
                      allowClear
                      gysParams={getFieldValue('zoneCode') || userInfo.deptCode}
                    />
                  </FormItem>
                </Col>
              )}
            </FormItem>
            <Col {...colStyle}>
              <FormItem label="员工类型" name="userType">
                <Select
                  allowClear
                  options={workTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="结算网点" name="accountZoneCode">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="外包熟练工等级" name="skilledLevel">
                <Select
                  options={skilledLevelList}
                  placeholder="请选择"
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="供应商结算方式" name="accountModule">
                <Select
                  options={[
                    {
                      label: '计时',
                      value: '计时',
                    },
                    {
                      label: '计重',
                      value: '计重',
                    },
                  ]}
                  placeholder="请选择"
                  allowClear
                />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con btn-space-between">
        <div>
          <AsyncExport
            text="导出"
            icon={<ExportOutlined />}
            disabled={
              taskView === 'subView'
                ? datas.list && datas.list.length < 1
                : mainViewdatas.list && mainViewdatas.list.length < 1
            }
            modulecode={moduleCode}
            code="sortForkliftTask-exportAll"
            options={{
              filename:
                taskView === 'subView'
                  ? '分拣叉车任务.xlsx'
                  : '分拣叉车主任务.xlsx',
              requstParams: [
                taskView === 'subView'
                  ? `/restApi/fcamsForkliftServices/sorterForklift/aSyncExport`
                  : `/restApi/fcamsForkliftServices/sorterForklift/aSyncMainTaskExport`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
          <Button
            disabled={!selectedRows.length}
            onClick={() => finishTaskBatch(selectedRows)}
            type="primary"
            style={{
              marginLeft: 20,
            }}
          >
            批量结束任务
          </Button>
          <span
            style={{
              marginLeft: 30,
              fontSize: 18,
            }}
          >
            合计总操作货量 : {`${avgWeight}T` || '--'}
          </span>

          <span
            style={{
              marginLeft: 30,
              fontSize: 18,
            }}
          >
            合计总操作工时 : {`${avgWorks}H` || '--'}
          </span>
        </div>
        <div className="switch-taskview-btn">
          <AuthButton
            modulecode={moduleCode}
            code="sortForkliftTask-mainTaskSwitch"
            size="small"
            type="link"
          >
            <Radio.Group
              defaultValue="subView"
              value={taskView}
              onChange={handleChangeTaskView}
            >
              <Radio.Button value="subView">子任务视图</Radio.Button>
              <Radio.Button value="mainView">主任务视图</Radio.Button>
            </Radio.Group>
          </AuthButton>
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      {taskView === 'subView' && (
        <StandardTable
          size="small"
          rowKey={(record, index) => `${index}${record.id}`}
          selectedRows={selectedRows}
          loading={loading}
          showSelection
          data={datas}
          columns={columns}
          onSelectRow={handleSelectRows}
          onChange={handleStandardTableChange}
        />
      )}
      {taskView === 'mainView' && (
        <StandardTable
          size="small"
          rowKey="id"
          selectedRows={selectedRows}
          loading={loading}
          showSelection={false}
          data={mainViewdatas}
          columns={mainViewColumns}
          onSelectRow={handleSelectRows}
          onChange={handleStandardTableChange}
        />
      )}
      {addModalVisible && (
        <Add
          initialValues={editingTarget}
          visible={addModalVisible}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          // onOk={() => {
          //   setAddModalVisible(false);
          //   setEditingTarget(null);
          //   refresh({ currentPage: 1 });
          // }}
        />
      )}
      {scanModalVisible && (
        <ScanModalTable
          visible={scanModalVisible}
          initialValues={scanRowData}
          handleCancel={handleScanCancel}
        ></ScanModalTable>
      )}
    </div>
  );
};
@connect(state => ({
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
  logRoleCode: state.global.logRoleCode,
}))
class Container extends PureComponent {
  render() {
    const { userInfo, areaListSX, areaListSF, logRoleCode } = this.props;
    return (
      <Page
        userInfo={userInfo}
        areaListSX={areaListSX}
        areaListSF={areaListSF}
        logRoleCode={logRoleCode}
      />
    );
  }
}

export default withRouter(Container);
