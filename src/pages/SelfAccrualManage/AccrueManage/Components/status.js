export const approvalStatusList = [
  { label: '待提交', value: 0 },
  { label: '提交审核', value: 1 },
  { label: '分拨区审核中', value: 2 },
  { label: '分拨区审核通过', value: 3 },
  { label: '人资审核中', value: 4 },
  { label: '人资审核通过', value: 5 },
  { label: '退回', value: 8 },
];

export const isModifyList = [
  { label: '否', value: 0 },
  { label: '是', value: 1 },
];

export const sourceTypeList = [
  { label: '顺丰', value: 'SF' },
  { label: '顺心', value: 'SX' },
];

// 计提奖金-区分顺心 顺丰
export const accrueMomenyList = [
  { title: '顺丰卸车', value: 'unloadAmount' },
  { title: '顺心卸车', value: 'unloadSxAmount' },
  { title: '顺丰卸车核准', value: 'approvalUnloadAmount' },
  { title: '顺心卸车核准', value: 'approvalUnloadSxAmount' },
  { title: '顺丰分拣', value: 'sortingAmount' },
  { title: '顺心分拣', value: 'sortingSxAmount' },
  { title: '顺丰分拣核准', value: 'approvalSortingAmount' },
  { title: '顺心分拣核准', value: 'approvalSortingSxAmount' },
  { title: '顺丰叉车', value: 'forkliftAmount' },
  { title: '顺心叉车', value: 'forkliftSxAmount' },
  { title: '顺丰叉车核准', value: 'approvalForkliftAmount' },
  { title: '顺心叉车核准', value: 'approvalForkliftSxAmount' },
  { title: '顺丰装车', value: 'loadAmount' },
  { title: '顺心装车', value: 'loadSxAmount' },
  { title: '顺丰装车核准', value: 'approvalLoadAmount' },
  { title: '顺心装车核准', value: 'approvalLoadSxAmount' },
];

// 计提货量-区分顺心 顺丰
export const accrueCorgoList = [
  { title: '顺丰卸车', value: 'unloadWeight' },
  { title: '顺心卸车', value: 'unloadSxWeight' },
  { title: '顺丰卸车核准', value: 'approvalUnloadWeight' },
  { title: '顺心卸车核准', value: 'approvalUnloadSxWeight' },
  { title: '顺丰分拣', value: 'sortingWeight' },
  { title: '顺心分拣', value: 'sortingSxWeight' },
  { title: '顺丰分拣核准', value: 'approvalSortingWeight' },
  { title: '顺心分拣核准', value: 'approvalSortingSxWeight' },
  { title: '顺丰叉车', value: 'forkliftWeight' },
  { title: '顺心叉车', value: 'forkliftSxWeight' },
  { title: '顺丰叉车核准', value: 'approvalForkliftWeight' },
  { title: '顺心叉车核准', value: 'approvalForkliftSxWeight' },
  { title: '顺丰装车', value: 'loadWeight' },
  { title: '顺心装车', value: 'loadSxWeight' },
  { title: '顺丰装车核准', value: 'approvalLoadWeight' },
  { title: '顺心装车核准', value: 'approvalLoadSxWeight' },
  { title: '有责装车错发量', value: 'loadDutyWeight' },
];

// 计提单价-区分顺心 顺丰
export const accruePriceList = [
  { title: '顺丰卸车', value: 'unloadPrice' },
  { title: '顺心卸车', value: 'unloadSxPrice' },
  { title: '顺丰卸车核准', value: 'approvalUnloadPrice' },
  { title: '顺心卸车核准', value: 'approvalUnloadSxPrice' },
  { title: '顺丰分拣', value: 'sortingPrice' },
  { title: '顺心分拣', value: 'sortingSxPrice' },
  { title: '顺丰分拣核准', value: 'approvalSortingPrice' },
  { title: '顺心分拣核准', value: 'approvalSortingSxPrice' },
  { title: '顺丰叉车', value: 'forkliftPrice' },
  { title: '顺心叉车', value: 'forkliftSxPrice' },
  { title: '顺丰叉车核准', value: 'approvalForkliftPrice' },
  { title: '顺心叉车核准', value: 'approvalForkliftSxPrice' },
  { title: '顺丰装车', value: 'loadPrice' },
  { title: '顺心装车', value: 'loadSxPrice' },
  { title: '顺丰装车核准', value: 'approvalLoadPrice' },
  { title: '顺心装车核准', value: 'approvalLoadSxPrice' },
];

// 保底-区分顺心 顺丰
export const accrueMinList = [
  { title: '顺丰卸车', value: 'unloadAmountMin' },
  { title: '顺心卸车', value: 'unloadSxAmountMin' },
  { title: '顺丰卸车核准', value: 'approvalUnloadAmountMin' },
  { title: '顺心卸车核准', value: 'approvalUnloadSxAmountMin' },
  { title: '顺丰分拣', value: 'sortingAmountMin' },
  { title: '顺心分拣', value: 'sortingSxAmountMin' },
  { title: '顺丰分拣核准', value: 'approvalSortingAmountMin' },
  { title: '顺心分拣核准', value: 'approvalSortingSxAmountMin' },
  { title: '顺丰叉车', value: 'forkliftAmountMin' },
  { title: '顺心叉车', value: 'forkliftSxAmountMin' },
  { title: '顺丰叉车核准', value: 'approvalForkliftAmountMin' },
  { title: '顺心叉车核准', value: 'approvalForkliftSxAmountMin' },
  { title: '顺丰装车', value: 'loadAmountMin' },
  { title: '顺心装车', value: 'loadSxAmountMin' },
  { title: '顺丰装车核准', value: 'approvalLoadAmountMin' },
  { title: '顺心装车核准', value: 'approvalLoadSxAmountMin' },
];

// 列表基本信息

export const baseInforListA = [
  { label: '月份', value: 'accrueMonth' },
  { label: '分拨区', value: 'allocationArea' },
  { label: '网点', value: 'zoneName' },
  { label: '工号', value: 'userNo' },
  { label: '姓名', value: 'userName' },
  { label: '发薪岗位', value: 'position' },
  { label: '计费类型', value: 'accrueType' },
  { label: '扣保底类型', value: 'calculationRules' },
  { label: '最终发放计提', value: 'approvalAccrueAmount' },
  { label: '核算计提奖金', value: 'approvalAccrueAmountOrgin' },
  { label: '累计计提(系统）', value: 'accrueAmount' },
  // { label: '综合系数', value: 'ctps' },
  { label: '补发补扣', value: 'cutPayment' },
  { label: '计提打折系数', value: 'accrueDiscountFactor' },
  // { label: '奖惩系数', value: 'punishmentCoefficient' },
  { label: '当月天数', value: 'attendNum' },
  { label: '当月核准天数', value: 'approvalDays' },
  { label: '修改人', value: 'modifier' },
  { label: '修改时间', value: 'modifyTime' },
  { label: '异常说明', value: 'errMsg' },
  // { label: '系数调整说明', value: 'coefficientAdjustmentTxt' },
  { label: '补发补扣说明', value: 'replacementBuckleTxt' },
];
