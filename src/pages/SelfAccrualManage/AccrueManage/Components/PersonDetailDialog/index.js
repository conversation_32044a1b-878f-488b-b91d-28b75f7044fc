import React, { useState, useEffect } from 'react';
import { connect } from 'dva';
import StandardTable from 'src/components/StandardTable';
import { ExportOutlined } from '@ant-design/icons';
import AsyncExport from 'src/components/AsyncExport';
import moment from 'moment';
import { queryDutyDetail } from '../../servers/api';

const columns = [
  {
    title: '操作日期',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'operateDt',
    render: text => (text ? moment(text).format('YYYY-MM-DD') : '-'),
  },
  {
    title: '运单号',
    align: 'center',
    ellipsis: true,
    width: 130,
    dataIndex: 'waybillNo',
  },
  {
    title: '责任网点',
    align: 'center',
    ellipsis: true,
    width: 80,
    dataIndex: 'dutyZone',
  },
  {
    title: '任务号',
    align: 'center',
    ellipsis: true,
    width: 150,
    dataIndex: 'taskId',
  },
  {
    title: '子任务号',
    align: 'center',
    ellipsis: true,
    width: 150,
    dataIndex: 'flowId',
  },
  {
    title: '扣除货量(kg)',
    align: 'center',
    ellipsis: true,
    width: 80,
    dataIndex: 'meterageWeightQty',
  },
];
function DetaiDialog(props) {
  const { visible, params } = props;
  // const moduleCode = useModuleCode();
  const [loading, setLoading] = useState(false);

  const [tableData, setTableData] = useState({ list: [] });

  const refresh = async () => {
    setLoading(true);
    try {
      const { obj, success } = await queryDutyDetail(params);
      if (success) {
        setTableData({ list: [] }); // 修复数据错乱问题
        setTableData({ list: obj });
      }
    } catch (error) {
      console.log('🚀 ~ file: index.js:69 ~ refresh ~ error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refresh();
  }, [params]);
  return (
    <div>
      <AsyncExport
        type="primary"
        style={{ marginRight: 15 }}
        icon={<ExportOutlined />}
        disabled={tableData.list && tableData.list.length < 1}
        text="导出全部"
        options={{
          requstParams: [
            '/tdmsAccrueService/accrueMonthStaffDetailRest/asyncExportDutyDetail',
            {
              method: 'POST',
              body: params,
            },
          ],
        }}
      />
      <StandardTable
        size="small"
        rowKey="taskId"
        showSelection={false}
        loading={loading}
        data={tableData}
        columns={columns}
        pagination={false}
      />
    </div>
  );
}
export default connect(() => ({}))(props => (
  <DetaiDialog {...props}></DetaiDialog>
));
