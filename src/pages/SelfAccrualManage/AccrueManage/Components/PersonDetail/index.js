import React, { useState, useEffect } from 'react';
import { Modal } from 'antd';
import { connect } from 'dva';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import { getPersonInformation } from '../../servers/api';

const columns = [
  {
    title: '工号',
    align: 'center',
    ellipsis: true,
    width: 150,
    dataIndex: 'userNo',
  },
  {
    title: '姓名',
    align: 'center',
    ellipsis: true,
    width: 150,
    dataIndex: 'userName',
  },
  {
    title: '岗位',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'position',
  },
  {
    title: '网点',
    align: 'center',
    ellipsis: true,
    width: 150,
    dataIndex: 'zoneName',
  },
  {
    title: '分拨区',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'allocationArea',
  },
  {
    title: '出勤天数',
    align: 'center',
    ellipsis: true,
    width: 80,
    dataIndex: 'attendNum',
  },
  {
    title: '装车货量',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'loadWeight',
  },
  {
    title: '装车计提',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'loadAmount',
  },
  {
    title: '卸车货量',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'unloadWeight',
  },
  {
    title: '卸车计提',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'unloadAmount',
  },
  {
    title: '分拣货量',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'sortingWeight',
  },
  {
    title: '分拣计提',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'sortingAmount',
  },
  {
    title: '叉车货量',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'forkliftWeight',
  },
  {
    title: '叉车计提',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'forkliftAmount',
  },
  {
    title: '累计计提',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'accrueAmount',
  },
];
function DetaiInfor(props) {
  const { visible, beChose, handleCancel } = props;
  const { zoneCode, accrueMonth } = beChose;
  // const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const cancle = () => {
    handleCancel('personInfor');
  };
  const [tableData, setTableData] = useState({
    list: [],
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
  });

  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    refresh({ pageNum, pageSize });
    return {
      pageNum,
      pageSize,
      zoneCode,
      accrueMonth,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = { zoneCode, accrueMonth, pageNum, pageSize };
    getPersonInformation(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum: current, total } = obj;
          // setTotalNum(total);
          setLoading(false);
          setTableData({
            pagination: {
              total,
              current,
              pageSize,
            },
            list,
          });
        } else {
          setLoading(false);
          setTableData({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => setLoading(false));
  };
  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  useEffect(() => {
    refresh();
  }, [visible]);
  return (
    <Modal
      maskClosable={false}
      centered
      title={null}
      visible={visible}
      onCancel={cancle}
      width={1300}
      footer={null}
    >
      <div>
        <AsyncExport
          disabled={tableData.list && tableData.list.length < 1}
          handleCancel={cancle}
          type="primary"
          // code="personExport"
          text="导出"
          options={{
            requstParams: [
              '/tdmsAccrueService/accrueMonthStaffDetailRest/exportSync',
              {
                method: 'POST',
                body: getQueryParams,
              },
            ],
          }}
        />
      </div>

      <StandardTable
        size="small"
        rowKey="id"
        showSelection={false}
        bordered={false}
        multiple={false}
        loading={loading}
        data={tableData}
        columns={columns}
        onChange={handleStandardTableChange}
      />
    </Modal>
  );
}
export default connect(() => ({}))(props => (
  <DetaiInfor {...props}></DetaiInfor>
));
