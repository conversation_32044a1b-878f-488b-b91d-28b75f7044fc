// 01385149 测试工号;
import React, { useState, useEffect, PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import {
  Row,
  Col,
  Form,
  Select,
  DatePicker,
  Input,
  Drawer,
  Button,
  Modal,
  Tooltip,
} from 'antd';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { cloneDeep } from 'lodash';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import AsyncExport from 'src/components/AsyncExport';
import ImportButton from 'src/components/ImportButton';
import {
  isModifyList,
  // accrueMomenyList,
  // accrueCorgoList,
  // accruePriceList,
  // accrueMinList,
} from '../status';
import { searchPersonalList } from '../../servers/api';
import PreviewDetail from '../PreviewDetail';
import PersonDetailDialog from '../PersonDetailDialog';

import './index.scss';
const { Item: FormItem } = Form;
let inputParam = {};
const Page = ({ userInfo, areaListSX, areaListSF, rowValue, logRoleCode }) => {
  const moduleCode = useModuleCode();
  const [form] = Form.useForm();
  // const [roleCode, setRoleCode] = useState(null); // 角色编码
  const [rowTable, setRowTable] = useState({}); // 某一行的数据
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  // const [previewClose, setPreviewClose] = useState(false);
  const [visibleClose, setVisibleClose] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false); // 弹窗 显示隐藏
  const [dialogParams, setDialogParams] = useState({}); // 弹窗 接口入参

  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '月份',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueMonth',
    },
    {
      title: '分拨区代码',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'allocationAreaCode',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'allocationArea',
    },
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'zoneCode',
    },
    {
      title: '网点',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'zoneName',
    },
    {
      title: '工号',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'userNo',
    },
    {
      title: '姓名',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'userName',
    },
    {
      title: '发薪岗位',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'position',
    },
    {
      title: '计费类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueType',
    },
    {
      title: '扣保底类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'calculationRules',
    },
    {
      title: () => (
        <div>
          <p style={{ marginBottom: 0 }}>
            最终发放计提
            <Tooltip
              overlayClassName="max-w-680"
              title="最终发放计提=核算计提奖金*计提打折系数"
            >
              {' '}
              <QuestionCircleOutlined />
            </Tooltip>
          </p>
        </div>
      ),
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'approvalAccrueAmount',
    },
    {
      title: () => (
        <div>
          <p style={{ marginBottom: 0 }}>
            核算计提奖金
            <Tooltip
              overlayClassName="max-w-680"
              title="核算计提奖金=累计计提(系统）+补发补扣"
            >
              {' '}
              <QuestionCircleOutlined />
            </Tooltip>
          </p>
        </div>
      ),
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'approvalAccrueAmountOrgin',
    },
    {
      title: '累计计提(系统）',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueAmount',
    },
    // {
    //   title: '综合系数',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 100,
    //   dataIndex: 'ctps',
    // },
    {
      title: '补发补扣',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'cutPayment',
    },
    {
      title: '计提打折系数',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueDiscountFactor',
    },
    // todo
    {
      title: '有责装车错发量(T)',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'loadDutyWeight',
      render: (value, row) => {
        if (value > 0) {
          return (
            <span
              style={{ color: 'red' }}
              onClick={() => handleDialogTable(row)}
            >
              {value}
            </span>
          );
        }
        return value;
      },
    },
    // {
    //   title: '奖惩系数',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 100,
    //   dataIndex: 'punishmentCoefficient',
    // },
    {
      title: '系统出勤天数',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'attendNum',
    },
    {
      title: '分拨区导入出勤天数',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'approvalDays',
    },
    {
      title: '异常说明',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'errMsg',
    },
    // {
    //   title: '系数调整说明',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 100,
    //   dataIndex: 'coefficientAdjustmentTxt',
    // },
    {
      title: '补发补扣说明',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'replacementBuckleTxt',
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'modifier',
    },
    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      dataIndex: 'operation',
      render: (_, record) => (
        <Fragment>
          <Button
            size="small"
            type="link"
            onClick={e => checkDetail(e, record)}
          >
            查看详情
          </Button>
        </Fragment>
      ),
    },
  ];

  const checkDetail = (e, row) => {
    e.stopPropagation();
    setRowTable(row);
    setVisibleClose(true);
  };

  const onClose = () => {
    setVisibleClose(false);
  };

  const onRowDBShow = row => {
    setRowTable(row);
    setVisibleClose(true);
  };

  // 查询参数的获取
  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    inputParam.accrueMonth = moment(inputParam.accrueMonth).format('YYYY-MM');
    refresh({ pageNum, pageSize, inputParam });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = { ...inputParam, pageNum, pageSize };
    searchPersonalList(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum: current, total } = obj;
          // setTotalNum(total);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current,
              pageSize,
            },
            list: list || [],
          });
        } else {
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 权限查询
  // const getCurrentRoleCode = () => {
  //   const roleId = sessionStorage.getItem('roleId');
  //   if (roleId && systemRole && systemRole.length) {
  //     const role = systemRole.find(item => item.roleId === roleId);
  //     if (role) {
  //       setRoleCode(role.roleCode);
  //     }
  //   }
  // };

  // useEffect(() => {
  //   // 当前登录的角色判断
  //   if (
  //     logRoleCode &&
  //     userInfo &&
  //     userInfo.deptCode &&
  //     (logRoleCode.roleCode === 'tp00001' ||
  //       logRoleCode.roleCode === '88888888888')
  //   ) {
  //     form.setFieldsValue({
  //       zoneCode:
  //         logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
  //       accrueMonth: moment(new Date()),
  //     });
  //     getQueryParams();
  //   } else {
  //     form.setFieldsValue({
  //       accrueMonth: moment(new Date()),
  //     });
  //   }
  // }, [logRoleCode, userInfo, rowValue]);

  useEffect(() => {
    if (userInfo && userInfo.deptCode) {
      if (rowValue) {
        form.setFieldsValue({
          zoneCode: rowValue.zoneCode,
          accrueMonth: moment(rowValue.accrueMonth),
        });
      } else {
        form.setFieldsValue({
          zoneCode: userInfo.deptCode,
          accrueMonth: moment(new Date()),
        });
      }
      getQueryParams();
    }
  }, [userInfo && rowValue]);

  const handleDialogTable = async row => {
    setDialogVisible(true);
    //   "userNo":"01393114", //工号
    // "accrueMonth":"2023-06"  //月份
    setDialogParams({
      userNo: row.userNo,
      accrueMonth: row.accrueMonth,
      zoneCode: row.zoneCode,
    });
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 选择数据
  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={{
        zoneCode: userInfo.deptCode,
        accrueMonth: moment(new Date()),
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="网点"
                name="zoneCode"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (getFieldValue('allocationAreaCode') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区和网点不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  // disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="分拨区"
                name="allocationAreaCode"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (getFieldValue('zoneCode') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区和网点不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <Select
                  // style={{backgroundColor}}
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="工号" name="userNo">
                <Input allowClear placeholder="请输入" />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="姓名" name="userName">
                <Input allowClear placeholder="请输入" />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="发薪岗位" name="position">
                <Input allowClear placeholder="请输入" />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="查询月份"
                name="accrueMonth"
                rules={[
                  {
                    required: true,
                    message: '请选择月份',
                  },
                ]}
              >
                <DatePicker
                  showToday
                  picker="month"
                  allowClear
                  style={{ width: '100%' }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="是否修改" name="isModify">
                <Select
                  allowClear
                  options={isModifyList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <ImportButton
            style={{ marginRight: 15 }}
            danger={false}
            type="primary"
            pagename="核准计提"
            moduleCode={moduleCode}
            code="accrualPayable-detailImport"
            title="导入"
            action="/tdmsAccrueService/accrueMonthStaffDetailRest/importApprovalAmount"
            modalUrl="/tdmsAccrueService/accrueMonthStaffDetailRest/downLoadTemplate"
            modalName="核准计提导入模板.xlsx"
            handleSyncImport={refresh}
          />
          <ImportButton
            danger={false}
            style={{ marginRight: 15 }}
            type="primary"
            pagename="导入补发补扣"
            moduleCode={moduleCode}
            code="accrualPayable-supplement"
            title="导入补发补扣"
            action="/tdmsAccrueService/accrueMonthStaffDetailRest/importApprovalPersonAmount"
            modalUrl="/tdmsAccrueService/accrueMonthStaffDetailRest/downLoadTemplatePersonMonth"
            modalName="导入补发补扣模板.xlsx"
            handleSyncImport={refresh}
          />
          <AsyncExport
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="accrualPayable-detailExport"
            type="primary"
            disabled={datas.list && datas.list.length < 1}
            text="导出"
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueMonthStaffDetailRest/exportSync',
                {
                  method: 'POST',
                  body: inputParam,
                },
              ],
            }}
          />
          <AsyncExport
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="accrualPayable-detailDayExport"
            type="primary"
            disabled={datas.list && datas.list.length < 1}
            text="导出人员每日明细"
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueMonthStaffDetailRest/exportSyncStaff',
                {
                  method: 'POST',
                  body: inputParam,
                },
              ],
            }}
          />
          <AsyncExport
            style={{ marginRight: 15 }}
            modulecode={moduleCode}
            code="accrualPayable-miscommunicationDetailExport"
            type="primary"
            disabled={datas.list && datas.list.length < 1}
            text="导出汇总有责装车错发明细"
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueMonthStaffDetailRest/asyncExportDutyDetail',
                {
                  method: 'POST',
                  body: inputParam,
                },
              ],
            }}
          />
        </div>
      </div>
      {/* <div>
        <span className="submitButtons">
          <Button icon={<ReloadOutlined />} type="primary" htmlType="submi">
            查询
          </Button>
          <Button
            icon={<RollbackOutlined />}
            style={{ marginLeft: 8 }}
            onClick={() => {
              form.resetFields();
              refresh();
            }}
          >
            重置
          </Button>
        </span>
      </div> */}
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        loading={loading}
        data={datas}
        showSelection={false}
        columns={columns}
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
        onRow={record => ({
          onClick: () => {},
          onDoubleClick: () => {
            onRowDBShow(record);
          },
        })}
      />
      <Drawer
        width={900}
        placement="right"
        closable={false}
        onClose={onClose}
        visible={visibleClose}
      >
        <PreviewDetail rowValue={rowTable} />
      </Drawer>
      <Modal
        width={800}
        bodyStyle={{ maxHeight: '500px', overflowY: 'scroll' }}
        maskClosable={false}
        visible={dialogVisible}
        footer={false}
        centered
        onOk={() => setDialogVisible(false)}
        onCancel={() => setDialogVisible(false)}
      >
        <PersonDetailDialog visible={dialogVisible} params={dialogParams} />
      </Modal>
    </div>
  );
};

@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  systemRole: state.global.roles,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const {
      dispatch,
      history,
      userInfo,
      areaListSF,
      areaListSX,
      systemRole,
      rowValue,
      logRoleCode,
    } = this.props;
    return (
      <Page
        dispatch={dispatch}
        logRoleCode={logRoleCode}
        history={history}
        systemRole={systemRole}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
        userInfo={userInfo}
        rowValue={rowValue}
      />
    );
  }
}

export default withRouter(Container);
