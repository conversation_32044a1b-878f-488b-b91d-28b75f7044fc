import React, { useEffect, useState } from 'react';
import { Descriptions, Divider } from 'antd';
import moment from 'moment';
import { isAvailable } from '../../servers/api';

import {
  baseInforListA,
  accrueMomenyList,
  accrueCorgoList,
  accruePriceList,
  accrueMinList,
} from '../status';
import './index.scss';

const Remark = props => {
  const { rowValue } = props;
  const [showDetail, setShowDetail] = useState(false);

  useEffect(() => {
    if (rowValue) {
      isAvailable({
        zoneCode: rowValue.zoneCode,
        accrueMonth: rowValue.accrueMonth,
      }).then(res => {
        setShowDetail(!res.obj);
      });
    }
  }, [rowValue]);

  return (
    <div>
      <Descriptions column={4} title="基本信息">
        {baseInforListA.map(({ label, value }) => (
          <Descriptions.Item
            // span={
            //   index === 15 && rowValue[value] && rowValue[value].length > 3
            //     ? 4
            //     : 1
            // }
            span={2}
            label={label}
            key={label}
          >
            {value === 'modifyTime'
              ? rowValue[value]
                ? moment(rowValue[value]).format('YYYY-MM-DD HH:mm:ss')
                : ''
              : rowValue[value]}
          </Descriptions.Item>
        ))}
      </Descriptions>
      <Divider />
      {showDetail && (
        <div>
          <div style={{ display: 'flex' }}>
            <Descriptions title="计提奖金" column={2} style={{ flex: 1 }}>
              {accrueMomenyList.map(({ title, value }) => (
                <Descriptions.Item span={1} label={title} key={value}>
                  {rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
            <Descriptions title="计提货量" column={2} style={{ flex: 1 }}>
              {accrueCorgoList.map(({ title, value }) => (
                <Descriptions.Item span={1} label={title} key={value}>
                  {rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </div>
          <Divider />
          <div style={{ display: 'flex' }}>
            <Descriptions title="计提单价" column={2} style={{ flex: 1 }}>
              {accruePriceList.map(({ title, value }) => (
                <Descriptions.Item span={1} label={title} key={value}>
                  {rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
            <Descriptions title="保底" column={2} style={{ flex: 1 }}>
              {accrueMinList.map(({ title, value }) => (
                <Descriptions.Item span={1} label={title} key={value}>
                  {rowValue[value]}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </div>
        </div>
      )}
    </div>
  );
};
export default Remark;
