import React, { useState } from 'react';
import { Modal, Button, Form, Input, message, Radio } from 'antd';
import { error } from 'src/utils/utils';
import { approval } from '../../servers/api';

const { Item: FormItem } = Form;
const { TextArea } = Input;

const tailLayout = {
  wrapperCol: { offset: 8, span: 16 },
};
const Approve = props => {
  const { visible, beChose, handleSearch, handleCancel } = props;
  const { staffUuid } = beChose;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [check, setCheck] = useState(false);

  const cancel = () => {
    form.resetFields();
    handleCancel('approve');
  };
  const refuse = type => {
    const { value } = type.target;
    setCheck(value === '0');
  };

  const submit = () => {
    form.validateFields().then(data => {
      setLoading(true);
      data.type = Number(data.type);
      approval({ ...data, staffUuid })
        .then(res => {
          setLoading(false);
          if (res.success) {
            message.success(res.obj);
            cancel();
            handleSearch();
          } else {
            error(res.errorMessage);
          }
        })
        .catch(err => {
          setLoading(false);
          message.error(err);
        });
    });
  };

  return (
    <div>
      <Modal
        title="审核"
        width={500}
        visible={visible}
        onCancel={cancel}
        maskClosable={false}
        footer={null}
        destroyOnClose
      >
        <Form form={form}>
          <FormItem
            label="审批意见"
            name="type"
            rules={[
              {
                required: true,
                message: '请选择审批意见',
              },
            ]}
          >
            <Radio.Group onChange={refuse}>
              <Radio value="1">通过</Radio>
              <Radio value="0">退回</Radio>
            </Radio.Group>
          </FormItem>
          <FormItem
            label="意见描述"
            name="approvalWord"
            rules={[
              {
                required: check,
                message: '请填写意见描述',
              },
            ]}
          >
            <TextArea maxLength={200} />
          </FormItem>
          <Form.Item {...tailLayout}>
            <Button onClick={cancel}>取消</Button>
            <Button
              type="primary"
              onClick={submit}
              loading={loading}
              style={{ marginLeft: '20px' }}
            >
              确定
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
export default Approve;
