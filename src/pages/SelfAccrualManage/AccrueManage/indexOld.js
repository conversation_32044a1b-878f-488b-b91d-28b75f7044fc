// 01385149 测试工号;
import React, { useState, useEffect, PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Button, Select, DatePicker } from 'antd';
import {
  ReloadOutlined,
  RollbackOutlined,
  ExportOutlined,
} from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  AuthButton,
  useModuleCode,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import AsyncExport from 'src/components/AsyncExport';
import ExportButton from 'src/components/ExportButton';
import PersonDetail from './Components/PersonDetail';
import ApproveRecord from './Components/ApproveRecord';
import SubmitApproval from './Components/SubmitApproval';
import Approval from './Components/Approval';
import { approvalStatusList } from './Components/status';
import { searchList } from './servers/api';

import './index.scss';
const { Item: FormItem } = Form;
const { Option } = Select;

let inputParam = {};
const Page = ({ userInfo, systemRole, areaListSX, areaListSF }) => {
  const moduleCode = useModuleCode();
  const [form] = Form.useForm();
  const [roleCode, setRoleCode] = useState(null); // 角色编码
  const [personVisible, setPersonVisible] = useState(false); // 人员明细
  const [recordVisible, setRecordVisible] = useState(false); // 审核记录
  const [submitApproveVisible, setSubmitApproveVisible] = useState(false); // 提交审核
  const [approveVisible, setApproveVisible] = useState(false); // 审核
  const [rowTable, setRowTable] = useState(false); // 某一行的数据
  const [selectedRows, setSelectedRows] = useState([]);
  const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '月份',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'accrueMonth',
    },
    {
      title: '网点',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'zoneCode',
    },
    {
      title: '区域',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'regionArea',
    },
    {
      title: '省区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'provincialArea',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: (v, row) => `${row.allocationAreaCode} ${v}`,
    },
    {
      title: '上月效率奖金',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'accrueAmountLastMonth',
    },
    {
      title: '效率奖金',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'accrueAmount',
    },
    {
      title: '运作员效率奖金',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'operatorAmount',
    },
    {
      title: '分拣员效率奖金',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'sorterAmount',
    },
    {
      title: '叉车司机效率奖金',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'forkliftAmount',
    },
    {
      title: '码货员效率奖金',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'coderAmount',
    },
    {
      title: '运作组长效率奖金',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'teamLeaderAmount',
    },
    {
      title: '审核状态',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'approvalStatus',
      render: value => {
        const data = approvalStatusList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '操作',
      align: 'center',
      ellipsis: true,
      width: 280,
      fixed: 'right',
      dataIndex: 'operate',
      render: (v, record) => (
        <Fragment>
          <AuthButton
            modulecode={moduleCode} // 角色列表
            code="accrueManage-personDetail" // 模块编码
            type="link"
            onClick={e => clickBtn(e, record, 'personInfor')}
          >
            人员明细
          </AuthButton>
          <AuthButton
            modulecode={moduleCode}
            code="accrueManage-approveRecord"
            type="link"
            onClick={e => clickBtn(e, record, 'approveRecord')}
          >
            审核记录
          </AuthButton>
          {/* 待提交&&退回--场地 */}
          {(record.approvalStatus === 0 || record.approvalStatus === 8) &&
            roleCode === 'tp00001' && (
              <AuthButton
                modulecode={moduleCode}
                code="accrueManage-submitApprove"
                type="link"
                onClick={e => clickBtn(e, record, 'submitApprove')}
              >
                提交审核
              </AuthButton>
            )}
          {/* 待审核--总部 6 baspAdmin 人资 4 89031212 区部 2 88888888888  */}
          {((record.approvalStatus === 6 && roleCode === 'baspAdmin') ||
            (record.approvalStatus === 4 && roleCode === '89031212') ||
            (record.approvalStatus === 2 && roleCode === '88888888888')) && (
            <AuthButton
              modulecode={moduleCode}
              code="accrueManage-approve"
              type="link"
              onClick={e => clickBtn(e, record, 'approve')}
            >
              审核
            </AuthButton>
          )}
        </Fragment>
      ),
    },
  ];

  // 查询参数的获取
  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    inputParam.accrueMonth = moment(inputParam.accrueMonth).format('YYYY-MM');
    refresh({ pageNum, pageSize, inputParam });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = { ...inputParam, pageNum, pageSize };
    searchList(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum: current, total } = obj;
          setTotalNum(total);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current,
              pageSize,
            },
            list,
          });
        } else {
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 权限查询
  const getCurrentRoleCode = () => {
    const roleId = sessionStorage.getItem('roleId');
    if (roleId && systemRole && systemRole.length) {
      const role = systemRole.find(item => item.roleId === roleId);
      if (role) {
        setRoleCode(role.roleCode);
      }
    }
  };

  const clickBtn = (e, record, value) => {
    e.stopPropagation();
    setRowTable(record);
    closeModal(value, true);
  };

  useEffect(() => {
    getCurrentRoleCode();
    form.setFieldsValue({
      zoneCode: userInfo.deptCode,
      sourceType: userInfo.orgCode,
      accrueMonth: moment(new Date()).subtract(1, 'months'),
      allocationAreaCode: userInfo.areaCode,
      // accrueMonth: moment(new Date()),
    });
    getQueryParams();
    refresh();
  }, [userInfo]);

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const closeModal = (value, isTrue) => {
    if (value === 'submitApprove') {
      // 提交审核;
      setSubmitApproveVisible(isTrue);
    } else if (value === 'approveRecord') {
      // 审核记录
      setRecordVisible(isTrue);
    } else if (value === 'personInfor') {
      // 人员明细;
      setPersonVisible(isTrue);
    } else if (value === 'approve') {
      // 审核;
      setApproveVisible(isTrue);
    }
  };

  // 选择数据
  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={{
        zoneCode: userInfo.deptCode,
        sourceType: userInfo.orgCode,
        accrueMonth: moment(new Date()).subtract(1, 'months'),
        allocationAreaCode: userInfo.areaCode,
        // accrueMonth: moment(new Date()),
      }}
    >
      <Row {...rowStyle}>
        <Col {...colStyle}>
          <FormItem label="网点" name="zoneCode">
            <DeptSearch
              placeholder="请输入中转场名称或者代码"
              disabled={roleCode === 'tp00001'}
              allowClear
            />
          </FormItem>
        </Col>
        <Col {...colStyle}>
          <FormItem label="分拨区" name="allocationAreaCode">
            <Select
              options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
              placeholder="请选择分拨区"
              allowClear
            ></Select>
          </FormItem>
        </Col>
        <Col {...colStyle}>
          <FormItem
            label="归属公司"
            name="sourceType"
            rules={[
              {
                required: true,
                message: '请选择',
              },
            ]}
          >
            <Select
              allowClear
              options={[
                { label: '顺丰', value: 'SF' },
                { label: '顺心', value: 'SX' },
              ]}
              placeholder="请选择归属公司"
            ></Select>
          </FormItem>
        </Col>
        <Col {...colStyle}>
          <FormItem label="审核状态" name="approvalStatus">
            <Select allowClear placeholder="请选择审核状态">
              {approvalStatusList.map(ele => (
                <Option key={ele.value}>{ele.label}</Option>
              ))}
            </Select>
          </FormItem>
        </Col>
        <Col {...colStyle}>
          <FormItem
            label="查询月份"
            name="accrueMonth"
            rules={[
              {
                required: true,
                message: '请选择月份',
              },
            ]}
          >
            <DatePicker
              showToday
              picker="month"
              allowClear
              style={{ width: '100%' }}
            />
          </FormItem>
        </Col>
      </Row>
      <div className="table-btn-group">
        <div className="tableListOperator">
          <div>
            <ExportButton
              moduleCode={moduleCode}
              code="accrueManage-exportSelect"
              text="导出所选"
              disabled={selectedRows.length < 1}
              icon={<ExportOutlined />}
              options={{
                filename: '计提应付列表.xls',
                total: totalNum,
                requstParams: [
                  `/tdmsAccrueService/accrueMothStaffRest/exportAccrueMonthStaff`,
                  {
                    method: 'POST',
                    body: { idList: selectedRows.map(ele => ele.id) },
                  },
                ],
              }}
            />
            <AsyncExport
              moduleCode={moduleCode}
              code="accrueManage-exportAll"
              type="primary"
              disabled={datas.list && datas.list.length < 1}
              text="导出全部"
              options={{
                requstParams: [
                  '/tdmsAccrueService/accrueMothStaffRest/accrueMonthStaffExportSync',
                  {
                    method: 'POST',
                    body: inputParam,
                  },
                ],
              }}
            />
          </div>
        </div>
        <div>
          <span className="submitButtons">
            <Button icon={<ReloadOutlined />} type="primary" htmlType="submi">
              查询
            </Button>
            <Button
              icon={<RollbackOutlined />}
              style={{ marginLeft: 8 }}
              onClick={() => {
                form.resetFields();
                refresh();
              }}
            >
              重置
            </Button>
          </span>
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        loading={loading}
        data={datas}
        columns={columns}
        selectedRows={selectedRows}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {/* 人员明细 */}
      {personVisible && (
        <PersonDetail
          beChose={rowTable}
          visible={personVisible}
          handleCancel={closeModal}
        ></PersonDetail>
      )}
      {/* 审核记录 */}
      {recordVisible && (
        <ApproveRecord
          beChose={rowTable}
          visible={recordVisible}
          handleCancel={closeModal}
        ></ApproveRecord>
      )}
      {/* 提交审核 */}
      {submitApproveVisible && (
        <SubmitApproval
          beChose={rowTable}
          visible={submitApproveVisible}
          handleSearch={refresh}
          handleCancel={closeModal}
        ></SubmitApproval>
      )}
      {/* 审核 */}
      {approveVisible && (
        <Approval
          beChose={rowTable}
          visible={approveVisible}
          handleSearch={refresh}
          handleCancel={closeModal}
        ></Approval>
      )}
    </div>
  );
};

@connect(state => ({
  userInfo: state.global.userInfo,
  systemRole: state.global.roles,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const {
      dispatch,
      history,
      userInfo,
      areaListSF,
      areaListSX,
      systemRole,
    } = this.props;
    return (
      <Page
        dispatch={dispatch}
        history={history}
        systemRole={systemRole}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
        userInfo={userInfo}
      />
    );
  }
}

export default withRouter(Container);
