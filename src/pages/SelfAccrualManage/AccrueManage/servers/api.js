import request from 'src/utils/request';

// 自有计提应付管理--列表查询
export async function searchList(params) {
  return request(`/tdmsAccrueService/accrueMothStaffRest/query`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 自有计提应付管理--人员明细--列表查询
export async function searchPersonalList(params) {
  return request(`/tdmsAccrueService/accrueMonthStaffDetailRest/getInfo`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 自有计提应付管理--人员明细
export async function getPersonInformation(params) {
  return request(`/tdmsAccrueService/accrueMonthStaffDetailRest/getInfo`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 自有计提应付管理--审批记录
export async function approveHistory(params) {
  return request(`/tdmsAccrueService/approvalRest/seeApprovalDetails`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 自有计提应付管理--提交审核
export async function submitApproval(params) {
  return request(`/tdmsAccrueService/approvalRest/submitApproval`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 自有计提应付管理--审核
export async function approval(params) {
  return request(`/tdmsAccrueService/approvalRest/passApproval`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 人员明细--是否展示计提详情明细
export async function isAvailable(params) {
  return request(`/tdmsAccrueService/accrueMothStaffRest/isAvailable`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 有责装车错发货量数据点击看详情接口：
export function queryDutyDetail(params) {
  return request(`/tdmsAccrueService/accrueMonthStaffDetailRest/dutyDetail`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
