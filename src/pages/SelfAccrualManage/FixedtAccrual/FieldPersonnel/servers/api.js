import request from 'src/utils/request';

// 查询
export function read(params) {
  return request(`/tdmsAccrueService/accrueAttendanceRestService/queryMonth`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
// 人员查询
export function queryDetail(params) {
  return request(`/tdmsAccrueService/accrueAttendanceRestService/queryDetail`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
// / 场内任务--列表查询
export async function searchTaskList(params) {
  return request(
    `/tdmsAccrueService/accrueRestService/queryAccrueTaskWebByCondition`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}
// 场内人员(任务详情页)--合计总操作货量
export async function allAccrueWeightSum(params) {
  return request(`/tdmsAccrueService/accrueRestService/queryAccrueTaskSumWeb`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
