import React, { useState, useEffect, PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Input, Select, DatePicker } from 'antd';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
  useModuleCode,
  AuthButton,
  ExportButton,
} from 'ky-giant';

import { ExportOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';

import moment from 'moment';
import { cloneDeep } from 'lodash';
// import { limitCrossMonths } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch';
import AsyncExport from 'src/components/AsyncExport';
import { read } from './servers/api';
import {
  // operationTypeList,
  // operateTypeList,
  dayStatusList,
  userTypeList,
} from './Components/status';
import PersonDetail from './Components/PersonDetail';

import './index.scss';
const { Item: FormItem } = Form;
// const { RangePicker } = DatePicker;

let inputParam = {};
const Page = ({ userInfo, areaListSF, areaListSX, logRoleCode }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  // const [roleCode, setRoleCode] = useState(null); // 角色编码
  // const [datesLimit, setDatesLimit] = useState([]);
  const [editVisible, setEditVisible] = useState(false); // 编辑框显示
  const [initialValues, setInitialValues] = useState({}); // 初始值
  const [selectedRows, setSelectedRows] = useState([]);
  const [totalNum, setTotalNum] = useState(0);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  // const [totalNum, setTotalNum] = useState(0);

  const [loading, setLoading] = useState(false);
  const columns = [
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'zoneCode',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'allocationArea',
      render: value => {
        const areaList = areaListSF.concat(areaListSX);
        const date = areaList.find(item => item.value === value);
        return date ? date.label : value;
      },
    },
    {
      title: '工号',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'userNo',
    },
    {
      title: '姓名',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'userName',
    },
    {
      title: '岗位',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'empDutyName',
    },
    {
      title: '员工类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'userType',
      render: v => {
        const date = userTypeList.find(item => item.value === v);
        return date ? date.label : v;
      },
    },
    {
      title: '累计出勤时长（H）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'avgWorks',
      render: v => (v ? (v / 60).toFixed(1) : ''),
    },
    {
      title: '全场累计出勤工时（H）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'totalWorks',
      render: v => (v ? (v / 60).toFixed(1) : ''),
    },
    {
      title: '出勤工时占比',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'ratio',
    },
    {
      title: '全场累计卸车货量（T）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'totalWeight',
    },
    {
      title: '个人卸车货量（T）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'avgWeight',
      // render: v => v || 0,
    },
    {
      title: '单价',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'price',
    },
    {
      title: '出勤天数',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'attendanceNums',
    },
    {
      title: '累计计提',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'avgAccrue',
    },
    {
      title: '1号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_1',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '2号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_2',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '3号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_3',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '4号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_4',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '5号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_5',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '6号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_6',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '7号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_7',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '8号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_8',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '9号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_9',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '10号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_10',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '11号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_11',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '12号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_12',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '13号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_13',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '14号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_14',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '15号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_15',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '16号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_16',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '17号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_17',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '18号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_18',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '19号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_19',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '20号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_20',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '21号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_21',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '22号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_22',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '23号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_23',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '24号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_24',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '25号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_25',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '26号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_26',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '27号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_27',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '28号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_28',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '29号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_29',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '30号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_30',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },
    {
      title: '31号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'days_31',
      render: v => {
        const date = dayStatusList.find(item => item.value === Number(v));
        return date ? date.label : v;
      },
    },

    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          <AuthButton
            modulecode={moduleCode}
            code="transitPerson-check"
            size="small"
            type="link"
            onClick={e => detail(e, record)}
          >
            查看
          </AuthButton>
        </Fragment>
      ),
    },
  ];

  const detail = (e, row) => {
    // setAddModalVisible(true);
    e.stopPropagation();
    setInitialValues(row);
    setEditVisible(true);
  };

  const closeModal = () => {
    setEditVisible(false);
  };

  // const swichSelete = type => {
  //   switch (Number(type)) {
  //     case 0:
  //       return '正常出勤';
  //     case 1:
  //       return '加班';
  //     case 2:
  //       return '休息';
  //     case 3:
  //       return '旷工';
  //     case 4:
  //       return '请假';

  //     default:
  //       return '';
  //   }
  // };

  // 查询参数的获取
  const getQueryParams = () => {
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    inputParam.yearMonthQr = moment(inputParam.yearMonthQr).format('YYYY-MM');
    // delete inputParam.time;
    // refresh({
    //   currentPage: 1,
    //   pageSize: 10,
    // });
    return {
      ...inputParam,
    };
  };
  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      condition: inputParam,
      current,
      size: pageSize,
      total: totalNum,
    };
    read(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, total } = obj;
          const rowList = list.map(({ dayStatus, ...rest }) => ({
            ...JSON.parse(dayStatus),
            ...rest,
          }));
          // console.log(rowList, 'rowList');
          setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.pageNum,
              pageSize: obj.pageSize,
            },
            list: rowList,
          });
        } else {
          setLoading(false);
          setSelectedRows([]);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  useEffect(() => {
    // 当前登录的角色判断
    if (
      logRoleCode &&
      userInfo &&
      userInfo.deptCode
      // (logRoleCode.roleCode === 'tp00001' ||
      //   logRoleCode.roleCode === '88888888888')
    ) {
      form.setFieldsValue({
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        yearMonthQr: moment(),
      });
      getQueryParams();
      refresh({ currentPage: 1 });
    }
  }, [logRoleCode, userInfo]);

  const resetForm = () => {
    form.resetFields();
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        // time: [moment().startOf('month'), moment().endOf('month')],
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="网点"
                name="zoneCode"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, v) {
                      if (getFieldValue('allocationAreaCode') || v) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区和网点不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="分拨区"
                name="allocationAreaCode"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, v) {
                      if (getFieldValue('zoneCode') || v) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区和网点不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="用工类型" name="userType">
                <Select
                  allowClear
                  options={userTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="工号" name="userNo">
                <Input placeholder="请输入工号" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="供应商名称" name="supplierName">
                <Input placeholder="请输入供应商" allowClear />
              </FormItem>
            </Col>

            <Col {...colStyle}>
              <FormItem
                label="查询月份"
                name="yearMonthQr"
                rules={[
                  {
                    required: true,
                    message: '请选择月份',
                  },
                ]}
              >
                <DatePicker
                  picker="month"
                  allowClear
                  style={{ width: '100%' }}
                />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <ExportButton
            type="primary"
            text="导出所选"
            modulecode={moduleCode}
            style={{ marginRight: 15 }}
            code="transitPerson-exportSelect"
            icon={<ExportOutlined />}
            // disabled={!selectedRows.length}
            disabled={selectedRows.length < 1}
            options={{
              total: totalNum,
              filename: '场内人员列表.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueAttendanceRestService/exportMonth`,
                {
                  method: 'POST',
                  body: {
                    idList: selectedRows.map(({ id }) => id),
                  },
                },
              ],
            }}
          />
          <AsyncExport
            type="primary"
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            modulecode={moduleCode}
            code="transitPerson-exportAll"
            text="导出全部"
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueAttendanceRestService/asyncMonth',
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        showSelection
        onSelectRow={handleSelectRows}
        loading={loading}
        data={datas}
        columns={columns}
        selectedRows={selectedRows}
        onChange={handleStandardTableChange}
      />
      {editVisible ? (
        <PersonDetail
          initialValues={initialValues}
          areaCode={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
          visible={editVisible}
          handleCancel={closeModal}
          // roles={roles}
        ></PersonDetail>
      ) : null}
    </div>
  );
};

@connect(state => ({
  userInfo: state.global.userInfo,
  logRoleCode: state.global.logRoleCode,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const { userInfo, logRoleCode, areaListSX, areaListSF } = this.props;
    return (
      <Page
        userInfo={userInfo}
        logRoleCode={logRoleCode}
        areaListSX={areaListSX}
        areaListSF={areaListSF}
      />
    );
  }
}

export default withRouter(Container);
