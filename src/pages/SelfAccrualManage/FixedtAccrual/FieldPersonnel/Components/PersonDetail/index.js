import React, { useState, useEffect } from 'react';
import { Row, Col, Form, Input, Modal, Select, DatePicker } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  // colStyle,
  // rowStyle,
  SearchFold,
  useModuleCode,
  // AsyncExport,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
// import ExportButton from 'src/components/ExportButton';
import AsyncExport from 'src/components/AsyncExport';

// import { timeLimit } from 'src/utils/utils';
import { queryDetail } from '../../servers/api';
// import Edit from '../Edit';
import { userTypeList } from '../status';
const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;

let inputParam = {};
const PersonDetail = props => {
  const moduleCode = useModuleCode();

  const { visible, handleCancel, initialValues, areaCode } = props;
  const [form] = Form.useForm();
  // const moduleCode = useModuleCode();
  const [selectedRows, setSelectedRows] = useState([]);
  // const [editVisible, setEditVisible] = useState(false); // 编辑框显示
  // const [initialValue, setInitialValues] = useState(false); // 初始值
  // const [personalAccrueWeightSum, setPersonalAccrueWeightSum] = useState(false); // 合计总操作货量
  // const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '网点',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'zoneCode',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: value => {
        const date = areaCode.find(item => item.value === value);
        return date ? date.label : value;
      },
    },
    {
      title: '工号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userNo',
    },
    {
      title: '姓名',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userName',
    },
    {
      title: '员工类型',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userType',
      render: text => {
        switch (text) {
          case 1:
            return '自有';
          case 2:
            return '外包';
          default:
            return '';
        }
      },
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierName',
    },
    {
      title: '上班打卡',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'startWorkTime',
    },
    {
      title: '下班打卡',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'endWorkTime',
    },
    {
      title: '出勤工时（H）',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'avgWorks',
      render: v => (v ? (v / 60).toFixed(1) : ''),
    },
    {
      title: '出勤日期',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'taskSourceDate',
      render: v => (v ? moment(v).format('YYYY-MM-DD') : ''),
    },
  ];
  // 初始化  根据工号查询网点组织
  useEffect(() => {
    if (initialValues) {
      // console.log(initialValues, 'initialValues');
      form.setFieldsValue({
        time: [moment().startOf('month'), moment().endOf('month')],
        zoneCode: initialValues.zoneCode,
        allocationAreaCode: initialValues.allocationAreaCode,
        userType: initialValues.userType,
        userNo: initialValues.userNo,
        supplierName: initialValues.supplierName,
      });
    }
    getQueryParams();
    refresh({ currentPage: 1 });
  }, [initialValues]);

  // const update = (value, bll) => {
  //   value.endTime = inputParam.endTime;
  //   value.startTime = inputParam.startTime;
  //   value.isEdit = bll;
  //   // setInitialValues(value);
  //   // setEditVisible(true);
  // };

  // 关闭弹出层
  // const closeModal = () => {
  //   setEditVisible(false);
  // };

  // 查询参数的获取
  const getQueryParams = () => {
    // const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    if (params.time && params.time.length > 0) {
      inputParam.startTime = moment(params.time[0]).format('YYYY-MM-DD');
      inputParam.endTime = moment(params.time[1]).format('YYYY-MM-DD');
    }
    delete inputParam.time;
    // refresh({ pageNum, pageSize });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = { condition: inputParam, current, size: pageSize };

    setLoading(true);
    queryDetail(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, currentPage, total } = obj;
          // setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              currentPage,
              pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        time: [moment().startOf('month'), moment().endOf('month')],

        zoneCode: initialValues ? initialValues.zoneCode : undefined,
        allocationAreaCode: initialValues
          ? initialValues.allocationAreaCode
          : undefined,
        userType: initialValues ? initialValues.userType : undefined,
        userNo: initialValues ? initialValues.userNo : '',
        supplierName: initialValues ? initialValues.supplierName : '',
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row>
            <Col md={8}>
              <FormItem label="网点" name="zoneCode">
                <Input placeholder="请输入" disabled allowClear />
              </FormItem>
            </Col>
            <Col md={8}>
              <FormItem label="分拨区" name="allocationAreaCode">
                <Select
                  allowClear
                  disabled
                  options={areaCode}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col md={8}>
              <FormItem label="出勤日期" name="time">
                <RangePicker allowClear style={{ width: '100%' }} />
              </FormItem>
            </Col>
            <Col md={8}>
              <FormItem label="工号" name="userNo">
                <Input placeholder="请输入" disabled allowClear />
              </FormItem>
            </Col>
            <Col md={8}>
              <FormItem label="员工类型" name="userType">
                <Select
                  disabled
                  allowClear
                  options={userTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col md={8}>
              <FormItem label="供应商名称" name="supplierName">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          {/* <ExportButton
            text="导出"
            type="primary"
            modulecode={moduleCode}
            code="transitPerson-detail"
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            options={{
              total: totalNum,
              filename: '出勤详情列表.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueRestService/exportAccrueTaskWeb`,
                {
                  method: 'POST',
                  body: inputParam,
                },
              ],
            }}
          /> */}
          <AsyncExport
            type="primary"
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            modulecode={moduleCode}
            code="transitPerson-detailAll"
            handleCancel={handleCancel}
            text="导出全部"
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueAttendanceRestService/asyncDetail',
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
          {/* <span style={{ marginLeft: 100, fontSize: 18 }}>
            合计出勤工时 : {personalAccrueWeightSum || '--'}
          </span> */}
        </div>
      </div>
    </Form>
  );

  return (
    <Modal
      title="出勤详情"
      width={1300}
      visible={visible}
      // onOk={handleOk}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <div className="table-list">
        <div className="tableListForm">{renderForm()}</div>
        <StandardTable
          size="small"
          // rowKey="id"
          rowKey={(record, index) => index + record.operateUserNo}
          selectedRows={selectedRows}
          showSelection
          loading={loading}
          data={datas}
          columns={columns}
          onSelectRow={handleSelectRows}
          onChange={handleStandardTableChange}
        />
      </div>
    </Modal>
  );
};

export default connect(() => ({}))(props => (
  <PersonDetail {...props}></PersonDetail>
));
