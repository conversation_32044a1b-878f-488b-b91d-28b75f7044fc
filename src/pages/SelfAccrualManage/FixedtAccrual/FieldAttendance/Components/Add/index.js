import React, { useEffect } from 'react';
import { Form, Modal, Col, Row, Input, DatePicker, Select } from 'antd';
import moment from 'moment';
import { operateTypeList } from '../status';

import DynamicForm from '../DynamicForm';

const { Item: FormItem } = Form;
const Add = props => {
  const {
    // onOk,
    visible,
    initialValues,
    // id,
    ...rest
  } = props;
  const [form] = Form.useForm();
  // const [loading, setLoading] = useState(false);
  // const [timeId, setTimeId] = useState([]);
  useEffect(() => {
    if (initialValues) {
      initialValues.list = initialValues.list.map(item => {
        const time = [];
        time[0] = item.startWorkTime ? moment(item.startWorkTime) : undefined;
        time[1] = item.endWorkTime ? moment(item.endWorkTime) : undefined;
        delete item.startTime;
        delete item.endWorkTime;
        item.time = time;
        return item;
      });
      form.setFieldsValue({
        taskId: initialValues.taskId,
        flowId: initialValues.flowId,
        operateType: initialValues.operateType,
        startTime: initialValues.startTime
          ? moment(initialValues.startTime)
          : undefined,
        finishTime: initialValues.finishTime
          ? moment(initialValues.finishTime)
          : undefined,
        totalsWorks: initialValues.totalsWorks,
        teamNum: initialValues.teamNum,
        list: initialValues.list,
      });
    }
  }, [visible]);

  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
    initialValues: {
      list: [
        {
          shiftType: undefined, // 班次类型
          time: [], // 日期范围
        },
      ],
    },
  };

  const handleCancel = () => {
    form.resetFields();
  };

  return (
    <Modal
      title="查看"
      onCancel={handleCancel}
      visible={visible}
      width={1200}
      {...rest}
      footer={null}
      // footer={[
      //   <Button key="back" onClick={rest.onCancel}>
      //     取消
      //   </Button>,
      //   // <Button key="submit" loading={loading} type="primary" onClick={submit}>
      //   <Button key="submit" type="primary" onClick={submit}>
      //     确定
      //   </Button>,
      // ]}
    >
      <Form {...formProps}>
        <Row>
          <Col md={10} sm={24}>
            <FormItem label="任务号" name="taskId">
              <Input disabled placeholder="请输入" />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="子任务号" name="flowId">
              <Input disabled placeholder="请输入" />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="作业环节" name="operateType">
              <Select
                disabled
                allowClear
                options={operateTypeList}
                placeholder="请选择"
              ></Select>
            </FormItem>
          </Col>
          {/*  startTime 是记录这条数据在表的创建时间，startTime 是任务的创建时间 */}
          <Col md={10} sm={24}>
            <FormItem label="任务创建时间" name="startTime">
              <DatePicker
                disabled
                showTime
                style={{ width: '100%' }}
                disabledDate
              />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="任务完成时间" name="finishTime">
              <DatePicker
                disabled
                showTime
                style={{ width: '100%' }}
                disabledDate
              />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="任务总工时" name="totalsWorks">
              <Input disabled placeholder="请输入" addonAfter="H" />
            </FormItem>
          </Col>
          <Col md={10} sm={24}>
            <FormItem label="任务总人数" name="teamNum">
              <Input disabled placeholder="请输入" />
            </FormItem>
          </Col>
        </Row>
        <div className="styleMargin">
          <DynamicForm propsName="list" />
        </div>
      </Form>
    </Modal>
  );
};

export default Add;
