import React, { useState, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Input, Select, DatePicker } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  ExportButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import DeptSearch from 'src/components/DeptSearch';
import { getValueFromEvent } from 'src/utils/utils';
import { search, querySum } from './servers/api';
import Add from './Components/Add';
import { workTypeList } from './Components/status';

import './index.scss';
const { RangePicker } = DatePicker;
const { Item: FormItem } = Form;

let inputParam = {};
const Page = ({ userInfo, areaListSF, areaListSX, logRoleCode }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  // const [dates, setDates] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [totalNum, setTotalNum] = useState(0);
  const [avgWeight, setAvgWeight] = useState('合计出勤工时：0H');
  // const [avgWorks, setAvgWorks] = useState(0);
  const [dates, setDates] = useState([]);
  const [hackValue, setHackValue] = useState();
  const [value, setValue] = useState();
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '网点代码',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'zoneCode',
    },
    {
      title: '分拨区',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'allocationAreaCode',
      render: values => {
        const areaList = areaListSF.concat(areaListSX);
        const date = areaList.find(item => item.value === values);
        return date ? date.label : values;
      },
    },
    {
      title: '工号',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'userNo',
    },
    {
      title: '姓名',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'userName',
    },
    {
      title: '员工类型',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'userType',
      render: v => {
        const date = workTypeList.find(item => item.value === v);
        return date ? date.label : v;
      },
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'supplierName',
    },
    {
      title: '出勤日期',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'taskSourceDate',
      render: v => (v ? moment(v).format('YYYY-MM-DD') : ''),
    },
    {
      title: '上班打卡',
      align: 'center',
      width: 150,
      ellipsis: true,
      dataIndex: 'startWorkTime',
      // render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '下班打卡',
      align: 'center',
      width: 150,
      ellipsis: true,
      dataIndex: 'endWorkTime',
      // render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '出勤工时(H)',
      align: 'center',
      width: 150,
      ellipsis: true,
      dataIndex: 'avgWorks',
      render: v => (v ? (v / 60).toFixed(1) : ''),
    },
  ];

  const getQueryParams = () => {
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    if (inputParam.timeRange && inputParam.timeRange.length > 0) {
      inputParam.startTime = moment(inputParam.timeRange[0]).format(
        'YYYY-MM-DD',
      );
      inputParam.endTime = moment(inputParam.timeRange[1]).format('YYYY-MM-DD');
    }
    delete inputParam.time;
    delete inputParam.timeRange;
    // refresh({
    //   currentPage: 1,
    //   pageSize: 10,
    // });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      condition: inputParam,
      current,
      size: pageSize,
      total: totalNum,
    };

    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, total } = obj;
          setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.pageNum,
              pageSize: obj.pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
    querySum(param).then(res => {
      // if(res.success){

      // }
      if (res.obj !== null) {
        setAvgWeight(res.obj);
      } else {
        setAvgWeight(0);
      }
    });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  // useEffect(() => {
  //   if (userInfo.deptCode && userInfo.areaCode) {
  //     form.setFieldsValue({
  //       time: [moment().startOf('day'), moment().endOf('day')],
  //       zoneCode:
  //         logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
  //       allocationAreaCode:
  //         logRoleCode.roleCode === '88888888888'
  //           ? userInfo.areaCode
  //           : undefined,
  //     });
  //     getQueryParams();
  //   }
  // }, [userInfo]);

  const disabledDate = current => {
    if (!dates || dates.length === 0) {
      return false;
    }
    const tooLate = dates[0] && current.diff(dates[0], 'days') > 31;
    const tooEarly = dates[1] && dates[1].diff(current, 'days') > 31;
    return tooEarly || tooLate;
  };

  const onOpenChange = open => {
    if (open) {
      setHackValue([]);
      setDates([]);
    } else {
      setHackValue(undefined);
    }
  };

  useEffect(() => {
    // 当前登录的角色判断
    if (
      logRoleCode &&
      userInfo &&
      userInfo.deptCode &&
      (logRoleCode.roleCode === 'tp00001' ||
        logRoleCode.roleCode === '88888888888')
    ) {
      form.setFieldsValue({
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        time: [moment(), moment()],
      });
      getQueryParams();
      refresh({ currentPage: 1 });
    } else {
      form.setFieldsValue({
        time: [moment(), moment()],
      });
    }
  }, [logRoleCode, userInfo]);

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        timeRange: [moment(), moment()],
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationAreaCode:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="网点"
                name="zoneCode"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, v) {
                      if (getFieldValue('allocationAreaCode') || v) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区和网点不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="分拨区"
                name="allocationAreaCode"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, v) {
                      if (getFieldValue('zoneCode') || v) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区和网点不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  // disabled={logRoleCode.roleCode === '88888888888'}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>

            <Col {...colStyle}>
              <FormItem
                label="出勤日期"
                name="timeRange"
                rules={[
                  {
                    required: true,
                    message: '请填写出勤日期',
                  },
                ]}
              >
                <RangePicker
                  allowClear
                  style={{
                    width: '100%',
                  }}
                  value={hackValue || value}
                  disabledDate={disabledDate}
                  onCalendarChange={val => setDates(val)}
                  onChange={val => setValue(val)}
                  onOpenChange={onOpenChange}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="工号" name="userNo">
                <Input placeholder="请输入工号" allowClear />
              </FormItem>
            </Col>

            <Col {...colStyle}>
              <FormItem label="员工类型" name="userType">
                <Select
                  allowClear
                  options={workTypeList}
                  placeholder="请选择员工类型"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="供应商"
                name="supplierName"
                getValueFromEvent={getValueFromEvent}
              >
                <Input placeholder="请输入供应商名称" allowClear />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <ExportButton
            type="primary"
            text="导出所选"
            modulecode={moduleCode}
            style={{ marginRight: 15 }}
            code="fieldAttendance-exportSelect"
            icon={<ExportOutlined />}
            disabled={selectedRows.length < 1}
            options={{
              total: totalNum,
              filename: '场内出勤列表.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueAttendanceRestService/exportDetail`,
                {
                  method: 'POST',
                  body: {
                    idList: selectedRows.map(({ id }) => id),
                  },
                },
              ],
            }}
          />
          <AsyncExport
            type="primary"
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            modulecode={moduleCode}
            code="fieldAttendance-exportAll"
            text="导出全部"
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueAttendanceRestService/asyncDetail',
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
          <span
            style={{
              marginLeft: 100,
              fontSize: 18,
            }}
          >
            {`${avgWeight}` || ''}
          </span>
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        selectedRows={selectedRows}
        loading={loading}
        showSelection={false}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          initialValues={editingTarget}
          visible={addModalVisible}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          // onOk={() => {
          //   setAddModalVisible(false);
          //   setEditingTarget(null);
          //   refresh({ currentPage: 1 });
          // }}
        />
      )}
    </div>
  );
};
@connect(state => ({
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
  logRoleCode: state.global.logRoleCode,
}))
class Container extends PureComponent {
  render() {
    const { userInfo, areaListSX, areaListSF, logRoleCode } = this.props;
    return (
      <Page
        userInfo={userInfo}
        areaListSX={areaListSX}
        areaListSF={areaListSF}
        logRoleCode={logRoleCode}
      />
    );
  }
}

export default withRouter(Container);
