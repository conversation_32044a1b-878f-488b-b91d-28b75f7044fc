import request from 'src/utils/request';

// 查询
export function search(params) {
  return request(`/tdmsAccrueService/accrueAttendanceRestService/queryDetail`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
// 查询组员明细
// export function searchDetail(params) {
//   return request(`/restApi/fcamsForkliftServices/sorterForklift/queryDetail`, {
//     method: 'POST',
//     body: {
//       ...params,
//     },
//   });
// }
// 查询总操作货量
export function querySum(params) {
  return request(
    `/tdmsAccrueService/accrueAttendanceRestService/queryDetailSum`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}
