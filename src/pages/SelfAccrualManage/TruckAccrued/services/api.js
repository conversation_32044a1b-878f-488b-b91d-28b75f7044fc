import request from 'src/utils/request';

// 场内任务--列表查询
export async function searchTaskList(params) {
  return request(
    `/tdmsAccrueService/accrueRestService/queryAccrueTaskWebByCondition`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 场内任务--异步导出
export function taskExport(params) {
  return request(
    `/tdmsAccrueService/accrueRestService/asyncExportAccrueTaskWeb`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 场内任务--合计总操作货量
export function taskAllAccrueWeightSum(params) {
  return request(
    `/tdmsAccrueService/accrueRestService/queryAccrueTaskWebByConditionSum`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 场内任务--子任务号明细
export async function getTaskItem(params) {
  return request(`/tdmsAccrueService/itemQueryService/queryTaskItem`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 修改班组
export function editTeamUser(params) {
  return request(`/tdmsAccrueService/accrueRestService/editTeamUser`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 根据归属网点和组员工号查询供应商列表
export async function searchSupplierName(params) {
  return request(`/opbdsUPMService/opEmployee/queryOperateEmpList`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

/** ************************** */
// 场内人员--列表查询
export async function searchPersonList(params) {
  return request(`/tdmsAccrueService/accrueRestService/queryAccrueWeb`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 场内人员--异步导出
export async function personExport(params) {
  return request(`/tdmsAccrueService/accrueRestService/asyncExportAccrueWeb`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 场内人员--合计操作货量 && 合计计提
export async function personAllAccrueWeightSum(params) {
  return request(`/tdmsAccrueService/accrueRestService/queryUserAccrueSumWeb`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 场内人员(任务详情页)--合计总操作货量
export async function allAccrueWeightSum(params) {
  return request(`/tdmsAccrueService/accrueRestService/queryAccrueTaskSumWeb`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
