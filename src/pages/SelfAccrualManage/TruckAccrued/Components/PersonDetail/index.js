import React, { useState, useEffect, Fragment } from 'react';
import { Row, Col, Form, Input, Button, Modal, Select, DatePicker } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  rangePickerColStyle,
  rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
  // useModuleCode,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import ExportButton from 'src/components/ExportButton';
// import { timeLimit } from 'src/utils/utils';
import { searchTaskList, allAccrueWeightSum } from '../../services/api';
import Edit from '../Edit';
import { operateTypeList } from '../status';
const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;

let inputParam = {};
const PersonDetail = props => {
  const { visible, handleCancel, initialValues, areaCode } = props;
  const [form] = Form.useForm();
  // const moduleCode = useModuleCode();
  const [selectedRows, setSelectedRows] = useState([]);
  const [editVisible, setEditVisible] = useState(false); // 编辑框显示
  const [initialValue, setInitialValues] = useState(false); // 初始值
  const [personalAccrueWeightSum, setPersonalAccrueWeightSum] = useState(false); // 合计总操作货量
  const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '工号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'operateUserNo',
    },
    {
      title: '姓名',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userName',
    },
    {
      title: '员工类型',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'userType',
      render: text => {
        switch (text) {
          case 1:
            return '自有';
          case 2:
            return '外包';
          default:
            return '';
        }
      },
    },
    {
      title: '网点',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'zoneCode',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: value => {
        const date = areaCode.find(item => item.value === value);
        return date ? date.label : value;
      },
    },
    {
      title: '作业环节',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'operateType',
      render: value => {
        const date = operateTypeList.find(item => item.value === value);
        return date ? date.label : value;
      },
    },
    {
      title: '任务号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'taskId',
    },
    {
      title: '子任务号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'flowId',
    },
    {
      title: '交接单号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'relayBillId',
    },
    {
      title: '车标号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'logoNo',
    },
    {
      title: '月台号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'platformNo',
    },
    {
      title: '班组组长',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'teamLeaderNo',
    },
    {
      title: '任务总操作货量',
      align: 'center',
      ellipsis: true,
      width: 250,
      dataIndex: 'totalWeight',
    },
    {
      title: '个人操作货量',
      align: 'center',
      ellipsis: true,
      width: 250,
      dataIndex: 'avgWeightMultiNum',
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierName',
    },
    {
      title: '任务创建时间',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'startTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '任务完成时间',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'finishTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '任务归属时间',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'taskSourceDate',
    },
    {
      title: '任务创建人工号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'creator',
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'modifier',
    },
    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '操作',
      align: 'center',
      width: 120,
      fixed: 'right',
      render: (v, record) => (
        <Fragment>
          <Button size="small" type="link" onClick={() => update(record, true)}>
            查看
          </Button>
          {/* {(!timeLimit(12) &&
            moment(record.taskSourceDate).format('X') >=
              moment()
                .subtract(1, 'months')
                .startOf('month')
                .format('X')) ||
          (timeLimit(12) && roles) ||
          (timeLimit(12) &&
            !roles &&
            moment(record.taskSourceDate).format('x') >
              moment()
                .startOf('month')
                .format('x')) ||
          roles ? (
            <Button
              size="small"
              type="link"
              onClick={() => update(record, false)}
            >
              修改
            </Button>
          ) : null} */}
        </Fragment>
      ),
    },
  ];
  // 初始化  根据工号查询网点组织
  useEffect(() => {
    form.setFieldsValue({
      time: [moment().startOf('month'), moment().endOf('month')],
      zoneCode: initialValues.zoneCode,
      operateUserNo: initialValues.operateUserNo,
    });
    getQueryParams();
  }, []);

  const update = (value, bll) => {
    value.endTime = inputParam.endTime;
    value.startTime = inputParam.startTime;
    value.isEdit = bll;
    setInitialValues(value);
    setEditVisible(true);
  };

  // 关闭弹出层
  const closeModal = () => {
    setEditVisible(false);
  };

  // 查询参数的获取
  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    if (params.time.length > 0) {
      params.startTime = moment(params.time[0]).format('x');
      params.endTime = moment(params.time[1])
        .endOf('day')
        .format('x');
    }
    inputParam = cloneDeep(params);
    delete inputParam.time;
    refresh({ pageNum, pageSize });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = {
      ...inputParam,
      pageNum,
      pageSize,
      queryByBatch: false, // 默认查询班次 否
    };
    setLoading(true);
    searchTaskList(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, currentPage, total } = obj;
          setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              currentPage,
              pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
      });
    allAccrueWeightSum(param).then(res => {
      if (res.obj !== null) {
        setPersonalAccrueWeightSum(res.obj.personalAccrueWeightSum);
      }
    });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 是否按班次查询的判断&&查询条件切换
  // const getWorkDetail = value => {
  //   setIsWork(value);
  // };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组员工号" name="operateUserNo">
                <Input placeholder="请输入" disabled allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="作业环节" name="operateType">
                <Select
                  allowClear
                  options={operateTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationArea">
                <Select
                  allowClear
                  options={areaCode}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务号" name="taskId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="子任务号" name="flowId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="月台号" name="platformNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="交接单号" name="relayBillId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="车标号" name="logoNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组长工号" name="teamLeaderNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="创建人工号" name="creator">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...rangePickerColStyle}>
              <FormItem
                {...rangePickerLayout}
                label="开始时间"
                name="time"
                rules={[
                  {
                    required: true,
                    message: '请填写',
                  },
                ]}
              >
                <RangePicker showTime allowClear />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <ExportButton
            text="导出"
            icon={<ExportOutlined />}
            options={{
              total: totalNum,
              filename: '个人任务详情列表.xlsx',
              requstParams: [
                `/tdmsAccrueService/accrueRestService/exportAccrueTaskWeb`,
                {
                  method: 'POST',
                  body: inputParam,
                },
              ],
            }}
          />
          <span style={{ marginLeft: 100, fontSize: 18 }}>
            合计总操作货量 : {personalAccrueWeightSum || '--'}
          </span>
        </div>
      </div>
    </Form>
  );

  return (
    <Modal
      title="任务详情"
      width={1300}
      visible={visible}
      // onOk={handleOk}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <div className="table-list">
        <div className="tableListForm">{renderForm()}</div>
        <StandardTable
          size="small"
          // rowKey="id"
          rowKey={(record, index) => index + record.operateUserNo}
          selectedRows={selectedRows}
          showSelection={false}
          loading={loading}
          data={datas}
          columns={columns}
          onSelectRow={handleSelectRows}
          onChange={handleStandardTableChange}
        />
      </div>
      {editVisible ? (
        <Edit
          initialValue={initialValue}
          visible={editVisible}
          handleCancel={closeModal}
        ></Edit>
      ) : null}
    </Modal>
  );
};

export default connect(() => ({}))(props => (
  <PersonDetail {...props}></PersonDetail>
));
