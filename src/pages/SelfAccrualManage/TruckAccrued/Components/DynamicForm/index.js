import React, { useState, useEffect } from 'react';
import { Form, Input, Row, Col, Select, message } from 'antd';
import { cloneDeep } from 'lodash';
// import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import './index.scss';
import { error } from 'src/utils/utils';
import { searchSupplierName } from '../../services/api';

const formItemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    span: 20,
    offset: 4,
  },
};
let lists = [];

const DynamicForm = props => {
  const { label, zoneCode, form, listArr, isEdit } = props;
  const [timeId, setTimeId] = useState([]);
  const [supplierList, setSupplierList] = useState([]); // 所有组员的供应商信息

  // 根据组员工号查询 姓名 归属供应商list
  const getDetailInfor = (value, index) => {
    if (value !== '') {
      const accrueTeams = form.getFieldValue('accrueTeams');
      // 重复组员的判断
      const copyAccrueTeams = cloneDeep(accrueTeams);
      if (copyAccrueTeams[index].userName === undefined) {
        copyAccrueTeams.splice(index, 1);
        const a = copyAccrueTeams.some(item => value === item.operateUserNo);
        if (a) {
          accrueTeams[index].operateUserNo = '';
          message.warning('小组内以有此人，请勿重复添加');
          return;
        }
      }

      // 根据工号调接口查询供应商list
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          searchSupplierName({
            operateUserNo: value,
            deptCode: zoneCode,
            includeDelFlag: true,
          }).then(res => {
            if (res.success) {
              if (res.obj.length > 0) {
                // 是否自有员工 1 自有
                if (res.obj[0].innerFlag === 0) {
                  const arrLists = res.obj.map(item => ({
                    value: `${item.supplierName}-${item.supplierId}`,
                    label: item.supplierName,
                  }));
                  if (lists.length === index) {
                    lists.push(arrLists);
                  } else {
                    lists[index] = arrLists;
                  }
                  setSupplierList(lists);
                  accrueTeams[index].operateUserName = res.obj[0].empName;
                  accrueTeams[index].supplierUserNum = '1';
                  accrueTeams[index].userType = 2;
                  accrueTeams[index].supplierNo = '';
                  accrueTeams[index].supplierName = '';
                } else if (res.obj[0].innerFlag === 1) {
                  accrueTeams[index].operateUserName = res.obj[0].empName;
                  accrueTeams[index].supplierUserNum = '1';
                  accrueTeams[index].userType = 1;
                  accrueTeams[index].supplierNo = '';
                  accrueTeams[index].supplierName = null;
                  if (lists.length === index) {
                    lists.push([]);
                  } else {
                    lists[index] = [];
                  }

                  setSupplierList(lists);
                }
                form.setFieldsValue({ accrueTeams });
              } else {
                error('请检查工号是否输入正确');
              }
            } else {
              error('工号查询失败');
            }
          });
        }, 1000),
      );
    }
  };

  // 点击修改和查看时  获取对应工号的供应商名称list
  useEffect(() => {
    lists = []; // 记录员工的数量 长度
    // debugger;
    listArr.forEach((item, index) => {
      if (item.userType === 2) {
        searchSupplierName({
          operateUserNo: item.operateUserNo,
          deptCode: zoneCode,
          includeDelFlag: true,
        }).then(res => {
          const arrLists = res.obj.map(v => ({
            value: `${v.supplierName}-${v.supplierId}`,
            label: v.supplierName,
          }));
          lists[index] = arrLists;
          if (listArr.length === lists.length) {
            // 当员工数和用供应商list长度一致赋值
            setSupplierList(lists);
          }
        });
      } else {
        // 自有员工 占位
        lists[index] = [{}];
      }
    });
  }, [listArr]);

  return (
    <Form.List name="accrueTeams">
      {fields => (
        <div style={{ width: '800px' }}>
          {fields.map((field, index) => (
            <Form.Item
              key={field.key}
              className="style-Bottom"
              label={`${index === 0 ? label : ''}`}
              {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
            >
              <Row style={{ width: '100%' }}>
                <Col md={6} style={{ paddingRight: '10px' }}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'operateUserNo']}
                    fieldKey={[field.fieldKey, 'operateUserNo']}
                    key={`${field.fieldKey}operateUserNo`}
                  >
                    <Input
                      disabled={isEdit}
                      placeholder="请输入"
                      onChange={e => {
                        getDetailInfor(e.target.value, index);
                      }}
                      allowClear
                    />
                  </Form.Item>
                </Col>
                <Col md={5} style={{ paddingRight: '10px' }}>
                  <Form.Item
                    {...field}
                    name={[field.name, 'operateUserName']}
                    fieldKey={[field.fieldKey, 'operateUserName']}
                    key={`${field.fieldKey}operateUserName`}
                  >
                    <Input disabled />
                  </Form.Item>
                </Col>
                <Col md={10}>
                  <Form.Item noStyle shouldUpdate>
                    {() => (
                      <Form.Item
                        {...field}
                        name={[field.name, 'supplierName']}
                        fieldKey={[field.fieldKey, 'supplierName']}
                        key={`${field.fieldKey}supplierName`}
                      >
                        <Select
                          style={{
                            width: '100%',
                          }}
                          disabled={isEdit}
                          placeholder="归属供应商:无"
                          options={supplierList[index] || []}
                          allowClear
                        ></Select>
                      </Form.Item>
                    )}
                  </Form.Item>
                </Col>
                {/* {!isEdit ? (
                  <Col md={2} sm={24}>
                    <Form.Item {...field}>
                      <PlusOutlined
                        onClick={() => {
                          add();
                        }}
                      />
                    </Form.Item>
                  </Col>
                ) : null} */}
                {/* {!isEdit ? (
                  <Col md={1} sm={24}>
                    <Form.Item {...field}>
                      {fields.length > 1 ? (
                        <DeleteOutlined
                          className="dynamic-delete-button"
                          onClick={() => {
                            remove(field.name);
                            supplierList.splice(index, 1);
                          }}
                        />
                      ) : null}
                    </Form.Item>
                  </Col>
                ) : null} */}
              </Row>
            </Form.Item>
          ))}
        </div>
      )}
    </Form.List>
  );
};

export default DynamicForm;
