import React, { useEffect } from 'react';
import { InputNumber } from 'antd';

export default props => {
  const { value, diffValue, onChange, diffDisabled, ...restProps } = props;
  useEffect(() => {}, [value]);

  const handleChange = v => {
    onChange(v);
  };
  return (
    <InputNumber
      min={0}
      precision={1}
      formatter={v => String(v).split('.')[0]}
      onChange={handleChange}
      value={value}
      {...restProps}
    />
  );
};
