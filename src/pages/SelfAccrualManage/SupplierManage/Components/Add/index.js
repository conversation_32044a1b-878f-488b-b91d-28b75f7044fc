import React, { useState, useEffect } from 'react';
import { Form, Modal, Col, Row, Input, Button, DatePicker } from 'antd';
import moment from 'moment';
import { success, error, getValueFromEvent } from 'src/utils/utils';

import DeptSearch from 'src/components/DeptSearch';
import { add, update } from '../../servers/api';

const { Item: FormItem } = Form;
const Add = props => {
  const { onOk, visible, initialValues, userInfo, roleCode, ...rest } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 表单默认值
  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
    initialValues,
  };

  // 提交时请求
  const submit = async () => {
    setLoading(true);
    const param = await form.validateFields();
    param.effectTime = moment(param.effectTime).format('x');
    param.expireTime = moment(param.expireTime).format('x');
    const res = initialValues
      ? await update({ ...param, id: initialValues.id })
      : await add(param);
    if (res.success) {
      setLoading(false);
      success(`${initialValues ? '修改' : '新增'}成功！`);
      onOk();
    } else {
      error(res.errorMessage);
      setLoading(false);
    }
  };
  const handleCancel = () => {
    form.resetFields();
  };

  const supplierChange = e => {
    const { value } = e.target;
    form.setFieldsValue({ supplierNo: value.replace(/\s+/g, '') });
  };

  useEffect(() => {
    if (!initialValues && roleCode === 'tp00001') {
      form.setFieldsValue({ zoneCode: userInfo.deptCode });
    }
  }, [initialValues]);

  return (
    <Modal
      title={
        initialValues
          ? initialValues.type === 'check'
            ? '查看'
            : '编辑'
          : '新增'
      }
      onCancel={handleCancel}
      maskClosable={false}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={
        initialValues
          ? initialValues.type === 'check'
            ? null
            : [
                <Button key="back" onClick={rest.onCancel}>
                  取消
                </Button>,
                <Button
                  key="submit"
                  loading={loading}
                  type="primary"
                  onClick={submit}
                >
                  确定
                </Button>,
              ]
          : [
              <Button key="back" onClick={rest.onCancel}>
                取消
              </Button>,
              <Button
                key="submit"
                loading={loading}
                type="primary"
                onClick={submit}
              >
                确定
              </Button>,
            ]
      }
    >
      <Form {...formProps}>
        <Row>
          <Col md={18} sm={24}>
            <FormItem
              label="归属组织"
              name="zoneCode"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DeptSearch
                disabled={
                  (initialValues && initialValues.type === 'check') ||
                  roleCode === 'tp00001'
                }
                placeholder="请输入中转场名称或者代码"
                allowClear
              />
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem
              label="供应商名称"
              name="supplierName"
              getValueFromEvent={getValueFromEvent}
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Input
                disabled={initialValues && initialValues.type === 'check'}
                placeholder="请输入"
                allowClear
              />
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem
              label="供应商代码"
              name="supplierNo"
              getValueFromEvent={getValueFromEvent}
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Input
                // value={formatValue}
                onChange={supplierChange}
                disabled={initialValues && initialValues.type === 'check'}
                placeholder="请输入"
              />
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem
              label="生效时间"
              name="effectTime"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DatePicker
                showTime
                disabled={initialValues && initialValues.type === 'check'}
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem
              label="失效时间"
              name="expireTime"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DatePicker
                showTime
                disabled={initialValues && initialValues.type === 'check'}
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default Add;
