import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Input, DatePicker } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  AuthButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import { search } from './servers/api';
import Add from './Components/Add';

import './index.scss';
const { Item: FormItem } = Form;

let inputParam = {};
const Page = ({ userInfo, logRoleCode }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '归属组织',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'zoneCode',
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'supplierName',
    },
    {
      title: '供应商编码',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'supplierNo',
    },
    {
      title: '生效时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'effectTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '失效时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'expireTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '创建人',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'creator',
      render: text => text || '--',
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'modifier',
      render: text => text || '--',
    },
    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '操作',
      align: 'center',
      // width: 100,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          <AuthButton
            modulecode={moduleCode}
            code="selfSupplierInformation-check"
            size="small"
            type="link"
            onClick={e => update(e, record, 'check')}
          >
            查看
          </AuthButton>
          <AuthButton
            modulecode={moduleCode}
            code="selfSupplierInformation-edit"
            size="small"
            type="link"
            onClick={e => update(e, record)}
          >
            编辑
          </AuthButton>
        </Fragment>
      ),
    },
  ];

  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    inputParam.effectTime = inputParam.effectTime
      ? moment(inputParam.effectTime).format('x')
      : '';
    inputParam.expireTime = inputParam.expireTime
      ? moment(inputParam.expireTime).format('x')
      : '';
    refresh({ pageNum, pageSize });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = { ...inputParam, pageNum, pageSize };
    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { rows, currentPage, total } = obj;
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: currentPage,
              pageSize,
            },
            list: rows,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 新增
  const add = () => {
    setAddModalVisible(true);
  };

  // 修改
  const update = async (e, row, type) => {
    e.stopPropagation();
    row.type = type;
    row.effectTime = moment(row.effectTime);
    row.expireTime = moment(row.expireTime);
    setEditingTarget(row);
    setAddModalVisible(true);
  };

  useEffect(() => {
    if (userInfo.deptCode) {
      form.setFieldsValue({
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
      });
      if (logRoleCode.roleCode === 'tp00001') {
        getQueryParams();
        refresh();
      }
    }
  }, [userInfo, logRoleCode]);

  const resetForm = () => {
    form.resetFields();
    if (logRoleCode.roleCode === 'tp00001') {
      getQueryParams();
      refresh();
    }
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={{
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="网点编码"
                name="zoneCode"
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <DeptSearch
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  placeholder="请输入中转场名称或者代码"
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="供应商" name="supplierName">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="生效时间" name="effectTime">
                <DatePicker showTime allowClear style={{ width: '100%' }} />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="失效时间" name="expireTime">
                <DatePicker showTime allowClear style={{ width: '100%' }} />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AuthButton
            modulecode={moduleCode}
            code="selfSupplierInformation-add"
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => add()}
          >
            新增
          </AuthButton>
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        // rowKey="id"
        rowKey={(record, index) => record.effectTime + index}
        selectedRows={selectedRows}
        showSelection={false}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          initialValues={editingTarget}
          roleCode={logRoleCode.roleCode}
          userInfo={userInfo}
          visible={addModalVisible}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh();
          }}
          width={850}
        />
      )}
    </div>
  );
};
@connect(state => ({
  // systemRole: state.global.roles,
  userInfo: state.global.userInfo,
  logRoleCode: state.global.logRoleCode,
}))
class Container extends PureComponent {
  render() {
    const { userInfo, logRoleCode } = this.props;
    return <Page logRoleCode={logRoleCode} userInfo={userInfo} />;
  }
}

export default withRouter(Container);
