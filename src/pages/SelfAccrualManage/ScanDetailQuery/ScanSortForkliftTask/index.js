// 场内任务-叉车分拣
import React, { useState, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Input, Select, DatePicker, message } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  rangePickerColStyle,
  rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import DeptSearch from 'src/components/DeptSearch';
import { queryRepeatItem } from './servers/api';
import { workTypeList, sourceTypeList } from './status';
// import { success, error } from '@/utils/utils';

import './index.scss';
const { RangePicker } = DatePicker;
const { Item: FormItem } = Form;

let inputParam = {};
const Page = ({ userInfo, areaListSF, areaListSX, logRoleCode }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [selectedRows, setSelectedRows] = useState([]);
  // const [totalNum, setTotalNum] = useState(0);

  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  const columns = [
    {
      title: '所属公司',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'sourceNet',
    },
    {
      title: '网点',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'transitDepotNo',
      render: (value, row) => `${row.transitDepotNo} ${row.transitDepotName}`,
    },
    {
      title: '地区',
      align: 'center',
      width: 150,
      ellipsis: true,
      dataIndex: 'areaCode',
      render: (value, row) => `${row.areaCode} ${row.areaName}`,
    },
    {
      title: '日期',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'taskSourceDate',
      render: v => moment(v).format('YYYY-MM-DD'),
    },
    {
      title: '工号',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'operatorNo',
    },
    {
      title: '姓名',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'operatorName',
    },
    {
      title: '岗位',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'positionName',
    },
    {
      title: '员工类型',
      align: 'center',
      width: 100,
      ellipsis: true,
      dataIndex: 'userType',
      render: v => {
        const date = workTypeList.find(item => item.value === v);
        return date ? date.label : v;
      },
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'supplierName',
    },
    {
      title: '供应商编码',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierNo',
    },
    {
      title: '托盘号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'palletNo',
    },
    {
      title: '流向个数',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'flowNum',
    },
    {
      title: '托盘总重量',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'palletWeight',
    },
    {
      title: '运单号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'waybillNo',
    },
    {
      title: '下一站流向',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'nextStation',
    },
    {
      title: '操作时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'operateTm',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
  ];
  const [dates, setDates] = useState(null);
  const disabledDate = current => {
    if (!dates) {
      return false;
    }
    const tooLate = dates[0] && current.diff(dates[0], 'days') > 30;
    const tooEarly = dates[1] && dates[1].diff(current, 'days') > 30;
    return !!tooEarly || !!tooLate;
  };
  const [operationTime, setOperationTime] = useState(null);
  const disabledOperationTime = current => {
    if (!operationTime) {
      return false;
    }
    const tooLate =
      operationTime[0] && current.diff(operationTime[0], 'days') > 30;
    const tooEarly =
      operationTime[1] && operationTime[1].diff(current, 'days') > 30;
    return !!tooEarly || !!tooLate;
  };
  const getQueryParams = () => {
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);
    if (inputParam.taskSourceDate && inputParam.taskSourceDate.length > 0) {
      inputParam.taskSourceDateBegin = moment(
        inputParam.taskSourceDate[0],
      ).format('YYYY-MM-DD');
      inputParam.taskSourceDateEnd = moment(
        inputParam.taskSourceDate[1],
      ).format('YYYY-MM-DD');
    }
    if (inputParam.time && inputParam.time.length > 0) {
      inputParam.operateTmBegin = moment(inputParam.time[0]).valueOf();
      inputParam.operateTmEnd = moment(inputParam.time[1]).valueOf();
    }
    delete inputParam.time;
    delete inputParam.taskSourceDate;
    // refresh({ pageNum, pageSize, inputParam });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      currentPage: current = datas.pagination.current,
      pageSize = datas.pagination.pageSize,
    } = pagination;
    const param = {
      ...inputParam,
      pageNum: current,
      pageSize,
    };
    if (
      !param.taskSourceDateBegin &&
      !param.taskSourceDateEnd &&
      !param.operateTmBegin &&
      !param.operateTmEnd
    ) {
      message.error('日期和操作时间不能同时为空');
      setLoading(false);
      return false;
    }
    queryRepeatItem(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { rows: list, total } = obj;
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.currentPage,
              pageSize: obj.limit,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  useEffect(() => {
    if (logRoleCode && userInfo && userInfo.deptCode) {
      form.setFieldsValue({
        transitDepotNo: userInfo.deptCode || undefined,
        areaCode: userInfo.areaCode || undefined,
        // time: [moment().startOf('month'), moment().endOf('month')],
        // taskSourceDate: [moment().startOf('month'), moment().endOf('month')],
      });
      getQueryParams();
      refresh({ currentPage: 1 });
    } else {
      form.setFieldsValue({
        time: [moment().startOf('month'), moment().endOf('month')],
      });
    }
  }, [logRoleCode, userInfo]);

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={() => {
        getQueryParams();
        refresh({ currentPage: 1 });
      }}
      className="tableListForm"
      initialValues={{
        // time: [moment().startOf('day'), moment().endOf('day')],
        time: [moment().startOf('month'), moment().endOf('month')],
        taskSourceDate: [moment().startOf('month'), moment().endOf('month')],
        transitDepotNo: userInfo.deptCode || undefined,
        areaCode: userInfo.areaCode || undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="所属公司" name="sourceNet">
                <Select
                  options={sourceTypeList}
                  placeholder="请选择所属公司"
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="网点"
                name="transitDepotNo"
                rules={[
                  {
                    required: true,
                    message: '请选择',
                  },
                ]}
              >
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  // disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="地区" name="areaCode">
                <Select
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.label ?? '')
                      .toLowerCase()
                      .includes(input.toLowerCase())
                  }
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  // disabled={logRoleCode.roleCode === '88888888888'}
                  placeholder="请输入分拨区名称或代码"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="日期"
                name="taskSourceDate"
                rules={[
                  {
                    required: false,
                    message: '请选择',
                  },
                ]}
              >
                <RangePicker
                  allowClear
                  style={{
                    width: '100%',
                  }}
                  disabledDate={disabledDate}
                  onCalendarChange={val => setDates(val)}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="托盘号" name="palletNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="工号" name="operatorNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="运单号" name="waybillNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="下一站流向" name="nextStation">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...rangePickerColStyle}>
              <FormItem {...rangePickerLayout} label="操作时间" name="time">
                <RangePicker
                  showTime
                  allowClear
                  style={{ width: '100%' }}
                  disabledDate={disabledOperationTime}
                  onCalendarChange={val => setOperationTime(val)}
                />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con btn-space-between">
        <div>
          <AsyncExport
            text="导出"
            icon={<ExportOutlined />}
            disabled={datas.list && datas.list.length < 1}
            modulecode={moduleCode}
            code="scanSortForkliftTask-exportAll"
            options={{
              filename: '叉车细分托盘扫描明细.xlsx',
              requstParams: [
                `/tdmsAccrueService/itemQueryService/exportPalletDetail`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey={(record, index) => `${index}${record.id}`}
        selectedRows={selectedRows}
        loading={loading}
        showSelection
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
    </div>
  );
};
@connect(state => ({
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
  logRoleCode: state.global.logRoleCode,
}))
class Container extends PureComponent {
  render() {
    const { userInfo, areaListSX, areaListSF, logRoleCode } = this.props;
    return (
      <Page
        userInfo={userInfo}
        areaListSX={areaListSX}
        areaListSF={areaListSF}
        logRoleCode={logRoleCode}
      />
    );
  }
}

export default withRouter(Container);
