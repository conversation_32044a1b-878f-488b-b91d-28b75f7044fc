import request from '@/utils/request';

// 查询
export function search(params) {
  return request(`/tdmsAccrueService/volumeWarningConfigRestService/query`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 新增
export function add(params) {
  return request(`/tdmsAccrueService/volumeWarningConfigRestService/add`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}

// 修改
export function update(params) {
  return request(`/tdmsAccrueService/volumeWarningConfigRestService/update`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
