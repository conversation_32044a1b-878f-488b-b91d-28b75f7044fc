import React, { useState, useEffect } from 'react';
import {
  Form,
  Modal,
  Col,
  Row,
  Input,
  Button,
  DatePicker,
  Select,
  Radio,
  message,
  TimePicker,
} from 'antd';
import moment from 'moment';
import { cloneDeep } from 'lodash';

import { success, error } from 'src/utils/utils';
import {
  getEmpDeptBaseInfo,
  cityCodeListSearch,
  queryPlatformList,
  queryShiftCode,
  zoneCodeListSearch,
} from 'src/services/api';
import DeptSearch from 'src/components/DeptSearch';
import DebounceSelect from '../DebounceSelect';
import { add, update } from '../../servers/api';
import { sortTypeList } from '../status';
const { RangePicker } = DatePicker;

const { Item: FormItem } = Form;
const AddModal = props => {
  const {
    onOk,
    visible,
    initialValues,
    onCancel,
    userInfo,
    roleCode,
    areaListSF,
    ...rest
  } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState([]);
  const [codeValue, setCodeValue] = useState([]);
  const [platformNoList, setPlatformNoList] = useState([]); // 月台号List
  const [shiftNoList, setShiftNoList] = useState([]); // 班次号List

  // 表单默认值
  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
    // initialValues,
  };

  // 提交表单
  const submit = async () => {
    const param = await form.validateFields();
    const inputParam = cloneDeep(param);
    setLoading(true);
    inputParam.startTime = moment(inputParam.startTime).format('HH:mm'); // 班次开始时间;
    inputParam.endTime = moment(inputParam.endTime).format('HH:mm'); // 班次结束时间;
    inputParam.startWorkTime = moment(inputParam.timeRange[0]).valueOf(); // 生效时间;
    inputParam.endWorkTime = moment(inputParam.timeRange[1]).valueOf(); // 失效时间;
    inputParam.flowDirectionCz =
      inputParam.sortType === 1
        ? inputParam.flowDirectionCzCode.map(({ label }) => label).toString()
        : inputParam.flowDirectionCz.map(({ label }) => label).toString(); // 城市流向;
    inputParam.platformNo = inputParam.platformNo.toString(); // 月台号;
    delete inputParam.timeRange;
    delete inputParam.flowDirectionCzCode;
    try {
      const res = initialValues
        ? await update({
            ...inputParam,
            id: initialValues.id,
          })
        : await add(inputParam);
      if (res.success) {
        setLoading(false);
        success(`${initialValues ? '修改' : '新增'}成功！`);
        onCancel();
        onOk();
        // if (roleCode === 'tp00001' || roleCode === '88888888888') {
        //   onOk();
        // }
      } else {
        setLoading(false);
        error(res.errorMessage);
      }
    } catch {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const formatTime = v => {
    const str = moment().format('YYYY-MM-DD');
    const formatValue = moment(`${str} ${v}:00`).valueOf();
    return formatValue;
  };

  useEffect(() => {
    // 新增&&网点用户
    if (!initialValues && roleCode === 'tp00001' && userInfo.deptCode) {
      form.setFieldsValue({
        zoneCode: userInfo.deptCode,
        allocationAreaCode: userInfo.areaCode,
      });
      getPlatformList(userInfo.deptCode); // 拉取月台号 &&  拉取班次号
    }
    // 新增&&战区用户
    if (!initialValues && roleCode === '88888888888' && userInfo.areaCode) {
      form.setFieldsValue({
        allocationAreaCode: userInfo.areaCode,
      });
    }
    if (initialValues) {
      getPlatformList(initialValues.zoneCode); // 拉取月台号
      getName(initialValues.userNo); // 兼容旧数据,编辑时重新拉取姓名和公司归属
      form.setFieldsValue({
        id: initialValues.id,
        zoneCode: initialValues.zoneCode,
        userNo: initialValues.userNo,
        sourceType: initialValues.sourceType,
        userName: initialValues.userName,
        shiftName: initialValues.shiftName,
        allocationAreaCode: initialValues.allocationAreaCode,
        startTime: initialValues.startTime
          ? moment(formatTime(initialValues.startTime))
          : undefined,
        endTime: initialValues.endTime
          ? moment(formatTime(initialValues.endTime))
          : undefined,
        platformNo: initialValues.platformNo.split(','),
        sortType: initialValues.sortType,
        timeRange: [
          moment(initialValues.startWorkTime),
          moment(initialValues.endWorkTime),
        ],
        [swichSelete(
          initialValues.sortType,
        )]: initialValues.flowDirectionCz.split(',').map(item => ({
          label: item,
          value: item,
        })),
      });
    }
  }, [visible, userInfo]);

  const swichSelete = type => {
    switch (Number(type)) {
      case 1:
        return 'flowDirectionCzCode';
      case 2:
        return 'flowDirectionCz';
      default:
        return '';
    }
  };

  // 获取承包组长姓名
  const getName = key => {
    if (key) {
      getEmpDeptBaseInfo(key).then(res => {
        if (res.success && res.obj) {
          form.setFieldsValue({
            userName: res.obj.empName,
            sourceType: res.obj.orgCode,
          });
        }
      });
    }
  };

  const getItem = (v, option, datas) => {
    if (datas.length > 0) {
      const objList = datas.filter(item => v === item.value);
      if (objList.length > 0) {
        //  setDeptNameValue(objList[0].deptName);
        //  setAreaName(objList[0].areaName);
        // setAreaCode(objList[0].areaCode);
        //  setType(objList[0].orgCode);
        form.setFieldsValue({
          allocationAreaCode: objList[0].areaCode,
        });
        getPlatformList(v);
      }
    }
  };

  // 获取月台号List
  const getPlatformList = v => {
    form.setFieldsValue({
      platformNo: undefined,
      shiftName: undefined,
      startTime: undefined,
      endTime: undefined,
    });
    if (v) {
      queryPlatformList({
        deptCode: v,
        module: 2,
      }).then(res => {
        if (res.success) {
          if (res.obj.length > 0) {
            const list = res.obj.map((item, index) => ({
              label: item.platformNo,
              value: item.platformNo,
              key: index,
            }));
            setPlatformNoList(list);
          } else {
            form.setFieldsValue({
              platformNo: undefined,
            });
            setPlatformNoList([]);
            message.warning('月台号查询为空,请重新输入场地代码！');
          }
        }
      });
      queryShiftCode({
        deptCode: v,
      }).then(res => {
        if (res.success) {
          if (res.obj.length > 0) {
            const list = res.obj.map((item, index) => ({
              label: item.shiftNo,
              value: item.shiftNo,
              dayShift: item.dayShift,
              shiftNo: item.shiftNo,
              effectiveDate: item.effectiveDate,
              expireDate: item.expireDate,
              key: index,
            }));
            setShiftNoList(list);
          } else {
            form.setFieldsValue({
              platformNo: undefined,
            });
            setShiftNoList([]);
            message.warning('班次号查询为空,请重新输入场地代码！');
          }
        }
      });
    } else {
      setPlatformNoList([]);
      setShiftNoList([]);
    }
  };

  // 城市代码的批量查询
  const fetchUserList = async username => {
    const paramList = username.replace(/，/, ',').split(',');
    return cityCodeListSearch(paramList).then(body =>
      body.obj.map(user => ({
        label: user.citycode,
        value: user.citycode,
        key: user.citycode,
      })),
    );
  };

  // 城市代码的批量查询
  const fetchUserListCode = async username => {
    const paramList = username.replace(/，/, ',').split(',');
    return zoneCodeListSearch(paramList).then(body =>
      body.obj
        .filter(item => item.valid)
        .map(user => ({
          label: user.deptCode,
          value: user.deptCode,
          key: user.deptCode,
        })),
    );
  };

  const formatTimeHH = v => {
    // debugger;
    let a = [];
    const b = [];
    const str = moment().format('YYYY-MM-DD');
    a = v ? v.split('-') : v;
    if (a) {
      const b0 = moment(`${str} ${a[0]}:00`).valueOf();
      const b1 = moment(`${str} ${a[1]}:00`).valueOf();
      b.push(b0, b1);
    }
    return b;
  };

  const getShiftDetail = v => {
    if (v) {
      const filterValues = shiftNoList.filter(item => item.shiftNo === v);
      const timeHH = formatTimeHH(filterValues[0].dayShift);
      form.setFieldsValue({
        startTime: moment(timeHH[0]),
        endTime: moment(timeHH[1]),
        timeRange: [
          moment(filterValues[0].effectiveDate),
          moment(filterValues[0].expireDate),
        ],
      });
    } else {
      form.setFieldsValue({
        startTime: undefined,
        endTime: undefined,
      });
    }
  };

  return (
    <Modal
      title={initialValues ? '编辑' : '新增'}
      onCancel={handleCancel}
      onOk={submit}
      visible={visible}
      maskClosable={false}
      {...rest}
      footer={[
        <Button key="back" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" loading={loading} type="primary" onClick={submit}>
          确定
        </Button>,
      ]}
    >
      <Form {...formProps}>
        <Row>
          <Col md={18}>
            <FormItem label="id" name="id" hidden>
              <Input />
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem
              label="场地代码"
              name="zoneCode"
              rules={[
                {
                  required: true,
                  message: '请输入场地代码',
                },
              ]}
            >
              <DeptSearch
                onChange={getItem}
                disabled={roleCode === 'tp00001'}
                placeholder="请输入中转场名称或者代码"
                allowClear
              />
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem label="战区" name="allocationAreaCode">
              <Select
                allowClear
                disabled={roleCode === '88888888888' || roleCode === 'tp00001'}
                options={areaListSF}
                placeholder="请选择战区"
              ></Select>
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem
              label="承包组长工号"
              name="userNo"
              rules={[
                {
                  required: true,
                  message: '请输入工号',
                },
              ]}
            >
              <Input
                onBlur={e => getName(e.target.value)}
                placeholder="请输入工号"
                allowClear
              />
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem label="公司归属" name="sourceType" hidden>
              <Input disabled placeholder="根据工号自动带出" />
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem
              label="承包组长姓名"
              name="userName"
              rules={[
                {
                  required: true,
                  message: '根据工号自动带出',
                },
              ]}
            >
              <Input disabled placeholder="根据工号自动带出" />
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem
              label="承包班次编码"
              name="shiftName"
              rules={[
                {
                  required: true,
                  message: '请选择班次编码',
                },
              ]}
            >
              <Select
                allowClear
                onChange={getShiftDetail}
                options={shiftNoList}
                placeholder="请选择班次编号"
              />
            </FormItem>
          </Col>
          <Col md={18}>
            <Form.Item
              label="班次工作时间"
              style={{ marginBottom: 0 }}
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Form.Item
                name="startTime"
                style={{
                  display: 'inline-block',
                  width: 'calc(50% - 12px)',
                }}
                rules={[
                  {
                    required: true,
                    message: '请选择班次开始时间',
                  },
                ]}
              >
                <TimePicker
                  disabled
                  style={{ width: '100%' }}
                  allowClear
                  format="HH:mm"
                  placeholder="班次开始时间"
                />
              </Form.Item>
              <span
                style={{
                  display: 'inline-block',
                  width: '24px',
                  lineHeight: '32px',
                  textAlign: 'center',
                }}
              >
                -
              </span>
              <Form.Item
                name="endTime"
                style={{
                  display: 'inline-block',
                  width: 'calc(50% - 12px)',
                }}
                rules={[
                  {
                    required: true,
                    message: '由班次编码带出',
                  },
                ]}
              >
                <TimePicker
                  disabled
                  placeholder="班次结束时间"
                  style={{ width: '100%' }}
                  allowClear
                  format="HH:mm"
                />
              </Form.Item>
            </Form.Item>
          </Col>
          <Col md={18}>
            <FormItem
              label="承包月台"
              name="platformNo"
              rules={[
                {
                  required: true,
                  message: '请选择月台号',
                },
              ]}
            >
              <Select
                mode="multiple"
                options={platformNoList}
                placeholder="请选择月台号,可多选"
              />
            </FormItem>
          </Col>
          <Col md={18}>
            <FormItem
              label="分拣类型"
              name="sortType"
              rules={[
                {
                  required: true,
                  message: '请选择分拣类型',
                },
              ]}
            >
              <Radio.Group options={sortTypeList} />
            </FormItem>
          </Col>

          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.sortType !== curValues.sortType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [1].includes(getFieldValue('sortType')) ? (
                <Col md={18}>
                  <FormItem
                    label="负责网点"
                    name="flowDirectionCzCode"
                    rules={[
                      {
                        required: true,
                        message: '请输入网点代码',
                      },
                    ]}
                  >
                    {/* <DeptSearch placeholder="请输入网点代码" allowClear /> */}
                    <DebounceSelect
                      mode="multiple"
                      placeholder="请输入网点代码,可多选"
                      value={codeValue}
                      fetchOptions={fetchUserListCode}
                      onChange={newValue => {
                        setCodeValue(newValue);
                      }}
                      style={{
                        width: '100%',
                      }}
                    />
                  </FormItem>
                </Col>
              ) : (
                <Col md={18}>
                  <FormItem
                    label="负责流向"
                    name="flowDirectionCz"
                    rules={[
                      {
                        required: true,
                        message: '请输入城市代码',
                      },
                    ]}
                  >
                    <DebounceSelect
                      mode="multiple"
                      placeholder="请输入数字进行城市代码查询,可多选"
                      value={value}
                      fetchOptions={fetchUserList}
                      onChange={newValue => {
                        setValue(newValue);
                      }}
                      style={{
                        width: '100%',
                      }}
                    />
                  </FormItem>
                </Col>
              )
            }
          </Form.Item>
          <Col md={18}>
            <FormItem
              label="生效时间范围"
              name="timeRange"
              rules={[
                {
                  required: true,
                  message: '请选择生效时间范围',
                },
              ]}
            >
              <RangePicker
                showTime
                placeholder={['生效时间', '失效时间']}
                allowClear
                style={{ width: '100%' }}
                ranges={{
                  今天: [moment().startOf('day'), moment().endOf('day')],
                  本周: [
                    moment()
                      .weekday(0)
                      .startOf('day'),
                    moment()
                      .weekday(6)
                      .endOf('day'),
                  ],
                  本月: [moment().startOf('month'), moment().endOf('month')],
                }}
              />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default AddModal;
