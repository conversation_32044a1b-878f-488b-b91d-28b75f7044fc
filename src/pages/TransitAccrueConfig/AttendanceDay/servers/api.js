// import { stringify } from 'qs';
import request from '@/utils/request';

// 查询
export function queryAttendanceDays(params) {
  return request(
    `/tdmsAccrueService/attendanceDaysConfig/queryAttendanceDaysConfigByPage`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}
// 新增
export function addAttendanceDays(params) {
  return request(
    `/tdmsAccrueService/attendanceDaysConfig/addAttendanceDaysConfig`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}
// 修改
export function updateAttendanceDays(params) {
  return request(
    `/tdmsAccrueService/attendanceDaysConfig/updateAttendanceDaysConfig`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}
