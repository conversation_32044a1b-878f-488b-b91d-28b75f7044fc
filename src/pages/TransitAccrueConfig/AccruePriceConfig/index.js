import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Input, Button, Modal, Select } from 'antd';
import {
  // SearchOutlined,
  PlusOutlined,
  // RollbackOutlined,
  ExportOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
  // ImportButton,
} from 'ky-giant';
import { success, error } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import ImportButton from 'src/components/ImportButton';
import authDecorator from 'src/components/AuthDecorator';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import { deleteAccruePrice, search, searchDetail } from './servers/api';
import Add from './Components/Add';
import {
  accrueTypeList,
  statusList,
  employTypeList,
  forkliftTypeList,
  shiftTypeList,
} from './Components/status';
// import './style.less';

const { Item: FormItem } = Form;
const rowKey = 'id';

let condition = {};
const Page = ({ userInfo, logRoleCode }) => {
  const [form] = Form.useForm();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  const [sfVisible, setSfvisible] = useState(false);
  const [outVisible, setOutvisible] = useState(false);
  // const [roleCode, setRoleCode] = useState(null); // 角色编码
  const [intZoneCode, setIntZoneCode] = useState(null); // 网点代码
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  const [selectedRows, setSelectedRows] = useState([]);
  const [loading, setLoading] = useState(false);
  // 权限查询
  // const getCurrentRoleCode = () => {
  //   if (logRoleCode) {
  //     setRoleCode(logRoleCode.roleCode);
  //     setIntZoneCode(userInfo.deptCode);
  //     getQueryParams();
  //     refresh();
  //   }
  // };

  const columns = [
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'zoneCode',
    },
    {
      title: '网点名称',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'zoneName',
    },
    {
      title: '网点归属',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'orgid',
    },
    {
      title: '供应商名称',
      align: 'center',
      ellipsis: true,
      width: 200,
      dataIndex: 'supplierName',
      render: text => text || '',
    },
    {
      title: '供应商编码',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierCode',
      render: text => text || '',
    },
    {
      title: '用工类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'employType',
      render: value => {
        const data = employTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '计费类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueType',
      render: value => {
        const data = accrueTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '班次类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'shiftType',
      render: value => {
        const data = shiftTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '日单价',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'dayPrice',
      render: text => text || '--',
    },
    {
      title: '小时单价',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'hourPrice',
      render: text => text || '--',
    },
    {
      title: '装车单价',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueLoadPrice',
    },
    {
      title: '装车日保底',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accrueLoadDayPrice',
      render: text => text || '--',
    },
    {
      title: '卸车单价',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'accrueUnloadPrice',
      render: text => text || '--',
    },
    {
      title: '卸车日保底',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accrueUnloadDayPrice',
      render: text => text || '--',
    },
    {
      title: '装卸车月保底',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'accrueUnloadMonPrice',
      render: text => text || '--',
    },
    {
      title: '叉车单价',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'forkliftPrice',
      render: text => text || '--',
    },
    {
      title: '叉车日保底',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'forkliftDayPrice',
      render: text => text || '--',
    },
    {
      title: '叉车类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'forkliftType',
      render: value => {
        const data = forkliftTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '分拣单价',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'sortPrice',
      render: text => text || '--',
    },
    {
      title: '分拣日保底',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'sortDayPrice',
      render: text => text || '--',
    },
    {
      title: '状态',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'status',
      render: value => {
        const data = statusList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '生效时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'startTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '失效时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'endTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },

    {
      title: '操作人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'creator',
    },
    {
      title: '操作时间',
      align: 'center',
      ellipsis: true,
      width: 170,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      dataIndex: 'a',
      render: (v, record) => (
        <Fragment>
          <Button size="small" type="link" onClick={e => update(e, record)}>
            编辑
          </Button>
        </Fragment>
      ),
    },
  ];
  // const [columns, setColumns] = useState(columns);

  const getQueryParams = (pagination = {}) => {
    const { currentPage: current = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    condition = params;
    refresh({ current, pageSize });
    return {
      ...params,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const { currentPage: current = 1, pageSize = 10 } = pagination;
    const param = { condition, current, size: pageSize };
    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, pageNum, total } = obj;
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: pageNum,
              pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const add = () => {
    setAddModalVisible(true);
    setOutvisible(false);
  };

  const update = async (e, v) => {
    e.stopPropagation();
    const reult = await searchDetail({ id: v.id });
    setEditingTarget(reult.obj);
    setSfvisible(true);
    setOutvisible(reult.obj.employType);
    setAddModalVisible(true);
  };

  // 删除
  const remove = record => {
    const ids = [];
    if (record instanceof Array) {
      for (const item of record) {
        ids.push(item.id);
      }
    } else {
      ids.push(record.id);
    }
    Modal.confirm({
      title: '提示',
      content: '确定删除当前数据，删除后无法恢复',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const res = await deleteAccruePrice(ids, record.modifier);
        if (res.success) {
          success('删除成功');
          refresh();
        } else {
          error(res.errorMessage);
        }
      },
      onCancel() {},
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={{
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="网点编码" name="zoneCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="供应商编码" name="supplierCode">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="用工类型" name="employType">
                <Select
                  allowClear
                  options={employTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="生效状态" name="status">
                <Select
                  allowClear
                  options={statusList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <Button
            icon={<PlusOutlined />}
            style={{ marginRight: 15 }}
            type="primary"
            onClick={() => add()}
          >
            新增
          </Button>
          <Button
            icon={<DeleteOutlined />}
            disabled={!selectedRows.length}
            onClick={() => remove(selectedRows)}
            type="primary"
            style={{ marginRight: 15 }}
          >
            删除
          </Button>
          <AsyncExport
            text="导出"
            style={{ marginRight: 15 }}
            icon={<ExportOutlined />}
            // disabled={!selectedRows.length}
            disabled={datas.list && datas.list.length < 1}
            // moduleCode={moduleCode}
            // code="priceConfig-export"
            options={{
              filename: '计提单价配置.xlsx',
              requstParams: [
                `/tdmsAccrueService/accruePriceInfoService/asyncExportAccruePrice`,
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
          <ImportButton
            danger={false}
            style={{ marginRight: 15 }}
            type="primary"
            pagename="计提单价配置"
            // moduleCode={moduleCode}
            // code="priceConfig-import"
            title="导入"
            action="/tdmsAccrueService/accruePriceInfoService/import"
            modalUrl="/tdmsAccrueService/accruePriceInfoService/import/download/tmp"
            modalName="计提单价配置模板.xlsx"
            handleSyncImport={refresh}
          />
        </div>
      </div>
    </Form>
  );

  useEffect(() => {
    if (logRoleCode.roleCode === 'tp00001') {
      form.setFieldsValue({ zoneCode: userInfo.deptCode });
    }
    if (logRoleCode && userInfo) {
      // setRoleCode(logRoleCode.roleCode);
      setIntZoneCode(userInfo.deptCode);
    }
    getQueryParams();
    refresh();
  }, [userInfo, logRoleCode]);

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey={rowKey}
        selectedRows={selectedRows}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          initialValues={editingTarget}
          roleCode={logRoleCode.roleCode}
          intZoneCode={intZoneCode}
          visible={addModalVisible}
          sfvisible={sfVisible}
          outvisible={outVisible}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh();
          }}
          width={1000}
        />
      )}
    </div>
  );
};
@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
}))
@authDecorator
class Container extends PureComponent {
  render() {
    const { moduleCode, userInfo, logRoleCode } = this.props;
    return (
      <Page
        moduleCode={moduleCode}
        userInfo={userInfo}
        logRoleCode={logRoleCode}
      />
    );
  }
}

export default withRouter(Container);
