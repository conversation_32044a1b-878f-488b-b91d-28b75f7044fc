import React, { useState, useEffect } from 'react';
import {
  Form,
  Modal,
  Col,
  Row,
  Input,
  Select,
  DatePicker,
  Button,
  Radio,
} from 'antd';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { success, error } from 'utils/utils';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import { getDept } from 'src/services/api';
import { forkliftTypeList, shiftTypeList, employTypeList } from '../status';
import { add, update, supplierSearch } from '../../servers/api';
const { Item: FormItem } = Form;
// let overTime = false; // 是否超过当前月12号   true 未超过  false 超过

// 顺心装卸车字段list
const wordListSX = [
  { label: '顺心装车单价', name: 'accrueLoadSxPrice', value: 'priceRule' },
  { label: '顺心装车日保底', name: 'accrueLoadDaySxPrice', value: 'price' },
  { label: '顺心卸车单价', name: 'accrueUnloadSxPrice', value: 'priceRule' },
  { label: '顺心卸车日保底', name: 'accrueUnloadDaySxPrice', value: 'price' },
];

const uerId = sessionStorage.username; // 获取当前登录人工号
const Add = props => {
  const {
    onOk,
    visible,
    initialValues,
    intZoneCode,
    roleCode,
    sfvisible,
    outvisible,
    ...rest
  } = props;
  const [form] = Form.useForm();
  const [isOut, setIsOut] = useState(outvisible);
  const [loading, setLoading] = useState(false);
  const [arrList, setArrList] = useState([]);
  const [timeId, setTimeId] = useState([]);

  useEffect(() => {
    // 本月12号23点59分59秒
    // const time1 = moment()
    //   .date(12)
    //   .endOf('day')
    //   .format('x');
    // 当前时间
    // const time2 = new Date().getTime();
    // overTime = time1 < time2;
    if (initialValues === null && roleCode === 'tp00001') {
      searchCode(intZoneCode);
      form.setFieldsValue({ zoneCode: intZoneCode });
    }
  }, []);

  // const { getFieldValue } = form;

  // 表单初始值的处理
  const tem = initialValues ? cloneDeep(initialValues) : null;
  if (tem !== null) {
    tem.startTime = moment(tem.startTime);
    tem.endTime = moment(tem.endTime);
    tem.endTime = moment(tem.endTime);
    tem.calculationRules =
      tem.calculationRules === -1 ? undefined : tem.calculationRules;
  } else {
    // tem.differenceType = 0;
    form.setFieldsValue({ differenceType: 0 });
  }
  // 表单默认值
  const formProps = {
    form,
    labelCol: { md: 10 },
    wrapperCol: { md: 14 },
    initialValues: {
      ...tem,
    },
  };
  const getValue = value => {
    form.setFieldsValue({ accrueType: undefined });
    setIsOut(value); // 外包为true
  };

  const getSupplierCode = value => {
    form.setFieldsValue({ supplierCode: value });
  };

  const getItem = (v, _, datas) => {
    if (datas.length > 0) {
      const objList = datas.filter(item => v === item.value);
      if (objList.length > 0) {
        // console.log(objList[0]);
        // setDeptNameValue(objList[0].deptName);
        form.setFieldsValue({
          zoneName: objList[0].deptName,
          zoneCode: objList[0].deptCode,
          orgid: objList[0].orgCode,
        });
      }
    }
  };

  // 根据网点编码查询对应网名名称
  const searchCode = value => {
    if (value !== '') {
      const valueTransit = value.toUpperCase();
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          getDept(valueTransit).then(async res => {
            if (res.obj.list != null) {
              res.obj.list.forEach(v => {
                if (v.deptCode === valueTransit) {
                  form.setFieldsValue({
                    zoneName: v.deptName,
                    zoneCode: valueTransit,
                    orgid: v.orgCode,
                  });
                  // 获取供应商列表
                  supplierSearch({
                    zoneCode: valueTransit,
                  })
                    .then(ress => {
                      const arrListss = ress.obj.map(item => ({
                        value: item.supplierNo,
                        label: item.supplierName,
                      }));
                      setArrList(arrListss);
                    })
                    .catch(() => {});
                }
              });
              const arrNew = res.obj.list.filter(
                item => item.deptCode === valueTransit,
              );
              if (arrNew.length === 0) {
                error('请输入正确的网点代码');
                form.setFieldsValue({
                  zoneName: '',
                  orgid: '',
                  zoneCode: '',
                });
              }
            } else {
              error('网点查询失败');
              form.setFieldsValue({
                zoneName: '',
                orgid: '',
                zoneCode: '',
              });
            }
          });
        }, 1000),
      );
    } else {
      form.setFieldsValue({
        zoneName: '',
        orgid: '',
      });
    }
  };
  // 提交时请求
  const submit = async () => {
    const param = await form.validateFields();
    param.startTime = moment(param.startTime).valueOf();
    param.endTime = moment(param.endTime).valueOf();
    arrList.forEach(v => {
      if (v.value === param.supplierCode) {
        param.supplierName = v.label;
      }
    });
    if (tem !== null) {
      param.modifier = uerId;
      param.id = tem.id;
    } else {
      param.creator = uerId;
    }
    try {
      setLoading(true);
      const res = tem ? await update(param) : await add(param);
      if (res.success) {
        setLoading(false);
        success(`${tem ? '修改' : '新增'}成功！`);
        onOk();
      } else {
        error(res.errorMessage);
        setLoading(false);
      }
    } catch {
      setLoading(false);
    }
  };
  const handleCancel = () => {
    form.resetFields();
  };
  const priceRule = [
    { required: true, message: '请输入' },
    {
      pattern: /(^(([1-9]([0-9]+)?)|(0{1}))(\.[0-9]{1,2})?$)/,
      message: '请输入数字，最多保留两位小数',
    },
  ];
  const price1 = [
    {
      pattern: /^0\.[1-9]{0,2}$/,
      message: '请输入0-1之间的数字，最多保留两位小数',
    },
  ];
  const price = [
    {
      pattern: /(^(([1-9]([0-9]+)?)|(0{1}))(\.[0-9]{1,2})?$)/,
      message: '请输入数字，最多保留两位小数',
    },
  ];

  // 当前时间在本月12号24点后 只能设置本月1号0点后 overTime true
  // 当前时间在本月12号24点前 只能设置上个月1号0点后  overTime false
  // const disabledStartTime = current =>
  //   current &&
  //   current <
  //     (overTime
  //       ? moment().startOf('month')
  //       : moment()
  //           .subtract(1, 'months')
  //           .startOf('month'));

  //   失效时间的校验
  // const disabledInvalidDate = current =>
  //   current &&
  //   current <
  //     (form.getFieldValue('startTime')
  //       ? form.getFieldValue('startTime').startOf('day')
  //       : moment().subtract(0, 'day'));

  return (
    <Modal
      title={`${initialValues ? '编辑' : '新增'}`}
      maskClosable={false}
      onCancel={handleCancel}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={[
        <Button key="back" onClick={rest.onCancel}>
          取消
        </Button>,
        <Button key="submit" loading={loading} type="primary" onClick={submit}>
          确定
        </Button>,
      ]}
    >
      <Form {...formProps}>
        <Row gutter={{ md: 4 }}>
          <Col md={10}>
            <FormItem
              label="网点代码"
              name="zoneCode"
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: '请输入',
                },
              ]}
            >
              <DeptSearch
                allowClear
                disabled={tem || roleCode === 'tp00001'}
                placeholder="请输入"
                // onChange={e => {
                //   searchCode(e.target.value);
                // }}
                onChange={getItem}
              />
            </FormItem>
          </Col>
          <Col md={10}>
            <FormItem label="网点归属" name="orgid">
              <Input disabled placeholder="请输入" />
            </FormItem>
          </Col>
          <Col md={10}>
            <FormItem label="网点名称" name="zoneName" hidden>
              <Input disabled placeholder="请输入" />
            </FormItem>
          </Col>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Col md={10}>
            <FormItem
              label="用工类型"
              name="employType"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Select
                disabled={tem}
                options={employTypeList}
                placeholder="请选择"
                onChange={getValue}
                allowClear
              ></Select>
            </FormItem>
          </Col>
        </Row>

        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.employType !== curValues.employType
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [2].includes(getFieldValue('employType')) ? (
              <Row gutter={{ md: 4 }}>
                <Col md={10}>
                  <FormItem
                    label="供应商名称"
                    name="supplierName"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                  >
                    <Select
                      onChange={getSupplierCode}
                      disabled={tem}
                      placeholder="请选择供应商"
                      options={arrList}
                      allowClear
                    ></Select>
                  </FormItem>
                </Col>
                <Col md={10}>
                  <FormItem label="供应商编码" name="supplierCode">
                    <Input disabled placeholder="请输入" />
                  </FormItem>
                </Col>
              </Row>
            ) : null
          }
        </Form.Item>

        <Row gutter={{ md: 4 }}>
          <Col md={10}>
            <FormItem
              label="计费类型"
              name="accrueType"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Select
                disabled={tem}
                placeholder="请选择计费类型"
                options={[
                  { value: 1, label: '计费重量', key: 1 },
                  { value: 2, label: '实际重量', key: 2 },
                  { value: 3, label: '操作重量', key: 3 },
                  // { value: 4, label: '计件', key: 4 },
                  { value: 5, label: '计板', key: 5 },
                  {
                    value: 6,
                    label: '按天',
                    key: 6,
                    disabled: !isOut,
                  },
                  {
                    value: 7,
                    label: '按时',
                    key: 7,
                    disabled: !isOut,
                  },
                ]}
                allowClear
              ></Select>
            </FormItem>
          </Col>
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType ||
              prevValues.orgid !== curValues.orgid
            }
            noStyle
          >
            {({ getFieldValue }) =>
              ['SF'].includes(getFieldValue('orgid')) &&
              [1].includes(getFieldValue('employType')) &&
              [1].includes(getFieldValue('accrueType')) ? (
                <Col md={10}>
                  <FormItem
                    label="计提规则"
                    name="calculationRules"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                  >
                    <Select
                      // disabled={tem}
                      options={[
                        { value: 0, label: '岗位主工序扣保底', key: 0 },
                        { value: 1, label: '四个岗位交叉扣保底', key: 1 },
                        // {
                        //   value: -1,
                        //   label: '无计提配置规则',
                        //   key: -1,
                        //   // disabled: true,
                        // },
                      ]}
                      placeholder="请选择计提规则"
                      allowClear
                    ></Select>
                  </FormItem>
                </Col>
              ) : null
            }
          </Form.Item>
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.employType !== curValues.employType ||
              prevValues.accrueType !== curValues.accrueType ||
              prevValues.orgid !== curValues.orgid
            }
            noStyle
          >
            {({ getFieldValue }) =>
              ['SF'].includes(getFieldValue('orgid')) &&
              [1].includes(getFieldValue('employType')) &&
              [1].includes(getFieldValue('accrueType')) ? (
                <Col md={10}>
                  <FormItem
                    label="是否区分顺丰顺心单价"
                    name="differenceType"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                    // labelCol={{ md: 14 }}
                    // wrapperCol={{ md: 10 }}
                  >
                    <Radio.Group
                      options={[
                        { value: 0, label: '否', key: 0 },
                        { value: 1, label: '是', key: 1 },
                      ]}
                      placeholder="请选择"
                      allowClear
                    />
                  </FormItem>
                </Col>
              ) : null
            }
          </Form.Item>
        </Row>

        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.employType !== curValues.employType ||
            prevValues.accrueType !== curValues.accrueType
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [2].includes(getFieldValue('employType')) &&
            [6].includes(getFieldValue('accrueType')) ? (
              <Row gutter={{ md: 4 }}>
                <Col md={10}>
                  <FormItem
                    label="日单价"
                    name="dayPrice"
                    rules={[...priceRule]}
                  >
                    <Input placeholder="请输入" addonAfter="元" />
                  </FormItem>
                </Col>
                <Col md={10}>
                  <FormItem
                    label="装卸分摊系数"
                    name="sharingCoefficient"
                    rules={[...price1]}
                  >
                    <Input placeholder="请输入" allowClear />
                  </FormItem>
                </Col>
              </Row>
            ) : null
          }
        </Form.Item>

        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.employType !== curValues.employType ||
            prevValues.accrueType !== curValues.accrueType
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [2].includes(getFieldValue('employType')) &&
            [7].includes(getFieldValue('accrueType')) ? (
              <Row gutter={{ md: 4 }}>
                <Col md={10}>
                  <FormItem
                    label="小时单价"
                    name="hourPrice"
                    rules={[...priceRule]}
                  >
                    <Input placeholder="请输入" addonAfter="元" />
                  </FormItem>
                </Col>
                <Col md={10}>
                  <FormItem
                    label="装卸分摊系数"
                    name="sharingCoefficient"
                    rules={[...price1]}
                  >
                    <Input placeholder="请输入" allowClear />
                  </FormItem>
                </Col>
              </Row>
            ) : null
          }
        </Form.Item>

        <Row gutter={{ md: 4 }}>
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.accrueType !== curValues.accrueType ||
              prevValues.differenceType !== curValues.differenceType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [1, 2, 3].includes(getFieldValue('accrueType')) ? (
                <Col md={10}>
                  <FormItem
                    label={
                      getFieldValue('differenceType') === 1
                        ? '顺丰装车单价'
                        : '装车单价'
                    }
                    name="accrueLoadPrice"
                    rules={[...priceRule]}
                  >
                    <Input placeholder="请输入" addonAfter="元/T" />
                  </FormItem>
                </Col>
              ) : null
            }
          </Form.Item>

          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.orgid !== curValues.orgid ||
              prevValues.accrueType !== curValues.accrueType ||
              prevValues.differenceType !== curValues.differenceType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              ['SF'].includes(getFieldValue('orgid')) &&
              [1, 2, 3].includes(getFieldValue('accrueType')) ? (
                <Col md={10}>
                  <FormItem
                    label={
                      getFieldValue('differenceType') === 1
                        ? '顺丰装车日保底'
                        : '装车日保底'
                    }
                    name="accrueLoadDayPrice"
                    rules={[...price]}
                  >
                    <Input placeholder="请输入" addonAfter="T" />
                  </FormItem>
                </Col>
              ) : null
            }
          </Form.Item>
        </Row>
        <Row gutter={{ md: 4 }}>
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.accrueType !== curValues.accrueType ||
              prevValues.differenceType !== curValues.differenceType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              [1, 2, 3].includes(getFieldValue('accrueType')) ? (
                <Col md={10}>
                  <FormItem
                    label={
                      getFieldValue('differenceType') === 1
                        ? '顺丰卸车单价'
                        : '卸车单价'
                    }
                    name="accrueUnloadPrice"
                    rules={[...priceRule]}
                  >
                    <Input placeholder="请输入" addonAfter="元/T" />
                  </FormItem>
                </Col>
              ) : null
            }
          </Form.Item>
          <Form.Item
            shouldUpdate={(prevValues, curValues) =>
              prevValues.orgid !== curValues.orgid ||
              prevValues.accrueType !== curValues.accrueType ||
              prevValues.differenceType !== curValues.differenceType
            }
            noStyle
          >
            {({ getFieldValue }) =>
              ['SF'].includes(getFieldValue('orgid')) &&
              [1, 2, 3].includes(getFieldValue('accrueType')) ? (
                <Col md={10}>
                  <FormItem
                    label={
                      getFieldValue('differenceType') === 1
                        ? '顺丰卸车日保底'
                        : '卸车日保底'
                    }
                    name="accrueUnloadDayPrice"
                    rules={[...price]}
                  >
                    <Input placeholder="请输入" addonAfter="T" />
                  </FormItem>
                </Col>
              ) : null
            }
          </Form.Item>
        </Row>

        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.differenceType !== curValues.differenceType
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [1].includes(getFieldValue('differenceType')) ? (
              <Row gutter={{ md: 4 }}>
                {wordListSX.map(item => (
                  <Col md={10}>
                    <FormItem
                      label={item.label}
                      name={item.name}
                      rules={
                        item.value === 'priceRule' ? [...priceRule] : [...price]
                      }
                    >
                      <Input
                        placeholder="请输入"
                        addonAfter={item.value === 'priceRule' ? '元/T' : 'T'}
                      />
                    </FormItem>
                  </Col>
                ))}
              </Row>
            ) : null
          }
        </Form.Item>

        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.accrueType !== curValues.accrueType
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [1, 2, 3, 5].includes(getFieldValue('accrueType')) ? (
              <Row gutter={{ md: 4 }}>
                <Col md={10}>
                  <FormItem
                    label="叉车单价"
                    name="forkliftPrice"
                    rules={[...priceRule]}
                  >
                    <Input placeholder="请输入" addonAfter="元/T" />
                  </FormItem>
                </Col>
                <Col md={10}>
                  <FormItem
                    label="叉车日保底"
                    name="forkliftDayPrice"
                    rules={[...price]}
                  >
                    <Input placeholder="请输入" addonAfter="T" />
                  </FormItem>
                </Col>
              </Row>
            ) : null
          }
        </Form.Item>

        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.employType !== curValues.employType ||
            prevValues.orgid !== curValues.orgid ||
            prevValues.accrueType !== curValues.accrueType
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [1].includes(getFieldValue('employType')) &&
            ['SF'].includes(getFieldValue('orgid')) &&
            [1].includes(getFieldValue('accrueType')) && (
              <Row gutter={{ md: 4 }}>
                <Col md={10}>
                  <FormItem
                    label="分拣单价"
                    name="sortPrice"
                    rules={[...priceRule]}
                  >
                    <Input placeholder="请输入" addonAfter="元/T" />
                  </FormItem>
                </Col>
                <Col md={10}>
                  <FormItem
                    label="分拣日保底"
                    name="sortDayPrice"
                    rules={[...price]}
                  >
                    <Input placeholder="请输入" addonAfter="T" />
                  </FormItem>
                </Col>
              </Row>
            )
          }
        </Form.Item>
        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.orgid !== curValues.orgid ||
            prevValues.accrueType !== curValues.accrueType
          }
          noStyle
        >
          {({ getFieldValue }) =>
            ['SX'].includes(getFieldValue('orgid')) &&
            [1, 2, 3].includes(getFieldValue('accrueType')) ? (
              <Row gutter={{ md: 4 }}>
                <Col md={10}>
                  <FormItem
                    label="装卸车月保底"
                    name="accrueUnloadMonPrice"
                    rules={[...price]}
                  >
                    <Input placeholder="请输入" addonAfter="T" />
                  </FormItem>
                </Col>
              </Row>
            ) : null
          }
        </Form.Item>

        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.accrueType !== curValues.accrueType
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [5].includes(getFieldValue('accrueType')) ? (
              <Row gutter={{ md: 4 }}>
                <Col md={10}>
                  <FormItem
                    label="叉车类型"
                    name="forkliftType"
                    rules={[
                      {
                        required: true,
                        message: '请选择',
                      },
                    ]}
                  >
                    <Select
                      disabled={tem}
                      options={forkliftTypeList}
                      placeholder="请选择"
                      allowClear
                    ></Select>
                  </FormItem>
                </Col>
              </Row>
            ) : null
          }
        </Form.Item>

        <Form.Item
          shouldUpdate={(prevValues, curValues) =>
            prevValues.employType !== curValues.employType
          }
          noStyle
        >
          {({ getFieldValue }) =>
            [1].includes(getFieldValue('employType')) ? (
              <Row gutter={{ md: 4 }}>
                <Col md={10}>
                  <FormItem
                    label="班次类型"
                    name="shiftType"
                    rules={[
                      {
                        required: true,
                        message: '请选择班次类型',
                      },
                    ]}
                  >
                    <Select
                      options={shiftTypeList}
                      placeholder="请选择班次类型"
                      allowClear
                    ></Select>
                  </FormItem>
                </Col>
              </Row>
            ) : null
          }
        </Form.Item>
        <Row gutter={{ md: 4 }}>
          <Col md={10}>
            <FormItem
              label="生效时间"
              name="startTime"
              rules={[
                {
                  required: true,
                  message: '请选择生效时间',
                },
              ]}
            >
              <DatePicker
                showTime
                style={{ width: '100%' }}
                // disabledDate={disabledStartTime}
              />
            </FormItem>
          </Col>
          <Col md={10}>
            <FormItem
              label="失效时间"
              name="endTime"
              rules={[
                {
                  required: true,
                  message: '请选择失效时间',
                },
              ]}
            >
              <DatePicker
                showTime
                style={{ width: '100%' }}
                // disabledDate={disabledInvalidDate}
              />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default Add;
