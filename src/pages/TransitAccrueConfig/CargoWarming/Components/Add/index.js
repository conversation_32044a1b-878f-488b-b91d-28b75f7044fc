import React, { useState, useEffect } from 'react';
import { Form, Modal, Col, Select, Row, Input, Button, DatePicker } from 'antd';
import moment from 'moment';
import { success, error } from 'src/utils/utils';
import DeptSearch from 'src/components/DeptSearch';
import { add, update } from '../../servers/api';
import { operateTypeList } from '../status';

const { Item: FormItem } = Form;
const Add = props => {
  const {
    onOk,
    visible,
    initialValues,
    // areaListSF,
    // areaListSX,
    userInfo,
    roleCode,
    ...rest
  } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [disabledType, setDisabledType] = useState(false);
  const [numType, setNumType] = useState(1); // 计费类型

  const dayValueRules = [
    {
      pattern: /(^(([1-9]([0-9]+)?)|(0{1}))(\.[0-9]{1,2})?$)/,
      message: '请输入数字，最多保留两位小数',
    },
  ];
  // 表单默认值
  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
    // initialValues,
  };

  // 提交时请求
  const submit = async () => {
    setLoading(true);
    const param = await form.validateFields();
    param.startTime = moment(param.startTime).format('x');
    param.endTime = moment(param.endTime).format('x');
    setLoading(false);
    const res = initialValues
      ? await update({ ...param, id: initialValues.id })
      : await add(param);
    if (res.success) {
      success(`${initialValues ? '修改' : '新增'}成功！`);
      onOk();
    } else {
      error(res.errorMessage);
    }
  };
  const handleCancel = () => {
    form.resetFields();
  };

  const getTypeValue = value => {
    if (value !== 4) {
      setDisabledType(true);
      form.setFieldsValue({ accrueType: 1 });
    } else {
      setDisabledType(false);
      form.setFieldsValue({ accrueType: undefined });
    }
  };

  const getAccrueTypeValue = value => {
    setNumType(value);
  };

  useEffect(() => {
    if (!initialValues && roleCode === 'tp00001') {
      form.setFieldsValue({ zoneCode: userInfo.deptCode });
    }
    if (initialValues) {
      form.setFieldsValue({
        zoneCode: initialValues.zoneCode,
        operateType: initialValues.operateType,
        accrueType: initialValues.accrueType,
        dayMaxValue: initialValues.dayMaxValue,
        dayMinValue: initialValues.dayMinValue,
        startTime: initialValues.startTime
          ? moment(initialValues.startTime)
          : undefined,
        endTime: initialValues.endTime
          ? moment(initialValues.endTime)
          : undefined,
      });
    }
  }, [visible]);

  return (
    <Modal
      title={initialValues ? '编辑' : '新增'}
      onCancel={handleCancel}
      maskClosable={false}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={[
        <Button key="back" onClick={rest.onCancel}>
          取消
        </Button>,
        <Button key="submit" loading={loading} type="primary" onClick={submit}>
          确定
        </Button>,
      ]}
    >
      <Form {...formProps}>
        <Row>
          <Col md={18} sm={24}>
            <FormItem
              label="网点"
              name="zoneCode"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DeptSearch
                disabled={
                  (initialValues && initialValues.type === 'check') ||
                  roleCode === 'tp00001'
                }
                placeholder="请输入中转场名称或者代码"
                allowClear
              />
            </FormItem>
          </Col>
          {/* <Col md={18} sm={24}>
            <FormItem label="战区" name="allocationArea">
              <Select
                allowClear
                options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                placeholder="请选择"
              ></Select>
            </FormItem>
          </Col> */}
          <Col md={18} sm={24}>
            <FormItem label="作业环节" name="operateType">
              <Select
                allowClear
                options={operateTypeList}
                placeholder="请选择"
                onChange={getTypeValue}
              ></Select>
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem label="计费类型" name="accrueType">
              <Select
                allowClear
                // options={accrueTypeList}
                options={[
                  { value: 1, label: '计费重量', key: 1 },
                  {
                    value: 2,
                    label: '计板',
                    key: 2,
                    disabled: disabledType,
                  },
                ]}
                onChange={getAccrueTypeValue}
                placeholder="请选择"
              ></Select>
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem
              label="日最高值"
              name="dayMaxValue"
              rules={[...dayValueRules]}
            >
              <Input
                placeholder="请输入"
                addonAfter={numType === 1 ? 'T' : '板'}
              />
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem
              label="日最低值"
              name="dayMinValue"
              rules={[...dayValueRules]}
            >
              <Input
                placeholder="请输入"
                addonAfter={numType === 1 ? 'T' : '板'}
              />
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem
              label="生效时间"
              name="startTime"
              // rules={[
              //   {
              //     required: true,
              //     message: '请选择',
              //   },
              // ]}
            >
              <DatePicker
                showTime
                disabled={initialValues && initialValues.type === 'check'}
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
          <Col md={18} sm={24}>
            <FormItem
              label="失效时间"
              name="endTime"
              // rules={[
              //   {
              //     required: true,
              //     message: '请选择',
              //   },
              // ]}
            >
              <DatePicker
                showTime
                disabled={initialValues && initialValues.type === 'check'}
                allowClear
                style={{ width: '100%' }}
              />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default Add;
