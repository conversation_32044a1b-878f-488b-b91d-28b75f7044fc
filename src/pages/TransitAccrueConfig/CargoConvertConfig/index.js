import React, { useState, Fragment, useEffect, PureComponent } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Select, DatePicker } from 'antd';
import {
  // SearchOutlined,
  PlusOutlined,
  // RollbackOutlined,
  // ExportOutlined,
} from '@ant-design/icons';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  AuthButton,
  SearchFold,
  useModuleCode,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询

import { search } from './servers/api';
import Add from './Components/Add';
import './index.scss';
const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;

let inputParam = {};
const Page = ({ userInfo, logRoleCode, areaListSF, areaListSX }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [editingTarget, setEditingTarget] = useState(null); // 一行的数据
  const [selectedRows, setSelectedRows] = useState([]);
  // const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '归属组织',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'sourceType',
    },
    {
      title: '网点代码',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'zoneCode',
    },
    {
      title: '网点名称',
      align: 'center',
      ellipsis: true,
      // width: 120,
      dataIndex: 'zoneName',
    },
    {
      title: '战区代码',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'allocationAreaCode',
    },
    {
      title: '战区名称',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'allocationArea',
    },
    {
      title: '折算系数',
      align: 'center',
      ellipsis: true,
      // width: 80,
      dataIndex: 'coefficient',
    },
    {
      title: '生效时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'startTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '失效时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'endTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '创建人',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'creator',
    },
    {
      title: '创建时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'createTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      // width: 100,
      dataIndex: 'modifier',
      render: text => text || '',
    },

    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      // width: 180,
      dataIndex: 'modifyTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '操作',
      align: 'center',
      // width: 100,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          <AuthButton
            modulecode={moduleCode}
            code="cargoConvertConfig-edit"
            size="small"
            type="link"
            onClick={e => update(e, record)}
          >
            编辑
          </AuthButton>
        </Fragment>
      ),
    },
  ];

  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    inputParam = cloneDeep(params);

    if (inputParam.time && inputParam.time.length > 0) {
      inputParam.startTime = moment(inputParam.time[0]).format('x');
      inputParam.endTime = moment(inputParam.time[1]).format('x');
    }
    delete inputParam.time;
    refresh({ pageNum, pageSize });
    return {
      ...inputParam,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = {
      ...inputParam,
      pageNum,
      pageSize,
    };
    search(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { list, currentPage, total } = obj;
          // setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: currentPage,
              pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setSelectedRows([]);
        setLoading(false);
      });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  // 新增
  const add = () => {
    setAddModalVisible(true);
  };

  // 修改
  const update = async (e, row, type) => {
    e.stopPropagation();
    row.type = type;
    row.effectTime = moment(row.effectTime);
    row.expireTime = moment(row.expireTime);
    setEditingTarget(row);
    setAddModalVisible(true);
  };

  useEffect(() => {
    form.setFieldsValue({
      zoneCode:
        logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
    });
    getQueryParams();
    refresh();
  }, [userInfo, logRoleCode]);

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={
        {
          // zoneCode: userInfo.deptCode,
        }
      }
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="网点编码"
                name="zoneCode"
                // rules={[
                //   {
                //     required: true,
                //     message: '请选择',
                //   },
                // ]}
              >
                <DeptSearch
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  placeholder="请输入中转场名称或者代码"
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="战区" name="allocationAreaCode">
                <Select
                  allowClear
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="生效时间" name="time">
                <RangePicker
                  allowClear
                  style={{
                    width: '100%',
                  }}
                  ranges={{
                    今天: [moment().startOf('day'), moment().endOf('day')],
                    本周: [
                      moment()
                        .weekday(0)
                        .startOf('day'),
                      moment()
                        .weekday(6)
                        .endOf('day'),
                    ],
                    本月: [moment().startOf('month'), moment().endOf('month')],
                  }}
                />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AuthButton
            modulecode={moduleCode}
            code="cargoConvertConfig-add"
            icon={<PlusOutlined />}
            type="primary"
            onClick={() => add()}
          >
            新增
          </AuthButton>
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey="id"
        selectedRows={selectedRows}
        showSelection={false}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {addModalVisible && (
        <Add
          initialValues={editingTarget}
          roleCode={logRoleCode.roleCode}
          userInfo={userInfo}
          visible={addModalVisible}
          onCancel={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
          }}
          onOk={() => {
            setAddModalVisible(false);
            setEditingTarget(null);
            refresh();
          }}
          width={850}
        />
      )}
    </div>
  );
};
@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const { logRoleCode, userInfo, areaListSF, areaListSX } = this.props;
    return (
      <Page
        logRoleCode={logRoleCode}
        userInfo={userInfo}
        areaListSF={areaListSF}
        areaListSX={areaListSX}
      />
    );
  }
}

export default withRouter(Container);
