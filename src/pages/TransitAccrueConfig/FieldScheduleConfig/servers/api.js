import request from '@/utils/request';

// 批量删除
export function deleteSchedulingConfig(params) {
  return request(
    `/tdmsAccrueService/accrueSchedulingConfigRest/deleteSchedulingConfig`,
    {
      method: 'POST',
      body: params,
    },
  );
}
// 查询
export function search(params) {
  return request(`/tdmsAccrueService/accrueSchedulingConfigRest/query`, {
    method: 'POST',
    body: {
      ...params,
    },
  });
}
// 根据ID查询单价详情（编辑）
export function searchDetail(params) {
  return request(
    `/tdmsAccrueService/accrueSchedulingConfigRest/queryByCondition`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 添加计提单价配置
export function add(params) {
  return request(
    `/tdmsAccrueService/accrueSchedulingConfigRest/addSchedulingConfig`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

// 更新计提单价配置
export function update(params) {
  return request(
    `/tdmsAccrueService/accrueSchedulingConfigRest/updateSchedulingConfig`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}

/**
 * @description: 根据网点查询供应商列表
 * @param {type} orgCode
 * @return:
 */
export function supplierSearch(params) {
  return request(
    `/tdmsAccrueService/supplierRestService/querySupplierByZoneCode`,
    {
      method: 'POST',
      body: {
        ...params,
      },
    },
  );
}
