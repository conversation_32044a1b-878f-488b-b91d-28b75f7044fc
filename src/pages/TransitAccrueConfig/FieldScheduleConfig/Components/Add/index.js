import React, { useState, useEffect } from 'react';
import { Form, Modal, Col, Row, Input, Button } from 'antd';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { success, error } from 'src/utils/utils';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import { getEmpDeptDetailInfo } from 'src/services/api'; // 根据工号查姓名
import { add, update } from '../../servers/api';
import DynamicForm from '../DynamicForm';

const { Item: FormItem } = Form;
const Add = props => {
  const {
    onOk,
    visible,
    initialValues,
    id,
    zoneCode,
    roleCode,
    ...rest
  } = props;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [timeId, setTimeId] = useState([]);
  useEffect(() => {
    if (initialValues) {
      initialValues.list = initialValues.list.map(item => {
        const time = [];
        time[0] = moment(item.startTime);
        time[1] = moment(item.endTime);
        delete item.startTime;
        delete item.endTime;
        item.time = time;
        return item;
      });
      form.setFieldsValue({
        zoneCode: initialValues.zoneCode,
        userNo: initialValues.userNo,
        userName: initialValues.userName,
        position: initialValues.position,
        list: initialValues.list,
      });
    }
    if (roleCode === 'tp00001') {
      form.setFieldsValue({
        zoneCode,
      });
    }
  }, [visible]);

  const formProps = {
    form,
    labelCol: { md: { span: 8 }, xs: { span: 8 } },
    wrapperCol: { md: { span: 16 } },
    initialValues: {
      list: [
        {
          shiftType: undefined, // 班次类型
          time: [], // 日期范围
        },
      ],
    },
  };

  // 根据组员工号查询 姓名 岗位
  const getDetailInfor = value => {
    if (value !== '') {
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          getEmpDeptDetailInfo(value).then(res => {
            if (res.obj) {
              const { empDutyName, empName } = res.obj;
              form.setFieldsValue({
                position: empDutyName,
                userName: empName,
              });
            } else {
              form.setFieldsValue({
                position: '',
                userName: '',
              });
            }
          });
        }, 500),
      );
    } else {
      form.setFieldsValue({ position: '', userName: '' });
    }
  };

  // 提交时请求
  const submit = async () => {
    const initialValue = await form.validateFields();
    const param = cloneDeep(initialValue);
    param.list = param.list.map(item => {
      item.startTime = moment(item.time[0]).format('YYYY-MM-DD');
      item.endTime = moment(item.time[1]).format('YYYY-MM-DD');
      delete item.time;
      return item;
    });
    const res = initialValues ? await update(param) : await add(param);
    setLoading(true);
    if (res.success) {
      setLoading(false);
      success(`${initialValues ? '修改' : '新增'}成功！`);
      onOk();
    } else {
      error(res.errorMessage);
      setLoading(false);
    }
  };
  const handleCancel = () => {
    form.resetFields();
  };

  return (
    <Modal
      title={`${initialValues ? '编辑' : '新增'}`}
      maskClosable={false}
      onCancel={handleCancel}
      onOk={submit}
      visible={visible}
      {...rest}
      footer={[
        <Button key="back" onClick={rest.onCancel}>
          取消
        </Button>,
        <Button key="submit" loading={loading} type="primary" onClick={submit}>
          确定
        </Button>,
      ]}
    >
      <Form {...formProps}>
        <Row>
          <Col md={10}>
            <FormItem
              label="网点编码"
              name="zoneCode"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <DeptSearch
                disabled={roleCode === 'tp00001'}
                placeholder="请输入中转场名称或者代码"
                allowClear
              />
            </FormItem>
          </Col>
          <Col md={10}>
            <FormItem
              label="工号"
              name="userNo"
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
            >
              <Input
                placeholder="请输入"
                onChange={e => {
                  getDetailInfor(e.target.value);
                }}
                allowClear
              />
            </FormItem>
          </Col>
          <Col md={10}>
            <FormItem label="姓名" name="userName">
              <Input disabled placeholder="请输入" />
            </FormItem>
          </Col>
          <Col md={10}>
            <FormItem label="岗位" name="position">
              <Input disabled placeholder="请输入" />
            </FormItem>
          </Col>
        </Row>
        <DynamicForm id={id} propsName="list" />
      </Form>
    </Modal>
  );
};

export default Add;
