import React, { useState, useEffect, Fragment, PureComponent } from 'react';
import { connect } from 'dva';
import {
  Col,
  Form,
  Input,
  Button,
  Select,
  DatePicker,
  Row,
  notification,
} from 'antd';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  rangePickerColStyle,
  rangePickerLayout,
  formItemLayout,
  colStyle,
  AuthButton,
  SearchFold,
  rowStyle,
  useModuleCode,
} from 'ky-giant';
import { timeLimit } from 'src/utils/utils';
import StandardTable from 'src/components/StandardTable'; // table组件
import AsyncExport from 'src/components/AsyncExport'; // 异步导出
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import { searchTaskList, taskAllAccrueWeightSum } from '../services/api';
import {
  operateTypeList,
  userTypeList,
  // lineTypeList,
  vehicleTypeList,
} from '../Components/status';
import SuppliersSearch from '../Components/SuppliersSearch'; // 供应商查询组件
import SubTaskInfor from '../Components/SubTaskInfor';
import Edit from '../Components/Edit';
import './style.less';

const { Item: FormItem } = Form;
const { RangePicker } = DatePicker;
let inputParam = {};

const Page = ({ userInfo, areaListSF, areaListSX, logRoleCode }) => {
  const [form] = Form.useForm();
  const moduleCode = useModuleCode();
  const [selectedRows, setSelectedRows] = useState([]);
  const [editVisible, setEditVisible] = useState(false); // 修改框显示
  const [editValue, setEditValue] = useState({}); // 初始值
  const [arriveTrue, setArriveTrue] = useState(false); // 触发网点的动态校验
  // const [unloadShow, setUnloadShow] = useState(false); // 查询条件 卸车任务类型显示隐藏
  const [timeId, setTimeId] = useState([]);
  const [personalAccrueWeightSum, setPersonalAccrueWeightSum] = useState(0);
  const [loading, setLoading] = useState(false);
  const [taskInforVisible, setTaskInforVisible] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '工号',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'operateUserNo',
    },
    {
      title: '姓名',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'userName',
    },
    {
      title: '员工类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'userType',
      render: value => {
        const data = userTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '岗位',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'empDutyName',
    },
    {
      title: '网点',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'zoneCode',
    },
    {
      title: '战区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: value => {
        const areaList = areaListSF.concat(areaListSX);
        const date = areaList.find(item => item.value === value);
        return date ? date.label : value;
      },
    },
    {
      title: '作业环节',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'operateType',
      render: value => {
        const data = operateTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '卸车任务类型',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'isSorterUnload',
      render: (text, row) => (
        <Fragment>
          {row.operateType === 2 && text === 0 && <sapn>普通件卸车</sapn>}
          {row.operateType === 2 && text === 1 && <sapn>分拣机卸车</sapn>}
        </Fragment>
      ),
    },
    {
      title: '任务号',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'taskId',
    },
    {
      title: '子任务号',
      align: 'center',
      ellipsis: true,
      width: 220,
      dataIndex: 'flowId',
      render: (v, record) => (
        <Fragment>
          <Button
            size="small"
            type="link"
            onClick={e => subTaskInfor(e, record)}
          >
            {v}
          </Button>
        </Fragment>
      ),
    },
    {
      title: '交接单号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'relayBillId',
    },
    {
      title: '车标号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'logoNo',
    },
    {
      title: '线路编码',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'lineCode',
    },
    {
      title: '班次号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'requireId',
    },
    {
      title: '车辆类型',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'vehicleType',
      render: value => {
        const data = vehicleTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    // {
    //   title: '用车类型',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 100,
    //   dataIndex: 'lineTypes',
    //   // render: value => {
    //   //   const data = lineTypeList.find(item => item.value === value);
    //   //   return data ? data.label : value;
    //   // },
    // },

    {
      title: '月台号',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'platformNo',
    },
    {
      title: '班组组长',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'teamLeaderNo',
    },
    {
      title: '子任务总操作货量（T）',
      align: 'center',
      ellipsis: true,
      width: 250,
      dataIndex: 'totalWeight',
    },
    {
      title: '子任务操作人数',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'teamNum',
    },
    {
      title: '个人操作货量（T）',
      align: 'center',
      ellipsis: true,
      width: 250,
      dataIndex: 'avgWeightMultiNum',
    },
    {
      title: '件数',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'taskTotalNum',
    },
    {
      title: '车标操作人数',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'taskUserNum',
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierName',
    },
    {
      title: '供应商编号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierNo',
    },
    {
      title: '任务创建时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'startTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '任务完成时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'finishTime',
      render: v => moment(v).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '任务归属日期',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'taskSourceDate',
    },
    {
      title: '任务创建人工号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'creator',
    },
    {
      title: '车牌号',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'chinaPlateSerial',
    },
    {
      title: '发出网点',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'sourceZoneCode',
    },
    {
      title: '到达网点',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'destZoneCode',
    },
    {
      title: '修改人',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'modifier',
    },
    {
      title: '修改时间',
      align: 'center',
      ellipsis: true,
      width: 180,
      dataIndex: 'editTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '操作',
      align: 'center',
      width: 160,
      fixed: 'right',
      dataIndex: 'operation',
      render: (v, record) => (
        <Fragment>
          <Button size="small" type="link" onClick={() => update(record, true)}>
            查看
          </Button>
          {/* 未超过 归属日期在上个月及本月的可修改 */}
          {/* 超过 总部&&分拨区  任意月份都可修改 */}
          {/* 超过 非--总部&&分拨区  本月份可修改 */}
          {timeLimit(10) ? (
            moment(record.taskSourceDate).format('x') >
            moment()
              .endOf('month')
              .subtract(1, 'months')
              .format('x') ? (
              <AuthButton
                modulecode={moduleCode}
                code="transitTask-edit"
                size="small"
                type="link"
                onClick={() => update(record)}
              >
                修改
              </AuthButton>
            ) : (
              ''
            )
          ) : moment(record.taskSourceDate).format('x') >
            moment()
              .endOf('month')
              .subtract(2, 'months')
              .format('x') ? (
            <AuthButton
              modulecode={moduleCode}
              code="transitTask-edit"
              size="small"
              type="link"
              onClick={() => update(record)}
            >
              修改
            </AuthButton>
          ) : (
            ''
          )}
        </Fragment>
      ),
    },
  ];

  useEffect(() => {
    // 当前登录的角色判断
    if (
      logRoleCode &&
      userInfo &&
      userInfo.deptCode &&
      (logRoleCode.roleCode === 'tp00001' ||
        logRoleCode.roleCode === '88888888888')
    ) {
      form.setFieldsValue({
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationArea:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        time: [
          moment()
            .startOf('days')
            .subtract(1, 'day')
            .add(8, 'hour'),
          moment()
            .startOf('days')
            .add(8, 'hour'),
        ],
      });
      getQueryParams();
    } else {
      form.setFieldsValue({
        time: [
          moment()
            .startOf('days')
            .subtract(1, 'day')
            .add(8, 'hour'),
          moment()
            .startOf('days')
            .add(8, 'hour'),
        ],
      });
    }
  }, [logRoleCode, userInfo]);

  // 查询参数的获取
  const getQueryParams = () => {
    // form.validateFields().then(values => {
    //   if (!values.allocationArea && !values.zoneCode) {
    //     notification.warning({
    //       message: '校验提示',
    //       description: '所属网点和分拨区不能同时为空',
    //     });
    //     return false;
    //   }
    // const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    if (!params.allocationArea && !params.zoneCode) {
      notification.warning({
        message: '校验提示',
        description: '所属网点和分拨区不能同时为空',
      });
      return false;
    }
    inputParam = cloneDeep(params);
    inputParam.startTime =
      inputParam.time.length > 0
        ? moment(inputParam.time[0]).valueOf()
        : moment().startOf('month');
    inputParam.endTime =
      inputParam.time.length > 0
        ? moment(inputParam.time[1]).valueOf()
        : moment().endOf('month');
    if (inputParam.arriveCode || inputParam.startCode) {
      inputParam.lineCode = inputParam.arriveCode
        ? `${inputParam.startCode.trim()}${inputParam.arriveCode.trim()}`
        : `${inputParam.startCode.trim()}`;
      inputParam.lineCode = inputParam.lineCode.replace(/\s*/g, '');
    }
    delete inputParam.time;
    delete inputParam.startCode;
    delete inputParam.arriveCode;
    refresh({
      current: 1,
      pageSize: datas.pagination.pageSize,
    });
    return {
      ...inputParam,
    };
    // });
  };
  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      pageNum = pagination.current,
      pageSize = pagination.pageSize,
    } = pagination;
    const param = {
      ...inputParam,
      pageNum,
      pageSize,
      queryByBatch: false,
    };
    searchTaskList(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess && obj) {
          const { list, total } = obj;
          // setTotalNum(total);
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.pageNum,
              pageSize: obj.pageSize,
            },
            list,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setLoading(false);
        setSelectedRows([]);
      });
    taskAllAccrueWeightSum(param).then(res => {
      if (res.obj !== null) {
        setPersonalAccrueWeightSum(res.obj.personalAccrueWeightSum);
      } else {
        setPersonalAccrueWeightSum(res.obj);
      }
    });
  };

  // 关闭弹出层
  const closeModal = () => {
    setEditVisible(false);
  };

  const taskHandleCancel = () => {
    setTaskInforVisible(false);
  };

  // 人员详情
  const subTaskInfor = (e, record) => {
    e.stopPropagation();
    setEditValue(record);
    setTaskInforVisible(true);
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };
  // const getOperateType = vaule => {
  //   if (vaule === 2) {
  //     setUnloadShow(true);
  //   } else {
  //     setUnloadShow(false);
  //   }
  // };

  // 出发时间和到达时间的动态校验
  const getArriveCode = value => {
    if (value.trim() !== '') {
      clearTimeout(timeId);
      setTimeId(
        setTimeout(() => {
          setArriveTrue(true);
        }, 1000),
      );
    } else {
      setArriveTrue(false);
    }
  };

  const update = (value, bll) => {
    value.endTime = inputParam.endTime;
    value.startTime = inputParam.startTime;
    value.isEdit = bll;
    setEditValue(value);
    setEditVisible(true);
  };

  // // 是否按班次查询的判断&&查询条件切换
  // const getWorkDetail = value => {
  //   setIsWork(value);
  // };

  const handleStandardTableChange = pagination => {
    refresh({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  const resetForm = () => {
    form.resetFields();
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={{
        time: [
          moment()
            .startOf('days')
            .subtract(1, 'day')
            .add(8, 'hour'),
          moment()
            .startOf('days')
            .add(8, 'hour'),
        ],
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationArea:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
        // queryByBatch: true,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem
                label="网点"
                name="zoneCode"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (getFieldValue('allocationArea') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区和网点不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="分拨区"
                name="allocationArea"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (getFieldValue('zoneCode') || value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('分拨区和网点不能同时为空'),
                      );
                    },
                  }),
                ]}
              >
                <Select
                  allowClear
                  disabled={logRoleCode.roleCode === '88888888888'}
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="作业环节" name="operateType">
                <Select
                  // onChange={getOperateType}
                  allowClear
                  options={operateTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务类型" name="isSorterUnload">
                <Select
                  allowClear
                  options={[
                    {
                      label: '分拣机任务',
                      value: 1,
                      key: 1,
                    },
                    {
                      label: '离线任务',
                      value: 2,
                      key: 2,
                    },
                  ]}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="员工类型" name="userType">
                <Select
                  allowClear
                  options={userTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...rangePickerColStyle}>
              <FormItem
                {...rangePickerLayout}
                label="开始时间"
                name="time"
                rules={[
                  {
                    required: true,
                    message: '请填写',
                  },
                ]}
              >
                <RangePicker showTime allowClear style={{ width: '100%' }} />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组员工号" name="operateUserNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="供应商" name="supplierName">
                <SuppliersSearch placeholder="请输入供应商" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务号" name="taskId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="子任务号" name="flowId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="交接单号" name="relayBillId">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="车标号" name="logoNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="月台号" name="platformNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组长姓名" name="teamLeaderName">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组长工号" name="teamLeaderNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="任务创建人工号" name="creator">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem
                label="出发网点"
                name="startCode"
                rules={[
                  {
                    required: arriveTrue,
                    message: '请填写',
                  },
                ]}
              >
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="到达网点" name="arriveCode">
                <Input
                  placeholder="请输入"
                  allowClear
                  onChange={e => {
                    getArriveCode(e.target.value);
                  }}
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="车辆类型" name="vehicleType">
                <Select
                  options={vehicleTypeList}
                  placeholder="请选择车辆类型"
                  allowClear
                />
              </FormItem>
            </Col>
            {/* <Col {...colStyle}>
              <FormItem label="用车类型" name="lineType">
                <Select
                  options={lineTypeList}
                  placeholder="请选择用车类型"
                  allowClear
                />
              </FormItem>
            </Col> */}
            <Col {...colStyle}>
              <FormItem label="班次号" name="requireId">
                <Input placeholder="请输入班次号" allowClear />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div>
          <AsyncExport
            type="primary"
            disabled={datas.list && datas.list.length < 1}
            // moudeCode={moudeCode}
            // code="personExport"
            text="任务导出"
            options={{
              requstParams: [
                '/tdmsAccrueService/accrueRestService/asyncExportAccrueTaskWeb',
                {
                  method: 'POST',
                  body: getQueryParams,
                },
              ],
            }}
          />
          <span
            style={{
              marginLeft: 100,
              fontSize: 18,
            }}
          >
            合计总操作货量 : {personalAccrueWeightSum || '--'}
          </span>
        </div>
      </div>
    </Form>
  );

  return (
    <div>
      <div className="table-list">
        <div className="tableListForm">{renderForm()}</div>
        <StandardTable
          size="small"
          rowKey={(record, index) => `${index}${record.operateUserNo}`}
          selectedRows={selectedRows}
          showSelection={false}
          loading={loading}
          data={datas}
          columns={columns}
          onSelectRow={handleSelectRows}
          onChange={handleStandardTableChange}
        />
      </div>

      {editVisible ? (
        <Edit
          refresh={refresh}
          initialValue={editValue}
          visible={editVisible}
          handleCancel={closeModal}
        ></Edit>
      ) : null}
      {/* 子任务明细 */}
      {taskInforVisible && (
        <SubTaskInfor
          beChose={editValue}
          visible={taskInforVisible}
          handleCancel={taskHandleCancel}
        ></SubTaskInfor>
      )}
    </div>
  );
};

@connect(state => ({
  logRoleCode: state.global.logRoleCode,
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
}))
class Container extends PureComponent {
  render() {
    const { areaListSX, areaListSF, userInfo, logRoleCode } = this.props;
    return (
      <Page
        logRoleCode={logRoleCode}
        userInfo={userInfo}
        areaListSX={areaListSX}
        areaListSF={areaListSF}
      />
    );
  }
}

export default withRouter(Container);
