import React, { useState, useEffect } from 'react';
import { Modal } from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import { connect } from 'dva';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
// import { AsyncExport } from 'ky-giant';
import { getTaskItem } from '../../services/api';

const columns = [
  {
    title: '运单号',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'waybillNo',
  },
  {
    title: '开单计重',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'meterageWeight',
  },
  {
    title: '有效计重',
    align: 'center',
    ellipsis: true,
    width: 100,
    dataIndex: 'validMeterageWeight',
  },
  {
    title: '计重是否变更',
    align: 'center',
    ellipsis: true,
    width: 80,
    dataIndex: 'waybillChanged',
    render: text => {
      switch (text) {
        case 0:
          return '否';
        case 1:
          return '是';
        default:
          return '';
      }
    },
  },
  {
    title: '班组变更内容',
    align: 'center',
    ellipsis: true,
    width: 250,
    dataIndex: 'teamChangeRecords',
  },
];
function DetaiInfor(props) {
  const { visible, beChose, handleCancel } = props;
  const { flowId, operateType, taskId, zoneCode } = beChose;
  // const [totalNum, setTotalNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const cancle = () => {
    handleCancel();
  };
  const [tableData, setTableData] = useState({
    list: [],
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
  });

  const getQueryParams = (pagination = {}) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    refresh({ pageNum, pageSize });
    return {
      pageNum,
      pageSize,
      flowId,
      operateType,
      taskId,
      zoneCode,
    };
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = { flowId, operateType, pageNum, pageSize, taskId, zoneCode };
    getTaskItem(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { rows, pageNum: current, total } = obj;
          // setTotalNum(total);
          setLoading(false);
          setTableData({
            pagination: {
              total,
              current,
              pageSize,
            },
            list: rows,
          });
        } else {
          setLoading(false);
          setTableData({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => setLoading(false));
  };
  const handleStandardTableChange = pagination => {
    refresh({
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    });
  };

  useEffect(() => {
    refresh();
  }, [visible]);
  return (
    <Modal
      centered
      title={null}
      visible={visible}
      onCancel={cancle}
      width={1300}
      footer={null}
    >
      <div>
        <AsyncExport
          icon={<ExportOutlined />}
          // moduleCode={moduleCode}
          // code="accrueManage-exportAll"
          handleCancel={handleCancel}
          // type="primary"
          disabled={tableData.list && tableData.list.length < 1}
          text="导出全部"
          options={{
            requstParams: [
              '/tdmsAccrueService/itemQueryService/asyncExportTaskItem',
              {
                method: 'POST',
                body: getQueryParams,
              },
            ],
          }}
        />
      </div>
      <StandardTable
        size="small"
        rowKey="id"
        showSelection={false}
        bordered={false}
        multiple={false}
        loading={loading}
        data={tableData}
        columns={columns}
        onChange={handleStandardTableChange}
      />
    </Modal>
  );
}
export default connect(() => ({}))(props => (
  <DetaiInfor {...props}></DetaiInfor>
));
