import React, { useState, useEffect, PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { Row, Col, Form, Input, Button, Select, DatePicker } from 'antd';
import { withRouter } from 'react-router-dom';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import {
  // rangePickerColStyle,
  // rangePickerLayout,
  formItemLayout,
  colStyle,
  rowStyle,
  SearchFold,
} from 'ky-giant';
import StandardTable from 'src/components/StandardTable';
import AsyncExport from 'src/components/AsyncExport';
import authDecorator from 'src/components/AuthDecorator';
import DeptSearch from 'src/components/DeptSearch'; // 网点代码查询
import { searchPersonList, personAllAccrueWeightSum } from '../services/api';
import { operateTypeList, userTypeList } from '../Components/status';
import SuppliersSearch from '../Components/SuppliersSearch';
import PersonDetail from '../Components/PersonDetail';
import './index.scss';
const { Item: FormItem } = Form;
const spanStyle = {
  fontWeight: 700,
  color: '#dc1e32',
};

let inputParam = {};

const Page = ({ logRoleCode, userInfo, areaListSF, areaListSX }) => {
  const [form] = Form.useForm();
  const [selectedRows, setSelectedRows] = useState([]);
  const [editVisible, setEditVisible] = useState(false); // 编辑框显示
  const [initialValues, setInitialValues] = useState(false); // 初始值
  const [totalWeightSum, setTotalWeightSum] = useState(0); // 合计操作货量
  const [totalAccrueAmountSum, setTotalAccrueAmountSum] = useState(0); // 合计计提
  const [loading, setLoading] = useState(false);
  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });

  const columns = [
    {
      title: '工号',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'operateUserNo',
    },
    {
      title: '姓名',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'userName',
    },
    {
      title: '岗位',
      align: 'center',
      ellipsis: true,
      width: 100,
      dataIndex: 'empDutyName',
    },
    {
      title: '员工类型',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'userType',
      render: value => {
        const data = userTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '网点',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'zoneCode',
    },
    {
      title: '省区',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'provincialArea',
    },
    {
      title: '区域',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'regionArea',
    },
    {
      title: '分拨区',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'allocationArea',
      render: value => {
        const areaList = areaListSF.concat(areaListSX);
        const date = areaList.find(item => item.value === value);
        return date ? date.label : value;
      },
    },
    {
      title: '作业环节',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'operateType',
      render: value => {
        const data = operateTypeList.find(item => item.value === value);
        return data ? data.label : value;
      },
    },
    {
      title: '累计操作货量（T）',
      align: 'center',
      ellipsis: true,
      width: 250,
      dataIndex: 'totalAccrueWeight',
    },
    {
      title: '累计计提（元）',
      align: 'center',
      ellipsis: true,
      width: 250,
      dataIndex: 'totalAccrueAmount',
    },
    {
      title: '出勤天数',
      align: 'center',
      ellipsis: true,
      width: 120,
      dataIndex: 'actuallyAttendanceDay',
    },
    {
      title: '供应商',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierName',
    },
    {
      title: '供应商编号',
      align: 'center',
      ellipsis: true,
      width: 150,
      dataIndex: 'supplierNo',
    },
    {
      title: '1号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_01',
      // render: text => text || '--',
      render: (text, row) => (
        <span style={row.warning_01 ? spanStyle : null}>{text}</span>
      ),
    },
    // {
    //   title: '修改状态',
    //   dataIndex: 'isModifyName',
    //   render: text => {
    //     if (text === '已修改') {
    //       return (
    //         <div style={divStyle}>
    //           <span style={{ ...iconStyle, backgroundColor: '#0CBF5B' }}></span>
    //           <span>{text}</span>
    //         </div>
    //       );
    //     }
    //     return (
    //       <div style={divStyle}>
    //         <span style={{ ...iconStyle, backgroundColor: '#bfbfbf' }}></span>
    //         <span>{text}</span>
    //       </div>
    //     );
    //   },
    //   width: 100,
    // },
    {
      title: '2号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_02',
      render: (text, row) => (
        <span style={row.warning_02 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '3号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_03',
      render: (text, row) => (
        <span style={row.warning_03 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '4号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_04',
      render: (text, row) => (
        <span style={row.warning_04 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '5号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_05',
      render: (text, row) => (
        <span style={row.warning_05 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '6号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_06',
      render: (text, row) => (
        <span style={row.warning_06 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '7号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_07',
      render: (text, row) => (
        <span style={row.warning_07 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '8号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_08',
      render: (text, row) => (
        <span style={row.warning_08 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '9号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_09',
      render: (text, row) => (
        <span style={row.warning_09 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '10号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_10',
      render: (text, row) => (
        <span style={row.warning_10 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '11号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_11',
      render: (text, row) => (
        <span style={row.warning_11 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '12号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_12',
      render: (text, row) => (
        <span style={row.warning_12 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '13号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_13',
      render: (text, row) => (
        <span style={row.warning_13 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '14号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_14',
      render: (text, row) => (
        <span style={row.warning_14 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '15号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_15',
      render: (text, row) => (
        <span style={row.warning_15 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '16号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_16',
      render: (text, row) => (
        <span style={row.warning_16 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '17号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_17',
      render: (text, row) => (
        <span style={row.warning_17 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '18号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_18',
      render: (text, row) => (
        <span style={row.warning_18 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '19号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_19',
      render: (text, row) => (
        <span style={row.warning_19 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '20号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_20',
      render: (text, row) => (
        <span style={row.warning_20 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '21号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_21',
      render: (text, row) => (
        <span style={row.warning_21 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '22号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_22',
      render: (text, row) => (
        <span style={row.warning_22 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '23号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_23',
      render: (text, row) => (
        <span style={row.warning_23 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '24号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_24',
      render: (text, row) => (
        <span style={row.warning_24 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '25号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_25',
      render: (text, row) => (
        <span style={row.warning_25 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '26号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_26',
      render: (text, row) => (
        <span style={row.warning_26 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '27号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_27',
      render: (text, row) => (
        <span style={row.warning_27 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '28号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_28',
      render: (text, row) => (
        <span style={row.warning_28 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '29号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_29',
      render: (text, row) => (
        <span style={row.warning_29 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '30号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_30',
      render: (text, row) => (
        <span style={row.warning_30 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },
    {
      title: '31号',
      align: 'center',
      ellipsis: true,
      width: 80,
      dataIndex: 'weight_31',
      render: (text, row) => (
        <span style={row.warning_31 ? spanStyle : null}>
          {text ? Math.floor(text * 100) / 100 : ''}
        </span>
      ),
    },

    {
      title: '操作',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: (v, record) => (
        <Fragment>
          <Button size="small" type="link" onClick={() => update(record)}>
            查看
          </Button>
        </Fragment>
      ),
    },
  ];

  const update = value => {
    setInitialValues(value);
    setEditVisible(true);
  };

  const resetForm = () => {
    form.resetFields();
  };

  useEffect(() => {
    if (
      logRoleCode &&
      userInfo &&
      userInfo.deptCode &&
      (logRoleCode.roleCode === 'tp00001' ||
        logRoleCode.roleCode === '88888888888')
    ) {
      form.setFieldsValue({
        createTime: moment(new Date()),
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationArea:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
      });
      getQueryParams();
    } else {
      form.setFieldsValue({
        createTime: moment(new Date()),
      });
      getQueryParams();
    }
  }, [userInfo, logRoleCode]);

  // 查询参数的获取
  const getQueryParams = () => {
    const params = form.getFieldValue();
    if (params.createTime) {
      params.startTime = moment(params.createTime)
        .startOf('month')
        .format('x');
      params.endTime = moment(params.createTime)
        .endOf('month')
        .format('x');
    }
    inputParam = cloneDeep(params);
    delete inputParam.createTime;
    refresh({
      current: 1,
      pageSize: datas.pagination.pageSize,
    });
    return {
      ...inputParam,
    };
    // });
  };

  const refresh = (pagination = {}) => {
    setLoading(true);
    const {
      pageNum = pagination.current,
      pageSize = pagination.pageSize,
    } = pagination;
    const param = { ...inputParam, pageNum, pageSize };
    searchPersonList(param)
      .then(res => {
        const { success: resSuccess, obj } = res;
        if (resSuccess) {
          const { rows, total } = obj;
          const rowList = rows.map(({ extendAttach, ...rest }) => ({
            ...JSON.parse(extendAttach),
            ...rest,
          }));
          setLoading(false);
          setDatas({
            pagination: {
              total,
              current: obj.currentPage,
              pageSize: obj.limit,
            },
            list: rowList,
          });
        } else {
          setSelectedRows([]);
          setLoading(false);
          setDatas({
            pagination: {
              total: 0,
              current: 1,
              pageSize: 10,
            },
            list: [],
          });
        }
      })
      .catch(() => {
        setLoading(false);
        setSelectedRows([]);
      });
    personAllAccrueWeightSum(param).then(res => {
      if (res.obj !== null) {
        setTotalWeightSum(res.obj.totalWeightSum);
        setTotalAccrueAmountSum(res.obj.totalAccrueAmountSum);
      } else {
        setTotalWeightSum(undefined);
        setTotalAccrueAmountSum(undefined);
      }
    });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = pagination => {
    refresh({
      current: pagination.current,
      pageSize: pagination.pageSize,
    });
  };
  const closeModal = () => {
    setEditVisible(false);
  };

  // 查询条件
  const renderForm = () => (
    <Form
      {...formItemLayout}
      form={form}
      onFinish={getQueryParams}
      className="tableListForm"
      initialValues={{
        createTime: moment(new Date()),
        zoneCode:
          logRoleCode.roleCode === 'tp00001' ? userInfo.deptCode : undefined,
        allocationArea:
          logRoleCode.roleCode === '88888888888'
            ? userInfo.areaCode
            : undefined,
      }}
    >
      <div className="search-con">
        <SearchFold rowHeight={48} onReset={resetForm}>
          <Row {...rowStyle}>
            <Col {...colStyle}>
              <FormItem label="网点" name="zoneCode">
                <DeptSearch
                  placeholder="请输入中转场名称或者代码"
                  disabled={logRoleCode.roleCode === 'tp00001'}
                  allowClear
                />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="分拨区" name="allocationArea">
                <Select
                  disabled={logRoleCode.roleCode === '88888888888'}
                  allowClear
                  options={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="作业环节" name="operateType">
                <Select
                  allowClear
                  options={operateTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="员工类型" name="userType">
                <Select
                  allowClear
                  options={userTypeList}
                  placeholder="请选择"
                ></Select>
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="组员工号" name="operateUserNo">
                <Input placeholder="请输入" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="供应商" name="supplierName">
                <SuppliersSearch placeholder="请输入供应商" allowClear />
              </FormItem>
            </Col>
            <Col {...colStyle}>
              <FormItem label="查询月份" name="createTime">
                <DatePicker
                  showToday
                  picker="month"
                  allowClear
                  style={{ width: '100%' }}
                />
              </FormItem>
            </Col>
          </Row>
        </SearchFold>
      </div>
      <div className="btn-con">
        <div style={{ display: 'flex' }}>
          <div style={{ width: '150px' }}>
            <AsyncExport
              type="primary"
              disabled={datas.list && datas.list.length < 1}
              // code="personExport"
              text="人员导出"
              options={{
                requstParams: [
                  '/tdmsAccrueService/accrueRestService/asyncExportAccrueWeb',
                  {
                    method: 'POST',
                    body: getQueryParams,
                  },
                ],
              }}
            />
          </div>

          <div style={{ flex: 1, lineHeight: '30px' }}>
            <span style={{ marginLeft: 20, fontSize: 16 }}>
              合计总操作货量 : {totalWeightSum || '--'}
            </span>
            <span style={{ marginLeft: 30, fontSize: 16 }}>
              合计计提 : {totalAccrueAmountSum || '--'}
            </span>
          </div>
        </div>
      </div>
    </Form>
  );

  return (
    <div className="table-list">
      <div className="tableListForm">{renderForm()}</div>
      <StandardTable
        size="small"
        rowKey={(record, index) => index + record.operateUserNo}
        selectedRows={selectedRows}
        showSelection={false}
        loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
      {editVisible ? (
        <PersonDetail
          initialValues={initialValues}
          areaCode={userInfo.orgCode === 'SX' ? areaListSX : areaListSF}
          visible={editVisible}
          handleCancel={closeModal}
          // roles={roles}
        ></PersonDetail>
      ) : null}
    </div>
  );
};

@connect(state => ({
  userInfo: state.global.userInfo,
  areaListSF: state.global.areaListSF,
  areaListSX: state.global.areaListSX,
  logRoleCode: state.global.logRoleCode,
}))
@authDecorator
class Container extends PureComponent {
  render() {
    const { userInfo, logRoleCode, areaListSX, areaListSF } = this.props;
    return (
      <Page
        userInfo={userInfo}
        logRoleCode={logRoleCode}
        areaListSX={areaListSX}
        areaListSF={areaListSF}
      />
    );
  }
}

export default withRouter(Container);
