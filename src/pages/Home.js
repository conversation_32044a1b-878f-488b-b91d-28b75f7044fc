import React from 'react';
import { connect } from 'dva';

export default connect(({ tabHistory }) => ({
  tabHistory,
}))(() => (
  // const { dispatch } = props;
  // function click() {
  // dispatch({
  //   type: 'tabHistory/custom',
  //   data: {
  //     path: `/bill/add?time=${Date.now()}`,
  //     moduleName: '新增',
  //     moduleIcon: '',
  //   },
  // });
  // }
  <div style={{ color: '#000', fontSize: 30, padding: 50 }}>
    欢迎访问大件计提系统
  </div>
));
