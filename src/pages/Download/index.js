import React, { useState, PureComponent, useEffect, Fragment } from 'react';
import { connect } from 'dva';
import rawRequest from 'dva/fetch';
import {
  Row,
  Col,
  DatePicker,
  Form,
  Input,
  Button,
  Modal,
  // Select,
  Tabs,
  Radio,
  Badge,
  message,
  notification,
} from 'antd';
import { success, error } from '@/utils/utils';

import { withRouter } from 'react-router-dom';
import moment from 'moment';
import request from '@/utils/request';
import StandardTable from '@/components/StandardTable';
import Config from '../../config';
import './index.scss';

const { Item: FormItem } = Form;
const { Search } = Input;
// const { Option } = Select;
const { TabPane } = Tabs;
const { Group: RadioGroup, Button: RadioButton } = Radio;

const tableTitle = '';
const rowKey = 'id';
const API = {
  getToken: data =>
    request(`/exportEntranceServices/taskManagerRestService/getFileToken`, {
      method: 'POST',
      body: data,
    }),
  read: data =>
    request(`/exportEntranceServices/taskManagerRestService/selectTaskList`, {
      method: 'POST',
      body: data,
    }),

  remove: data =>
    request(`/exportEntranceServices/taskManagerRestService/deleteTasks`, {
      method: 'POST',
      body: data,
    }),
};

const handleOptions = [
  { key: -1, value: -1, label: '全部' },
  { key: 0, value: 0, label: '处理中' },
  { key: 1, value: 1, label: '完成' },
  { key: 2, value: 2, label: '异常' },
];
let inputParam = {};

// const refreshColumn = data => {
//   debugger;
//   data;
// };
const https = !!window.location.origin.match(/^https/);
let timer; // 定义一个轮询
let historyParam;
const Page = pageParams => {
  const { listKey, dispatch } = pageParams;

  const [form] = Form.useForm();
  const [tabKey, setTabKey] = useState('1');

  const [datas, setDatas] = useState({
    pagination: {
      total: 0,
      current: 1,
      pageSize: 10,
    },
    list: [],
  });
  // useMemo(() => refreshColumn(datas.list), [datas.list]);
  const [selectedRows, setSelectedRows] = useState([]);

  // const [keys, setKeys] = useState(listKey);
  // const [loading, setLoading] = useState(false);

  // const [timeFlag, setTimeFlag] = useState(false);

  // let timeFlag = false;
  const downloadBtn = record => {
    const [downloading, setDownloading] = useState(false);
    const download = v => {
      const { path, taskUi } = v;
      setDownloading(true);

      API.getToken({ url: path })
        .then(res => {
          const data = res.obj.token;
          if (data) return data;
        })
        .then(data => {
          // exportFile(path, data, taskUi, v);
          exportFile(path, data, taskUi);
          // v.downLoading = false;
        });
    };

    // const exportFile = (path, token, fileName, v) => {
    const exportFile = (path, token, fileName) => {
      const requstParams = [
        path,
        {
          method: 'GET',
          responseType: 'blob',
          // cache: 'force-cache',
        },
      ];
      requstParams[1].withCredentials = false;
      const body =
        typeof requstParams[1].body === 'function'
          ? requstParams[1].body()
          : requstParams[1].body;
      requstParams[1].body = requstParams[1].body
        ? JSON.stringify(body)
        : undefined;

      requstParams[1].headers = {
        userId: sessionStorage.userid,
        'sgs-userid': sessionStorage.userid,
        'X-Auth-Token': token,
        systemKey: Config.systemKey,
        'Content-Type': 'application/json; charset=utf-8',
        'gw-bdus-rid': sessionStorage.getItem('roleId') || '',
      };
      rawRequest(...requstParams).then(data => {
        data.blob().then(blob => {
          if (typeof FileReader === 'undefined') {
            notification.open({
              message: '您的浏览器不支持 FileReader，请升级浏览器',
            });
          }
          const reader = new FileReader();
          reader.addEventListener('loadend', () => {
            let resu = '';
            try {
              resu = JSON.parse(reader.result);
              // resu = eval('('+ reader.result + ')')
              if (resu.code === 500) {
                notification.open({
                  message: resu.msg,
                });
              } else if (resu.code === 401) {
                notification.error({
                  message: resu.msg,
                });
              }
            } catch (e) {
              // 判断文件类型
              const slashPos = path.lastIndexOf('/');
              const mediaName = path.slice(slashPos + 1);
              const mediaType = mediaName.split('.')[1];
              // 捕获错误 说明是文本字符串
              resu = reader.result;
              downloadBlob(blob, `${fileName}.${mediaType}`);
            }
          });
          reader.readAsText(blob);

          // 下载
          function downloadBlob(blob_, fileNames) {
            const blobUrl = window.URL.createObjectURL(blob_);
            const a = document.createElement('a');
            a.href = blobUrl;
            a.target = '_blank';
            a.style.display = 'none';
            a.download = fileNames;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(blobUrl);
            document.body.removeChild(a);
            setDownloading(false);
          }
        });
      });
    };
    return (
      <Fragment>
        {record.path && (
          <Button
            loading={downloading}
            type="link"
            onClick={() => download(record)}
          >
            下载
          </Button>
        )}
      </Fragment>
    );
  };
  const rawColumns = [
    {
      title: '任务界面',

      width: 150,
      ellipsis: true,
      dataIndex: 'taskUi',
    },
    {
      title: `导入文件名称`,

      ellipsis: true,
      width: 100,
      dataIndex: 'fileName',
    },
    {
      title: '处理状态',

      ellipsis: true,
      width: 150,
      dataIndex: 'state',
      render: v =>
        ({
          0: <Badge status="processing" text="处理中" />,
          1: <Badge status="success" text="处理完成" />,
          2: <Badge status="error" text="处理异常" />,
        }[v]),
    },
    {
      title: '创建时间',

      width: 200,
      ellipsis: true,
      dataIndex: 'createdTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '处理开始时间',

      width: 200,
      ellipsis: true,
      dataIndex: 'startTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '处理结束时间',

      width: 200,
      ellipsis: true,
      dataIndex: 'endTime',
      render: v => (v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : ''),
    },

    {
      title: '路径',

      width: 80,
      ellipsis: true,
      dataIndex: 'siteType',
      render: (v, record) => downloadBtn(record),
    },

    {
      title: '备注',

      ellipsis: true,
      width: 200,
      dataIndex: 'remark',
    },
  ];
  // const memoizedColumns = useMemo(() => refreshColumn(rawColumns), [
  //   rawColumns,
  // ]);
  const [columns, setColumns] = useState(rawColumns);

  const handleTabChange = tbk => {
    if (tbk === '1') {
      setColumns(columns.filter(({ dataIndex }) => dataIndex !== 'fileName'));
    } else {
      setColumns(rawColumns);
    }
  };

  const getQueryParams = (pagination = {}, type) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const params = form.getFieldValue();
    if (params.startTime) {
      inputParam = {
        ...params,
        startTime: params.startTime
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
      };
    } else {
      inputParam = {
        ...params,
      };
    }
    if (timer) {
      clearTimeout(timer);
      timer = '';
    }

    // setSearchParams(params);
    historyParam = {
      ...inputParam,
      pageNum,
      pageSize,
      type: type || tabKey,
      https,
    };
    // setTimeFlag(false);
    refresh({ pageNum, pageSize }, type || tabKey);

    return {
      ...params,
    };
  };
  const curC = (pagination, type) => {
    // setTimeFlag(true);
    timer = setTimeout(() => {
      refresh(pagination, type);
    }, 5000);
  };
  const refresh = async (pagination = {}, type) => {
    const { currentPage: pageNum = 1, pageSize = 10 } = pagination;
    const param = { ...inputParam, pageNum, pageSize, type, https };

    if (timer) {
      clearTimeout(timer);
    }
    if (timer && JSON.stringify(historyParam) !== JSON.stringify(param)) {
      return;
    }

    historyParam = param;
    const res = await API.read(param);
    const { success: resSuccess, obj } = res;
    if (resSuccess) {
      const { list, pageNum: current, total } = obj;
      const hasUnfinished = list.some(item => item.state === 0);
      if (list.length > 0 && hasUnfinished) {
        curC(pagination, type);
      } else {
        // setTimeFlag(false);
        clearTimeout(timer);
      }
      setSelectedRows([]);
      setDatas({
        pagination: {
          total,
          current,
          pageSize,
        },
        list,
      });
    } else {
      setSelectedRows([]);
      setDatas({
        pagination: {
          total: 0,
          current: 1,
          pageSize: 10,
        },
        list: [],
      });
    }

    // });
  };

  const handleSelectRows = rows => {
    setSelectedRows(rows);
  };

  const handleStandardTableChange = (
    pagination, // filtersArg, sorter
  ) => {
    if (timer) {
      clearTimeout(timer);
      timer = '';
    }
    historyParam = {
      ...inputParam,
      pageNum: pagination.current,
      pageSize: pagination.pageSize,
      type: tabKey,
      https,
    };

    refresh(
      {
        currentPage: pagination.current,
        pageSize: pagination.pageSize,
      },
      tabKey,
    );
  };

  const pageType = type => ({ import: '0', export: '1', list: tabKey }[type]);
  useEffect(() => {
    setTabKey(pageType(listKey));
    handleTabChange(pageType(listKey));
    if (listKey !== 'list') {
      getQueryParams({}, pageType(listKey));
    }
    dispatch({
      type: 'global/updateListKey',
      data: 'list',
    });
    // getQueryParams({}, listKey);
  }, [listKey]);
  useEffect(() => {
    if (listKey === 'list') {
      getQueryParams({}, tabKey);
    }
    // refresh({}, tabKey);
  }, [tabKey]);

  const remove = record => {
    const idList = [];
    const hasUnfinished = record.some(i => i.state === 0);
    if (hasUnfinished) {
      message.warning('请勿选择删除未处理完成的数据~');
      return;
    }
    if (record instanceof Array) {
      for (const item of record) {
        idList.push(item.id);
      }
    } else {
      idList.push(record.id);
    }
    Modal.confirm({
      title: '提示',
      content: '确定删除当前数据',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const res = await API.remove({
          ids: idList,
          type: +tabKey,
        });
        if (res.success) {
          success('删除成功');
          getQueryParams({}, tabKey);
        } else {
          error('删除失败');
        }
      },
      onCancel() {},
    });
  };

  const renderForm = () => (
    <Form
      className="select-form"
      form={form}
      onFinish={getQueryParams}
      initialValues={{ state: -1 }}
    >
      {/* <Card> */}
      <div className="delete-list">
        <div className="left-float">
          <Button
            disabled={!selectedRows.length}
            onClick={() => remove(selectedRows)}
            type="danger"
          >
            删除
          </Button>
          <FormItem name="startTime">
            <DatePicker
              style={{ width: '120px', marginLeft: '10px' }}
              onChange={() => {
                // form.setFieldsValue({ startTime: dateString });
                getQueryParams();
              }}
            />
          </FormItem>
        </div>
      </div>
      <Row>
        <Col md={15} sm={24} xs={24}>
          <FormItem name="state">
            <RadioGroup>
              {handleOptions.map(item => (
                <RadioButton
                  key={item.key}
                  value={item.value}
                  onChange={e => {
                    form.setFieldsValue({ state: e.target.value });
                    getQueryParams();
                  }}
                >
                  {item.label}
                </RadioButton>
              ))}
            </RadioGroup>
          </FormItem>
        </Col>
        <Col md={8} sm={24} xs={24}>
          <FormItem name="displayView">
            <Search
              placeholder="请输入任务界面"
              onSearch={() => getQueryParams()}
            />
          </FormItem>
        </Col>
      </Row>
    </Form>
  );

  return (
    <div className="download-list">
      {/* <div className="tableListForm">{renderForm()}</div> */}
      <Tabs
        tabBarExtraContent={renderForm()}
        animated={false}
        activeKey={tabKey}
        // defaultActiveKey={tabKey}
        onChange={e => {
          // setKeys(+e);
          setTabKey(e);
          handleTabChange(e);
          // setLoading(true);
          dispatch({
            type: 'global/updateListKey',
            data: 'list',
          });
        }}
      >
        <TabPane tab="导入结果" key="0"></TabPane>
        <TabPane tab="导出结果" key="1"></TabPane>
      </Tabs>
      <StandardTable
        size="small"
        rowKey={rowKey}
        title={tableTitle}
        selectedRows={selectedRows}
        // loading={loading}
        data={datas}
        columns={columns}
        onSelectRow={handleSelectRows}
        onChange={handleStandardTableChange}
      />
    </div>
  );
};
@connect(state => ({
  listKey: state.global.listKey,
  readyStatus: state.global.readyStatus,
}))
// @connect(listKey => ({ listKey }))
class Container extends PureComponent {
  render() {
    const { listKey, dispatch, readyStatus } = this.props;
    return readyStatus ? <Page listKey={listKey} dispatch={dispatch} /> : null;
  }
}

export default withRouter(Container);
