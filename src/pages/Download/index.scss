// @import '~antd/lib/style/themes/default.less';
// @import '~@/utils/utils.less';

.react-resizable {
  position: relative;
  background-clip: padding-box;
}

.react-resizable-handle {
  position: absolute;
  width: 10px;
  height: 100%;
  bottom: 0;
  right: -5px;
  cursor: col-resize;
  z-index: 1;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
  div.ant-typography {
    margin-bottom: 0;
  }
}
.ant-table-body {
  overflow: auto;
}

:global {
  .ant-input-affix-wrapper {
    display: flex;
  }
}

.tableListForm {
  .ant-card-body {
    padding-bottom: 0;
  }
  :global {
    .ant-input-affix-wrapper {
      // display: block;
    }
    .ant-form-item {
      display: flex;
      margin-right: 0;
      // margin-bottom: 24px;
      > .ant-form-item-label {
        // width: auto;
        // width: auto;
        padding-right: 8px;
        line-height: 32px;
      }
      .ant-form-item-control {
        line-height: 32px;
      }
    }
    .ant-form-item-control-wrapper {
      flex: 1;
    }
  }
  .submitButtons {
    display: block;
    // margin-bottom: 24px;
    white-space: nowrap;
  }
}

.ant-form-item-children {
  display: flex;
  span {
    flex: 1;
    text-indent: 5px;
  }
}
.download-list {
  padding-left: 10px;
  padding-top: 5px;
}
.select-form {
  padding-top: 5px;
  .delete-list {
    float: left;
    margin-right: 10px;
    .left-float {
      display: flex;
      &:not(:first-child) {
        margin-left: 10px;
      }
    }
  }
}

.ant-input-number {
  width: 100%;
}
