{"presets": [["@babel/preset-env", {"useBuiltIns": "entry", "corejs": "3", "targets": {"chrome": "49", "ie": "11"}}], "@babel/react"], "plugins": ["@babel/plugin-transform-runtime", "@babel/plugin-transform-spread", "@babel/plugin-syntax-dynamic-import", "react-activation/babel", "@babel/plugin-transform-async-to-generator", "babel-plugin-dva-hmr", "@babel/plugin-syntax-class-properties", ["@babel/plugin-proposal-decorators", {"legacy": true}], ["@babel/plugin-proposal-class-properties", {"loose": true}]]}